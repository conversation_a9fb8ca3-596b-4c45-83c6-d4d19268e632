<?php
/**
 * Тест для проверки устранения путаницы между именами заказчика и получателя
 */

echo "=== Тест устранения путаницы имен ===\n\n";

// Симулируем логику из исправленного кода
class ConfusionFixTest {
    
    // Логика из send_delivery_info (основная логика для CRM)
    public function get_customer_name_for_delivery($order) {
        // Получаем имя для CRM - приоритет: заказчик (billing), затем получатель (shipping)
        $billing_first_name = $order['billing_first_name'];
        $billing_last_name = $order['billing_last_name'];
        $customer_name = trim($billing_first_name . ' ' . $billing_last_name);

        echo "📋 Данные заказчика (billing): '{$billing_first_name} {$billing_last_name}' -> '{$customer_name}'\n";

        // Если имя заказчика пустое, используем имя получателя из данных доставки
        if (empty($customer_name)) {
            $shipping_first_name = $order['shipping_first_name'];
            $shipping_last_name = $order['shipping_last_name'];
            $customer_name = trim($shipping_first_name . ' ' . $shipping_last_name);

            if (!empty($customer_name)) {
                echo "⚠️ Имя заказчика пустое. Используется имя получателя из данных доставки: '{$customer_name}'\n";
            } else {
                // Если и в данных доставки пусто, используем email
                $email = $order['billing_email'];
                if (!empty($email)) {
                    $customer_name = $email;
                    echo "⚠️ Имена не найдены. Используется email: {$email}\n";
                } else {
                    $customer_name = 'Получатель';
                    echo "⚠️ Ничего не найдено. Используется fallback: 'Получатель'\n";
                }
            }
        }
        
        return $customer_name;
    }
    
    // Исправленный метод get_customer_name_for_crm
    public function get_customer_name_for_crm($order) {
        // ИСПРАВЛЕНО: Сначала получаем имя из платежных данных (заказчик)
        $billing_name = trim($order['billing_first_name'] . ' ' . $order['billing_last_name']);
        if ($billing_name !== '') {
            $name = $billing_name;
        } else {
            // Если в платежных данных нет, берем из данных доставки
            $shipping_name = trim($order['shipping_first_name'] . ' ' . $order['shipping_last_name']);
            if ($shipping_name !== '') {
                $name = $shipping_name;
            } else {
                // Если и там нет, берем email или плейсхолдер
                $email = $order['billing_email'];
                $name = $email !== '' ? $email : 'Получатель';
            }
        }
        
        // Очищаем имя от телефонов и лишних пробелов
        $name = preg_replace('/\s*\+?[78]?[\s\-\(\)\+]*\d[\s\-\(\)\d]*\d\s*/', ' ', $name);
        $name = trim(preg_replace('/\s+/', ' ', $name));
        
        return $name;
    }
    
    // Логика для получения имен для очистки адреса
    public function get_names_for_address_cleanup($order) {
        // Получаем все возможные варианты имени
        $names = array();

        // Имя из доставки (получатель)
        $shipping_first = trim($order['shipping_first_name']);
        $shipping_last = trim($order['shipping_last_name']);
        if (!empty($shipping_first)) $names[] = $shipping_first;
        if (!empty($shipping_last)) $names[] = $shipping_last;
        if (!empty($shipping_first) && !empty($shipping_last)) {
            $names[] = $shipping_first . ' ' . $shipping_last;
        }

        // Имя из платежных данных (заказчик)
        $billing_first = trim($order['billing_first_name']);
        $billing_last = trim($order['billing_last_name']);
        if (!empty($billing_first)) $names[] = $billing_first;
        if (!empty($billing_last)) $names[] = $billing_last;
        if (!empty($billing_first) && !empty($billing_last)) {
            $names[] = $billing_first . ' ' . $billing_last;
        }

        // Возвращаем самое длинное имя для более точного удаления
        if (!empty($names)) {
            usort($names, function($a, $b) {
                return strlen($b) - strlen($a);
            });
            return $names[0];
        }

        return '';
    }
}

// Тестовые данные
$test_cases = [
    [
        'name' => 'Случай 1: Разные заказчик и получатель',
        'order' => [
            'billing_first_name' => 'Анна',
            'billing_last_name' => 'Заказчикова',
            'shipping_first_name' => 'Иван',
            'shipping_last_name' => 'Получателев',
            'billing_email' => '<EMAIL>'
        ]
    ],
    [
        'name' => 'Случай 2: Заказчик пустой, есть получатель',
        'order' => [
            'billing_first_name' => '',
            'billing_last_name' => '',
            'shipping_first_name' => 'Мария',
            'shipping_last_name' => 'Получательница',
            'billing_email' => '<EMAIL>'
        ]
    ],
    [
        'name' => 'Случай 3: Одинаковые заказчик и получатель',
        'order' => [
            'billing_first_name' => 'Петр',
            'billing_last_name' => 'Петров',
            'shipping_first_name' => 'Петр',
            'shipping_last_name' => 'Петров',
            'billing_email' => '<EMAIL>'
        ]
    ]
];

$tester = new ConfusionFixTest();

foreach ($test_cases as $test) {
    echo "--- {$test['name']} ---\n";
    echo "👤 Заказчик (billing): '{$test['order']['billing_first_name']} {$test['order']['billing_last_name']}'\n";
    echo "📦 Получатель (shipping): '{$test['order']['shipping_first_name']} {$test['order']['shipping_last_name']}'\n";
    echo "📧 Email: '{$test['order']['billing_email']}'\n\n";
    
    // Тестируем основной метод для доставки
    echo "🚚 Имя для доставки в CRM:\n";
    $delivery_name = $tester->get_customer_name_for_delivery($test['order']);
    echo "Результат: '{$delivery_name}'\n\n";
    
    // Тестируем исправленный метод
    $crm_name = $tester->get_customer_name_for_crm($test['order']);
    echo "🏢 Имя для CRM (исправленный метод): '{$crm_name}'\n";
    
    // Тестируем имена для очистки адреса
    $cleanup_name = $tester->get_names_for_address_cleanup($test['order']);
    echo "🧹 Имя для очистки адреса: '{$cleanup_name}'\n";
    
    // Проверяем консистентность
    if ($delivery_name === $crm_name) {
        echo "✅ Методы возвращают одинаковый результат - путаницы нет!\n";
    } else {
        echo "❌ Методы возвращают разные результаты - есть путаница!\n";
        echo "   Доставка: '{$delivery_name}' vs CRM: '{$crm_name}'\n";
    }
    
    echo "\n" . str_repeat("=", 60) . "\n\n";
}

echo "=== Тест завершен ===\n";
?>
