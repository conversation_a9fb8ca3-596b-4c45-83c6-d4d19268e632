<?php
// Check and fix WordPress timezone settings
echo "=== ПРОВЕРКА НАСТРОЕК ЧАСОВОГО ПОЯСА WORDPRESS ===\n";

$wp_path = '/home/<USER>/bekflom3/mosbuketik.ru/public_html';
require_once($wp_path . '/wp-config.php');
require_once($wp_path . '/wp-load.php');

echo "WordPress загружен успешно\n\n";

// Получаем текущие настройки времени
$timezone_string = get_option('timezone_string');
$gmt_offset = get_option('gmt_offset');
$date_format = get_option('date_format');
$time_format = get_option('time_format');

echo "=== ТЕКУЩИЕ НАСТРОЙКИ ВРЕМЕНИ ===\n";
echo "Часовой пояс (timezone_string): " . ($timezone_string ?: 'НЕ УСТАНОВЛЕН') . "\n";
echo "GMT смещение (gmt_offset): " . $gmt_offset . "\n";
echo "Формат даты: " . $date_format . "\n";
echo "Формат времени: " . $time_format . "\n\n";

// Показываем текущее время в разных форматах
echo "=== ТЕКУЩЕЕ ВРЕМЯ ===\n";
echo "Серверное время (PHP): " . date('Y-m-d H:i:s') . "\n";
echo "WordPress время (current_time): " . current_time('Y-m-d H:i:s') . "\n";
echo "WordPress время UTC (current_time): " . current_time('Y-m-d H:i:s', true) . "\n";
echo "WordPress время (get_date_from_gmt): " . get_date_from_gmt(gmdate('Y-m-d H:i:s'), 'Y-m-d H:i:s') . "\n\n";

// Проверяем настройки PHP
echo "=== НАСТРОЙКИ PHP ===\n";
echo "PHP timezone: " . date_default_timezone_get() . "\n";
echo "PHP date: " . date('Y-m-d H:i:s') . "\n\n";

// Рекомендуемые настройки для России (Москва)
$recommended_timezone = 'Europe/Moscow';
$recommended_gmt_offset = 3;

echo "=== РЕКОМЕНДУЕМЫЕ НАСТРОЙКИ ДЛЯ МОСКВЫ ===\n";
echo "Часовой пояс: $recommended_timezone\n";
echo "GMT смещение: +$recommended_gmt_offset\n\n";

// Проверяем, нужно ли исправлять настройки
$needs_fix = false;

if (empty($timezone_string) || $timezone_string !== $recommended_timezone) {
    echo "❌ Часовой пояс не установлен или неверный\n";
    $needs_fix = true;
}

if ($gmt_offset != $recommended_gmt_offset) {
    echo "❌ GMT смещение неверное (должно быть +3 для Москвы)\n";
    $needs_fix = true;
}

if (!$needs_fix) {
    echo "✅ Настройки времени корректны\n";
} else {
    echo "\n=== ИСПРАВЛЕНИЕ НАСТРОЕК ===\n";
    
    // Устанавливаем правильный часовой пояс
    $result1 = update_option('timezone_string', $recommended_timezone);
    echo "Установка timezone_string: " . ($result1 ? "✅ УСПЕШНО" : "❌ ОШИБКА") . "\n";
    
    // Устанавливаем GMT смещение
    $result2 = update_option('gmt_offset', $recommended_gmt_offset);
    echo "Установка gmt_offset: " . ($result2 ? "✅ УСПЕШНО" : "❌ ОШИБКА") . "\n";
    
    // Проверяем результат
    echo "\n=== ПРОВЕРКА ПОСЛЕ ИСПРАВЛЕНИЯ ===\n";
    $new_timezone = get_option('timezone_string');
    $new_offset = get_option('gmt_offset');
    
    echo "Новый часовой пояс: $new_timezone\n";
    echo "Новое GMT смещение: $new_offset\n";
    echo "Новое WordPress время: " . current_time('Y-m-d H:i:s') . "\n";
}

// Проверяем последние заказы WooCommerce
if (class_exists('WooCommerce')) {
    echo "\n=== ПРОВЕРКА ВРЕМЕНИ ЗАКАЗОВ WOOCOMMERCE ===\n";
    
    $orders = wc_get_orders(array(
        'limit' => 5,
        'orderby' => 'date',
        'order' => 'DESC'
    ));
    
    foreach ($orders as $order) {
        $order_id = $order->get_id();
        $order_date = $order->get_date_created();
        $order_date_gmt = $order->get_date_created()->getTimestamp();
        
        echo "Заказ #$order_id:\n";
        echo "  Дата создания: " . $order_date->format('Y-m-d H:i:s') . "\n";
        echo "  Timestamp: $order_date_gmt\n";
        echo "  Время назад: " . human_time_diff($order_date_gmt, current_time('timestamp')) . " назад\n";
        echo "  Статус: " . $order->get_status() . "\n\n";
    }
} else {
    echo "\n❌ WooCommerce не активен\n";
}

echo "=== ПРОВЕРКА ЗАВЕРШЕНА ===\n";
?> 