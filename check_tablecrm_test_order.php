<?php
/**
 * Скрипт для проверки тестового заказа в TableCRM
 * Использует токен из предоставленной ссылки
 */

echo "🔍 Проверка тестового заказа в TableCRM\n";
echo "========================================\n\n";

// Используем токен из предоставленной ссылки
$api_token = 'af1874616430e04cfd4bce30035789907e899fc7c3a1a4bb27254828ff304a77';
$api_url = 'https://app.tablecrm.com/api/v1/';

echo "📋 Параметры подключения:\n";
echo "API URL: $api_url\n";
echo "Token: " . substr($api_token, 0, 10) . "...\n\n";

/**
 * Функция для выполнения API запроса к TableCRM
 */
function make_api_request($endpoint, $params = []) {
    global $api_url, $api_token;
    
    $url = rtrim($api_url, '/') . '/' . ltrim($endpoint, '/');
    
    // Добавляем токен к параметрам
    $params['token'] = $api_token;
    $url .= '?' . http_build_query($params);
    
    echo "🔗 Запрос к: " . str_replace($api_token, '***', $url) . "\n";
    
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => [
                'User-Agent: TableCRM-Test-Script/1.0',
                'Accept: application/json'
            ],
            'timeout' => 30
        ],
        'ssl' => [
            'verify_peer' => false,
            'verify_peer_name' => false
        ]
    ]);
    
    $response = file_get_contents($url, false, $context);
    
    if ($response === false) {
        echo "❌ Ошибка при выполнении запроса\n";
        return false;
    }
    
    // Получаем HTTP статус из headers
    $http_status = 200;
    if (isset($http_response_header[0])) {
        preg_match('/HTTP\/\d\.\d\s+(\d+)/', $http_response_header[0], $matches);
        if (isset($matches[1])) {
            $http_status = (int)$matches[1];
        }
    }
    
    echo "📊 HTTP статус: $http_status\n";
    
    if ($http_status !== 200) {
        echo "❌ Ошибка API: HTTP $http_status\n";
        echo "Ответ: " . substr($response, 0, 500) . "\n";
        return false;
    }
    
    $decoded = json_decode($response, true);
    if ($decoded === null) {
        echo "❌ Ошибка декодирования JSON\n";
        echo "Ответ: " . substr($response, 0, 500) . "\n";
        return false;
    }
    
    return $decoded;
}

// 1. Проверяем соединение с API
echo "1️⃣ Тестирование соединения с API...\n";
echo "====================================\n";

$test_response = make_api_request('organizations/');

if (!$test_response) {
    echo "❌ Не удалось подключиться к API TableCRM\n";
    exit(1);
}

echo "✅ Соединение с API успешно установлено\n\n";

// 2. Получаем список организаций
if (isset($test_response['results']) && !empty($test_response['results'])) {
    echo "🏢 Доступные организации:\n";
    foreach ($test_response['results'] as $org) {
        echo "   ID: {$org['id']}, Название: {$org['name']}\n";
    }
    $org_id = $test_response['results'][0]['id']; // Используем первую организацию
    echo "\n📌 Используем организацию ID: $org_id\n\n";
} else {
    echo "⚠️ Организации не найдены, используем ID по умолчанию: 38\n\n";
    $org_id = 38;
}

// 3. Получаем последние документы продаж
echo "2️⃣ Получение последних документов продаж...\n";
echo "=============================================\n";

$docs_params = [
    'limit' => 10,
    'offset' => 0,
    'ordering' => '-created_at' // Сортировка по дате создания (новые первые)
];

$docs_response = make_api_request('docs_sales/', $docs_params);

if (!$docs_response) {
    echo "❌ Не удалось получить документы продаж\n";
    exit(1);
}

echo "✅ Получен ответ от API\n\n";

if (isset($docs_response['results']) && !empty($docs_response['results'])) {
    $count = count($docs_response['results']);
    $total = $docs_response['count'] ?? $count;
    
    echo "📋 Найдено документов: $count из $total\n\n";
    
    foreach ($docs_response['results'] as $index => $doc) {
        $doc_date = isset($doc['created_at']) ? date('d.m.Y H:i', strtotime($doc['created_at'])) : 
                   (isset($doc['dated']) ? $doc['dated'] : 'не указана');
        
        echo "📄 Документ #" . ($index + 1) . ":\n";
        echo "   🆔 ID: " . ($doc['id'] ?? 'не указан') . "\n";
        echo "   📝 Номер: " . ($doc['number'] ?? 'не указан') . "\n";
        echo "   📅 Дата: $doc_date\n";
        echo "   🔧 Операция: " . ($doc['operation'] ?? 'не указана') . "\n";
        echo "   💰 Сумма: " . ($doc['paid_rubles'] ?? $doc['total_rubles'] ?? 'не указана') . " руб.\n";
        echo "   ✅ Статус оплаты: " . ($doc['is_paid'] ?? $doc['status'] ?? false ? 'Оплачен' : 'Не оплачен') . "\n";
        echo "   👤 Контрагент ID: " . ($doc['contragent'] ?? 'не указан') . "\n";
        echo "   🏢 Организация ID: " . ($doc['organization'] ?? 'не указана') . "\n";
        
        // Показываем комментарий, если есть
        if (!empty($doc['comment'])) {
            $comment = strlen($doc['comment']) > 100 ? 
                      substr($doc['comment'], 0, 100) . '...' : 
                      $doc['comment'];
            echo "   💬 Комментарий: $comment\n";
        }
        
        // Показываем external_id, если есть
        if (!empty($doc['external_id'])) {
            echo "   🔗 External ID: {$doc['external_id']}\n";
        }
        
        // Показываем товары, если есть
        if (isset($doc['goods']) && !empty($doc['goods'])) {
            echo "   📦 Товары (" . count($doc['goods']) . "):\n";
            foreach ($doc['goods'] as $good_index => $good) {
                echo "     - #" . ($good_index + 1) . ": ";
                echo "Номенклатура ID " . ($good['nomenclature'] ?? 'не указана');
                echo ", кол-во: " . ($good['quantity'] ?? 'не указано');
                echo ", цена: " . ($good['price'] ?? 'не указана') . " руб.\n";
            }
        }
        
        echo "\n";
    }
    
    // Детальная проверка последнего документа
    $last_doc = $docs_response['results'][0];
    $last_doc_id = $last_doc['id'];
    
    echo "\n3️⃣ Детальная проверка последнего документа (ID: $last_doc_id)...\n";
    echo "================================================================\n";
    
    // Проверяем контрагента
    $contragent_id = $last_doc['contragent'] ?? null;
    if ($contragent_id) {
        echo "\n👤 Проверка контрагента (ID: $contragent_id):\n";
        echo "===========================================\n";
        
        $contragent_response = make_api_request("contragents/$contragent_id/");
        
        if ($contragent_response) {
            echo "✅ Данные контрагента:\n";
            echo "   📛 Имя: " . ($contragent_response['name'] ?? 'не указано') . "\n";
            echo "   📧 Email: " . ($contragent_response['email'] ?? 'не указан') . "\n";
            echo "   📞 Телефон: " . ($contragent_response['phone'] ?? 'не указан') . "\n";
            echo "   🏷️ Тип: " . ($contragent_response['type'] ?? 'не указан') . "\n";
            echo "   🔗 External ID: " . ($contragent_response['external_id'] ?? 'не указан') . "\n";
            
            if (!empty($contragent_response['comment'])) {
                echo "   💬 Комментарий: " . $contragent_response['comment'] . "\n";
            }
            
            // Особая проверка на имя "Айгуль"
            $name = $contragent_response['name'] ?? '';
            if (stripos($name, 'Айгуль') !== false || stripos($name, 'Aigul') !== false) {
                echo "\n🚨 ОБНАРУЖЕН КОНТРАГЕНТ 'АЙГУЛЬ'!\n";
                echo "🚨 Это может указывать на проблему с тестовыми данными!\n";
            }
        } else {
            echo "❌ Не удалось получить данные контрагента\n";
        }
    }
    
    // Проверяем тип документа и источник
    echo "\n🔍 Анализ документа:\n";
    echo "==================\n";
    
    $created_at = $last_doc['created_at'] ?? $last_doc['dated'] ?? null;
    if ($created_at) {
        $doc_time = strtotime($created_at);
        $now = time();
        $diff = $now - $doc_time;
        
        if ($diff < 3600) { // Меньше часа
            echo "🕐 Документ создан недавно (" . round($diff/60) . " минут назад)\n";
        } elseif ($diff < 86400) { // Меньше дня
            echo "🕐 Документ создан сегодня (" . round($diff/3600) . " часов назад)\n";
        } else {
            echo "🕐 Документ создан " . round($diff/86400) . " дней назад\n";
        }
    }
    
    // Проверяем, является ли это тестовым заказом
    $is_test = false;
    $test_indicators = [];
    
    if (isset($last_doc['comment']) && 
        (stripos($last_doc['comment'], 'тест') !== false || 
         stripos($last_doc['comment'], 'test') !== false)) {
        $is_test = true;
        $test_indicators[] = 'Комментарий содержит "тест"';
    }
    
    if (isset($last_doc['external_id']) && 
        (stripos($last_doc['external_id'], 'test') !== false)) {
        $is_test = true;
        $test_indicators[] = 'External ID содержит "test"';
    }
    
    if ($is_test) {
        echo "🧪 ТЕСТОВЫЙ ДОКУМЕНТ ОБНАРУЖЕН!\n";
        echo "   Признаки: " . implode(', ', $test_indicators) . "\n";
    } else {
        echo "📋 Обычный рабочий документ\n";
    }
    
} else {
    echo "📝 Документы продаж не найдены\n";
    echo "Возможно, в системе еще нет заказов или у токена нет доступа к ним.\n";
}

echo "\n✅ Проверка завершена!\n";
echo "========================\n";

// Показываем время выполнения
$end_time = microtime(true);
$start_time = $_SERVER['REQUEST_TIME_FLOAT'] ?? $end_time;
$execution_time = round(($end_time - $start_time) * 1000, 2);
echo "⏱️ Время выполнения: {$execution_time} мс\n";