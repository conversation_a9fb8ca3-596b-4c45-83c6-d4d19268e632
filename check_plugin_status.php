<?php
/**
 * Скрипт для проверки статуса плагина TableCRM Integration (Fixed v2)
 */

// Отключаем кэширование, чтобы всегда получать актуальные данные
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");

echo "<pre style='font-family: monospace; background-color: #f5f5f5; padding: 20px; border: 1px solid #ccc; border-radius: 5px;'>";

echo "===============================================\n";
echo "🔍 ДИАГНОСТИКА АКТИВНЫХ ПЛАГИНОВ TABLECRM\n";
echo "===============================================\n\n";

// Загружаем ядро WordPress
define('WP_USE_THEMES', false);
$wp_load_path = __DIR__ . '/wp-load.php';

if (!file_exists($wp_load_path)) {
    echo "❌ ОШИБКА: Не удалось найти файл wp-load.php. Убедитесь, что этот скрипт находится в корневой папке WordPress.";
    exit;
}

require_once($wp_load_path);

echo "✅ Ядро WordPress успешно загружено.\n\n";

// Проверяем, доступны ли функции управления плагинами
if (!function_exists('get_plugins')) {
    require_once ABSPATH . 'wp-admin/includes/plugin.php';
    echo "✅ Функции управления плагинами подключены.\n\n";
} else {
    echo "✅ Функции управления плагинами уже доступны.\n\n";
}

// Получаем список всех плагинов
$all_plugins = get_plugins();

// Ищем все плагины, связанные с TableCRM
$tablecrm_plugins = [];
foreach ($all_plugins as $plugin_path => $plugin_data) {
    if (stripos($plugin_path, 'tablecrm') !== false) {
        $tablecrm_plugins[$plugin_path] = $plugin_data;
    }
}

if (empty($tablecrm_plugins)) {
    echo "🟡 ВНИМАНИЕ: Не найдено ни одного плагина с 'tablecrm' в названии файла.\n";
    exit;
}

echo " ditemukan " . count($tablecrm_plugins) . " плагинов TableCRM:\n";
echo "-----------------------------------------------\n";

foreach ($tablecrm_plugins as $plugin_path => $plugin_data) {
    $is_active = is_plugin_active($plugin_path);
    
    echo "📁 **Файл плагина:** " . esc_html($plugin_path) . "\n";
    echo "   - **Название:** " . esc_html($plugin_data['Name']) . "\n";
    echo "   - **Версия:** " . esc_html($plugin_data['Version']) . "\n";
    
    if ($is_active) {
        echo "   - **СТАТУС:** <strong style='color: green;'>✅ АКТИВЕН</strong>\n";
    } else {
        echo "   - **СТАТУС:** <strong style='color: red;'>❌ НЕ АКТИВЕН</strong>\n";
    }
    echo "\n";
}

echo "===============================================\n";
echo "🎯 РЕЗЮМЕ:\n";
echo "===============================================\n\n";

$active_plugin_found = false;
foreach ($tablecrm_plugins as $plugin_path => $plugin_data) {
    if (is_plugin_active($plugin_path)) {
        echo "<strong><p style='color:green;'>✅ Активный плагин: " . esc_html($plugin_data['Name']) . " (Версия: " . esc_html($plugin_data['Version']) . ")</p></strong>";
        
        if (strpos($plugin_data['Version'], '1.0.15') !== false) {
            echo "<strong><p style='color:green;'>🎉 ПОЗДРАВЛЯЕМ! Активна правильная версия v1.0.15. Проблема с названиями товаров должна быть решена.</p></strong>";
        } else {
            echo "<strong><p style='color:red;'>❌ ВНИМАНИЕ! Активна старая версия (" . esc_html($plugin_data['Version']) . "). Необходимо активировать версию 1.0.15.</p></strong>";
        }
        $active_plugin_found = true;
        break;
    }
}

if (!$active_plugin_found) {
    echo "<strong><p style='color:red;'>❌ КРИТИЧЕСКАЯ ОШИБКА: Ни один из плагинов TableCRM не активен. Интеграция не работает.</p></strong>";
}

echo "\n\n";
echo "===============================================\n";
echo "🔒 Не забудьте удалить этот файл с сервера после завершения диагностики!\n";
echo "===============================================\n";
echo "</pre>";

?> 