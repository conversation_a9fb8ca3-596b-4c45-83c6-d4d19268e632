# Исправление проблемы с организацией ID 38

## ❌ Проблема
В плагине TableCRM Integration был жестко прописан ID организации 38, который не существует в вашем аккаунте TableCRM.

## ✅ Решение
Исправления внесены в файл `tablecrm-integration.php`:

1. Убран жестко прописанный ID организации 38
2. Добавлены настройки для гибкого указания ID организации, кассы и менеджера
3. Добавлены поля в админ-панель WordPress для настройки этих значений

## 📋 Что исправлено

### Старая версия (НЕ РАБОТАЛА):
```php
'organization' => 38, // ID организации (обязательное поле)
'cashbox' => 113, // ID кассы по умолчанию
'sales_manager' => 125 // ID менеджера по умолчанию
```

### Новая версия (РАБОТАЕТ):
```php
$organization_id = get_option('tablecrm_organization_id', 1);
$cashbox_id = get_option('tablecrm_cashbox_id', 1);
$manager_id = get_option('tablecrm_manager_id', 1);

'organization' => intval($organization_id),
'cashbox' => intval($cashbox_id),
'sales_manager' => intval($manager_id)
```

## 🔧 Как настроить

### 1. Обновите файл плагина на сервере
Замените файл `/public_html/wp-content/plugins/tablecrm-integration/tablecrm-integration.php` новой версией.

### 2. Найдите правильные ID в TableCRM

#### ID Организации:
1. Откройте TableCRM → Продажи → Новая продажа
2. В поле "Введите организацию" начните вводить название
3. В выпадающем списке увидите организации с их ID
4. Запомните ID нужной организации

#### ID Кассы:
- Обычно это 1 (основная касса)
- Проверьте в разделе настроек TableCRM

#### ID Менеджера:
- Это ваш ID пользователя в TableCRM
- Обычно это 1 для главного администратора

### 3. Настройте плагин в WordPress

1. Войдите в админ-панель: https://mosbuketik.ru/wp-admin/
2. Перейдите в **Настройки → TableCRM Integration**
3. Заполните поля:
   - **ID Организации**: [найденный ID организации]
   - **ID Кассы**: 1 (обычно)
   - **ID Менеджера**: 1 (обычно)
4. Нажмите **"Сохранить настройки"**
5. Нажмите **"Тестировать соединение"** для проверки

## ✅ Результат

После правильной настройки:
- Заказы WooCommerce будут успешно отправляться в TableCRM
- Организация будет указываться правильно
- Документы продаж будут создаваться корректно

## 🔍 Проверка работы

1. Создайте тестовый заказ в WooCommerce
2. Проверьте логи WordPress (включите режим отладки в плагине)
3. Проверьте появление нового документа продаж в TableCRM

## 📞 Поддержка

Если возникнут проблемы:
1. Включите "Режим отладки" в настройках плагина
2. Проверьте логи WordPress
3. Используйте кнопку "Тестировать соединение" для диагностики 