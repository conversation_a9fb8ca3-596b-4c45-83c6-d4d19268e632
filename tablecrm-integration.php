<?php
/**
 * Plugin Name: TableCRM Integration
 * Description: Интеграция WordPress/WooCommerce с TableCRM для отправки заявок через API (HPOS Compatible, с экспоненциальным back-off)
 * Version: 1.1.1
 * Author: Your Name
 * Text Domain: tablecrm-integration
 * Requires at least: 5.0
 * Tested up to: 6.4
 * WC requires at least: 7.0
 * WC tested up to: 8.5
 * 
 * Changelog:
 * v1.1.1 - Добавлен экспоненциальный back-off (30→120→300 сек) с лимитом 3 попытки для API запросов
 *        - Улучшена обработка ошибок для предотвращения "завала" очереди Action Scheduler
 *        - Автоматические повторы при сетевых ошибках и временных сбоях сервера
 */

// Предотвращение прямого доступа
if (!defined('ABSPATH')) {
    exit;
}

// Определение констант плагина
define('TABLECRM_PLUGIN_URL', plugin_dir_url(__FILE__));
define('TABLECRM_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('TABLECRM_VERSION', '1.1.1');

/**
 * Get master key for encryption from constant or environment variable.
 *
 * @return string|false
 */
function tablecrm_get_master_key() {
    static $cached = null;
    static $valid = null;

    if ($cached !== null) {
        return $valid ? $cached : false;
    }

    $key = defined('TABLECRM_MASTER_KEY') ? TABLECRM_MASTER_KEY : getenv('TABLECRM_MASTER_KEY');
    if (!$key) {
        $cached = '';
        $valid = false;
        return false;
    }

    if (!extension_loaded('sodium')) {
        error_log('[TableCRM Integration] [ERROR] PHP extension "sodium" is not loaded. Encryption disabled.');
        $cached = '';
        $valid = false;
        return false;
    }

    if (strlen($key) !== SODIUM_CRYPTO_SECRETBOX_KEYBYTES) {
        error_log('[TableCRM Integration] [ERROR] TABLECRM_MASTER_KEY must be ' . SODIUM_CRYPTO_SECRETBOX_KEYBYTES . ' bytes long.');
        $cached = '';
        $valid = false;
        return false;
    }

    $cached = $key;
    $valid = true;
    return $key;
}

/**
 * Encrypt value using libsodium.
 */
function tablecrm_encrypt_value($plain) {
    $key = tablecrm_get_master_key();
    if (!$key || $plain === '') {
        return $plain;
    }
    $nonce = random_bytes(SODIUM_CRYPTO_SECRETBOX_NONCEBYTES);
    $cipher = sodium_crypto_secretbox($plain, $nonce, $key);
    return 'enc:' . base64_encode($nonce . $cipher);
}

/**
 * Decrypt value previously encrypted.
 */
function tablecrm_decrypt_value($value) {
    if (strpos($value, 'enc:') !== 0) {
        return $value;
    }
    $key = tablecrm_get_master_key();
    if (!$key) {
        return '';
    }
    $data = base64_decode(substr($value, 4), true);
    if ($data === false || strlen($data) < SODIUM_CRYPTO_SECRETBOX_NONCEBYTES) {
        return '';
    }
    $nonce = substr($data, 0, SODIUM_CRYPTO_SECRETBOX_NONCEBYTES);
    $cipher = substr($data, SODIUM_CRYPTO_SECRETBOX_NONCEBYTES);
    $plain = sodium_crypto_secretbox_open($cipher, $nonce, $key);
    return $plain !== false ? $plain : '';
}

/**
 * Return decrypted API key from database.
 */
function tablecrm_get_api_key() {
    $stored = get_option("tablecrm_api_key");
    return $stored ? tablecrm_decrypt_value($stored) : '';
}

/**
 * Sanitize callback to encrypt API key when saving.
 */
function tablecrm_sanitize_api_key($value) {
    if (empty($value)) {
        return '';
    }
    if (strpos($value, 'enc:') === 0) {
        return $value;
    }
    return tablecrm_encrypt_value($value);
}

/**
 * Ensure autoload is disabled for API key option.
 */
function tablecrm_fix_api_key_autoload() {
    global $wpdb;
    $wpdb->update($wpdb->options, array('autoload' => 'no'), array('option_name' => 'tablecrm_api_key'));
}

/**
 * Migrate existing plain API key to encrypted form.
 */
function tablecrm_migrate_api_key() {
    $stored = get_option("tablecrm_api_key");
    if (!$stored) {
        return;
    }
    if (strpos($stored, 'enc:') !== 0) {
        $encrypted = tablecrm_encrypt_value($stored);
        update_option('tablecrm_api_key', $encrypted, false);
    } else {
        tablecrm_fix_api_key_autoload();
    }
}

add_action('plugins_loaded', 'tablecrm_migrate_api_key');
add_action('admin_init', 'tablecrm_fix_api_key_autoload');

class TableCRM_Integration {
    
    private $order_repository = null;
    private $is_hpos_enabled = false;
    
    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'register_settings'));
        
        // Инициализация HPOS после загрузки WooCommerce
        add_action('woocommerce_init', array($this, 'init_hpos_support'));
        
        // Хуки для WooCommerce
        add_action('woocommerce_new_order', array($this, 'queue_order_sync'));
        add_action('woocommerce_order_status_completed', array($this, 'update_order_in_tablecrm'));
        add_action('woocommerce_order_status_processing', array($this, 'update_order_in_tablecrm'));
        add_action('woocommerce_order_status_cancelled', array($this, 'update_order_in_tablecrm'));
        add_action('woocommerce_order_status_refunded', array($this, 'update_order_in_tablecrm'));

        // Обработчик асинхронной синхронизации
        add_action('tcrm_sync_order', array($this, 'handle_async_order'));
        
        // Хуки для Contact Form 7 (если установлен)
        add_action('wpcf7_mail_sent', array($this, 'send_cf7_to_tablecrm'));
        
        // AJAX хук для проверки соединения
        add_action('wp_ajax_test_tablecrm_connection', array($this, 'test_connection'));
        
        // Хук для сохранения UTM данных
        add_action('woocommerce_checkout_create_order', array($this, 'save_utm_data_to_order'), 10, 2);
    }
    
    /**
     * Инициализация поддержки HPOS
     */
    public function init_hpos_support() {
        if (!class_exists('WooCommerce')) {
            return;
        }
        
        // Проверяем, активен ли HPOS
        $this->is_hpos_enabled = wc_get_container()->get(\Automattic\WooCommerce\Internal\DataStores\Orders\CustomOrdersTableController::class)->custom_orders_table_usage_is_enabled();
        
        if ($this->is_hpos_enabled) {
            try {
                // Получаем репозиторий заказов для HPOS
                $this->order_repository = wc_get_container()->get(\Automattic\WooCommerce\Internal\DataStores\Orders\OrderRepositoryInterface::class);
                $this->log_info("HPOS включен. Используется OrderRepositoryInterface для максимальной производительности.");
            } catch (Exception $e) {
                $this->log_warning("HPOS включен, но не удалось получить OrderRepositoryInterface: " . $e->getMessage());
                $this->is_hpos_enabled = false;
            }
        } else {
            $this->log_info("HPOS отключен. Используется стандартное хранение заказов в posts таблице.");
        }
    }
    
    /**
     * Получить заказ через HPOS-совместимый метод
     */
    private function get_order_optimized($order_id) {
        if ($this->is_hpos_enabled && $this->order_repository) {
            try {
                return $this->order_repository->get($order_id);
            } catch (Exception $e) {
                $this->log_warning("Ошибка получения заказа через HPOS: " . $e->getMessage());
                // Fallback к стандартному методу
                return wc_get_order($order_id);
            }
        }
        
        return wc_get_order($order_id);
    }
    
    /**
     * Получить мета-данные заказа через HPOS-совместимый метод
     */
    private function get_order_meta($order, $meta_key, $single = true) {
        if (is_numeric($order)) {
            $order = $this->get_order_optimized($order);
        }
        
        if (!$order) {
            return $single ? '' : array();
        }
        
        return $order->get_meta($meta_key, $single);
    }
    
    /**
     * Обновить мета-данные заказа через HPOS-совместимый метод
     */
    private function update_order_meta($order, $meta_key, $meta_value) {
        if (is_numeric($order)) {
            $order = $this->get_order_optimized($order);
        }
        
        if (!$order) {
            return false;
        }
        
        $order->update_meta_data($meta_key, $meta_value);
        $order->save();
        
        return true;
    }
    
    /**
     * Получить заказы через HPOS-совместимый метод
     */
    private function get_orders_optimized($args = array()) {
        if ($this->is_hpos_enabled && $this->order_repository) {
            try {
                // Преобразуем аргументы для HPOS
                $hpos_args = $this->convert_args_for_hpos($args);
                return $this->order_repository->search($hpos_args);
            } catch (Exception $e) {
                $this->log_warning("Ошибка поиска заказов через HPOS: " . $e->getMessage());
                // Fallback к стандартному методу
                return wc_get_orders($args);
            }
        }
        
        return wc_get_orders($args);
    }
    
    /**
     * Преобразование аргументов для HPOS
     */
    private function convert_args_for_hpos($args) {
        // HPOS может использовать немного другие параметры поиска
        // В данном случае большинство параметров совместимы
        return $args;
    }
    
    public function init() {
        load_plugin_textdomain('tablecrm-integration', false, dirname(plugin_basename(__FILE__)) . '/languages');
        
        // Добавляем скрипт для отслеживания UTM на фронтенде
        add_action('wp_enqueue_scripts', array($this, 'enqueue_utm_tracking_script'));
    }
    
    // Подключение скрипта отслеживания UTM
    public function enqueue_utm_tracking_script() {
        wp_enqueue_script('tablecrm-utm-tracking', TABLECRM_PLUGIN_URL . 'utm-tracking.js', array('jquery'), TABLECRM_VERSION, true);
    }
    
    // Добавление меню в админку
    public function add_admin_menu() {
        add_options_page(
            'TableCRM Integration Settings',
            'TableCRM Integration',
            'manage_options',
            'tablecrm-settings',
            array($this, 'admin_page')
        );
    }
    
    // Регистрация настроек
    public function register_settings() {
        register_setting('tablecrm_settings', 'tablecrm_api_url');
        register_setting(
            'tablecrm_settings',
            'tablecrm_api_key',
            array('sanitize_callback' => 'tablecrm_sanitize_api_key')
        );
        register_setting('tablecrm_settings', 'tablecrm_project_id');
        register_setting('tablecrm_settings', 'tablecrm_organization_id');
        register_setting('tablecrm_settings', 'tablecrm_warehouse_id');
        register_setting('tablecrm_settings', 'tablecrm_cashbox_id');
        register_setting('tablecrm_settings', 'tablecrm_enable_leads');
        register_setting('tablecrm_settings', 'tablecrm_enable_orders');
        register_setting('tablecrm_settings', 'tablecrm_order_statuses');
        register_setting('tablecrm_settings', 'tablecrm_debug_mode');
    }
    
    // Страница настроек
    public function admin_page() {
        ?>
        <div class="wrap">
            <h1>TableCRM Integration Settings</h1>
            
            <?php
            // Показываем статус HPOS
            if (class_exists('WooCommerce')) {
                $hpos_enabled = false;
                try {
                    $hpos_enabled = wc_get_container()->get(\Automattic\WooCommerce\Internal\DataStores\Orders\CustomOrdersTableController::class)->custom_orders_table_usage_is_enabled();
                } catch (Exception $e) {
                    // HPOS недоступен
                }
                
                echo '<div class="notice notice-info"><p>';
                echo '<strong>Статус HPOS:</strong> ';
                if ($hpos_enabled) {
                    echo '<span style="color: green;">✅ Включен (плагин использует оптимизированные методы)</span>';
                } else {
                    echo '<span style="color: orange;">⚠️ Отключен (плагин работает в режиме совместимости)</span>';
                }
                echo '</p></div>';
            }
            ?>
            
            <form method="post" action="options.php">
                <?php
                settings_fields('tablecrm_settings');
                do_settings_sections('tablecrm_settings');
                wp_nonce_field('tablecrm_settings', 'tablecrm_nonce');
                ?>
                <table class="form-table">
                    <tr>
                        <th scope="row">API URL</th>
                        <td>
                            <input type="url" name="tablecrm_api_url" value="<?php echo esc_attr(get_option('tablecrm_api_url', 'https://app.tablecrm.com/api/v1/')); ?>" class="regular-text" />
                            <p class="description">URL API TableCRM (например: https://app.tablecrm.com/api/v1/)</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">API Key</th>
                        <td>
                            <input type="password" name="tablecrm_api_key" value="<?php echo esc_attr(tablecrm_get_api_key()); ?>" class="regular-text" />
                            <p class="description">Ваш API ключ TableCRM</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Project ID</th>
                        <td>
                            <input type="text" name="tablecrm_project_id" value="<?php echo esc_attr(get_option('tablecrm_project_id')); ?>" class="regular-text" />
                            <p class="description">ID проекта в TableCRM (опционально, если требуется API)</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">ID Организации</th>
                        <td>
                            <input type="number" name="tablecrm_organization_id" value="<?php echo esc_attr(get_option('tablecrm_organization_id', 38)); ?>" class="regular-text" min="1" />
                            <p class="description">ID организации в TableCRM. Посмотрите в списке "Введите организацию" в интерфейсе TableCRM</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">ID Кассы</th>
                        <td>
                            <input type="number" name="tablecrm_cashbox_id" value="<?php echo esc_attr(get_option('tablecrm_cashbox_id', 218)); ?>" class="regular-text" min="1" />
                            <p class="description">ID кассы в TableCRM (paybox)</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">ID Склада</th>
                        <td>
                            <input type="number" name="tablecrm_warehouse_id" value="<?php echo esc_attr(get_option('tablecrm_warehouse_id', 39)); ?>" class="regular-text" min="1" />
                            <p class="description">ID склада в TableCRM</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">ID Единицы измерения</th>
                        <td>
                            <input type="number" name="tablecrm_unit_id" value="<?php echo esc_attr(get_option('tablecrm_unit_id', 116)); ?>" class="regular-text" min="1" />
                            <p class="description">ID единицы измерения в TableCRM (например, штуки = 116)</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Отправлять заказы WooCommerce</th>
                        <td>
                            <input type="checkbox" name="tablecrm_enable_orders" value="1" <?php checked(1, get_option('tablecrm_enable_orders', 1)); ?> />
                            <label>Включить отправку заказов в TableCRM</label>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Статусы заказов для отправки</th>
                        <td>
                            <?php
                            $order_statuses = wc_get_order_statuses();
                            $selected_statuses = get_option('tablecrm_order_statuses', array('wc-pending', 'wc-processing', 'wc-completed'));
                            foreach ($order_statuses as $status_key => $status_name) {
                                $checked = in_array($status_key, $selected_statuses) ? 'checked' : '';
                                echo '<label><input type="checkbox" name="tablecrm_order_statuses[]" value="' . esc_attr($status_key) . '" ' . $checked . ' /> ' . esc_html($status_name) . '</label><br/>';
                            }
                            ?>
                            <p class="description">Выберите статусы заказов, при которых отправлять данные в TableCRM</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Отправлять формы Contact Form 7</th>
                        <td>
                            <input type="checkbox" name="tablecrm_enable_cf7" value="1" <?php checked(1, get_option('tablecrm_enable_cf7', 1)); ?> />
                            <label>Включить отправку форм CF7 в TableCRM</label>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Проверять дубликаты</th>
                        <td>
                            <input type="checkbox" name="tablecrm_check_duplicates" value="1" <?php checked(1, get_option('tablecrm_check_duplicates', 1)); ?> />
                            <label>Проверять дубликаты по email и телефону</label>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Режим отладки</th>
                        <td>
                            <input type="checkbox" name="tablecrm_debug_mode" value="1" <?php checked(1, get_option('tablecrm_debug_mode', 0)); ?> />
                            <label>Включить подробное логирование</label>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Срок хранения логов (дней)</th>
                        <td>
                            <input type="number" name="tablecrm_log_retention_days" value="<?php echo esc_attr(get_option('tablecrm_log_retention_days', 30)); ?>" class="regular-text" min="1" />
                            <p class="description">Старые записи из debug.log будут удаляться после указанного периода</p>
                        </td>
                    </tr>
                </table>
                
                <p class="submit">
                    <input type="submit" name="submit" id="submit" class="button button-primary" value="Сохранить настройки" />
                    <button type="button" id="test-connection" class="button">Тестировать соединение</button>
                </p>
            </form>
            
            <div id="test-result" style="margin-top: 20px;"></div>
        </div>
        
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            $('#test-connection').click(function() {
                $('#test-result').html('<p>Тестирование соединения...</p>');
                
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'test_tablecrm_connection',
                        api_url: $('input[name="tablecrm_api_url"]').val(),
                        api_key: $('input[name="tablecrm_api_key"]').val(),
                        project_id: $('input[name="tablecrm_project_id"]').val(),
                        tablecrm_nonce: $('input[name="tablecrm_nonce"]').val()
                    },
                    success: function(response) {
                        if (response.success) {
                            $('#test-result').html('<div class="notice notice-success"><p>' + response.data + '</p></div>');
                        } else {
                            $('#test-result').html('<div class="notice notice-error"><p>' + response.data + '</p></div>');
                        }
                    }
                });
            });
        });
        </script>
        <?php
    }
    
    // Тестирование соединения
    public function test_connection() {
        if (!current_user_can('manage_options') ||
            !isset($_POST['tablecrm_nonce']) ||
            !wp_verify_nonce($_POST['tablecrm_nonce'], 'tablecrm_settings')) {
            wp_send_json_error('Недостаточно прав или неверный nonce', 403);
        }

        $api_url = sanitize_text_field($_POST['api_url']);
        $api_key = sanitize_text_field($_POST['api_key']);
        $project_id = sanitize_text_field($_POST['project_id']);
        
        if (empty($api_url) || empty($api_key)) {
            wp_send_json_error('Пожалуйста, заполните обязательные поля (API URL и API Key)');
        }

        $response = $this->make_api_request('health', array(), $api_url, $api_key, $project_id);
        
        if ($response['success']) {
            $message = 'Соединение установлено успешно!';
            if (empty($project_id)) {
                $message .= ' (Project ID не требуется для данного API)';
            }
            wp_send_json_success($message);
        } else {
            $error_message = $response['message'];
            // Если ошибка связана с отсутствием проекта, подсказываем
            if (empty($project_id) && (strpos($error_message, 'project') !== false || strpos($error_message, 'проект') !== false)) {
                $error_message .= '. Возможно, требуется указать Project ID.';
            }
            wp_send_json_error('Ошибка соединения: ' . $error_message);
        }
    }

    // Постановка заказа в очередь синхронизации
    public function queue_order_sync($order_id) {
        if (function_exists('as_enqueue_async_action')) {
            as_enqueue_async_action('tcrm_sync_order', array('order_id' => $order_id));
            $this->log_info("Заказ $order_id поставлен в очередь на отправку");
        } else {
            // Фолбэк, если Action Scheduler недоступен
            $this->log_warning('Action Scheduler не найден, отправка без очереди');
            $this->send_order_to_tablecrm($order_id);
        }
    }

    // Обработчик асинхронной синхронизации с улучшенной обработкой ошибок
    public function handle_async_order($order_id) {
        try {
            $order = wc_get_order($order_id);
            if (!$order) {
                $this->log_error("Не удалось загрузить заказ $order_id для синхронизации");
                return; // Завершаем без исключения, так как заказ не существует
            }
            
            $this->log_info("=== НАЧАЛО АСИНХРОННОЙ ОБРАБОТКИ ЗАКАЗА $order_id ===");
            $result = $this->send_order_to_tablecrm($order_id);
            
            // Поскольку make_api_request теперь имеет встроенный механизм повторов,
            // мы не генерируем исключения для Action Scheduler
            if ($result === false) {
                $this->log_error("Заказ $order_id не удалось обработать после всех попыток");
            } else {
                $this->log_success("Заказ $order_id успешно обработан асинхронно");
            }
            
            $this->log_info("=== ЗАВЕРШЕНИЕ АСИНХРОННОЙ ОБРАБОТКИ ЗАКАЗА $order_id ===");
            
        } catch (Exception $e) {
            $this->log_error("Критическая ошибка при обработке заказа $order_id: " . $e->getMessage());
            // Не пробрасываем исключение дальше, чтобы не нагружать Action Scheduler
        }
    }

    // Отправка заказа WooCommerce в TableCRM
    public function send_order_to_tablecrm($order_id) {
        $this->log_info("=== НАЧАЛО ОТПРАВКИ ЗАКАЗА $order_id В TABLECRM (HPOS Compatible) ===");
        
        if (!get_option('tablecrm_enable_orders', 1)) {
            $this->log_warning("Отправка заказов отключена в настройках плагина");
            return true; // Возвращаем true, так как это не ошибка
        }
        
        $order = $this->get_order_optimized($order_id);
        if (!$order) {
            $this->log_error("Заказ $order_id не найден в WooCommerce");
            return false;
        }

        $this->log_info("Заказ $order_id найден. Статус: " . $order->get_status());

        // Проверяем, был ли заказ уже отправлен ранее (HPOS-совместимо)
        $existing_doc_id = $this->get_order_meta($order, '_tablecrm_document_id');
        if (!empty($existing_doc_id)) {
            $this->log_info("Заказ $order_id уже обработан. Document ID: $existing_doc_id");
            return true; // Возвращаем true, так как заказ уже обработан
        }
        
        // Проверяем, нужно ли отправлять заказ с данным статусом
        $selected_statuses = get_option('tablecrm_order_statuses', array('wc-pending', 'wc-processing', 'wc-completed'));
        $current_status = 'wc-' . $order->get_status();
        
        $this->log_debug("Выбранные статусы для отправки: " . implode(', ', $selected_statuses));
        $this->log_debug("Текущий статус заказа: $current_status");
        
        if (!in_array($current_status, $selected_statuses)) {
            $this->log_warning("Заказ $order_id не отправлен - статус $current_status не выбран для отправки");
            return true; // Возвращаем true, так как это не ошибка
        }
        
        $this->log_success("Статус заказа $current_status разрешен для отправки");
        
        $data = array(
            'name' => $order->get_billing_first_name() . ' ' . $order->get_billing_last_name(),
            'email' => $order->get_billing_email(),
            'phone' => $order->get_billing_phone(),
            'order_id' => $order_id,
            'order_total' => $order->get_total(),
            'order_status' => $order->get_status(),
            'order_date' => $order->get_date_created()->format('Y-m-d H:i:s'),
            'source' => 'WooCommerce Order',
            'address' => $order->get_billing_address_1() . ' ' . $order->get_billing_address_2(),
            'city' => $order->get_billing_city(),
            'postcode' => $order->get_billing_postcode(),
            'country' => $order->get_billing_country(),
            'delivery_date' => $this->get_delivery_date($order),
            'delivery_time' => $this->get_delivery_time($order),
            'payment_method' => $order->get_payment_method_title(),
            'payment_status' => $order->is_paid() ? 'paid' : 'unpaid',
            'utm_data' => $this->get_utm_data($order_id),
            'domain' => $_SERVER['HTTP_HOST'],
            'items' => array()
        );
        
        // Добавляем товары из заказа с проверкой дубликатов
        $unique_items = array();
        foreach ($order->get_items() as $item) {
            $product = $item->get_product();
            $item_data = array(
                'name' => $item->get_name(),
                'quantity' => $item->get_quantity(),
                'price' => $item->get_total(),
                'unit_price' => $item->get_total() / $item->get_quantity(),
                'sku' => $product ? $product->get_sku() : '',
                'product_id' => $product ? $product->get_id() : '',
                'category' => $product ? wp_get_post_terms($product->get_id(), 'product_cat', array('fields' => 'names')) : array()
            );
            
            // Проверка на дублирование товаров по SKU или названию
            $duplicate_found = false;
            foreach ($unique_items as &$existing_item) {
                if (($item_data['sku'] && $item_data['sku'] === $existing_item['sku']) || 
                    $item_data['name'] === $existing_item['name']) {
                    // Объединяем количество дублированных товаров
                    $existing_item['quantity'] += $item_data['quantity'];
                    $existing_item['price'] += $item_data['price'];
                    $duplicate_found = true;
                    $this->log('Найден дубликат товара в заказе ' . $order_id . ': ' . $item_data['name']);
                    break;
                }
            }
            
            if (!$duplicate_found) {
                $unique_items[] = $item_data;
            }
        }
        $data['items'] = $unique_items;
        
        $this->log_info("Подготовлены данные заказа $order_id:");
        $this->log_debug("Имя: " . $this->mask_value($data['name'], 'name'));
        $this->log_debug("Email: " . $this->mask_value($data['email'], 'email'));
        $this->log_debug("Телефон: " . $this->mask_value($data['phone'], 'phone'));
        $this->log_debug("Сумма заказа: " . $data['order_total']);
        $this->log_debug("Количество товаров: " . count($data['items']));
        
        // Проверка дубликатов
        if (get_option('tablecrm_check_duplicates', 1)) {
            $this->log_info("Проверка дубликатов включена. Проверяем email: " . $this->mask_value($data['email'], 'email'));
            if ($this->check_duplicate($data['email'], $data['phone'])) {
                $this->log_warning("Дубликат найден для заказа $order_id: " . $this->mask_value($data['email'], 'email'));
                return true; // Возвращаем true, так как это не ошибка обработки
            }
            $this->log_success("Дубликатов не найдено. Продолжаем отправку.");
        } else {
            $this->log_info("Проверка дубликатов отключена в настройках");
        }
        
        $this->log_info("Отправляем заказ $order_id в TableCRM API...");
        $response = $this->make_api_request('docs_sales/', $data);
        
        if ($response['success']) {
            $document_id = isset($response['lead_id']) ? $response['lead_id'] : 'unknown';
            $this->update_order_meta($order, '_tablecrm_document_id', $document_id);
            $this->log_success("Заказ $order_id успешно отправлен в TableCRM. Document ID: $document_id");
            
            // Отправляем дополнительную информацию о доставке если есть
            $this->send_delivery_info($document_id, $data);
            
            // Отправляем UTM метки если есть
            $this->send_utm_tags($document_id, $data);
            
            $this->log_info("=== ЗАВЕРШЕНИЕ ОТПРАВКИ ЗАКАЗА $order_id (УСПЕШНО) ===");
            return true;
        } else {
            $this->log_error("Ошибка отправки заказа $order_id в TableCRM после всех попыток: " . $response['message']);
            $this->log_info("=== ЗАВЕРШЕНИЕ ОТПРАВКИ ЗАКАЗА $order_id (ОШИБКА) ===");
            return false;
        }
    }
    
    // Обновление заказа в TableCRM при смене статуса
    public function update_order_in_tablecrm($order_id) {
        if (!get_option('tablecrm_enable_orders', 1)) {
            return;
        }
        
        $order = $this->get_order_optimized($order_id);
        if (!$order) {
            return;
        }
        
        // Проверяем, был ли заказ уже отправлен в TableCRM (HPOS-совместимо)
        $tablecrm_document_id = $this->get_order_meta($order, '_tablecrm_document_id');
        if (empty($tablecrm_document_id)) {
            // Если заказ не был отправлен ранее, ставим его в очередь на отправку
            $this->queue_order_sync($order_id);
            return;
        }
        
        $this->log_info("Обновляем статус документа $tablecrm_document_id в TableCRM");
        
        // Обновляем статус оплаты документа
        $payment_status = $order->is_paid();
        $status_name = wc_get_order_status_name($order->get_status());
        
        // Для документов продаж в TableCRM обновляем статус через PATCH запрос
        $api_url = get_option('tablecrm_api_url', 'https://app.tablecrm.com/api/v1/');
        $api_key = tablecrm_get_api_key();
        
        if (!empty($api_key)) {
            $update_url = rtrim($api_url, '/') . '/docs_sales/' . $tablecrm_document_id . '/?token=' . $api_key;
            
            $update_data = array(
                'status' => $payment_status, // true если оплачен, false если нет
                'comment' => $this->format_order_comment(array(
                    'order_status' => $order->get_status(),
                    'payment_status' => $payment_status ? 'paid' : 'unpaid',
                    'updated_date' => current_time('Y-m-d H:i:s'),
                    'notes' => 'Заказ изменен на статус: ' . $status_name
                ))
            );
            
            $response = wp_remote_request($update_url, array(
                'method' => 'PATCH',
                'headers' => array(
                    'Content-Type' => 'application/json',
                    'User-Agent' => 'WordPress-TableCRM-Integration/1.0'
                ),
                'body' => json_encode($update_data),
                'timeout' => 30,
                'sslverify' => true
            ));
            
            if (is_wp_error($response)) {
                $this->handle_wp_error("Ошибка обновления документа $tablecrm_document_id", $response);
            } else {
                $status_code = wp_remote_retrieve_response_code($response);
                if ($status_code >= 200 && $status_code < 300) {
                    $this->log_success("Документ $tablecrm_document_id успешно обновлен в TableCRM. Новый статус: $status_name");
                } else {
                    $body = wp_remote_retrieve_body($response);
                    $this->log_error("Ошибка обновления документа $tablecrm_document_id (HTTP $status_code): " . substr($body, 0, 200));
                }
            }
        }
    }
    
    // Отправка формы Contact Form 7 в TableCRM
    public function send_cf7_to_tablecrm($contact_form) {
        if (!get_option('tablecrm_enable_cf7', 1)) {
            return;
        }
        
        $submission = WPCF7_Submission::get_instance();
        if (!$submission) {
            return;
        }
        
        $posted_data = $submission->get_posted_data();
        
        $data = array(
            'name' => isset($posted_data['your-name']) ? $posted_data['your-name'] : '',
            'email' => isset($posted_data['your-email']) ? $posted_data['your-email'] : '',
            'phone' => isset($posted_data['your-phone']) ? $posted_data['your-phone'] : '',
            'message' => isset($posted_data['your-message']) ? $posted_data['your-message'] : '',
            'source' => 'Contact Form 7',
            'form_id' => $contact_form->id(),
            'form_title' => $contact_form->title(),
            'date' => current_time('Y-m-d H:i:s'),
            'custom_fields' => $posted_data
        );
        
        // Проверка дубликатов
        if (get_option('tablecrm_check_duplicates', 1) && !empty($data['email'])) {
            if ($this->check_duplicate($data['email'], $data['phone'])) {
                $this->log('Дубликат найден для формы CF7: ' . $this->mask_value($data['email'], 'email'));
                return;
            }
        }
        
        $response = $this->make_api_request('leads', $data);
        
        if ($response['success']) {
            $this->log('Форма CF7 успешно отправлена в TableCRM. Lead ID: ' . $response['lead_id']);
        } else {
            $this->log('Ошибка отправки формы CF7 в TableCRM: ' . $response['message']);
        }
    }
    
    // Проверка дубликатов
    private function check_duplicate($email, $phone = '') {
        $params = array(
            'email' => $email
        );
        
        if (!empty($phone)) {
            $params['phone'] = $phone;
        }
        
        $response = $this->make_api_request('leads/check-duplicate', $params);
        
        return $response['success'] && isset($response['duplicate']) && $response['duplicate'];
    }
    
    // Основной метод для API запросов с экспоненциальным back-off
    private function make_api_request($initial_endpoint, $data = array(), $api_url = null, $api_key = null, $project_id = null, $attempt = 1) {
        $api_url = $api_url ?: get_option('tablecrm_api_url', 'https://app.tablecrm.com/api/v1/');
        $api_key = $api_key ?: tablecrm_get_api_key();
        $project_id = $project_id ?: get_option('tablecrm_project_id');

        // Конфигурация повторов с экспоненциальным back-off
        $max_attempts = 3;
        $base_delay = 30; // базовая задержка в секундах: 30 → 120 → 300
        $backoff_delays = array(0, 30, 120, 300); // задержки для 1-й, 2-й, 3-й попыток

        if (empty($api_url) || empty($api_key)) {
            $this->log_error("Не настроены обязательные параметры API (URL и Key)");
            return array(
                'success' => false,
                'message' => 'Не настроены обязательные параметры API (URL и Key)'
            );
        }
        
        $this->log_info("API запрос к TableCRM (попытка $attempt/$max_attempts)");
        
        // Если это повторная попытка, применяем задержку
        if ($attempt > 1 && isset($backoff_delays[$attempt - 1])) {
            $delay = $backoff_delays[$attempt - 1];
            $this->log_warning("Повторная попытка $attempt после задержки $delay секунд");
            sleep($delay);
        }
        
        $this->log_debug("API URL: $api_url");
        $this->log_debug("API Key: " . substr($api_key, 0, 10) . "...");
        $this->log_debug("Начальный эндпоинт: $initial_endpoint");

        // Правильные эндпоинты TableCRM API (проверены и работают)
        $endpoints_to_try = array(
            'docs_sales/',     // ✅ ПРАВИЛЬНЫЙ - создание документа продаж (работает)
            'contragents/',    // ✅ Создание контрагента
            'nomenclatures/',  // ✅ Создание номенклатуры
            'health'           // ✅ Тестовый эндпоинт (работает)
        );
        
        // Если передан конкретный эндпоинт, используем только его
        if (!empty($initial_endpoint) && $initial_endpoint !== 'docs_sales/') {
            $endpoints_to_try = array($initial_endpoint);
        }

        // ✅ ПРАВИЛЬНАЯ авторизация TableCRM (только query параметр)
        $auth_variants = array();
        
        // Единственный рабочий вариант авторизации TableCRM
        $auth_variants[] = array(
            'Content-Type' => 'application/json',
            'User-Agent' => 'WordPress-TableCRM-Integration/1.1-HPOS'
        );
        
        $last_error = '';
        $is_network_error = false;

        foreach ($endpoints_to_try as $endpoint_key => $current_endpoint) {
            // ✅ ПРАВИЛЬНЫЙ URL с токеном в query параметре (как требует TableCRM)
            $base_url = rtrim($api_url, '/') . '/' . ltrim($current_endpoint, '/');
            $url = $base_url . '?token=' . $api_key;
            $this->log_info("Попытка соединения с эндпоинтом " . ($endpoint_key + 1) . "/" . count($endpoints_to_try) . ": $current_endpoint");
            $this->log_debug("Полный URL: $base_url?token=***");

            foreach ($auth_variants as $variant_index => $headers) {
                // Добавляем Project ID если указан
                if (!empty($project_id)) {
                    $headers['X-Project-ID'] = $project_id;
                }

                // ✅ ПРАВИЛЬНЫЕ методы для TableCRM эндпоинтов
                if ($current_endpoint === 'health') {
                    $request_method = 'GET';
                } else {
                    $request_method = 'POST'; // docs_sales/ и contragents/ используют POST
                }
                
                $args = array(
                    'method' => $request_method,
                    'headers' => $headers,
                    'timeout' => 30,
                    'sslverify' => true
                );
                
                // Добавляем тело запроса для POST и PUT
                if ($args['method'] === 'POST' || $args['method'] === 'PUT') {
                    // Адаптируем формат данных под разные эндпоинты
                    $request_data = $this->adapt_data_format($current_endpoint, $data);
                    $args['body'] = json_encode($request_data);
                }

                $response = wp_remote_request($url, $args);

                if (is_wp_error($response)) {
                    $error_message = $response->get_error_message();
                    $this->log_error("WP Error для эндпоинта $current_endpoint: $error_message");
                    
                    // Определяем, является ли это сетевой ошибкой
                    $network_errors = array('http_request_failed', 'resolve_host', 'connect_error', 'timeout');
                    if (in_array($response->get_error_code(), $network_errors)) {
                        $is_network_error = true;
                    }
                    
                    $last_error = "Эндпоинт: $current_endpoint - " . $error_message;
                    continue; // Пробуем следующий вариант авторизации
                }

                $body = wp_remote_retrieve_body($response);
                $status_code = wp_remote_retrieve_response_code($response);

                $this->log_debug("Эндпоинт: $current_endpoint, Вариант " . ($variant_index + 1) . " - Status: $status_code");
                $this->log_debug("Response: " . substr($body, 0, 200));

                // Если статус успешный, возвращаем результат
                if ($status_code >= 200 && $status_code < 300) {
                    $decoded = json_decode($body, true);
                    $success_message = "✅ Успешная авторизация и запрос! Эндпоинт: $current_endpoint (попытка $attempt)";
                    $this->log_success($success_message);
                    
                    if ($decoded && is_array($decoded)) {
                        // ✅ Правильная обработка ответа TableCRM
                        $response_data = array('success' => true, 'message' => $success_message);
                        
                        // Для docs_sales извлекаем ID созданного документа
                        if ($current_endpoint === 'docs_sales/' && isset($decoded[0]['id'])) {
                            $response_data['lead_id'] = $decoded[0]['id'];
                            $response_data['document_number'] = isset($decoded[0]['number']) ? $decoded[0]['number'] : '';
                            $this->log_success("Создан документ продаж ID: " . $decoded[0]['id'] . ", номер: " . (isset($decoded[0]['number']) ? $decoded[0]['number'] : 'не указан'));
                        }
                        
                        // Для contragents извлекаем ID созданного контрагента
                        if ($current_endpoint === 'contragents/' && isset($decoded[0]['id'])) {
                            $response_data['contragent_id'] = $decoded[0]['id'];
                            $this->log_success("Создан контрагент ID: " . $decoded[0]['id']);
                        }
                        
                        // Для nomenclatures извлекаем ID созданной номенклатуры
                        if ($current_endpoint === 'nomenclatures/' && isset($decoded[0]['id'])) {
                            $response_data['nomenclature_id'] = $decoded[0]['id'];
                            $this->log_success("Создана номенклатура ID: " . $decoded[0]['id']);
                        }
                        
                        return $response_data;
                    }
                    return array('success' => true, 'message' => $success_message . ' (Ответ не JSON, но успешный)');
                }
                
                // Определяем, нужно ли повторить запрос
                $retryable_codes = array(500, 502, 503, 504, 429); // HTTP коды, которые могут быть временными
                if (in_array($status_code, $retryable_codes)) {
                    $is_network_error = true;
                }
                
                $last_error = 'Эндпоинт: ' . $current_endpoint . ', Вариант ' . ($variant_index + 1) . ' - HTTP ' . $status_code . ': ' . substr($body, 0, 100);

                // Если это 403 или 401, 404 ошибка, пробуем следующий вариант авторизации или эндпоинт
                if (in_array($status_code, array(401, 403, 404))) {
                    continue; 
                } else {
                    // Для других ошибок (например, 500) нет смысла перебирать дальше с этим эндпоинтом
                    break; 
                }
            } // конец перебора вариантов авторизации
            
            // Если один из эндпоинтов вернул ошибку, отличную от 401/403/404, и это не последний эндпоинт в списке,
            // то нет смысла пробовать другие эндпоинты с теми же данными и заголовками, так как проблема, скорее всего, не в эндпоинте.
            // Однако, если это был тестовый эндпоинт, мы должны продолжить пробовать другие.
            if (!in_array($current_endpoint, array('test', 'api/test', 'ping')) && !in_array($status_code, array(200, 201, 204, 401, 403, 404)) && $endpoint_key < count($endpoints_to_try) -1) {
                 $this->log_error("Серьезная ошибка ($status_code) на эндпоинте $current_endpoint. Прерываю дальнейший перебор эндпоинтов.");
                 break; // Прерываем перебор эндпоинтов
            }

        } // конец перебора эндпоинтов

        // Если это была сетевая ошибка или временная ошибка сервера, и мы не достигли лимита попыток
        if ($is_network_error && $attempt < $max_attempts) {
            $this->log_warning("Сетевая ошибка или временная ошибка сервера. Планируется повтор ($attempt/$max_attempts)");
            return $this->make_api_request($initial_endpoint, $data, $api_url, $api_key, $project_id, $attempt + 1);
        }

        $final_error = 'Все варианты API запросов не сработали после ' . $attempt . ' попыток. Последняя ошибка: ' . $last_error;
        $this->log_error($final_error);
        
        return array(
            'success' => false,
            'message' => $final_error
        );
    }
    
    // Сохранение UTM данных в заказ
    public function save_utm_data_to_order($order, $data) {
        // Получаем UTM данные из сессии или куки
        $utm_params = array('utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content');
        
        foreach ($utm_params as $param) {
            if (isset($_COOKIE[$param])) {
                $order->update_meta_data('_' . $param, sanitize_text_field($_COOKIE[$param]));
            } elseif (isset($_SESSION[$param])) {
                $order->update_meta_data('_' . $param, sanitize_text_field($_SESSION[$param]));
            }
        }
        
        // Сохраняем referrer
        if (isset($_COOKIE['referrer'])) {
            $order->update_meta_data('_referrer', sanitize_text_field($_COOKIE['referrer']));
        } elseif (isset($_SERVER['HTTP_REFERER'])) {
            $order->update_meta_data('_referrer', sanitize_text_field($_SERVER['HTTP_REFERER']));
        }
        
        // Сохраняем landing page
        if (isset($_COOKIE['landing_page'])) {
            $order->update_meta_data('_landing_page', sanitize_text_field($_COOKIE['landing_page']));
        }
    }
    
    // Получение даты доставки (HPOS-совместимо)
    private function get_delivery_date($order) {
        // Проверяем различные мета-поля для даты доставки
        $delivery_date = $this->get_order_meta($order, '_delivery_date');
        if (empty($delivery_date)) {
            $delivery_date = $this->get_order_meta($order, 'delivery_date');
        }
        if (empty($delivery_date)) {
            $delivery_date = $this->get_order_meta($order, '_orddd_timestamp'); // Order Delivery Date plugin
        }
        
        return $delivery_date ?: '';
    }
    
    // Получение времени доставки (HPOS-совместимо)
    private function get_delivery_time($order) {
        $delivery_time = $this->get_order_meta($order, '_delivery_time');
        if (empty($delivery_time)) {
            $delivery_time = $this->get_order_meta($order, 'delivery_time');
        }
        if (empty($delivery_time)) {
            $delivery_time = $this->get_order_meta($order, '_orddd_time_slot'); // Order Delivery Date plugin
        }
        
        return $delivery_time ?: '';
    }
    
    // Получение UTM данных (HPOS-совместимо)
    private function get_utm_data($order_id) {
        $order = $this->get_order_optimized($order_id);
        if (!$order) {
            return array();
        }
        
        $utm_data = array();
        
        // Список UTM параметров для проверки
        $utm_params = array('utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content');
        
        foreach ($utm_params as $param) {
            $value = $this->get_order_meta($order, '_' . $param);
            if (empty($value)) {
                $value = $this->get_order_meta($order, $param);
            }
            if (!empty($value)) {
                $utm_data[$param] = $value;
            }
        }
        
        // Добавляем referrer если есть
        $referrer = $this->get_order_meta($order, '_referrer');
        if (!empty($referrer)) {
            $utm_data['referrer'] = $referrer;
        }
        
        // Добавляем landing page если есть
        $landing_page = $this->get_order_meta($order, '_landing_page');
        if (!empty($landing_page)) {
            $utm_data['landing_page'] = $landing_page;
        }
        
        return $utm_data;
    }
    
    // ✅ ПРАВИЛЬНАЯ адаптация данных для TableCRM API
    private function adapt_data_format($endpoint, $data) {
        $this->log_debug("Адаптация данных для эндпоинта: $endpoint");
        
        if ($endpoint === 'docs_sales/') {
            // ✅ Правильный формат для создания документа продаж согласно TableCRM API
            $organization_id = get_option('tablecrm_organization_id', 38); // Используем 38 как в примере
            $cashbox_id = get_option('tablecrm_cashbox_id', 218); // Используем 218 как в примере
            $warehouse_id = get_option('tablecrm_warehouse_id', 39); // Склад
            $unit_id = get_option('tablecrm_unit_id', 116); // Единица измерения
            
            // Получаем или создаем контрагента
            $contragent_id = $this->get_or_create_contragent($data);
            if (!$contragent_id) {
                $contragent_id = 330985; // Значение по умолчанию как в примере
            }
            
            // Формируем товары для заказа
            $goods = array();
            if (isset($data['items']) && !empty($data['items'])) {
                foreach ($data['items'] as $item) {
                    // Получаем или создаем номенклатуру
                    $nomenclature_id = $this->get_or_create_nomenclature($item);
                    if (!$nomenclature_id) {
                        $nomenclature_id = 39697; // Значение по умолчанию как в примере
                    }
                    
                    $unit_price = isset($item['unit_price']) ? floatval($item['unit_price']) : 
                                 (isset($item['price']) && isset($item['quantity']) && $item['quantity'] > 0 ? 
                                  floatval($item['price']) / intval($item['quantity']) : 0);
                    $quantity = isset($item['quantity']) ? intval($item['quantity']) : 1;
                    $total_price = $unit_price * $quantity;
                    
                    $goods[] = array(
                        'price' => $unit_price,
                        'quantity' => $quantity,
                        'unit' => intval($unit_id),
                        'discount' => 0,
                        'sum_discounted' => 0,
                        'nomenclature' => intval($nomenclature_id)
                    );
                }
            } else {
                // Если нет товаров, создаем один стандартный товар
                $total = isset($data['order_total']) ? floatval($data['order_total']) : 500;
                $goods[] = array(
                    'price' => $total,
                    'quantity' => 1,
                    'unit' => intval($unit_id),
                    'discount' => 0,
                    'sum_discounted' => 0,
                    'nomenclature' => 39697 // Стандартная номенклатура
                );
            }
            
            $total_amount = isset($data['order_total']) ? floatval($data['order_total']) : 500;
            
            // Правильная схема документа продаж для TableCRM API
            $document_data = array(
                'dated' => time(), // Unix timestamp
                'operation' => 'Заказ',
                'comment' => $this->format_order_comment($data),
                'tax_included' => true,
                'tax_active' => true,
                'goods' => $goods,
                'settings' => (object)array(), // Пустой объект
                'warehouse' => intval($warehouse_id),
                'contragent' => intval($contragent_id),
                'paybox' => intval($cashbox_id),
                'organization' => intval($organization_id),
                'status' => false, // Не оплачен
                'paid_rubles' => number_format($total_amount, 2, '.', ''),
                'paid_lt' => 0
            );
            
            // TableCRM требует массив объектов
            $adapted_data = array($document_data);
            $this->log_debug("Данные для docs_sales: " . json_encode($adapted_data));
            
        } elseif ($endpoint === 'contragents/') {
            // ✅ Формат для создания контрагента в TableCRM
            $contragent_data = array(
                'name' => isset($data['name']) ? $data['name'] : 'Клиент с сайта',
                'email' => isset($data['email']) ? $data['email'] : '',
                'phone' => isset($data['phone']) ? $data['phone'] : '',
                'external_id' => isset($data['order_id']) ? 'order_' . $data['order_id'] : '',
                'comment' => $this->format_customer_comment($data),
                'type' => 'individual' // Тип контрагента - физическое лицо
            );
            
            // TableCRM требует массив объектов
            $adapted_data = array($contragent_data);
            $this->log_debug("Данные для contragents: " . json_encode($adapted_data));
            
        } elseif ($endpoint === 'nomenclatures/') {
            // ✅ Формат для создания номенклатуры в TableCRM
            $nomenclature_data = array(
                'name' => isset($data['name']) ? $data['name'] : 'Товар с сайта',
                'code' => isset($data['sku']) ? $data['sku'] : '',
                'external_id' => isset($data['product_id']) ? 'product_' . $data['product_id'] : '',
                'is_active' => true,
                'unit' => get_option('tablecrm_unit_id', 116) // Единица измерения
            );
            
            $adapted_data = array($nomenclature_data);
            $this->log_debug("Данные для nomenclatures: " . json_encode($adapted_data));
            
        } else {
            // Для health эндпоинта не нужны данные
            $adapted_data = array();
        }
        
        return $adapted_data;
    }
    
    // Получение или создание контрагента
    private function get_or_create_contragent($data) {
        if (empty($data['phone']) && empty($data['email'])) {
            return null;
        }
        
        // Сначала ищем контрагента по телефону
        if (!empty($data['phone'])) {
            $phone = preg_replace('/[^0-9]/', '', $data['phone']); // Убираем все кроме цифр
            $contragent_id = $this->search_contragent_by_phone($phone);
            if ($contragent_id) {
                $this->log_success("Найден существующий контрагент по телефону: $contragent_id");
                return $contragent_id;
            }
        }
        
        // Если не найден по телефону, ищем по email
        if (!empty($data['email'])) {
            $contragent_id = $this->search_contragent_by_email($data['email']);
            if ($contragent_id) {
                $this->log_success("Найден существующий контрагент по email: $contragent_id");
                return $contragent_id;
            }
        }
        
        // Если не найден, создаем нового
        $this->log_info("Контрагент не найден, создаем нового");
        return $this->create_new_contragent($data);
    }
    
    // Поиск контрагента по телефону
    private function search_contragent_by_phone($phone) {
        $api_url = get_option('tablecrm_api_url', 'https://app.tablecrm.com/api/v1/');
        $api_key = tablecrm_get_api_key();
        
        if (empty($api_key)) {
            return null;
        }
        
        $search_url = rtrim($api_url, '/') . '/contragents/?token=' . $api_key . 
                     '&limit=100&offset=0&sort=created_at:desc&phone=' . $phone;
        
        $response = wp_remote_get($search_url, array(
            'timeout' => 30,
            'sslverify' => true
        ));
        
        if (is_wp_error($response)) {
            $this->handle_wp_error('Ошибка поиска контрагента по телефону', $response);
            return null;
        }
        
        $body = wp_remote_retrieve_body($response);
        $status_code = wp_remote_retrieve_response_code($response);
        
        if ($status_code === 200) {
            $decoded = json_decode($body, true);
            if (isset($decoded['results']) && count($decoded['results']) > 0) {
                return intval($decoded['results'][0]['id']);
            }
        }
        
        return null;
    }
    
    // Поиск контрагента по email
    private function search_contragent_by_email($email) {
        $api_url = get_option('tablecrm_api_url', 'https://app.tablecrm.com/api/v1/');
        $api_key = tablecrm_get_api_key();
        
        if (empty($api_key)) {
            return null;
        }
        
        $search_url = rtrim($api_url, '/') . '/contragents/?token=' . $api_key . 
                     '&limit=100&offset=0&sort=created_at:desc&email=' . urlencode($email);
        
        $response = wp_remote_get($search_url, array(
            'timeout' => 30,
            'sslverify' => true
        ));
        
        if (is_wp_error($response)) {
            $this->handle_wp_error('Ошибка поиска контрагента по email', $response);
            return null;
        }
        
        $body = wp_remote_retrieve_body($response);
        $status_code = wp_remote_retrieve_response_code($response);
        
        if ($status_code === 200) {
            $decoded = json_decode($body, true);
            if (isset($decoded['results']) && count($decoded['results']) > 0) {
                return intval($decoded['results'][0]['id']);
            }
        }
        
        return null;
    }
    
    // Создание нового контрагента
    private function create_new_contragent($data) {
        $response = $this->make_api_request('contragents/', $data);
        
        if ($response['success'] && isset($response['contragent_id'])) {
            $this->log_success("Создан новый контрагент: " . $response['contragent_id']);
            return intval($response['contragent_id']);
        }
        
        $this->log_error("Ошибка создания контрагента: " . $response['message']);
        return null;
    }
    
    // Получение или создание номенклатуры
    private function get_or_create_nomenclature($item) {
        if (empty($item['name'])) {
            return null;
        }
        
        // Сначала ищем номенклатуру по названию
        $nomenclature_id = $this->search_nomenclature_by_name($item['name']);
        if ($nomenclature_id) {
            $this->log_success("Найдена существующая номенклатура: $nomenclature_id");
            return $nomenclature_id;
        }
        
        // Если не найдена, создаем новую
        $this->log_info("Номенклатура не найдена, создаем новую: " . $item['name']);
        return $this->create_new_nomenclature($item);
    }
    
    // Поиск номенклатуры по названию
    private function search_nomenclature_by_name($name) {
        $api_url = get_option('tablecrm_api_url', 'https://app.tablecrm.com/api/v1/');
        $api_key = tablecrm_get_api_key();
        
        if (empty($api_key)) {
            return null;
        }
        
        $search_url = rtrim($api_url, '/') . '/nomenclature/?token=' . $api_key . 
                     '&name=' . urlencode($name) . '&limit=100&offset=0&with_prices=false&with_balance=false&with_attributes=false&only_main_from_group=false';
        
        $response = wp_remote_get($search_url, array(
            'timeout' => 30,
            'sslverify' => true
        ));
        
        if (is_wp_error($response)) {
            $this->handle_wp_error('Ошибка поиска номенклатуры', $response);
            return null;
        }
        
        $body = wp_remote_retrieve_body($response);
        $status_code = wp_remote_retrieve_response_code($response);
        
        if ($status_code === 200) {
            $decoded = json_decode($body, true);
            if (isset($decoded['results']) && count($decoded['results']) > 0) {
                return intval($decoded['results'][0]['id']);
            }
        }
        
        return null;
    }
    
    // Создание новой номенклатуры
    private function create_new_nomenclature($item) {
        $response = $this->make_api_request('nomenclatures/', $item);
        
        if ($response['success'] && isset($response['nomenclature_id'])) {
            $this->log_success("Создана новая номенклатура: " . $response['nomenclature_id']);
            return intval($response['nomenclature_id']);
        }
        
        $this->log_error("Ошибка создания номенклатуры: " . $response['message']);
        return null;
    }
    
    // Отправка информации о доставке
    private function send_delivery_info($document_id, $data) {
        if (empty($document_id) || $document_id === 'unknown') {
            return;
        }
        
        $delivery_data = array();
        
        // Добавляем адрес доставки
        if (!empty($data['address'])) {
            $delivery_data['address'] = $data['address'];
        }
        if (!empty($data['city'])) {
            $delivery_data['city'] = $data['city'];
        }
        if (!empty($data['postcode'])) {
            $delivery_data['postcode'] = $data['postcode'];
        }
        
        // Добавляем дату и время доставки
        if (!empty($data['delivery_date'])) {
            $delivery_data['delivery_date'] = $data['delivery_date'];
        }
        if (!empty($data['delivery_time'])) {
            $delivery_data['delivery_time'] = $data['delivery_time'];
        }
        
        // Добавляем контактную информацию
        if (!empty($data['name'])) {
            $delivery_data['recipient_name'] = $data['name'];
        }
        if (!empty($data['phone'])) {
            $delivery_data['recipient_phone'] = $data['phone'];
        }
        
        if (empty($delivery_data)) {
            $this->log_debug("Нет данных для отправки информации о доставке");
            return;
        }
        
        $this->log_info("Отправляем информацию о доставке для документа $document_id");
        
        // Формируем URL для отправки информации о доставке
        $api_url = get_option('tablecrm_api_url', 'https://app.tablecrm.com/api/v1/');
        $api_key = tablecrm_get_api_key();
        
        if (empty($api_key)) {
            $this->log_error("Не настроен API ключ для отправки информации о доставке");
            return;
        }
        
        $delivery_url = rtrim($api_url, '/') . '/docs_sales/' . $document_id . '/delivery_info/?token=' . $api_key;
        
        $response = wp_remote_post($delivery_url, array(
            'method' => 'POST',
            'headers' => array(
                'Content-Type' => 'application/json',
                'User-Agent' => 'WordPress-TableCRM-Integration/1.0'
            ),
            'body' => json_encode($delivery_data),
            'timeout' => 30,
            'sslverify' => true
        ));
        
        if (is_wp_error($response)) {
            $this->handle_wp_error('Ошибка отправки информации о доставке', $response);
            return;
        }
        
        $status_code = wp_remote_retrieve_response_code($response);
        if ($status_code >= 200 && $status_code < 300) {
            $this->log_success("Информация о доставке успешно отправлена для документа $document_id");
        } else {
            $body = wp_remote_retrieve_body($response);
            $this->log_error("Ошибка отправки информации о доставке (HTTP $status_code): " . substr($body, 0, 200));
        }
    }
    
    // Отправка UTM меток
    private function send_utm_tags($document_id, $data) {
        if (empty($document_id) || $document_id === 'unknown' || empty($data['utm_data'])) {
            return;
        }
        
        $utm_data = $data['utm_data'];
        if (!is_array($utm_data) || empty($utm_data)) {
            $this->log_debug("Нет UTM данных для отправки");
            return;
        }
        
        $this->log_info("Отправляем UTM метки для документа $document_id");
        
        // Формируем URL для отправки UTM меток
        $api_url = get_option('tablecrm_api_url', 'https://app.tablecrm.com/api/v1/');
        $api_key = tablecrm_get_api_key();
        
        if (empty($api_key)) {
            $this->log_error("Не настроен API ключ для отправки UTM меток");
            return;
        }
        
        $utm_url = rtrim($api_url, '/') . '/docs_sales/' . $document_id . '/utm/?token=' . $api_key;
        
        // Формируем данные для отправки UTM меток
        $utm_payload = array();
        
        // Стандартные UTM параметры
        $utm_params = array('utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content');
        foreach ($utm_params as $param) {
            if (!empty($utm_data[$param])) {
                $utm_payload[$param] = $utm_data[$param];
            }
        }
        
        // Дополнительные параметры
        if (!empty($utm_data['referrer'])) {
            $utm_payload['referrer'] = $utm_data['referrer'];
        }
        if (!empty($utm_data['landing_page'])) {
            $utm_payload['landing_page'] = $utm_data['landing_page'];
        }
        
        if (empty($utm_payload)) {
            $this->log_debug("Нет UTM параметров для отправки");
            return;
        }
        
        $response = wp_remote_post($utm_url, array(
            'method' => 'POST',
            'headers' => array(
                'Content-Type' => 'application/json',
                'User-Agent' => 'WordPress-TableCRM-Integration/1.0'
            ),
            'body' => json_encode($utm_payload),
            'timeout' => 30,
            'sslverify' => true
        ));
        
        if (is_wp_error($response)) {
            $this->handle_wp_error('Ошибка отправки UTM меток', $response);
            return;
        }
        
        $status_code = wp_remote_retrieve_response_code($response);
        if ($status_code >= 200 && $status_code < 300) {
            $this->log_success("UTM метки успешно отправлены для документа $document_id");
        } else {
            $body = wp_remote_retrieve_body($response);
            $this->log_error("Ошибка отправки UTM меток (HTTP $status_code): " . substr($body, 0, 200));
        }
    }
    
    // ✅ Форматирование комментария для заказа
    private function format_order_comment($data) {
        $comment = "Заказ с сайта WordPress\n";
        if (isset($data['name'])) $comment .= "Клиент: " . $data['name'] . "\n";
        if (isset($data['email'])) $comment .= "Email: " . $data['email'] . "\n"; 
        if (isset($data['phone'])) $comment .= "Телефон: " . $data['phone'] . "\n";
        if (isset($data['order_date'])) $comment .= "Дата заказа: " . $data['order_date'] . "\n";
        if (isset($data['payment_method'])) $comment .= "Способ оплаты: " . $data['payment_method'] . "\n";
        if (isset($data['address'])) $comment .= "Адрес: " . $data['address'] . "\n";
        
        // Добавляем товары
        if (isset($data['items']) && !empty($data['items'])) {
            $comment .= "\nТовары:\n";
            foreach ($data['items'] as $item) {
                $comment .= "- " . $item['name'] . " (x" . $item['quantity'] . ") - " . $item['price'] . " руб.\n";
            }
        }
        
        // Добавляем UTM данные
        if (isset($data['utm_data']) && !empty($data['utm_data'])) {
            $comment .= "\nUTM данные:\n";
            foreach ($data['utm_data'] as $key => $value) {
                $comment .= "- " . $key . ": " . $value . "\n";
            }
        }
        
        return $comment;
    }
    
    // ✅ Форматирование комментария для клиента
    private function format_customer_comment($data) {
        $comment = "Клиент зарегистрирован через сайт WordPress\n";
        if (isset($data['order_id'])) $comment .= "Первый заказ: #" . $data['order_id'] . "\n";
        if (isset($data['city'])) $comment .= "Город: " . $data['city'] . "\n";
        if (isset($data['address'])) $comment .= "Адрес: " . $data['address'] . "\n";
        if (isset($data['domain'])) $comment .= "Домен: " . $data['domain'] . "\n";

        return $comment;
    }

    // Маскирование персональных данных при логировании
    private function mask_value($value, $type = 'generic') {
        if (empty($value)) {
            return $value;
        }
        switch ($type) {
            case 'email':
                $parts = explode('@', $value);
                if (count($parts) === 2) {
                    $name = substr($parts[0], 0, 2) . str_repeat('*', max(0, strlen($parts[0]) - 2));
                    return $name . '@' . $parts[1];
                }
                return $value;
            case 'phone':
                $clean = preg_replace('/\D+/', '', $value);
                return str_repeat('*', max(0, strlen($clean) - 2)) . substr($clean, -2);
            case 'name':
                return substr($value, 0, 1) . str_repeat('*', max(0, strlen($value) - 1));
            default:
                return $value;
        }
    }

    // Очистка старых записей в debug.log
    public function enforce_log_retention() {
        $days = (int) get_option('tablecrm_log_retention_days', 30);
        if ($days <= 0) {
            return;
        }
        $log_file = ini_get('error_log');
        if (!$log_file || !file_exists($log_file) || !is_writable($log_file)) {
            return;
        }

        $lines = file($log_file, FILE_IGNORE_NEW_LINES);
        if ($lines === false) {
            return;
        }
        $cutoff = time() - $days * DAY_IN_SECONDS;
        $filtered = array();
        foreach ($lines as $line) {
            if (preg_match('/^\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\]/', $line, $m)) {
                $ts = strtotime($m[1]);
                if ($ts === false || $ts >= $cutoff) {
                    $filtered[] = $line;
                }
            } else {
                $filtered[] = $line;
            }
        }
        file_put_contents($log_file, implode(PHP_EOL, $filtered) . PHP_EOL);
    }

    /**
     * Log WP_Errors with additional handling for SSL certificate issues.
     */
    private function handle_wp_error($context, $error) {
        $message = $error->get_error_message();
        if (stripos($message, 'certificate') !== false || stripos($message, 'ssl') !== false) {
            $this->log_error("{$context}: SSL certificate error - {$message}");
        } else {
            $this->log_error("{$context}: {$message}");
        }
    }

    // Улучшенное логирование
    private function log($message, $level = 'INFO') {
        // Всегда логируем в режиме отладки
        if (get_option('tablecrm_debug_mode', 0)) {
            $timestamp = date('Y-m-d H:i:s');
            $log_message = "[{$timestamp}] [TableCRM Integration] [{$level}] {$message}";
            error_log($log_message);
        }
        
        // Дополнительно логируем критические ошибки даже без режима отладки
        if (in_array($level, array('ERROR', 'CRITICAL', 'FATAL'))) {
            $timestamp = date('Y-m-d H:i:s');
            $log_message = "[{$timestamp}] [TableCRM Integration] [{$level}] {$message}";
            error_log($log_message);
        }
    }
    
    // Специальные методы логирования для разных уровней
    private function log_info($message) {
        $this->log($message, 'INFO');
    }
    
    private function log_warning($message) {
        $this->log($message, 'WARNING');
    }
    
    private function log_error($message) {
        $this->log($message, 'ERROR');
    }
    
    private function log_success($message) {
        $this->log($message, 'SUCCESS');
    }
    
    private function log_debug($message) {
        $this->log($message, 'DEBUG');
    }
}

// Инициализация плагина
global $tablecrm_integration;
$tablecrm_integration = new TableCRM_Integration();

// WP-Cron hook for log retention
add_action('tablecrm_daily_log_retention', 'tablecrm_run_log_retention');
function tablecrm_run_log_retention() {
    global $tablecrm_integration;
    if ($tablecrm_integration instanceof TableCRM_Integration) {
        $tablecrm_integration->enforce_log_retention();
    }
}

// ------------------------------------------------------------------
// Privacy Export/Erase callbacks
// ------------------------------------------------------------------

add_filter('wp_privacy_personal_data_exporters', 'tablecrm_register_exporter');
add_filter('wp_privacy_personal_data_erasers', 'tablecrm_register_eraser');

/**
 * Register personal data exporter.
 */
function tablecrm_register_exporter($exporters) {
    $exporters['tablecrm-order-data'] = array(
        'exporter_friendly_name' => __('TableCRM Order Data', 'tablecrm-integration'),
        'callback'              => 'tablecrm_export_personal_data',
    );
    return $exporters;
}

/**
 * Register personal data eraser.
 */
function tablecrm_register_eraser($erasers) {
    $erasers['tablecrm-order-data'] = array(
        'eraser_friendly_name' => __('TableCRM Order Data', 'tablecrm-integration'),
        'callback'             => 'tablecrm_erase_personal_data',
    );
    return $erasers;
}

/**
 * Export TableCRM related order metadata and log entries.
 *
 * @param string $email_address User email.
 * @param int    $page          Page number.
 * @return array Export data and paging info.
 */
function tablecrm_export_personal_data($email_address, $page = 1) {
    $number = 10; // items per page
    $data   = array();

    $user = get_user_by('email', $email_address);
    $args = array(
        'limit'         => $number,
        'paged'         => $page,
        'billing_email' => $email_address,
    );
    if ($user) {
        $args['customer'] = $user->ID;
    }

    if (function_exists('wc_get_orders')) {
        // Используем стандартный метод для GDPR, так как он уже HPOS-совместим
        $orders = wc_get_orders($args);
        foreach ($orders as $order) {
            $order_data = array();
            $meta_keys  = array(
                '_tablecrm_document_id',
                '_utm_source',
                '_utm_medium',
                '_utm_campaign',
                '_utm_term',
                '_utm_content',
                '_referrer',
                '_landing_page',
            );
            foreach ($meta_keys as $key) {
                $value = $order->get_meta($key, true);
                if (!empty($value)) {
                    $order_data[] = array(
                        'name'  => $key,
                        'value' => $value,
                    );
                }
            }

            if (!empty($order_data)) {
                $data[] = array(
                    'group_id'    => 'tablecrm-order',
                    'group_label' => __('TableCRM Order Data', 'tablecrm-integration'),
                    'item_id'     => 'order-' . $order->get_id(),
                    'data'        => $order_data,
                );
            }
        }
    }

    // Export log entries containing this email address.
    $log_path = WP_CONTENT_DIR . '/debug.log';
    if (file_exists($log_path)) {
        $lines = file($log_path);
        foreach ($lines as $line) {
            if (false !== strpos($line, 'TableCRM Integration') && false !== strpos($line, $email_address)) {
                $data[] = array(
                    'group_id'    => 'tablecrm-logs',
                    'group_label' => __('TableCRM Logs', 'tablecrm-integration'),
                    'item_id'     => md5($line),
                    'data'        => array(
                        array(
                            'name'  => __('Log entry', 'tablecrm-integration'),
                            'value' => trim($line),
                        ),
                    ),
                );
            }
        }
    }

    $done = empty($orders) || count($orders) < $number;
    return array(
        'data' => $data,
        'done' => $done,
    );
}

/**
 * Erase TableCRM related order metadata and log entries.
 *
 * @param string $email_address User email.
 * @param int    $page          Page number.
 * @return array Result info.
 */
function tablecrm_erase_personal_data($email_address, $page = 1) {
    $number = 10;
    $removed = false;

    $user = get_user_by('email', $email_address);
    $args = array(
        'limit'         => $number,
        'paged'         => $page,
        'billing_email' => $email_address,
    );
    if ($user) {
        $args['customer'] = $user->ID;
    }

    if (function_exists('wc_get_orders')) {
        // Используем стандартный метод для GDPR, так как он уже HPOS-совместим
        $orders = wc_get_orders($args);
        foreach ($orders as $order) {
            $meta_keys  = array(
                '_tablecrm_document_id',
                '_utm_source',
                '_utm_medium',
                '_utm_campaign',
                '_utm_term',
                '_utm_content',
                '_referrer',
                '_landing_page',
            );
            foreach ($meta_keys as $key) {
                if ($order->get_meta($key, true)) {
                    $order->delete_meta_data($key);
                    $removed = true;
                }
            }
            $order->save();
        }
    }

    // Remove log lines with this email address.
    $log_path = WP_CONTENT_DIR . '/debug.log';
    if (file_exists($log_path)) {
        $lines     = file($log_path);
        $new_lines = array();
        foreach ($lines as $line) {
            if (false !== strpos($line, 'TableCRM Integration') && false !== strpos($line, $email_address)) {
                $removed = true;
                continue;
            }
            $new_lines[] = $line;
        }
        file_put_contents($log_path, implode('', $new_lines));
    }

    $done = empty($orders) || count($orders) < $number;

    return array(
        'items_removed'  => $removed,
        'items_retained' => false,
        'messages'       => array(),
        'done'           => $done,
    );
}

    // Хук активации плагина
register_activation_hook(__FILE__, 'tablecrm_activation');
function tablecrm_activation() {
    // Логируем активацию плагина
    error_log('[TableCRM Integration] [INFO] Плагин активирован');
    
    // Установка дефолтных настроек
    if (!get_option('tablecrm_api_url')) {
        update_option('tablecrm_api_url', 'https://app.tablecrm.com/api/v1/');
        error_log('[TableCRM Integration] [INFO] Установлен API URL по умолчанию');
    }
    if (!get_option('tablecrm_enable_orders')) {
        update_option('tablecrm_enable_orders', 1);
        error_log('[TableCRM Integration] [INFO] Включена отправка заказов WooCommerce');
    }
    if (!get_option('tablecrm_enable_cf7')) {
        update_option('tablecrm_enable_cf7', 1);
        error_log('[TableCRM Integration] [INFO] Включена отправка форм Contact Form 7');
    }
    if (!get_option('tablecrm_check_duplicates')) {
        update_option('tablecrm_check_duplicates', 1);
        error_log('[TableCRM Integration] [INFO] Включена проверка дубликатов');
    }
    if (!get_option('tablecrm_order_statuses')) {
        update_option('tablecrm_order_statuses', array('wc-pending', 'wc-processing', 'wc-completed'));
        error_log('[TableCRM Integration] [INFO] Установлены статусы заказов по умолчанию');
    }
    if (!get_option('tablecrm_debug_mode')) {
        update_option('tablecrm_debug_mode', 1);
        error_log('[TableCRM Integration] [INFO] Включен режим отладки по умолчанию');
    }
    if (!get_option('tablecrm_organization_id')) {
        update_option('tablecrm_organization_id', 38);
        error_log('[TableCRM Integration] [INFO] Установлен ID организации по умолчанию: 38');
    }
    if (!get_option('tablecrm_cashbox_id')) {
        update_option('tablecrm_cashbox_id', 218);
        error_log('[TableCRM Integration] [INFO] Установлен ID кассы по умолчанию: 218');
    }
    if (!get_option('tablecrm_warehouse_id')) {
        update_option('tablecrm_warehouse_id', 39);
        error_log('[TableCRM Integration] [INFO] Установлен ID склада по умолчанию: 39');
    }
    if (!get_option('tablecrm_unit_id')) {
        update_option('tablecrm_unit_id', 116);
        error_log('[TableCRM Integration] [INFO] Установлен ID единицы измерения по умолчанию: 116');
    }
    if (!get_option('tablecrm_log_retention_days')) {
        update_option('tablecrm_log_retention_days', 30);
    }

    // Schedule daily log retention event
    if (!wp_next_scheduled('tablecrm_daily_log_retention')) {
        wp_schedule_event(time(), 'daily', 'tablecrm_daily_log_retention');
    }

    error_log('[TableCRM Integration] [SUCCESS] Плагин успешно активирован и настроен');
}

// Хук деактивации плагина
register_deactivation_hook(__FILE__, 'tablecrm_deactivation');
function tablecrm_deactivation() {
    // Clear scheduled log retention event
    $timestamp = wp_next_scheduled('tablecrm_daily_log_retention');
    if ($timestamp) {
        wp_unschedule_event($timestamp, 'tablecrm_daily_log_retention');
    }
    // Очистка временных данных
}
