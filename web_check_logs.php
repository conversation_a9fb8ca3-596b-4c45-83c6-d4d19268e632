<?php
// Веб-версия проверки логов TableCRM интеграции
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html>
<head>
    <title>TableCRM Logs Check</title>
    <style>
        body { font-family: monospace; background: #1e1e1e; color: #fff; padding: 20px; }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        .info { color: #2196F3; }
        .section { margin: 20px 0; padding: 10px; border: 1px solid #333; }
        pre { background: #2d2d2d; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
<h1>🔍 TableCRM Integration Logs Check</h1>

<?php
$wp_path = '/home/<USER>/bekflom3/mosbuketik.ru/public_html';
require_once($wp_path . '/wp-config.php');
require_once($wp_path . '/wp-load.php');

echo "<div class='success'>✅ WordPress загружен успешно</div>";

// 1. Проверяем лог файл плагина
$log_file = WP_CONTENT_DIR . '/uploads/tablecrm_integration_complete.log';
echo "<div class='section'>";
echo "<h2>📄 ЛОГ ФАЙЛ ПЛАГИНА</h2>";
echo "<p>Путь к логу: <code>$log_file</code></p>";

if (file_exists($log_file)) {
    echo "<div class='success'>✅ Лог файл существует</div>";
    $log_size = filesize($log_file);
    echo "<p>Размер файла: " . number_format($log_size) . " байт</p>";
    
    // Читаем последние 30 строк лога
    echo "<h3>📋 ПОСЛЕДНИЕ 30 СТРОК ЛОГА</h3>";
    $lines = file($log_file);
    $total_lines = count($lines);
    echo "<p>Всего строк в логе: $total_lines</p>";
    
    echo "<pre>";
    $start_line = max(0, $total_lines - 30);
    for ($i = $start_line; $i < $total_lines; $i++) {
        $line = trim($lines[$i]);
        if (strpos($line, 'ERROR') !== false) {
            echo "<span class='error'>" . htmlspecialchars($line) . "</span>\n";
        } elseif (strpos($line, 'WARNING') !== false) {
            echo "<span class='warning'>" . htmlspecialchars($line) . "</span>\n";
        } elseif (strpos($line, 'SUCCESS') !== false) {
            echo "<span class='success'>" . htmlspecialchars($line) . "</span>\n";
        } else {
            echo "<span class='info'>" . htmlspecialchars($line) . "</span>\n";
        }
    }
    echo "</pre>";
} else {
    echo "<div class='error'>❌ Лог файл не найден</div>";
}
echo "</div>";

// 2. Проверяем последние заказы
echo "<div class='section'>";
echo "<h2>🛒 ПОСЛЕДНИЕ ЗАКАЗЫ WOOCOMMERCE</h2>";

$orders = wc_get_orders(array(
    'limit' => 5,
    'orderby' => 'date',
    'order' => 'DESC',
    'status' => array('processing', 'completed', 'pending')
));

foreach ($orders as $order) {
    $order_id = $order->get_id();
    $order_status = $order->get_status();
    $order_date = $order->get_date_created()->format('Y-m-d H:i:s');
    
    echo "<div style='border: 1px solid #555; margin: 10px 0; padding: 10px;'>";
    echo "<h3>📦 ЗАКАЗ #$order_id</h3>";
    echo "<p><strong>Статус:</strong> $order_status</p>";
    echo "<p><strong>Дата создания:</strong> $order_date</p>";
    
    // Проверяем мета-данные TableCRM
    $tablecrm_doc_id = get_post_meta($order_id, '_tablecrm_complete_document_id', true);
    $tablecrm_sent_at = get_post_meta($order_id, '_tablecrm_complete_sent_at', true);
    
    if (!empty($tablecrm_doc_id)) {
        echo "<div class='success'>✅ TableCRM Document ID: $tablecrm_doc_id</div>";
        if (!empty($tablecrm_sent_at)) {
            echo "<div class='success'>✅ Отправлен в TableCRM: $tablecrm_sent_at</div>";
        }
    } else {
        echo "<div class='error'>❌ НЕ отправлен в TableCRM</div>";
    }
    
    // Проверяем данные доставки
    $delivery_date = $order->get_meta('delivery_date');
    $delivery_time = $order->get_meta('delivery_time');
    $delivery_type = $order->get_meta('delivery_type');
    
    if (!empty($delivery_date)) {
        echo "<p><strong>📅 Дата доставки:</strong> $delivery_date</p>";
    }
    if (!empty($delivery_time)) {
        echo "<p><strong>⏰ Время доставки:</strong> $delivery_time</p>";
    }
    if (!empty($delivery_type)) {
        echo "<p><strong>🚚 Тип доставки:</strong> $delivery_type</p>";
    }
    
    // Проверяем стоимость доставки
    $shipping_total = $order->get_shipping_total();
    if ($shipping_total > 0) {
        echo "<p><strong>💰 Стоимость доставки:</strong> $shipping_total руб.</p>";
    } else {
        echo "<div class='warning'>❌ Доставка бесплатная или не настроена</div>";
    }
    
    // Проверяем заметки заказа
    $notes = wc_get_order_notes(array('order_id' => $order_id, 'limit' => 3));
    if (!empty($notes)) {
        echo "<p><strong>📝 Последние заметки:</strong></p>";
        echo "<ul>";
        foreach ($notes as $note) {
            $note_content = strip_tags($note->content);
            $note_date = $note->date_created->format('Y-m-d H:i:s');
            echo "<li>$note_content <em>($note_date)</em></li>";
        }
        echo "</ul>";
    }
    echo "</div>";
}
echo "</div>";

// 3. Настройки плагина
echo "<div class='section'>";
echo "<h2>⚙️ НАСТРОЙКИ ПЛАГИНА</h2>";
$enable_orders = get_option('tablecrm_complete_enable_orders', 1);
$api_key = get_option('tablecrm_complete_api_key');
$api_url = get_option('tablecrm_complete_api_url');
$trigger_statuses = get_option('tablecrm_complete_order_statuses', array('processing', 'completed'));

echo "<p><strong>Отправка заказов включена:</strong> " . ($enable_orders ? '<span class="success">ДА</span>' : '<span class="error">НЕТ</span>') . "</p>";
echo "<p><strong>API URL:</strong> $api_url</p>";
echo "<p><strong>API Key настроен:</strong> " . (!empty($api_key) ? '<span class="success">ДА</span>' : '<span class="error">НЕТ</span>') . "</p>";
echo "<p><strong>Статусы для отправки:</strong> " . implode(', ', $trigger_statuses) . "</p>";
echo "</div>";

// 4. Проверка хуков
echo "<div class='section'>";
echo "<h2>🔗 ПРОВЕРКА ХУКОВ WORDPRESS</h2>";

global $wp_filter;

$hooks_to_check = array(
    'woocommerce_order_status_changed',
    'woocommerce_new_order',
    'woocommerce_checkout_order_processed'
);

foreach ($hooks_to_check as $hook) {
    if (isset($wp_filter[$hook])) {
        echo "<div class='success'>✅ Хук '$hook' зарегистрирован</div>";
        
        // Проверяем наши колбэки
        foreach ($wp_filter[$hook]->callbacks as $priority => $callbacks) {
            foreach ($callbacks as $callback) {
                if (is_array($callback['function']) && 
                    is_object($callback['function'][0]) && 
                    get_class($callback['function'][0]) === 'TableCRM_Integration_Complete') {
                    echo "<p style='margin-left: 20px;'>- Наш колбэк найден с приоритетом $priority</p>";
                }
            }
        }
    } else {
        echo "<div class='error'>❌ Хук '$hook' НЕ зарегистрирован</div>";
    }
}
echo "</div>";

echo "<div class='success'><h2>✅ ПРОВЕРКА ЗАВЕРШЕНА</h2></div>";
?>

</body>
</html> 