<?php
// Создание тестового заказа WooCommerce для проверки интеграции с TableCRM
echo "🛒 СОЗДАНИЕ ТЕСТОВОГО ЗАКАЗА WOOCOMMERCE\n";
echo "========================================\n\n";

// Данные для тестового заказа
$test_order_data = array(
    'billing_first_name' => 'Тестовый',
    'billing_last_name' => 'Покупатель',
    'billing_email' => 'woocommerce_test_' . date('His') . '@example.com',
    'billing_phone' => '+7 (999) 123-' . date('His'),
    'billing_address_1' => 'ул. Тестовая, д. 1',
    'billing_city' => 'Москва',
    'billing_postcode' => '123456',
    'billing_country' => 'RU',
    'payment_method' => 'bacs',
    'payment_method_title' => 'Банковский перевод',
    'status' => 'pending'
);

echo "📝 Подготовка данных для тестового заказа:\n";
echo "Имя: " . $test_order_data['billing_first_name'] . " " . $test_order_data['billing_last_name'] . "\n";
echo "Email: " . $test_order_data['billing_email'] . "\n";
echo "Телефон: " . $test_order_data['billing_phone'] . "\n";
echo "Адрес: " . $test_order_data['billing_address_1'] . ", " . $test_order_data['billing_city'] . "\n\n";

// SFTP подключение
$sftp_host = 'bekflom3.beget.tech';
$sftp_user = 'bekflom3_wz';
$sftp_pass = 'zl2Shj!e&6ng';

echo "🔌 Подключение к SFTP серверу...\n";
$connection = ssh2_connect($sftp_host, 22);

if (!$connection) {
    echo "❌ Ошибка: Не удалось подключиться к SFTP серверу\n";
    exit(1);
}

if (!ssh2_auth_password($connection, $sftp_user, $sftp_pass)) {
    echo "❌ Ошибка: Неверные учетные данные SFTP\n";
    exit(1);
}

$sftp = ssh2_sftp($connection);

echo "✅ Успешное подключение к SFTP серверу\n\n";

// Создаем PHP скрипт для создания заказа на сервере
$order_creation_script = '<?php
// Подключение к WordPress
require_once("/home/<USER>/bekflom3/mosbuketik.ru/public_html/wp-config.php");

// Проверяем что WooCommerce активен
if (!class_exists("WC_Order")) {
    echo "ERROR: WooCommerce не активен\n";
    exit(1);
}

echo "🛒 СОЗДАНИЕ ТЕСТОВОГО ЗАКАЗА WOOCOMMERCE\n";
echo "========================================\n";

// Данные заказа
$order_data = array(
    "billing_first_name" => "' . $test_order_data['billing_first_name'] . '",
    "billing_last_name" => "' . $test_order_data['billing_last_name'] . '", 
    "billing_email" => "' . $test_order_data['billing_email'] . '",
    "billing_phone" => "' . $test_order_data['billing_phone'] . '",
    "billing_address_1" => "' . $test_order_data['billing_address_1'] . '",
    "billing_city" => "' . $test_order_data['billing_city'] . '",
    "billing_postcode" => "' . $test_order_data['billing_postcode'] . '",
    "billing_country" => "' . $test_order_data['billing_country'] . '",
    "payment_method" => "' . $test_order_data['payment_method'] . '",
    "payment_method_title" => "' . $test_order_data['payment_method_title'] . '",
    "status" => "' . $test_order_data['status'] . '"
);

try {
    // Создаем новый заказ
    $order = wc_create_order();
    
    if (!$order) {
        throw new Exception("Не удалось создать объект заказа");
    }
    
    echo "📦 Создан заказ ID: " . $order->get_id() . "\n";
    
    // Устанавливаем данные покупателя
    $order->set_billing_first_name($order_data["billing_first_name"]);
    $order->set_billing_last_name($order_data["billing_last_name"]);
    $order->set_billing_email($order_data["billing_email"]);
    $order->set_billing_phone($order_data["billing_phone"]);
    $order->set_billing_address_1($order_data["billing_address_1"]);
    $order->set_billing_city($order_data["billing_city"]);
    $order->set_billing_postcode($order_data["billing_postcode"]);
    $order->set_billing_country($order_data["billing_country"]);
    
    // Устанавливаем способ оплаты
    $order->set_payment_method($order_data["payment_method"]);
    $order->set_payment_method_title($order_data["payment_method_title"]);
    
    echo "👤 Установлены данные покупателя\n";
    
    // Ищем товар для добавления в заказ
    $products = wc_get_products(array(
        "limit" => 1,
        "status" => "publish"
    ));
    
    if (!empty($products)) {
        $product = $products[0];
        $product_id = $product->get_id();
        $product_name = $product->get_name();
        $product_price = $product->get_price();
        
        echo "🌸 Найден товар: $product_name (ID: $product_id, Цена: $product_price руб.)\n";
        
        // Добавляем товар в заказ
        $order->add_product($product, 1);
        echo "✅ Товар добавлен в заказ\n";
    } else {
        echo "⚠️ Товары не найдены, создаем заказ без товаров\n";
    }
    
    // Рассчитываем итоговую сумму
    $order->calculate_totals();
    
    // Устанавливаем статус
    $order->set_status($order_data["status"]);
    
    // Сохраняем заказ
    $order->save();
    
    $order_id = $order->get_id();
    $order_total = $order->get_total();
    
    echo "💰 Сумма заказа: $order_total руб.\n";
    echo "📊 Статус заказа: " . $order->get_status() . "\n";
    echo "✅ Заказ #$order_id успешно создан!\n\n";
    
    // Проверяем TableCRM интеграцию
    echo "🔍 ПРОВЕРКА ИНТЕГРАЦИИ С TABLECRM:\n";
    echo "==================================\n";
    
    // Проверяем TableCRM ID в метаданных заказа
    sleep(5); // Даем время плагину обработать заказ
    
    $tablecrm_id = get_post_meta($order_id, "_tablecrm_lead_id", true);
    
    if ($tablecrm_id && $tablecrm_id !== "unknown") {
        echo "✅ TableCRM ID найден: $tablecrm_id\n";
        echo "✅ Интеграция работает корректно!\n";
    } else {
        echo "❌ TableCRM ID не найден или равен \"unknown\"\n";
        echo "⚠️ Возможная проблема с интеграцией\n";
    }
    
    echo "\n📋 ИТОГОВАЯ ИНФОРМАЦИЯ:\n";
    echo "========================\n";
    echo "🆔 ID заказа: $order_id\n";
    echo "👤 Покупатель: " . $order_data["billing_first_name"] . " " . $order_data["billing_last_name"] . "\n";
    echo "📧 Email: " . $order_data["billing_email"] . "\n";
    echo "📱 Телефон: " . $order_data["billing_phone"] . "\n";
    echo "💰 Сумма: $order_total руб.\n";
    echo "📊 Статус: " . $order->get_status() . "\n";
    echo "🔗 TableCRM ID: " . ($tablecrm_id ?: "НЕ УСТАНОВЛЕН") . "\n";
    
} catch (Exception $e) {
    echo "❌ ОШИБКА: " . $e->getMessage() . "\n";
}
?>';

// Загружаем скрипт на сервер
$script_path = '/home/<USER>/bekflom3/mosbuketik.ru/public_html/create_woocommerce_test_order.php';

echo "📤 Загрузка скрипта создания заказа на сервер...\n";

$stream = fopen("ssh2.sftp://$sftp$script_path", 'w');
if (!$stream) {
    echo "❌ Ошибка: Не удалось создать файл на сервере\n";
    exit(1);
}

fwrite($stream, $order_creation_script);
fclose($stream);

echo "✅ Скрипт загружен на сервер: $script_path\n\n";

// Выполняем скрипт через SSH
echo "🚀 Выполнение скрипта создания заказа...\n";
echo str_repeat("=", 50) . "\n";

$command = "cd /home/<USER>/bekflom3/mosbuketik.ru/public_html && php create_woocommerce_test_order.php";
$stream = ssh2_exec($connection, $command);

if (!$stream) {
    echo "❌ Ошибка: Не удалось выполнить команду\n";
    exit(1);
}

stream_set_blocking($stream, true);
$output = stream_get_contents($stream);
fclose($stream);

echo $output;

echo str_repeat("=", 50) . "\n";
echo "🏁 Скрипт выполнен\n\n";

// Удаляем временный скрипт
echo "🧹 Удаление временного скрипта...\n";
ssh2_sftp_unlink($sftp, $script_path);
echo "✅ Временный скрипт удален\n\n";

echo "📊 ПРОВЕРКА ЗАВЕРШЕНА\n";
echo "=====================\n";
echo "Проверьте логи WordPress и TableCRM для подтверждения интеграции.\n";
echo "Если заказ получил корректный TableCRM ID, интеграция работает!\n";
?> 