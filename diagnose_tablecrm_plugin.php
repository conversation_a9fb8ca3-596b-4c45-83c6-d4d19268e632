<?php
/**
 * Скрипт диагностики плагина TableCRM Integration
 * Версия: 2.0.0 - обновлено для Complete v1.0.24
 * Дата: 2025-06-04
 */

echo "🔍 ДИАГНОСТИКА ПЛАГИНА TABLECRM INTEGRATION COMPLETE v1.0.24\n";
echo "============================================================\n\n";

// Определяем путь к WordPress
$wp_path = '/home/<USER>/bekflom3/mosbuketik.ru/public_html';
$plugins_path = $wp_path . '/wp-content/plugins';

echo "📂 Проверка путей:\n";
echo "WordPress: " . $wp_path . "\n";
echo "Плагины: " . $plugins_path . "\n\n";

// Проверяем наличие файлов плагина
echo "📋 ПОИСК ФАЙЛОВ ПЛАГИНА:\n";
echo "========================\n";

$plugin_files = [
    'tablecrm-integration/tablecrm-integration.php',
    'tablecrm-integration.php',
    'tablecrm-integration-complete-v1.0.24.php',
    'tablecrm-integration-latest.zip'
];

foreach ($plugin_files as $file) {
    $full_path = $plugins_path . '/' . $file;
    if (file_exists($full_path)) {
        echo "✅ НАЙДЕН: $file\n";
        echo "   Размер: " . filesize($full_path) . " байт\n";
        echo "   Изменен: " . date('Y-m-d H:i:s', filemtime($full_path)) . "\n";
        
        // Читаем заголовок плагина
        $content = file_get_contents($full_path, false, null, 0, 2000);
        if (preg_match('/Plugin Name:\s*(.+)/i', $content, $matches)) {
            echo "   Название: " . trim($matches[1]) . "\n";
        }
        if (preg_match('/Version:\s*(.+)/i', $content, $matches)) {
            echo "   Версия: " . trim($matches[1]) . "\n";
        }
        echo "\n";
    } else {
        echo "❌ НЕ НАЙДЕН: $file\n";
    }
}

// Проверяем активные плагины
echo "🔌 АКТИВНЫЕ ПЛАГИНЫ:\n";
echo "===================\n";

if (file_exists($wp_path . '/wp-config.php')) {
    // Подключаем WordPress
    define('WP_USE_THEMES', false);
    require_once($wp_path . '/wp-config.php');
    require_once($wp_path . '/wp-load.php');
    
    $active_plugins = get_option('active_plugins', []);
    echo "Всего активных плагинов: " . count($active_plugins) . "\n";
    
    foreach ($active_plugins as $plugin) {
        if (stripos($plugin, 'tablecrm') !== false) {
            echo "✅ АКТИВЕН: $plugin\n";
            
            // Проверяем файл плагина
            $plugin_file = $plugins_path . '/' . $plugin;
            if (file_exists($plugin_file)) {
                $content = file_get_contents($plugin_file, false, null, 0, 3000);
                
                // Ищем версию
                if (preg_match('/Version:\s*(.+)/i', $content, $matches)) {
                    echo "   Версия в файле: " . trim($matches[1]) . "\n";
                }
                
                // Ищем ключевые функции
                if (strpos($content, 'get_or_create_tablecrm_nomenclature_id') !== false) {
                    echo "   ✅ Есть динамическое создание номенклатуры\n";
                } else {
                    echo "   ❌ НЕТ динамического создания номенклатуры\n";
                }
                
                if (strpos($content, 'get_or_create_tablecrm_contragent_id') !== false) {
                    echo "   ✅ Есть динамическое создание контрагентов\n";
                } else {
                    echo "   ❌ НЕТ динамического создания контрагентов\n";
                }
                
                // Проверяем тип контрагента
                if (preg_match('/"type":\s*"([^"]+)"/', $content, $matches)) {
                    echo "   Тип контрагента: " . $matches[1] . "\n";
                }
            }
        }
    }
} else {
    echo "❌ wp-config.php не найден\n";
}

echo "\n";

// Проверяем логи
echo "📝 АНАЛИЗ ЛОГОВ:\n";
echo "===============\n";

$log_files = [
    $wp_path . '/wp-content/debug.log',
    $wp_path . '/debug.log',
    $wp_path . '/wp-content/tablecrm_debug.log'
];

foreach ($log_files as $log_file) {
    if (file_exists($log_file)) {
        echo "✅ НАЙДЕН ЛОГ: " . basename($log_file) . "\n";
        echo "   Размер: " . filesize($log_file) . " байт\n";
        echo "   Изменен: " . date('Y-m-d H:i:s', filemtime($log_file)) . "\n";
        
        // Последние 10 строк
        $lines = file($log_file);
        $last_lines = array_slice($lines, -10);
        echo "   Последние записи:\n";
        foreach ($last_lines as $line) {
            if (stripos($line, 'tablecrm') !== false) {
                echo "   > " . trim($line) . "\n";
            }
        }
        echo "\n";
    }
}

// Проверяем настройки плагина
echo "⚙️ НАСТРОЙКИ ПЛАГИНА:\n";
echo "====================\n";

if (function_exists('get_option')) {
    $tablecrm_options = [
        'tablecrm_api_token',
        'tablecrm_api_url', 
        'tablecrm_warehouse_id',
        'tablecrm_organization_id',
        'tablecrm_contragent_id',
        'tablecrm_nomenclature_id'
    ];
    
    foreach ($tablecrm_options as $option) {
        $value = get_option($option, 'НЕ УСТАНОВЛЕНО');
        echo "$option: $value\n";
    }
}

echo "\n";

// Проверяем последние заказы
echo "🛒 ПОСЛЕДНИЕ ЗАКАЗЫ:\n";
echo "===================\n";

if (function_exists('wc_get_orders')) {
    $orders = wc_get_orders([
        'limit' => 5,
        'orderby' => 'date',
        'order' => 'DESC'
    ]);
    
    foreach ($orders as $order) {
        echo "Заказ #{$order->get_id()}: {$order->get_status()} - {$order->get_date_created()->date('Y-m-d H:i:s')}\n";
        
        // Проверяем метаданные TableCRM
        $tablecrm_sent = get_post_meta($order->get_id(), '_tablecrm_sent', true);
        $tablecrm_doc_id = get_post_meta($order->get_id(), '_tablecrm_document_id', true);
        
        if ($tablecrm_sent) {
            echo "  ✅ Отправлен в TableCRM";
            if ($tablecrm_doc_id) {
                echo " (Document ID: $tablecrm_doc_id)";
            }
            echo "\n";
        } else {
            echo "  ❌ НЕ отправлен в TableCRM\n";
        }
    }
}

echo "\n";
echo "🏁 ДИАГНОСТИКА ЗАВЕРШЕНА\n";
echo "Время выполнения: " . date('Y-m-d H:i:s') . "\n"; 