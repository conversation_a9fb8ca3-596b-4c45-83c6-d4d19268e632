<?php
/**
 * Скрипт для проверки логов TableCRM интеграции
 * Запускать через браузер: https://mosbuketik.ru/check_integration_logs.php
 */

echo "<h1>🔍 Проверка логов TableCRM Integration</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} pre{background:#f5f5f5;padding:10px;border-radius:5px;}</style>";

// Функция для безопасного отображения данных
function safe_output($data) {
    return htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
}

echo "<h2>📋 Информация о последнем созданном заказе</h2>";

// Проверяем последний заказ
try {
    require_once('wp-config.php');
    require_once('wp-load.php');
    
    echo "<div class='info'>✅ WordPress загружен успешно</div>";
    
    // Получаем последний заказ
    $args = array(
        'limit' => 1,
        'orderby' => 'date',
        'order' => 'DESC',
        'status' => array('pending', 'processing', 'completed')
    );
    
    $orders = wc_get_orders($args);
    
    if (!empty($orders)) {
        $order = $orders[0];
        echo "<div class='success'>📦 Последний заказ найден: #{$order->get_id()}</div>";
        echo "<pre>";
        echo "ID заказа: " . $order->get_id() . "\n";
        echo "Дата: " . $order->get_date_created()->format('Y-m-d H:i:s') . "\n";
        echo "Статус: " . $order->get_status() . "\n";
        echo "Покупатель: " . safe_output($order->get_billing_first_name() . ' ' . $order->get_billing_last_name()) . "\n";
        echo "Email: " . safe_output($order->get_billing_email()) . "\n";
        echo "Телефон: " . safe_output($order->get_billing_phone()) . "\n";
        echo "Сумма: " . $order->get_total() . " руб.\n";
        
        // Проверяем мета-поля TableCRM
        $tablecrm_id = $order->get_meta('_tablecrm_id');
        $tablecrm_sent = $order->get_meta('_tablecrm_sent');
        
        echo "\nTableCRM данные:\n";
        echo "- TableCRM ID: " . ($tablecrm_id ? safe_output($tablecrm_id) : 'Не установлен') . "\n";
        echo "- Отправлено в TableCRM: " . ($tablecrm_sent ? 'Да' : 'Нет') . "\n";
        echo "</pre>";
        
    } else {
        echo "<div class='error'>❌ Заказы не найдены</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Ошибка при загрузке WordPress: " . safe_output($e->getMessage()) . "</div>";
}

echo "<h2>📄 Проверка debug.log</h2>";

$debug_log_path = 'wp-content/debug.log';
if (file_exists($debug_log_path)) {
    echo "<div class='success'>✅ debug.log найден</div>";
    
    $log_content = file_get_contents($debug_log_path);
    $log_lines = explode("\n", $log_content);
    
    // Ищем записи TableCRM
    $tablecrm_logs = array();
    foreach ($log_lines as $line) {
        if (stripos($line, 'tablecrm') !== false) {
            $tablecrm_logs[] = $line;
        }
    }
    
    if (!empty($tablecrm_logs)) {
        echo "<div class='info'>📝 Найдено " . count($tablecrm_logs) . " записей TableCRM в логах</div>";
        echo "<h3>Последние 10 записей TableCRM:</h3>";
        echo "<pre>";
        $recent_logs = array_slice($tablecrm_logs, -10);
        foreach ($recent_logs as $log) {
            echo safe_output($log) . "\n";
        }
        echo "</pre>";
    } else {
        echo "<div class='error'>❌ Записи TableCRM в логах не найдены</div>";
    }
    
    // Показываем последние 20 строк общего лога
    echo "<h3>Последние 20 строк debug.log:</h3>";
    echo "<pre>";
    $recent_lines = array_slice($log_lines, -20);
    foreach ($recent_lines as $line) {
        if (trim($line)) {
            echo safe_output($line) . "\n";
        }
    }
    echo "</pre>";
    
} else {
    echo "<div class='error'>❌ debug.log не найден в wp-content/</div>";
    echo "<div class='info'>💡 Возможно, логирование отключено в wp-config.php</div>";
}

echo "<h2>⚙️ Проверка настроек плагина</h2>";

// Проверяем статус плагина
$plugin_path = 'wp-content/plugins/tablecrm-integration/tablecrm-integration.php';
if (file_exists($plugin_path)) {
    echo "<div class='success'>✅ Плагин найден</div>";
    
    // Проверяем настройки
    try {
        if (function_exists('tablecrm_get_api_key')) {
            $tablecrm_api_key = tablecrm_get_api_key();
        } else {
            $tablecrm_api_key = get_option('tablecrm_api_key', '');
        }
        $tablecrm_project_id = get_option('tablecrm_project_id', '');
        $tablecrm_endpoint = get_option('tablecrm_endpoint', '');
        
        echo "<pre>";
        echo "API Key: " . ($tablecrm_api_key ? '***установлен***' : 'НЕ УСТАНОВЛЕН') . "\n";
        echo "Project ID: " . ($tablecrm_project_id ? safe_output($tablecrm_project_id) : 'НЕ УСТАНОВЛЕН') . "\n";
        echo "Endpoint: " . ($tablecrm_endpoint ? safe_output($tablecrm_endpoint) : 'НЕ УСТАНОВЛЕН') . "\n";
        echo "</pre>";
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ Ошибка при получении настроек: " . safe_output($e->getMessage()) . "</div>";
    }
    
} else {
    echo "<div class='error'>❌ Плагин не найден</div>";
}

echo "<h2>🔧 Проверка настроек WordPress</h2>";

if (file_exists('wp-config.php')) {
    $wp_config = file_get_contents('wp-config.php');
    
    echo "<pre>";
    if (strpos($wp_config, "define('WP_DEBUG', true)") !== false) {
        echo "✅ WP_DEBUG включен\n";
    } else {
        echo "❌ WP_DEBUG отключен\n";
    }
    
    if (strpos($wp_config, "define('WP_DEBUG_LOG', true)") !== false) {
        echo "✅ WP_DEBUG_LOG включен\n";
    } else {
        echo "❌ WP_DEBUG_LOG отключен\n";
    }
    echo "</pre>";
} else {
    echo "<div class='error'>❌ wp-config.php не найден</div>";
}

echo "<hr>";
echo "<h2>📋 Рекомендации по диагностике</h2>";
echo "<ul>";
echo "<li>Если TableCRM ID не установлен - проверьте логи на наличие ошибок API</li>";
echo "<li>Если записей в логах нет - проверьте активацию плагина и настройки отладки</li>";
echo "<li>Если настройки плагина пусты - перенастройте их в админ-панели</li>";
echo "<li>Рекомендуется создать новый тестовый заказ и повторить проверку через 2-3 минуты</li>";
echo "</ul>";

echo "<div style='margin-top:20px; padding:10px; background:#e7f3ff; border-radius:5px;'>";
echo "<strong>🎯 Следующие шаги:</strong><br>";
echo "1. Убедитесь, что все настройки плагина корректны<br>";
echo "2. Создайте новый тестовый заказ<br>";
echo "3. Подождите 2-3 минуты<br>";
echo "4. Обновите эту страницу для проверки результатов";
echo "</div>";
?> 