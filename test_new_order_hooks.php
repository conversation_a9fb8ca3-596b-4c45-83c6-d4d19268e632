<?php
// Test new order hooks
echo "=== ТЕСТ ХУКОВ НОВЫХ ЗАКАЗОВ ===\n";

$wp_path = '/home/<USER>/bekflom3/mosbuketik.ru/public_html';
require_once($wp_path . '/wp-config.php');
require_once($wp_path . '/wp-load.php');

echo "WordPress загружен успешно\n\n";

// Check if WooCommerce is active
if (!class_exists('WooCommerce')) {
    echo "❌ WooCommerce не активен\n";
    exit;
}

echo "✅ WooCommerce активен\n";

// Check if our plugin is loaded
global $tablecrm_integration_complete;
if (isset($tablecrm_integration_complete)) {
    echo "✅ Плагин TableCRM загружен\n";
} else {
    echo "❌ Плагин TableCRM не загружен\n";
    exit;
}

// Check new hooks
echo "\n=== ПРОВЕРКА НОВЫХ ХУКОВ ===\n";

$hooks_to_check = array(
    'woocommerce_new_order',
    'woocommerce_checkout_order_processed',
    'woocommerce_order_status_changed'
);

foreach ($hooks_to_check as $hook_name) {
    $hooks = $GLOBALS['wp_filter'][$hook_name] ?? null;
    if ($hooks) {
        echo "✅ Хук $hook_name зарегистрирован\n";
        
        // Check if our function is in the hooks
        $found_our_hook = false;
        foreach ($hooks->callbacks as $priority => $callbacks) {
            foreach ($callbacks as $callback) {
                if (is_array($callback['function']) && 
                    isset($callback['function'][0]) && 
                    is_object($callback['function'][0]) &&
                    get_class($callback['function'][0]) === 'TableCRM_Integration_Complete') {
                    echo "  ✅ Наш хук найден с приоритетом: $priority\n";
                    $found_our_hook = true;
                    break 2;
                }
            }
        }
        
        if (!$found_our_hook) {
            echo "  ❌ Наш хук НЕ найден\n";
        }
    } else {
        echo "❌ Хук $hook_name НЕ зарегистрирован\n";
    }
}

// Test manual trigger of new order hook
echo "\n=== ТЕСТ РУЧНОГО ВЫЗОВА ХУКА НОВОГО ЗАКАЗА ===\n";
$test_order_id = 21773;
$order = wc_get_order($test_order_id);

if ($order) {
    echo "Тестируем заказ #$test_order_id\n";
    echo "Текущий статус: " . $order->get_status() . "\n";
    
    // Clear existing data
    delete_post_meta($test_order_id, '_tablecrm_complete_document_id');
    delete_post_meta($test_order_id, '_tablecrm_complete_sent_at');
    echo "Метаданные очищены\n";
    
    echo "Вызываем хук woocommerce_new_order вручную...\n";
    do_action('woocommerce_new_order', $test_order_id);
    
    // Check if it was sent
    $new_doc_id = get_post_meta($test_order_id, '_tablecrm_complete_document_id', true);
    if ($new_doc_id) {
        echo "✅ УСПЕХ! Хук нового заказа сработал, Document ID: $new_doc_id\n";
    } else {
        echo "❌ Хук нового заказа не сработал\n";
    }
} else {
    echo "❌ Заказ #$test_order_id не найден\n";
}

echo "\n=== ИНФОРМАЦИЯ О НАСТРОЙКАХ ===\n";
$enable_orders = get_option('tablecrm_complete_enable_orders', 0);
$trigger_statuses = get_option('tablecrm_complete_order_statuses', array());

echo "Отправка заказов включена: " . ($enable_orders ? 'ДА' : 'НЕТ') . "\n";
echo "Статусы для отправки: " . implode(', ', $trigger_statuses) . "\n";

echo "\n=== ТЕСТ ЗАВЕРШЕН ===\n";
?> 