<?php
// Manually test sending order to TableCRM
echo "=== РУЧНАЯ ОТПРАВКА ЗАКАЗА В TABLECRM ===\n";

$wp_path = '/home/<USER>/bekflom3/mosbuketik.ru/public_html';
require_once($wp_path . '/wp-config.php');
require_once($wp_path . '/wp-load.php');

echo "WordPress загружен успешно\n\n";

// Get latest order
if (class_exists('WooCommerce')) {
    $orders = wc_get_orders(array(
        'limit' => 1,
        'orderby' => 'date',
        'order' => 'DESC'
    ));
    
    if (!empty($orders)) {
        $order = $orders[0];
        $order_id = $order->get_id();
        
        echo "Тестируем заказ #$order_id\n";
        echo "Текущий статус: " . $order->get_status() . "\n";
        echo "Email клиента: " . $order->get_billing_email() . "\n";
        echo "Телефон клиента: " . $order->get_billing_phone() . "\n";
        echo "Сумма заказа: " . $order->get_total() . "\n";
        
        // Check order items
        $items = $order->get_items();
        echo "Товары в заказе:\n";
        foreach ($items as $item) {
            echo "  - " . $item->get_name() . " (кол-во: " . $item->get_quantity() . ")\n";
        }
        
        // Check if TableCRM plugin is available
        global $tablecrm_integration_complete;
        if (isset($tablecrm_integration_complete)) {
            echo "\n✅ Плагин TableCRM доступен\n";
            
            // Try to send order manually
            echo "Попытка отправить заказ в TableCRM...\n";
            $result = $tablecrm_integration_complete->send_order_to_tablecrm($order_id);
            
            if ($result) {
                echo "✅ УСПЕХ! Заказ отправлен в TableCRM\n";
                
                // Check if document ID was saved
                $doc_id = get_post_meta($order_id, '_tablecrm_complete_document_id', true);
                if ($doc_id) {
                    echo "📄 Document ID: $doc_id\n";
                } else {
                    echo "⚠️ Document ID не сохранен\n";
                }
            } else {
                echo "❌ ОШИБКА! Заказ не отправлен\n";
            }
        } else {
            echo "❌ Плагин TableCRM недоступен\n";
        }
        
    } else {
        echo "❌ Заказы не найдены\n";
    }
} else {
    echo "❌ WooCommerce не активен\n";
}

echo "\n=== ТЕСТ ЗАВЕРШЕН ===\n";
?> 