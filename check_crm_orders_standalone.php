<?php
/**
 * Standalone скрипт для проверки последних заказов в TableCRM через API
 * Не требует подключения к WordPress
 */

echo "🔍 Проверка последних заказов в TableCRM API\n";
echo "==========================================\n\n";

// Настройки API (замените на ваши)
$api_url = 'https://app.tablecrm.com/api/v1/';
$api_key = ''; // ВСТАВЬТЕ ВАШ API КЛЮЧ СЮДА
$organization_id = 38;

// Если API ключ не указан, просим ввести
if (empty($api_key)) {
    echo "❌ API ключ не указан!\n";
    echo "Откройте файл check_crm_orders_standalone.php и укажите ваш API ключ в переменной \$api_key\n";
    echo "Или введите API ключ сейчас: ";
    $api_key = trim(fgets(STDIN));
    
    if (empty($api_key)) {
        echo "❌ API ключ обязателен для работы скрипта!\n";
        exit(1);
    }
}

echo "📋 Настройки подключения:\n";
echo "API URL: $api_url\n";
echo "API Key: " . substr($api_key, 0, 10) . "...\n";
echo "ID Организации: $organization_id\n\n";

// Функция для выполнения API запроса
function make_api_request($endpoint, $params = array()) {
    global $api_url, $api_key;
    
    $url = rtrim($api_url, '/') . '/' . ltrim($endpoint, '/');
    
    // Добавляем токен и параметры
    $query_params = array_merge($params, array('token' => $api_key));
    $url .= '?' . http_build_query($query_params);
    
    echo "🔗 Запрос к: " . str_replace($api_key, '***', $url) . "\n";
    
    $context = stream_context_create(array(
        'http' => array(
            'method' => 'GET',
            'header' => "User-Agent: TableCRM-Checker/1.0\r\n",
            'timeout' => 30
        )
    ));
    
    $response = file_get_contents($url, false, $context);
    
    if ($response === false) {
        echo "❌ Ошибка запроса к API\n";
        return false;
    }
    
    // Получаем HTTP статус
    $status_line = $http_response_header[0] ?? '';
    preg_match('/HTTP\/\d\.\d\s+(\d+)/', $status_line, $matches);
    $status_code = $matches[1] ?? 'unknown';
    
    echo "📊 HTTP статус: $status_code\n";
    
    if ($status_code != '200') {
        echo "❌ Ошибка API: HTTP $status_code\n";
        echo "Ответ: " . substr($response, 0, 500) . "\n";
        return false;
    }
    
    $decoded = json_decode($response, true);
    if (!$decoded) {
        echo "❌ Ошибка декодирования JSON\n";
        echo "Ответ: " . substr($response, 0, 500) . "\n";
        return false;
    }
    
    return $decoded;
}

// 1. Тестируем соединение
echo "🔌 Тестирование соединения с API...\n";
echo "===================================\n";

$health_response = make_api_request('health');
if ($health_response) {
    echo "✅ Соединение с API установлено успешно!\n\n";
} else {
    echo "❌ Не удалось подключиться к API\n";
    echo "Проверьте:\n";
    echo "- Правильность API URL\n";
    echo "- Правильность API ключа\n";
    echo "- Интернет соединение\n";
    exit(1);
}

// 2. Получаем последние документы продаж
echo "📋 Получение последних документов продаж...\n";
echo "==========================================\n";

$docs_params = array(
    'limit' => 5,
    'offset' => 0,
    'sort' => 'created_at:desc'
);

$docs_response = make_api_request('docs_sales/', $docs_params);

if (!$docs_response) {
    echo "❌ Не удалось получить документы продаж\n";
    exit(1);
}

echo "✅ Получен ответ от API\n\n";

if (isset($docs_response['results']) && !empty($docs_response['results'])) {
    echo "📋 Найдено документов: " . count($docs_response['results']) . "\n\n";
    
    foreach ($docs_response['results'] as $index => $doc) {
        echo "📄 Документ #" . ($index + 1) . ":\n";
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
        echo "   🆔 ID: " . ($doc['id'] ?? 'не указан') . "\n";
        echo "   🔢 Номер: " . ($doc['number'] ?? 'не указан') . "\n";
        echo "   📅 Дата: " . (isset($doc['dated']) ? date('Y-m-d H:i:s', $doc['dated']) : 'не указана') . "\n";
        echo "   🏷️  Операция: " . ($doc['operation'] ?? 'не указана') . "\n";
        echo "   💰 Сумма: " . ($doc['paid_rubles'] ?? 'не указана') . " руб.\n";
        echo "   ✅ Статус: " . ($doc['status'] ? '✅ Оплачен' : '⏳ Не оплачен') . "\n";
        echo "   👤 Контрагент ID: " . ($doc['contragent'] ?? 'не указан') . "\n";
        echo "   🏢 Организация ID: " . ($doc['organization'] ?? 'не указана') . "\n";
        echo "   🏪 Склад ID: " . ($doc['warehouse'] ?? 'не указан') . "\n";
        echo "   💳 Касса ID: " . ($doc['paybox'] ?? 'не указана') . "\n";
        
        $comment = $doc['comment'] ?? '';
        if ($comment) {
            echo "   💬 Комментарий: " . (strlen($comment) > 100 ? substr($comment, 0, 100) . '...' : $comment) . "\n";
        }
        
        // Если есть товары
        if (isset($doc['goods']) && !empty($doc['goods'])) {
            echo "   📦 Товары (" . count($doc['goods']) . "):\n";
            foreach ($doc['goods'] as $good_index => $good) {
                echo "     🛍️  Товар #" . ($good_index + 1) . ":\n";
                echo "       - Номенклатура ID: " . ($good['nomenclature'] ?? 'не указана') . "\n";
                echo "       - Количество: " . ($good['quantity'] ?? 'не указано') . "\n";
                echo "       - Цена: " . ($good['price'] ?? 'не указана') . " руб.\n";
                echo "       - Единица измерения ID: " . ($good['unit'] ?? 'не указана') . "\n";
            }
        }
        echo "\n";
    }
    
    // Берем первый (самый последний) документ для детальной проверки
    $last_doc = $docs_response['results'][0];
    $last_doc_id = $last_doc['id'];
    $contragent_id = $last_doc['contragent'] ?? null;
    
    echo "\n🔍 ДЕТАЛЬНАЯ ПРОВЕРКА ПОСЛЕДНЕГО ДОКУМЕНТА\n";
    echo "=========================================\n";
    echo "📄 Документ ID: $last_doc_id\n\n";
    
    // Получаем полную информацию о контрагенте
    if ($contragent_id) {
        echo "👤 ИНФОРМАЦИЯ О КОНТРАГЕНТЕ (ID: $contragent_id)\n";
        echo "==============================================\n";
        
        $contragent_response = make_api_request("contragents/$contragent_id/");
        
        if ($contragent_response) {
            echo "✅ Данные контрагента получены:\n";
            echo "   👤 Имя: " . ($contragent_response['name'] ?? 'не указано') . "\n";
            echo "   📧 Email: " . ($contragent_response['email'] ?? 'не указан') . "\n";
            echo "   📞 Телефон: " . ($contragent_response['phone'] ?? 'не указан') . "\n";
            echo "   🏷️  Тип: " . ($contragent_response['type'] ?? 'не указан') . "\n";
            echo "   🔗 External ID: " . ($contragent_response['external_id'] ?? 'не указан') . "\n";
            echo "   💬 Комментарий: " . ($contragent_response['comment'] ?? 'нет') . "\n";
            
            // Проверяем, не является ли это контрагентом "Айгуль"
            $name = $contragent_response['name'] ?? '';
            if (stripos($name, 'Айгуль') !== false || stripos($name, 'Aigul') !== false) {
                echo "\n🚨🚨🚨 НАЙДЕН КОНТРАГЕНТ С ИМЕНЕМ 'АЙГУЛЬ'! 🚨🚨🚨\n";
                echo "🎯 ЭТО ИСТОЧНИК ПРОБЛЕМЫ!\n";
                echo "💡 Контрагент с ID $contragent_id имеет имя: '$name'\n";
                echo "🔧 РЕШЕНИЕ: Измените имя этого контрагента в TableCRM\n";
            } else {
                echo "✅ Имя контрагента не содержит 'Айгуль'\n";
            }
        } else {
            echo "❌ Не удалось получить данные контрагента\n";
        }
    } else {
        echo "⚠️  Контрагент не указан в документе\n";
    }
    
    // Получаем информацию о номенклатурах
    if (isset($last_doc['goods']) && !empty($last_doc['goods'])) {
        echo "\n📦 ТОВАРЫ В ДОКУМЕНТЕ\n";
        echo "====================\n";
        
        foreach ($last_doc['goods'] as $index => $good) {
            $nomenclature_id = $good['nomenclature'] ?? null;
            if ($nomenclature_id) {
                echo "\n🛍️  Товар #" . ($index + 1) . " (Номенклатура ID: $nomenclature_id):\n";
                
                $nomenclature_response = make_api_request("nomenclature/$nomenclature_id/");
                
                if ($nomenclature_response) {
                    echo "   📋 Название: " . ($nomenclature_response['name'] ?? 'не указано') . "\n";
                    echo "   🔢 Код: " . ($nomenclature_response['code'] ?? 'не указан') . "\n";
                    echo "   🔗 External ID: " . ($nomenclature_response['external_id'] ?? 'не указан') . "\n";
                    echo "   ✅ Активен: " . ($nomenclature_response['is_active'] ? 'Да' : 'Нет') . "\n";
                } else {
                    echo "   ❌ Не удалось получить данные номенклатуры\n";
                }
            }
        }
    }
    
} else {
    echo "❌ Документы продаж не найдены\n";
    echo "💡 Возможные причины:\n";
    echo "   - Нет документов в выбранной организации\n";
    echo "   - Неправильный API ключ\n";
    echo "   - Неправильные права доступа\n";
}

// 3. Поиск всех контрагентов с именем "Айгуль"
echo "\n\n🔍 ПОИСК КОНТРАГЕНТОВ С ИМЕНЕМ 'АЙГУЛЬ'\n";
echo "=====================================\n";

$search_params = array(
    'limit' => 100,
    'offset' => 0,
    'sort' => 'created_at:desc'
);

$contragents_response = make_api_request('contragents/', $search_params);

if ($contragents_response && isset($contragents_response['results'])) {
    $found_aigul = array();
    
    foreach ($contragents_response['results'] as $contragent) {
        $name = $contragent['name'] ?? '';
        if (stripos($name, 'Айгуль') !== false || stripos($name, 'Aigul') !== false) {
            $found_aigul[] = $contragent;
        }
    }
    
    if (!empty($found_aigul)) {
        echo "🚨 НАЙДЕНО " . count($found_aigul) . " КОНТРАГЕНТОВ С ИМЕНЕМ 'АЙГУЛЬ':\n";
        echo "================================================\n";
        
        foreach ($found_aigul as $index => $contragent) {
            echo "\n👤 Контрагент #" . ($index + 1) . ":\n";
            echo "   🆔 ID: " . ($contragent['id'] ?? 'не указан') . "\n";
            echo "   👤 Имя: " . ($contragent['name'] ?? 'не указано') . "\n";
            echo "   📧 Email: " . ($contragent['email'] ?? 'не указан') . "\n";
            echo "   📞 Телефон: " . ($contragent['phone'] ?? 'не указан') . "\n";
            echo "   🔗 External ID: " . ($contragent['external_id'] ?? 'не указан') . "\n";
            echo "   📅 Создан: " . ($contragent['created_at'] ?? 'не указано') . "\n";
        }
        
        echo "\n🔧 РЕШЕНИЕ ПРОБЛЕМЫ:\n";
        echo "===================\n";
        echo "1. Войдите в TableCRM\n";
        echo "2. Перейдите в раздел 'Контрагенты'\n";
        echo "3. Найдите контрагентов с именем 'Айгуль'\n";
        echo "4. Удалите их или измените имена на правильные\n";
        echo "5. Создайте новый тестовый заказ в WooCommerce\n";
        
    } else {
        echo "✅ Контрагенты с именем 'Айгуль' не найдены в последних 100 записях\n";
        echo "💡 Возможно, проблема в другом:\n";
        echo "   - Проверьте тестовые заказы в WooCommerce\n";
        echo "   - Проверьте пользователей WordPress\n";
        echo "   - Проверьте логи плагина\n";
    }
} else {
    echo "❌ Не удалось получить список контрагентов\n";
}

echo "\n\n✅ ПРОВЕРКА ЗАВЕРШЕНА!\n";
echo "======================\n";
echo "🕒 Время: " . date('Y-m-d H:i:s') . "\n";
echo "📝 Сохраните результаты для анализа\n";
?> 