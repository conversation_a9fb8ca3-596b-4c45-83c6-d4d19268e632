<?php
/**
 * Тестирование совместимости TableCRM Integration с HPOS
 * Запуск: php test_hpos_compatibility.php
 */

// Подключение WordPress
require_once('./wp-config.php');
require_once('./wp-load.php');

echo "🔍 Тестирование совместимости TableCRM Integration с HPOS\n";
echo "======================================================\n\n";

// Проверка WooCommerce
if (!class_exists('WooCommerce')) {
    echo "❌ WooCommerce не активен!\n";
    exit(1);
}

echo "✅ WooCommerce активен. Версия: " . WC()->version . "\n";

// Проверка наличия HPOS
$hpos_available = false;
$hpos_enabled = false;

try {
    $controller = wc_get_container()->get(\Automattic\WooCommerce\Internal\DataStores\Orders\CustomOrdersTableController::class);
    $hpos_available = true;
    $hpos_enabled = $controller->custom_orders_table_usage_is_enabled();
} catch (Exception $e) {
    echo "⚠️ HPOS недоступен: " . $e->getMessage() . "\n";
}

if ($hpos_available) {
    echo "✅ HPOS доступен\n";
    echo "📊 HPOS статус: " . ($hpos_enabled ? "✅ ВКЛЮЧЕН" : "⚠️ ОТКЛЮЧЕН") . "\n";
} else {
    echo "❌ HPOS недоступен (требуется WooCommerce 7.1+)\n";
}

// Проверка TableCRM Integration плагина
$plugin_active = false;
$tablecrm_instance = null;

if (class_exists('TableCRM_Integration')) {
    $plugin_active = true;
    echo "✅ TableCRM Integration плагин активен\n";
    
    // Создаем экземпляр для тестирования
    $tablecrm_instance = new TableCRM_Integration();
    
    // Проверяем версию плагина
    if (defined('TABLECRM_VERSION')) {
        echo "📋 Версия плагина: " . TABLECRM_VERSION . "\n";
        
        if (version_compare(TABLECRM_VERSION, '1.1.0', '>=')) {
            echo "✅ Плагин поддерживает HPOS\n";
        } else {
            echo "⚠️ Плагин требует обновления для поддержки HPOS\n";
        }
    }
} else {
    echo "❌ TableCRM Integration плагин не активен\n";
    exit(1);
}

echo "\n";

// Тестирование методов HPOS
if ($plugin_active && $tablecrm_instance) {
    echo "🧪 ТЕСТИРОВАНИЕ МЕТОДОВ HPOS\n";
    echo "============================\n";
    
    // Принудительно вызываем инициализацию HPOS
    if (method_exists($tablecrm_instance, 'init_hpos_support')) {
        $tablecrm_instance->init_hpos_support();
        echo "✅ Инициализация HPOS выполнена\n";
    }
    
    // Проверяем последние заказы
    echo "\n📦 ТЕСТИРОВАНИЕ ПОЛУЧЕНИЯ ЗАКАЗОВ:\n";
    echo "==================================\n";
    
    $args = array(
        'limit' => 5,
        'orderby' => 'date',
        'order' => 'DESC',
        'status' => array('completed', 'processing', 'pending')
    );
    
    // Тестируем стандартный метод
    $start_time = microtime(true);
    $orders_standard = wc_get_orders($args);
    $time_standard = microtime(true) - $start_time;
    
    echo "📊 Стандартный метод wc_get_orders():\n";
    echo "   Найдено заказов: " . count($orders_standard) . "\n";
    echo "   Время выполнения: " . round($time_standard * 1000, 2) . " мс\n";
    
    // Тестируем оптимизированный метод (если доступен)
    if (method_exists($tablecrm_instance, 'get_orders_optimized')) {
        $reflection = new ReflectionClass($tablecrm_instance);
        $method = $reflection->getMethod('get_orders_optimized');
        $method->setAccessible(true);
        
        $start_time = microtime(true);
        $orders_optimized = $method->invoke($tablecrm_instance, $args);
        $time_optimized = microtime(true) - $start_time;
        
        echo "🚀 Оптимизированный метод get_orders_optimized():\n";
        echo "   Найдено заказов: " . count($orders_optimized) . "\n";
        echo "   Время выполнения: " . round($time_optimized * 1000, 2) . " мс\n";
        
        if ($hpos_enabled && $time_optimized < $time_standard) {
            $improvement = round((($time_standard - $time_optimized) / $time_standard) * 100, 1);
            echo "   🎯 Улучшение производительности: +" . $improvement . "%\n";
        }
    }
    
    // Тестирование работы с мета-данными
    if (!empty($orders_standard)) {
        echo "\n🏷️ ТЕСТИРОВАНИЕ МЕТА-ДАННЫХ:\n";
        echo "============================\n";
        
        $test_order = $orders_standard[0];
        echo "🛒 Тестируем заказ #" . $test_order->get_id() . "\n";
        
        // Проверяем, есть ли метод get_order_meta
        if (method_exists($tablecrm_instance, 'get_order_meta')) {
            $reflection = new ReflectionClass($tablecrm_instance);
            $method = $reflection->getMethod('get_order_meta');
            $method->setAccessible(true);
            
            // Тестируем получение различных мета-данных
            $meta_keys = array(
                '_tablecrm_document_id',
                '_billing_email',
                '_utm_source',
                '_delivery_date'
            );
            
            foreach ($meta_keys as $meta_key) {
                $start_time = microtime(true);
                $meta_value = $method->invoke($tablecrm_instance, $test_order, $meta_key);
                $time_meta = microtime(true) - $start_time;
                
                $value_preview = !empty($meta_value) ? 
                    (strlen($meta_value) > 30 ? substr($meta_value, 0, 30) . '...' : $meta_value) : 
                    'пусто';
                
                echo "   📝 $meta_key: $value_preview (" . round($time_meta * 1000, 2) . " мс)\n";
            }
        }
        
        // Тестирование update_order_meta
        if (method_exists($tablecrm_instance, 'update_order_meta')) {
            echo "\n💾 ТЕСТИРОВАНИЕ СОХРАНЕНИЯ МЕТА-ДАННЫХ:\n";
            echo "=======================================\n";
            
            $reflection = new ReflectionClass($tablecrm_instance);
            $method = $reflection->getMethod('update_order_meta');
            $method->setAccessible(true);
            
            $test_key = '_tablecrm_hpos_test';
            $test_value = 'HPOS test ' . date('Y-m-d H:i:s');
            
            $start_time = microtime(true);
            $result = $method->invoke($tablecrm_instance, $test_order, $test_key, $test_value);
            $time_update = microtime(true) - $start_time;
            
            echo "   📝 Тестовое сохранение: " . ($result ? '✅ успешно' : '❌ ошибка') . "\n";
            echo "   ⏱️ Время сохранения: " . round($time_update * 1000, 2) . " мс\n";
            
            // Проверяем, что значение сохранилось
            $get_method = $reflection->getMethod('get_order_meta');
            $get_method->setAccessible(true);
            $saved_value = $get_method->invoke($tablecrm_instance, $test_order, $test_key);
            
            if ($saved_value === $test_value) {
                echo "   ✅ Значение корректно сохранено и считано\n";
                
                // Удаляем тестовое значение
                $test_order->delete_meta_data($test_key);
                $test_order->save();
                echo "   🗑️ Тестовое значение удалено\n";
            } else {
                echo "   ❌ Ошибка сохранения или чтения значения\n";
            }
        }
    }
}

// Общий отчет
echo "\n📊 ИТОГОВЫЙ ОТЧЕТ\n";
echo "================\n";

if ($hpos_available) {
    if ($hpos_enabled) {
        echo "🚀 HPOS активен и поддерживается плагином\n";
        echo "   ✅ Ожидается улучшение производительности до 30%\n";
        echo "   ✅ Оптимизированные запросы к базе данных\n";
    } else {
        echo "⚠️ HPOS доступен, но отключен\n";
        echo "   💡 Рекомендуется включить HPOS для улучшения производительности\n";
        echo "   📋 Плагин работает в режиме совместимости\n";
    }
} else {
    echo "📋 HPOS недоступен\n";
    echo "   ✅ Плагин работает в стандартном режиме\n";
    echo "   💡 Рекомендуется обновить WooCommerce до версии 7.1+\n";
}

if ($plugin_active && defined('TABLECRM_VERSION')) {
    if (version_compare(TABLECRM_VERSION, '1.1.0', '>=')) {
        echo "✅ Плагин TableCRM Integration готов к работе с HPOS\n";
    } else {
        echo "⚠️ Требуется обновление плагина до версии 1.1.0+\n";
    }
}

echo "\n🎯 РЕКОМЕНДАЦИИ:\n";
echo "================\n";

if (!$hpos_enabled && $hpos_available) {
    echo "1. Включите HPOS в настройках WooCommerce для улучшения производительности\n";
}

if ($plugin_active && version_compare(TABLECRM_VERSION, '1.1.0', '<')) {
    echo "2. Обновите плагин TableCRM Integration до версии 1.1.0\n";
}

if ($hpos_enabled) {
    echo "3. Проверьте логи WordPress на наличие записей о режиме HPOS\n";
    echo "4. Создайте тестовый заказ для проверки корректности работы\n";
}

echo "\n✅ Тестирование завершено!\n";
?> 