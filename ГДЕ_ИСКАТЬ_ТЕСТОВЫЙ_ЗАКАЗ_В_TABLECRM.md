# 🔍 ГДЕ ИСКАТЬ ТЕСТОВЫЙ ЗАКАЗ В TableCRM

## 📍 Текущее местоположение
Вы сейчас находитесь в разделе **"Платежи"** - это НЕ то место, где нужно искать заказы.

## ✅ ПРАВИЛЬНОЕ МЕСТО ПОИСКА

### 1️⃣ Перейдите в раздел "Продажи"
- В левом меню кликните на **"Продажи"** (с иконкой стрелки вниз)
- Выберите **"Продажи"** из выпадающего меню

### 2️⃣ Что искать в документах продаж:

#### 🔎 **Критерии поиска тестового заказа:**

| Поле | Что искать |
|------|------------|
| **Название операции** | "Заказ с сайта" или просто "Заказ" |
| **Дата операции** | Сегодняшняя дата (05.06.2025) |
| **Сумма** | 5500 руб. (из наших тестов) |
| **Контрагент** | Может быть "Тест Тестов" или ID контрагента |
| **Комментарий** | Содержит данные заказа и клиента |

### 3️⃣ **Фильтры для поиска:**

1. **По дате**: Установите сегодняшнюю дату (05.06.2025)
2. **По сумме**: Найдите операции на 5500 руб.
3. **По типу**: "Приход" (как на вашем скриншоте)

## 📋 ПРИЗНАКИ ТЕСТОВОГО ЗАКАЗА

### В комментарии к документу будет:
```
Заказ #[номер заказа]
Дата: 2025-06-05

КЛИЕНТ:
Имя: Тест Тестов
Email: <EMAIL>
Телефон: +7(999)123-45-67

ДОСТАВКА:
Получатель: Получатель Тестовый
Адрес: ул. Доставки, д. 456, Санкт-Петербург
Телефон: +7(999)987-65-43

ОПЛАТА:
Способ: Банковский перевод
Статус: Не оплачено

UTM МЕТКИ:
utm_source: google
utm_medium: cpc
utm_campaign: test_campaign
```

### В товарах будут:
- **Товар 1**: Тестовый товар 1 (TEST-001), количество 2, цена 1500 руб.
- **Товар 2**: Тестовый товар 2 (TEST-002), количество 1, цена 2500 руб.

## 🚀 АЛЬТЕРНАТИВНЫЕ СПОСОБЫ ПОИСКА

### 1️⃣ Поиск по API (для проверки)
Если заказ не виден в интерфейсе, проверьте через API:
```
GET https://app.tablecrm.com/api/v1/docs_sales/
Authorization: Bearer af1874616430e04cfd4bce30035789907e899fc7c3a1a4bb27254828ff304a77
```

### 2️⃣ Проверка последних документов
В разделе "Продажи" → "Продажи" отсортируйте по дате (новые сверху) и найдите документы за сегодня.

### 3️⃣ Фильтр по организации
Убедитесь, что выбрана правильная организация (ID: 38).

## ⚠️ ЕСЛИ ЗАКАЗ НЕ НАЙДЕН

### Возможные причины:
1. **Неправильный раздел** - убедитесь, что вы в "Продажи" → "Продажи"
2. **Неправильная организация** - проверьте ID организации (должно быть 38)
3. **Ошибка отправки** - проверьте логи WordPress
4. **Фильтры** - сбросьте все фильтры и посмотрите все документы

### Что проверить:
1. Логи WordPress: `wp-content/debug.log`
2. Статус плагина: должен быть активен
3. API токен: правильность настроек

## 🎯 БЫСТРАЯ ПРОВЕРКА

Если заказ был отправлен нашими тестами, то в разделе **"Продажи" → "Продажи"** должен быть документ:
- **Дата**: 05.06.2025
- **Сумма**: 5500.00 руб.
- **Тип**: Приход
- **Статус**: Не проведен (серый переключатель)

## 📞 КОНТАКТЫ ДЛЯ ПОДДЕРЖКИ

Если заказ так и не найден, возможно потребуется:
1. Проверить настройки API в плагине
2. Создать реальный тестовый заказ в WooCommerce
3. Проанализировать логи интеграции 