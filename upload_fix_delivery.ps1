# Upload and run delivery API fix
$localFile = "fix_delivery_api.php"
$remoteFile = "/home/<USER>/bekflom3/mosbuketik.ru/public_html/fix_delivery_api.php"
$server = "bekflom3.beget.tech"
$username = "bekflom3"

Write-Host "=== DELIVERY API FIX ===" -ForegroundColor Green

# Upload file
Write-Host "Uploading file..." -ForegroundColor Yellow
scp $localFile "${username}@${server}:${remoteFile}"

if ($LASTEXITCODE -eq 0) {
    Write-Host "File uploaded successfully" -ForegroundColor Green
    
    # Run diagnostic
    Write-Host "Running diagnostic..." -ForegroundColor Yellow
    ssh "${username}@${server}" "cd /home/<USER>/bekflom3/mosbuketik.ru/public_html; php fix_delivery_api.php"
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Diagnostic completed successfully" -ForegroundColor Green
    } else {
        Write-Host "Error running diagnostic" -ForegroundColor Red
    }
} else {
    Write-Host "Error uploading file" -ForegroundColor Red
}

Write-Host "=== COMPLETED ===" -ForegroundColor Green 