# 🔧 КРИТИЧЕСКИЕ ИСПРАВЛЕНИЯ API v1.0.21

## 📋 Анализ проблем из логов

### ❌ Найденные ошибки:

1. **Ошибка URL номенклатуры**: `?sku=product_id_20092?token=...` (два знака вопроса)
2. **Ошибка типа параметра**: `Argument #4 ($type) must be of type string, array given`
3. **422 ошибка**: `nomenclature field required` - API требует поле nomenclature
4. **Неправильные API запросы**: использование `make_api_request` вместо прямых HTTP запросов

## ✅ ИСПРАВЛЕНИЯ в v1.0.21:

### 1. Функция `find_nomenclature_by_sku()`
**ДО:**
```php
$response = $this->make_api_request("nomenclatures/?sku=" . urlencode($sku), array(), 'GET');
```

**ПОСЛЕ:**
```php
$search_url = rtrim($api_url, '/') . '/nomenclatures/?token=' . $api_key . '&sku=' . urlencode($sku);
$response = wp_remote_get($search_url, array(
    'timeout' => 30,
    'sslverify' => true,
    'headers' => array(
        'User-Agent' => 'WordPress-TableCRM-Integration-v3/1.0.21'
    )
));
```

### 2. Функция `create_nomenclature()`
**ДО:**
```php
$response = $this->make_api_request("nomenclatures/", [$product_data], 'POST');
```

**ПОСЛЕ:**
```php
$create_url = rtrim($api_url, '/') . '/nomenclatures/?token=' . $api_key;
$response = wp_remote_post($create_url, array(
    'timeout' => 30,
    'sslverify' => true,
    'headers' => array(
        'Content-Type' => 'application/json; charset=utf-8',
        'User-Agent' => 'WordPress-TableCRM-Integration-v3/1.0.21'
    ),
    'body' => json_encode(array($product_data), JSON_UNESCAPED_UNICODE)
));
```

### 3. Добавлено поле `discount` в товары
```php
$goods[] = array(
    'name'           => $item['name'],
    'price'          => floatval($item['unit_price']),
    'quantity'       => intval($item['quantity']),
    'unit'           => $default_unit_id,
    'discount'       => 0, // ДОБАВЛЕНО для совместимости с API
    'sum_discounted' => floatval($item['price']),
    'nomenclature'   => intval($nomenclature_id), // ДИНАМИЧЕСКИ СОЗДАННАЯ НОМЕНКЛАТУРА
    'sku'            => $search_key,
);
```

### 4. Улучшена обработка ответов API
- Добавлена поддержка разных форматов ответов (`results` массив или прямой массив)
- Улучшено логирование HTTP статусов и ответов
- Добавлена обработка статусов 200 и 201 для создания номенклатуры

## 🎯 ОЖИДАЕМЫЙ РЕЗУЛЬТАТ:

1. **URL корректные**: `https://app.tablecrm.com/api/v1/nomenclatures/?token=xxx&sku=product_id_20092`
2. **Нет ошибок типов**: все параметры передаются правильно
3. **Номенклатура создается**: динамически для каждого уникального товара
4. **Названия товаров**: отображаются корректно в TableCRM вместо "Testing"

## 📦 УСТАНОВКА:

1. Скачайте `tablecrm-integration-final-v1.0.21-fixed.zip`
2. Деактивируйте текущий плагин
3. Удалите старую версию
4. Установите новую версию v1.0.21
5. Активируйте плагин
6. Создайте тестовый заказ для проверки

## 🔍 ПРОВЕРКА РЕЗУЛЬТАТА:

После установки:
1. Создайте тестовый заказ с товарами
2. Проверьте логи на отсутствие ошибок 422
3. Проверьте TableCRM - товары должны отображаться с правильными названиями
4. Убедитесь, что номенклатура создается автоматически

---
**Версия:** 1.0.21  
**Дата:** 05.06.2025  
**Статус:** КРИТИЧЕСКИЕ ОШИБКИ API ИСПРАВЛЕНЫ 