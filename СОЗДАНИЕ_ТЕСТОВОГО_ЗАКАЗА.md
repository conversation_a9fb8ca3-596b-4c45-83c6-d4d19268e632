# 🛒 СОЗДАНИЕ ТЕСТОВОГО ЗАКАЗА

## 🎯 **ЦЕЛЬ: Проверить работу интеграции с TableCRM**

---

## 📋 **ТЕСТОВЫЕ ДАННЫЕ ДЛЯ ЗАКАЗА:**

### **👤 Данные покупателя:**
- **Имя:** Тестовый Покупатель 10589
- **Email:** <EMAIL>
- **Телефон:** +7 (999) 123-10-58

### **📍 Адрес доставки:**
- **Адрес:** ул. Тестовая, д. 10589
- **Город:** Москва
- **Индекс:** 123456
- **Страна:** Россия

---

## 🚀 **СПОСОБ 1: РУЧНОЕ СОЗДАНИЕ ЧЕРЕЗ САЙТ (РЕКОМЕНДУЕТСЯ)**

### **1️⃣ Перейдите на сайт:**
```
https://mosbuketik.ru
```

### **2️⃣ Добавьте товар в корзину:**
- Выберите любой товар на сайте
- Нажмите "Добавить в корзину"
- Перейдите в корзину

### **3️⃣ Оформите заказ:**
- Нажмите "Оформить заказ"
- Заполните форму тестовыми данными:

```
Имя: Тестовый
Фамилия: Покупатель 10589
Email: <EMAIL>
Телефон: +7 (999) 123-10-58
Адрес: ул. Тестовая, д. 10589
Город: Москва
Индекс: 123456
```

### **4️⃣ Завершите заказ:**
- Выберите способ оплаты (любой)
- Нажмите "Разместить заказ"

---

## 🔧 **СПОСОБ 2: СОЗДАНИЕ ЧЕРЕЗ АДМИН-ПАНЕЛЬ**

### **1️⃣ Войдите в админ-панель:**
```
https://mosbuketik.ru/wp-admin
```

### **2️⃣ Перейдите к заказам:**
- WooCommerce → Заказы
- Нажмите "Добавить новый"

### **3️⃣ Заполните данные заказа:**
- **Данные покупателя:** используйте тестовые данные выше
- **Товары:** добавьте любой товар
- **Статус:** установите "В ожидании оплаты"

### **4️⃣ Сохраните заказ:**
- Нажмите "Сохранить заказ"

---

## 📊 **ПРОВЕРКА РЕЗУЛЬТАТА**

### **1️⃣ Проверьте создание заказа:**
```
https://mosbuketik.ru/wp-admin/edit.php?post_type=shop_order
```
- Найдите созданный заказ
- Запомните его ID

### **2️⃣ Проверьте логи WordPress:**

**Через SFTP:**
```bash
sftp <EMAIL>
cd public_html/wp-content
ls -la debug.log
get debug.log
quit

# Локально:
grep -i "tablecrm" debug.log | tail -20
```

**Или запустите скрипт:**
```bash
./logs_manual_check.sh
```

### **3️⃣ Проверьте TableCRM:**
```
https://app.tablecrm.com
```
- Войдите в систему
- Проверьте появление нового лида
- Email должен быть: <EMAIL>

---

## 📋 **ЧТО ДОЛЖНО ПОЯВИТЬСЯ В ЛОГАХ:**

### **✅ Успешная отправка:**
```
[TableCRM Integration] [INFO] === НАЧАЛО ОТПРАВКИ ЗАКАЗА XXXX В TABLECRM ===
[TableCRM Integration] [INFO] Заказ XXXX найден. Статус: pending
[TableCRM Integration] [SUCCESS] Статус заказа wc-pending разрешен для отправки
[TableCRM Integration] [INFO] Подготовлены данные заказа XXXX:
[TableCRM Integration] [DEBUG] Имя: Тестовый Покупатель 10589
[TableCRM Integration] [DEBUG] Email: <EMAIL>
[TableCRM Integration] [DEBUG] Телефон: +7 (999) 123-10-58
[TableCRM Integration] [INFO] Отправляем заказ XXXX в TableCRM API...
[TableCRM Integration] [SUCCESS] Заказ XXXX успешно отправлен в TableCRM. Lead ID: abc123
[TableCRM Integration] [INFO] === ЗАВЕРШЕНИЕ ОТПРАВКИ ЗАКАЗА XXXX (УСПЕШНО) ===
```

### **❌ Возможные ошибки:**
```
[TableCRM Integration] [WARNING] Отправка заказов отключена в настройках плагина
[TableCRM Integration] [ERROR] Не настроены обязательные параметры API (URL и Key)
[TableCRM Integration] [WARNING] Заказ XXXX не отправлен - статус wc-cancelled не выбран
```

---

## 🔧 **ЕСЛИ ЛОГОВ НЕТ:**

### **1️⃣ Включите логирование WordPress:**
Следуйте инструкции: `ВКЛЮЧЕНИЕ_ЛОГИРОВАНИЯ_WORDPRESS.md`

### **2️⃣ Проверьте активность плагина:**
```
https://mosbuketik.ru/wp-admin/plugins.php
```
- Найдите "TableCRM Integration"
- Убедитесь что он активен

### **3️⃣ Проверьте настройки плагина:**
```
https://mosbuketik.ru/wp-admin/options-general.php?page=tablecrm-settings
```
- **API URL:** `https://app.tablecrm.com/api/v1/`
- **API Key:** `af1874616430e04cfd4bce30035789907e899fc7c3a1a4bb27254828ff304a77`
- **Отправлять заказы WooCommerce:** ✅ Включено
- **Режим отладки:** ✅ Включено

---

## 🚨 **ДИАГНОСТИКА ПРОБЛЕМ**

### **Проблема: Заказ создался, но нет логов TableCRM**

**Возможные причины:**
1. ❌ Плагин неактивен
2. ❌ Логирование WordPress отключено
3. ❌ Неверные настройки API
4. ❌ Статус заказа не выбран для отправки

**Решение:**
1. ✅ Активируйте плагин
2. ✅ Включите WP_DEBUG в wp-config.php
3. ✅ Проверьте настройки API
4. ✅ Убедитесь что статус "pending" выбран

### **Проблема: Логи есть, но ошибки API**

**Возможные причины:**
1. ❌ Неверный API ключ
2. ❌ Неверный URL API
3. ❌ Проблемы с сетью

**Решение:**
1. ✅ Проверьте API ключ в настройках
2. ✅ Убедитесь что URL: `https://app.tablecrm.com/api/v1/`
3. ✅ Нажмите "Тестировать соединение"

---

## 📞 **АЛЬТЕРНАТИВНЫЕ СПОСОБЫ ТЕСТИРОВАНИЯ**

### **1️⃣ Создание через WooCommerce REST API:**
```bash
curl -X POST https://mosbuketik.ru/wp-json/wc/v3/orders \
  -u consumer_key:consumer_secret \
  -H "Content-Type: application/json" \
  -d '{
    "billing": {
      "first_name": "Тестовый",
      "last_name": "Покупатель",
      "email": "<EMAIL>",
      "phone": "+7 (999) 123-10-58"
    }
  }'
```

### **2️⃣ Создание через Contact Form 7:**
- Найдите форму обратной связи на сайте
- Заполните тестовыми данными
- Отправьте форму

---

## ✅ **ОЖИДАЕМЫЙ РЕЗУЛЬТАТ**

После создания тестового заказа:

1. **✅ В WooCommerce:** Появится новый заказ с ID
2. **✅ В логах:** Подробная информация о процессе отправки
3. **✅ В TableCRM:** Новый лид с данными:
   - Email: <EMAIL>
   - Телефон: +7 (999) 123-10-58
   - Источник: WooCommerce Order

---

## 🎯 **БЫСТРАЯ ПРОВЕРКА (5 МИНУТ)**

1. **Создать заказ** на сайте с тестовыми данными
2. **Проверить админ-панель** WooCommerce
3. **Скачать debug.log** и найти записи TableCRM
4. **Проверить TableCRM** на наличие нового лида

**🎉 Готово! Интеграция протестирована!** 