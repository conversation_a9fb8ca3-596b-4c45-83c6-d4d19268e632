# Download fresh logs from server
$ftpServer = "ftp://tablecrm.com"
$username = "tablecrm_ruslan"
$password = "Ruslan2024!"

# Create directory for fresh logs
$logDir = "server_logs_fresh"
if (!(Test-Path $logDir)) {
    New-Item -ItemType Directory -Path $logDir
}

Write-Host "Downloading fresh logs..." -ForegroundColor Green

# Download tablecrm_complete.log
try {
    $webclient = New-Object System.Net.WebClient
    $webclient.Credentials = New-Object System.Net.NetworkCredential($username, $password)
    $webclient.DownloadFile("$ftpServer/wp-content/tablecrm_complete.log", "$logDir\tablecrm_complete_fresh.log")
    Write-Host "Downloaded tablecrm_complete.log" -ForegroundColor Green
} catch {
    Write-Host "Error downloading tablecrm_complete.log: $($_.Exception.Message)" -ForegroundColor Red
}

# Download debug.log
try {
    $webclient = New-Object System.Net.WebClient
    $webclient.Credentials = New-Object System.Net.NetworkCredential($username, $password)
    $webclient.DownloadFile("$ftpServer/wp-content/debug.log", "$logDir\debug_fresh.log")
    Write-Host "Downloaded debug.log" -ForegroundColor Green
} catch {
    Write-Host "Error downloading debug.log: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "Log download completed!" -ForegroundColor Green 