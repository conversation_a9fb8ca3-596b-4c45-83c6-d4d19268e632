# Upload and run order data check
$server = "bekflom3.beget.tech"
$user = "bekflom3_wz"
$pass = "zl2Shj!e&6ng"

Write-Host "Uploading order data check script..." -ForegroundColor Green

try {
    $uri = "ftp://$server/public_html/check_order_data.php"
    $webclient = New-Object System.Net.WebClient
    $webclient.Credentials = New-Object System.Net.NetworkCredential($user, $pass)
    $webclient.UploadFile($uri, "check_order_data.php")
    Write-Host "Order data check script uploaded!" -ForegroundColor Green
    
    # Run the check
    Write-Host "Running order data check..." -ForegroundColor Yellow
    $result = Invoke-WebRequest -Uri "https://mosbuketik.ru/check_order_data.php" -UseBasicParsing
    Write-Host $result.Content -ForegroundColor White
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

$webclient.Dispose()
Write-Host "Order data check completed!" -ForegroundColor Green 