Write-Host "📥 СКАЧИВАЮ ЛОГИ С СЕРВЕРА..." -ForegroundColor Green

# Создаем папку
if (!(Test-Path "server_logs_latest")) {
    New-Item -ItemType Directory -Path "server_logs_latest"
}

# Параметры FTP
$server = "bekflom3.beget.tech"
$user = "bekflom3_wz"
$pass = 'zl2Shj!e&6ng'

try {
    # Скачиваем debug.log
    Write-Host "Скачиваю debug.log..." -ForegroundColor Yellow
    $uri1 = "ftp://$server/public_html/wp-content/debug.log"
    $webclient = New-Object System.Net.WebClient
    $webclient.Credentials = New-Object System.Net.NetworkCredential($user, $pass)
    $webclient.DownloadFile($uri1, "server_logs_latest/debug_latest.log")
    Write-Host "✅ debug.log скачан" -ForegroundColor Green
    
    # Скачиваем tablecrm_complete.log
    Write-Host "Скачиваю tablecrm_complete.log..." -ForegroundColor Yellow
    $uri2 = "ftp://$server/public_html/wp-content/tablecrm_complete.log"
    $webclient.DownloadFile($uri2, "server_logs_latest/tablecrm_complete_latest.log")
    Write-Host "✅ tablecrm_complete.log скачан" -ForegroundColor Green
    
    $webclient.Dispose()
}
catch {
    Write-Host "❌ Ошибка: $($_.Exception.Message)" -ForegroundColor Red
}

# Показываем последние записи
Write-Host ""
Write-Host "📋 ПОСЛЕДНИЕ ЗАПИСИ В ЛОГАХ:" -ForegroundColor Cyan

if (Test-Path "server_logs_latest/debug_latest.log") {
    Write-Host ""
    Write-Host "=== DEBUG.LOG (последние 15 строк) ===" -ForegroundColor Yellow
    Get-Content "server_logs_latest/debug_latest.log" | Select-Object -Last 15
}

if (Test-Path "server_logs_latest/tablecrm_complete_latest.log") {
    Write-Host ""
    Write-Host "=== TABLECRM_COMPLETE.LOG (последние 15 строк) ===" -ForegroundColor Yellow
    Get-Content "server_logs_latest/tablecrm_complete_latest.log" | Select-Object -Last 15
}

Write-Host ""
Write-Host "✅ ГОТОВО!" -ForegroundColor Green 