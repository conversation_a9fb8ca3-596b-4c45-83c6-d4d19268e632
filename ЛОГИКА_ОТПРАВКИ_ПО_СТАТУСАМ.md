# 📊 Логика отправки заявок по статусам заказов

## 🎯 Как работает отправка заказов в TableCRM

### ✅ **Заказы отправляются в следующих случаях:**

#### 1. **При создании нового заказа** (`woocommerce_new_order`)
- ✅ **Проверяется статус заказа** против настроек администратора
- ✅ **По умолчанию отправляются статусы:** `pending`, `processing`, `completed`
- ✅ **Можно настроить** любые статусы через админ-панель

#### 2. **При изменении статуса заказа**
- ✅ `processing` - В обработке
- ✅ `completed` - Выполнен  
- ✅ `cancelled` - Отменен
- ✅ `refunded` - Возврат

### 🔧 **Настройка статусов для отправки**

#### Местоположение настроек:
**WordPress Admin → Настройки → TableCRM Integration → "Статусы заказов для отправки"**

#### Доступные статусы WooCommerce:
- ✅ `Ожидает оплаты` (pending)
- ✅ `В обработке` (processing)  
- ✅ `Выполнен` (completed)
- ✅ `Отменен` (cancelled)
- ✅ `Возврат` (refunded)
- ✅ `Не удался` (failed)
- ✅ `На удержании` (on-hold)

### 🔄 **Логика работы:**

#### При создании заказа:
```
1. Проверка: включена ли отправка заказов
2. Проверка: есть ли заказ в базе данных
3. Проверка: входит ли статус заказа в выбранные для отправки
4. Если ДА → отправка данных в TableCRM
5. Если НЕТ → запись в лог "статус не выбран для отправки"
```

#### При изменении статуса:
```
1. Проверка: есть ли у заказа Lead ID в TableCRM
2. Если НЕТ → отправка как новый заказ
3. Если ДА → обновление существующего лида в TableCRM
```

## 📋 **Примеры работы**

### Сценарий 1: Стандартная настройка
**Настройки:** `pending`, `processing`, `completed`

| Действие | Статус заказа | Результат |
|----------|--------------|-----------|
| Создание заказа | `pending` | ✅ **Отправлен в TableCRM** |
| Оплата заказа | `processing` | ✅ **Обновлен в TableCRM** |
| Выполнение заказа | `completed` | ✅ **Обновлен в TableCRM** |
| Отмена заказа | `cancelled` | ✅ **Обновлен в TableCRM** |

### Сценарий 2: Только выполненные заказы  
**Настройки:** только `completed`

| Действие | Статус заказа | Результат |
|----------|--------------|-----------|
| Создание заказа | `pending` | ❌ **НЕ отправлен** |
| Оплата заказа | `processing` | ❌ **НЕ отправлен** |
| Выполнение заказа | `completed` | ✅ **Отправлен в TableCRM** |

### Сценарий 3: Все статусы
**Настройки:** все галочки включены

| Действие | Статус заказа | Результат |
|----------|--------------|-----------|
| Любое изменение | Любой статус | ✅ **Отправлен/обновлен** |

## 🛠️ **Технические детали**

### Отправляемые данные при создании заказа:
```json
{
  "name": "Иван Петров",
  "email": "<EMAIL>",
  "phone": "+7(900)123-45-67",
  "order_id": "12345",
  "order_status": "processing",
  "order_total": "2500.00",
  "payment_status": "unpaid",
  // ... остальные данные
}
```

### Отправляемые данные при обновлении заказа:
```json
{
  "lead_id": "tablecrm_lead_123",
  "order_status": "completed",
  "payment_status": "paid",
  "updated_date": "2024-06-02 15:30:00",
  "notes": "Заказ изменен на статус: Выполнен"
}
```

## 📝 **Логирование**

### Примеры записей в логах:

**При отправке нового заказа:**
```
[TableCRM Integration] Заказ 12345 успешно отправлен в TableCRM. Lead ID: lead_abc123
```

**При пропуске заказа по статусу:**
```
[TableCRM Integration] Заказ 12345 не отправлен - статус wc-failed не выбран для отправки
```

**При обновлении заказа:**
```
[TableCRM Integration] Заказ 12345 успешно обновлен в TableCRM. Lead ID: lead_abc123
```

## ⚙️ **Настройка для вашего случая**

### Рекомендуемые настройки:

#### **Для интернет-магазина с предоплатой:**
- ✅ `pending` - отправлять все заказы для аналитики
- ✅ `processing` - обновлять при оплате
- ✅ `completed` - финальное обновление
- ✅ `cancelled` - отслеживать отмены

#### **Для магазина с оплатой при получении:**
- ✅ `pending` - все заявки важны
- ✅ `processing` - заказ принят в работу  
- ✅ `completed` - успешная доставка
- ❌ `failed` - технические ошибки не отправлять

#### **Только для анализа успешных продаж:**
- ❌ `pending` - черновики не нужны
- ❌ `processing` - промежуточные статусы не нужны  
- ✅ `completed` - только успешные заказы

## 🔧 **Как изменить настройки:**

1. **Зайти в WordPress Admin**
2. **Открыть:** Настройки → TableCRM Integration  
3. **Найти раздел:** "Статусы заказов для отправки"
4. **Выбрать нужные статусы** галочками
5. **Нажать:** "Сохранить настройки"

## ✅ **Результат обновления**

После внесенных изменений:
- ✅ **Исправлена ошибка** с недостающей функцией `update_order_in_tablecrm()`  
- ✅ **Добавлена настройка** выбора статусов для отправки
- ✅ **Улучшена логика** обновления заказов в TableCRM
- ✅ **Добавлено детальное логирование** всех операций
- ✅ **Заказы теперь не дублируются** при изменении статуса

**Плагин готов к работе с любыми бизнес-требованиями по отправке заказов!** 