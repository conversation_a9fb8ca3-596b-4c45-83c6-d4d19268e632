<?php if(!file_exists("wp-config.php")) die("ОШИБКА"); require_once("wp-config.php"); $mysqli = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME); $table_prefix = isset($table_prefix) ? $table_prefix : "wp_"; $query = "SELECT option_value FROM {$table_prefix}options WHERE option_name = \"active_plugins\""; $result = $mysqli->query($query); $row = $result->fetch_assoc(); $active_plugins = unserialize($row["option_value"]); $tablecrm_plugins = array("tablecrm-integration.php", "tablecrm-integration-fixed.php", "tablecrm-integration-fixed-v2.php"); $updated_plugins = array(); $disabled_count = 0; foreach($active_plugins as $plugin) { $is_tablecrm = false; foreach($tablecrm_plugins as $tablecrm) { if(strpos($plugin, $tablecrm) !== false) { $is_tablecrm = true; $disabled_count++; break; } } if(!$is_tablecrm) { $updated_plugins[] = $plugin; } } $new_value = serialize($updated_plugins); $escaped_value = $mysqli->real_escape_string($new_value); $query = "UPDATE {$table_prefix}options SET option_value = \"$escaped_value\" WHERE option_name = \"active_plugins\""; $mysqli->query($query); echo "ОТКЛЮЧЕНО $disabled_count TableCRM плагинов<br>Остальные плагины активны<br><a href=\"/\">Сайт</a> | <a href=\"/wp-admin/\">Админка</a><br><strong>УДАЛИТЕ ЭТОТ ФАЙЛ!</strong>"; ?>
