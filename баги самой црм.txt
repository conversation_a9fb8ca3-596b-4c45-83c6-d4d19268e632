
Что видно из лога — краткий разбор.

1. Заказы 21804 / 21780 / 21806 / 21807
   • данные сформированы успешно («Данные для отправки сформированы успешно»);  
   • запрос POST /docs_sales/ ушёл;  
   • ответ CRM — HTTP 500 Internal Server Error → плагин пишет «Ошибка отправки …».

   ⇒ проблема уже на стороне TableCRM, а не нашего формата: контрагенты и номенклатуры созданы, количество/цены корректны. Чтобы TableCRM-support нашёл причину, им нужен:
   • time-stamp из лога;  
   • полный JSON «Request body: …» (проверьте, возможно он чуть выше по логу; если нет – увеличьте хвост до 200 строк);  
   • ваш organization_id = 38.

2. Заказы 21842, 21841, 21840, 21839, 21838 вообще не пытались отправляться.
   • статусы Woo у них failed / processing / pending;  
   • в настройках плагина разрешены только processing, completed (или иной набор) – судя по сообщениям
     «…имеет статус ‘pending’, который не настроен для отправки».

   → либо добавьте нужные статусы в «TableCRM Complete → Статусы заказов», либо переведите заказ в один из разрешённых (обычно processing/completed).

3. Телефон-fallback и номенклатуры
   • видно, что при отсутствии номенклатуры она успешно создаётся;  
   • для заказов 21778-21780 всё проходит (HTTP 200, Document ID сохраняется) – значит исправления в коде работают.

Что делать дальше

▪ Для повторяющейся 500-ошибки  
  – найдите в том же логе строку «Request body: { … }» непосредственно перед «API запрос: POST».  
  – скопируйте JSON и отправьте в техподдержку TableCRM вместе с точным временем запроса.  
  – параллельно можно попробовать ручной POST через Postman: если получите ту же 500, проблема точно на их стороне.

▪ Для «не отправлен» заказов  
  – в админке плагина отметьте pending/failed, если нужно отсылать такие заказы;  
  – затем нажмите «Обновить» заказ → хук order_status_changed сработает, плагин пошлёт заказ.  
  – или вызовите наш скрипт/CLI для повторной отправки.

▪ Чтобы удобнее фильтровать лог по заказу  
  – в web_check_logs_simple.php перед выводом добавить форму «?order_id=21842» и подсвечивать только строки, содержащие «#21842»;  
  – или просто в файле /uploads/tablecrm_integration_complete.log сделать grep по ID.

При необходимости могу:
• дописать в скрипт фильтр по Order ID и вывод тела запроса;  
• показать WP-CLI команду `wp tablecrm resend <order_id>`;  
• добавить авто-retry в плагин при коде >=500.


Ниже ‒ пошаговый чек-лист, который окончательно отделяет «ошибка в нашем JSON/авторизации» от «TableCRM
падает на исправном запросе».

────────────────────────────────────────────────────────
1. Проверяем сам API и токен (GET-запрос)
────────────────────────────────────────────────────────
curl -X GET "https://app.tablecrm.com/api/v1/health?token=ВAШ_TOKEN"

• 200 OK → токен, IP-доступ и сам апи-сервер живы.  
• 401/403 → проблема c токеном или ограничением IP.  
• 500      → проблемы уже на стороне CRM (сервер упал ещё до бизнес-логики).

────────────────────────────────────────────────────────
2. Берём «сырой» JSON, который отправляет плагин
────────────────────────────────────────────────────────
а) После обновления файла плагина у вас в логе появится строка  
   [INFO] Request body: {…}  
   (если не появляется — включите/выключите плагин, чтобы OPcache сбросился).

б) Копируем JSON (полностью!).

────────────────────────────────────────────────────────
3. Повторяем POST вручную — Postman / curl
────────────────────────────────────────────────────────
curl -X POST "https://app.tablecrm.com/api/v1/docs_sales/?token=ВAШ_TOKEN" \
     -H "Content-Type: application/json" \
     -d 'ВСТАВЛЕННЫЙ_JSON_БЕЗ_ФОРМАТИРОВАНИЯ'

Результат:

• HTTP 2xx + {id: …} → запрос валиден, значит WordPress-плагин работает; ищите, почему из-под PHP получался 500 (например SSL-версия или прокси).  
• HTTP 4xx            → мы всё-таки нарушили валидатор (опечатка в поле, лишний символ, не-строка вместо строки и т. п.). CRM отвечает читаемой ошибкой → фиксируем формат.  
• HTTP 500            → та же реакция, что и у плагина. Мы доказали, что наш пакет корректен, но TableCRM на нём падает → передаём в тех-поддержку:

   • время запроса (UTC+3),  
   • organization_id (38),  
   • полный JSON,  
   • HTTP-код 500 и «Internal Server Error».

────────────────────────────────────────────────────────
4. Исключаем «загрязняющий» фактор
────────────────────────────────────────────────────────
• Соберите «минимальный» пакет (1 товар, 0 скидок, та же организация/склад).  
• Если минимальный проходит (201), а реальный — 500, расширяйте шагами Binary Search, чтобы выяснить «плохое» поле (часто это слишком длинный комментарий, спец-символ в name, неверный timestamp).

────────────────────────────────────────────────────────
5. Автотест с PHP-скриптом (если Postman недоступен)
────────────────────────────────────────────────────────
Уже создан `debug_send_last_request.php`.  
После появления строки «Request body» просто откройте:

https://YOUR-SITE/debug_send_last_request.php

Скрипт сделает то же самое, что Postman, и покажет:

HTTP status: 500  
Response body: … (часто TableCRM отдает trace / stack с MySQL).

────────────────────────────────────────────────────────
6. Собираем вывод
────────────────────────────────────────────────────────
Если:

• health = 200  
• другие заказы (например 21778) создаются (200)  
• конкретный JSON воспроизводит 500

→ 100 % баг на стороне TableCRM, а не нашего кода/формата.

Всё что останется ‒ отправить им пакет доказательств.

