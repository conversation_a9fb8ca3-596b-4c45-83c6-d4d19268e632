<?php
/**
 * Комплексная проверка соответствия плагина TableCRM Integration требованиям задачи
 * 
 * Проверяет:
 * 1. Отправку данных от кого (имя/тел)
 * 2. Отправку данных кому (имя, тел, адрес, время)
 * 3. Список товаров и количество
 * 4. Тип оплаты и статус оплаты
 * 5. Аналитику (UTM-ки и домен)
 * 6. Проверку на дубли товаров
 * 7. Проверку на дубли контрагентов
 */

echo "🔍 КОМПЛЕКСНАЯ ПРОВЕРКА ИНТЕГРАЦИИ TableCRM\n";
echo "============================================\n\n";

// Функция для анализа PHP кода
function analyze_php_function($file_path, $function_name, $start_line = null) {
    $content = file_get_contents($file_path);
    if (!$content) {
        return false;
    }
    
    $lines = explode("\n", $content);
    $function_content = [];
    $in_function = false;
    $brace_count = 0;
    
    foreach ($lines as $line_num => $line) {
        if (strpos($line, "function $function_name") !== false || 
            strpos($line, "public function $function_name") !== false ||
            strpos($line, "private function $function_name") !== false) {
            $in_function = true;
        }
        
        if ($in_function) {
            $function_content[] = ['line' => $line_num + 1, 'content' => $line];
            
            $brace_count += substr_count($line, '{') - substr_count($line, '}');
            
            if ($brace_count <= 0 && strpos($line, '}') !== false) {
                break;
            }
        }
    }
    
    return $function_content;
}

// Функция для поиска ключевых слов в коде
function find_keywords_in_function($function_content, $keywords) {
    $found = [];
    
    foreach ($function_content as $line_data) {
        $line = $line_data['content'];
        $line_num = $line_data['line'];
        
        foreach ($keywords as $keyword) {
            if (stripos($line, $keyword) !== false) {
                $found[] = [
                    'keyword' => $keyword,
                    'line' => $line_num,
                    'content' => trim($line)
                ];
            }
        }
    }
    
    return $found;
}

echo "📋 АНАЛИЗ ОСНОВНОГО ФАЙЛА ПЛАГИНА\n";
echo "=================================\n";

$plugin_file = 'tablecrm-integration-fixed-v3.php';

if (!file_exists($plugin_file)) {
    echo "❌ Файл плагина не найден: $plugin_file\n";
    exit(1);
}

echo "✅ Файл плагина найден: $plugin_file\n";

// Проверяем версию плагина
$plugin_content = file_get_contents($plugin_file);
if (preg_match('/Version:\s*([0-9.]+)/', $plugin_content, $matches)) {
    echo "📌 Версия плагина: {$matches[1]}\n";
}

echo "\n";

// 1. ПРОВЕРКА ОТПРАВКИ ДАННЫХ "ОТ КОГО" (имя/телефон)
echo "1️⃣ ПРОВЕРКА ДАННЫХ 'ОТ КОГО' (имя/телефон)\n";
echo str_repeat("=", 50) . "\n";

$send_order_function = analyze_php_function($plugin_file, 'send_order_to_tablecrm');

if ($send_order_function) {
    echo "✅ Найдена функция send_order_to_tablecrm\n";
    
    $from_keywords = [
        'billing_first_name', 'billing_last_name', 'billing_phone', 
        'get_billing_first_name', 'get_billing_last_name', 'get_billing_phone'
    ];
    
    $found_from = find_keywords_in_function($send_order_function, $from_keywords);
    
    if (!empty($found_from)) {
        echo "✅ Отправка данных 'от кого' реализована:\n";
        foreach ($found_from as $item) {
            echo "   - Строка {$item['line']}: {$item['keyword']}\n";
        }
    } else {
        echo "❌ Данные 'от кого' не найдены\n";
    }
} else {
    echo "❌ Функция send_order_to_tablecrm не найдена\n";
}

echo "\n";

// 2. ПРОВЕРКА ДАННЫХ "КОМУ" (имя, тел, адрес, время)
echo "2️⃣ ПРОВЕРКА ДАННЫХ 'КОМУ' (имя, тел, адрес, время)\n";
echo str_repeat("=", 50) . "\n";

$to_keywords = [
    'shipping_first_name', 'shipping_last_name', 'shipping_phone', 
    'shipping_address', 'shipping_city', 'get_shipping',
    'order_date', 'get_date_created'
];

$found_to = find_keywords_in_function($send_order_function, $to_keywords);

if (!empty($found_to)) {
    echo "✅ Отправка данных 'кому' реализована:\n";
    foreach ($found_to as $item) {
        echo "   - Строка {$item['line']}: {$item['keyword']}\n";
    }
} else {
    echo "⚠️ Данные 'кому' частично реализованы (возможно, используется billing адрес)\n";
}

echo "\n";

// 3. ПРОВЕРКА ТОВАРОВ И КОЛИЧЕСТВА
echo "3️⃣ ПРОВЕРКА ТОВАРОВ И КОЛИЧЕСТВА\n";
echo str_repeat("=", 50) . "\n";

$items_keywords = [
    'get_items', 'get_name', 'get_quantity', 'get_total', 
    'items', 'quantity', 'price', 'product'
];

$found_items = find_keywords_in_function($send_order_function, $items_keywords);

if (!empty($found_items)) {
    echo "✅ Отправка товаров и количества реализована:\n";
    foreach ($found_items as $item) {
        echo "   - Строка {$item['line']}: {$item['keyword']}\n";
    }
} else {
    echo "❌ Отправка товаров не найдена\n";
}

echo "\n";

// 4. ПРОВЕРКА ТИПА ОПЛАТЫ И СТАТУСА
echo "4️⃣ ПРОВЕРКА ТИПА ОПЛАТЫ И СТАТУСА\n";
echo str_repeat("=", 50) . "\n";

$payment_keywords = [
    'payment_method', 'get_payment_method', 'is_paid', 
    'payment_status', 'paid', 'unpaid'
];

$found_payment = find_keywords_in_function($send_order_function, $payment_keywords);

if (!empty($found_payment)) {
    echo "✅ Отправка типа оплаты и статуса реализована:\n";
    foreach ($found_payment as $item) {
        echo "   - Строка {$item['line']}: {$item['keyword']}\n";
    }
} else {
    echo "❌ Данные оплаты не найдены\n";
}

echo "\n";

// 5. ПРОВЕРКА UTM АНАЛИТИКИ
echo "5️⃣ ПРОВЕРКА UTM АНАЛИТИКИ\n";
echo str_repeat("=", 50) . "\n";

// Проверяем функцию сохранения UTM
$utm_function = analyze_php_function($plugin_file, 'save_utm_data_to_order');

if ($utm_function) {
    echo "✅ Найдена функция save_utm_data_to_order\n";
    
    $utm_keywords = [
        'utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 
        'utm_content', 'domain', 'referrer', '_COOKIE'
    ];
    
    $found_utm = find_keywords_in_function($utm_function, $utm_keywords);
    
    if (!empty($found_utm)) {
        echo "✅ UTM аналитика реализована:\n";
        foreach ($found_utm as $item) {
            echo "   - Строка {$item['line']}: {$item['keyword']}\n";
        }
    } else {
        echo "❌ UTM параметры не найдены\n";
    }
} else {
    echo "❌ Функция save_utm_data_to_order не найдена\n";
}

// Проверяем UTM JavaScript файл
echo "\n📜 ПРОВЕРКА UTM JAVASCRIPT:\n";
if (file_exists('utm-tracking.js')) {
    echo "✅ Файл utm-tracking.js найден\n";
    
    $js_content = file_get_contents('utm-tracking.js');
    $utm_js_features = [
        'utm_source' => strpos($js_content, 'utm_source') !== false,
        'utm_medium' => strpos($js_content, 'utm_medium') !== false,
        'utm_campaign' => strpos($js_content, 'utm_campaign') !== false,
        'setCookie' => strpos($js_content, 'setCookie') !== false,
        'getUrlParameter' => strpos($js_content, 'getUrlParameter') !== false
    ];
    
    foreach ($utm_js_features as $feature => $found) {
        echo "   " . ($found ? "✅" : "❌") . " $feature\n";
    }
} else {
    echo "❌ Файл utm-tracking.js не найден\n";
}

echo "\n";

// 6. ПРОВЕРКА НА ДУБЛИ ТОВАРОВ
echo "6️⃣ ПРОВЕРКА НА ДУБЛИ ТОВАРОВ\n";
echo str_repeat("=", 50) . "\n";

$duplicate_keywords = [
    'nomenclature', 'product_id', 'sku', 'check_duplicates'
];

$found_duplicates = find_keywords_in_function($send_order_function, $duplicate_keywords);

if (!empty($found_duplicates)) {
    echo "✅ Проверка дублей товаров частично реализована:\n";
    foreach ($found_duplicates as $item) {
        echo "   - Строка {$item['line']}: {$item['keyword']}\n";
    }
} else {
    echo "⚠️ Явная проверка дублей товаров не найдена\n";
}

// Проверяем настройки дублей
if (strpos($plugin_content, 'check_duplicates') !== false) {
    echo "✅ В плагине есть настройка проверки дублей\n";
} else {
    echo "❌ Настройка проверки дублей не найдена\n";
}

echo "\n";

// 7. ПРОВЕРКА НА ДУБЛИ КОНТРАГЕНТОВ
echo "7️⃣ ПРОВЕРКА НА ДУБЛИ КОНТРАГЕНТОВ\n";
echo str_repeat("=", 50) . "\n";

$contragent_keywords = [
    'contragent', '_tablecrm_v3_document_id', 'existing_doc_id'
];

$found_contragents = find_keywords_in_function($send_order_function, $contragent_keywords);

if (!empty($found_contragents)) {
    echo "✅ Проверка дублей контрагентов реализована:\n";
    foreach ($found_contragents as $item) {
        echo "   - Строка {$item['line']}: {$item['keyword']}\n";
    }
} else {
    echo "❌ Проверка дублей контрагентов не найдена\n";
}

echo "\n";

// 8. ПРОВЕРКА IDEMPOTENCY (устранение фантомных сделок)
echo "8️⃣ ПРОВЕРКА IDEMPOTENCY KEY\n";
echo str_repeat("=", 50) . "\n";

$api_function = analyze_php_function($plugin_file, 'make_api_request');

if ($api_function) {
    $idempotency_keywords = ['Idempotency-Key', 'order-', 'lead-'];
    $found_idempotency = find_keywords_in_function($api_function, $idempotency_keywords);
    
    if (!empty($found_idempotency)) {
        echo "✅ Idempotency Key реализован для устранения фантомных сделок:\n";
        foreach ($found_idempotency as $item) {
            echo "   - Строка {$item['line']}: {$item['keyword']}\n";
        }
    } else {
        echo "❌ Idempotency Key не найден\n";
    }
} else {
    echo "❌ Функция make_api_request не найдена\n";
}

echo "\n";

// 9. ПРОВЕРКА ЗАЩИТЫ ОТ ДУБЛИРОВАНИЯ ЗАКАЗОВ
echo "9️⃣ ПРОВЕРКА ЗАЩИТЫ ОТ ДУБЛИРОВАНИЯ ЗАКАЗОВ\n";
echo str_repeat("=", 50) . "\n";

$anti_duplicate_keywords = [
    '_tablecrm_v3_processing', 'add_post_meta', 'Action Scheduler',
    'as_enqueue_async_action', 'queue_order_sync'
];

$queue_function = analyze_php_function($plugin_file, 'queue_order_sync');
$found_anti_duplicate = [];

if ($queue_function) {
    $found_anti_duplicate = find_keywords_in_function($queue_function, $anti_duplicate_keywords);
}

if (!empty($found_anti_duplicate)) {
    echo "✅ Защита от дублирования заказов реализована:\n";
    foreach ($found_anti_duplicate as $item) {
        echo "   - Строка {$item['line']}: {$item['keyword']}\n";
    }
} else {
    echo "❌ Защита от дублирования не найдена\n";
}

echo "\n";

// 10. ПРОВЕРКА ИТОГОВОЙ ОЦЕНКИ
echo "📊 ИТОГОВАЯ ОЦЕНКА СООТВЕТСТВИЯ ТРЕБОВАНИЯМ\n";
echo str_repeat("=", 50) . "\n";

$requirements = [
    'От кого (имя/тел)' => !empty($found_from),
    'Кому (имя, тел, адрес, время)' => !empty($found_to),
    'Товары (список и количество)' => !empty($found_items),
    'Тип оплаты и статус' => !empty($found_payment),
    'UTM аналитика' => !empty($found_utm) && file_exists('utm-tracking.js'),
    'Проверка дублей товаров' => !empty($found_duplicates),
    'Проверка дублей контрагентов' => !empty($found_contragents),
    'Idempotency Key' => !empty($found_idempotency),
    'Защита от дублирования' => !empty($found_anti_duplicate)
];

$total_requirements = count($requirements);
$met_requirements = array_sum($requirements);
$percentage = round(($met_requirements / $total_requirements) * 100, 1);

echo "Выполнено требований: $met_requirements из $total_requirements ($percentage%)\n\n";

foreach ($requirements as $requirement => $met) {
    $status = $met ? "✅" : "❌";
    echo "$status $requirement\n";
}

echo "\n";

if ($percentage >= 90) {
    echo "🎉 ОТЛИЧНО! Плагин полностью соответствует требованиям задачи\n";
} elseif ($percentage >= 70) {
    echo "✅ ХОРОШО! Плагин в основном соответствует требованиям\n";
} elseif ($percentage >= 50) {
    echo "⚠️ УДОВЛЕТВОРИТЕЛЬНО! Плагин частично соответствует требованиям\n";
} else {
    echo "❌ НЕУДОВЛЕТВОРИТЕЛЬНО! Плагин не соответствует большинству требований\n";
}

echo "\n";

// 11. РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ
echo "💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ\n";
echo str_repeat("=", 50) . "\n";

if (empty($found_to)) {
    echo "🔧 Добавить отправку shipping адреса в TableCRM\n";
}

if (empty($found_duplicates)) {
    echo "🔧 Реализовать проверку дублей товаров по SKU или nomenclature\n";
}

if (!file_exists('utm-tracking.js')) {
    echo "🔧 Добавить JavaScript файл для UTM трекинга\n";
}

if (empty($found_idempotency)) {
    echo "🔧 Добавить Idempotency-Key для предотвращения фантомных сделок\n";
}

echo "\n✅ ПРОВЕРКА ЗАВЕРШЕНА\n";
echo "=====================\n";
echo "⏱️ Время выполнения: " . round((microtime(true) - $_SERVER['REQUEST_TIME_FLOAT']) * 1000, 2) . " мс\n";