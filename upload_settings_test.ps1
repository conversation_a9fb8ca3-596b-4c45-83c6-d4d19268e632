# Upload and run settings test
$server = "bekflom3.beget.tech"
$user = "bekflom3_wz"
$pass = "zl2Shj!e&6ng"

Write-Host "Uploading settings test script..." -ForegroundColor Green

try {
    $uri = "ftp://$server/public_html/check_settings_and_test.php"
    $webclient = New-Object System.Net.WebClient
    $webclient.Credentials = New-Object System.Net.NetworkCredential($user, $pass)
    $webclient.UploadFile($uri, "check_settings_and_test.php")
    Write-Host "Settings test script uploaded!" -ForegroundColor Green
    
    # Run the test
    Write-Host "Running settings test..." -ForegroundColor Yellow
    $result = Invoke-WebRequest -Uri "https://mosbuketik.ru/check_settings_and_test.php" -UseBasicParsing
    Write-Host $result.Content -ForegroundColor White
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

$webclient.Dispose()
Write-Host "Settings test completed!" -ForegroundColor Green 