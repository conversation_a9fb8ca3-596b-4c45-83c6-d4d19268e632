<?php
// Тест исправленной логики даты доставки
echo "=== ТЕСТ ИСПРАВЛЕННОЙ ДАТЫ ДОСТАВКИ ===\n";

$wp_path = '/home/<USER>/bekflom3/mosbuketik.ru/public_html';
require_once($wp_path . '/wp-config.php');
require_once($wp_path . '/wp-load.php');

echo "WordPress загружен успешно\n\n";

// Тестируем заказ из скриншота
$order_id = 21780;
$order = wc_get_order($order_id);

if (!$order) {
    echo "❌ Заказ #$order_id не найден\n";
    exit;
}

echo "✅ Заказ #$order_id найден\n";
echo "Статус: " . $order->get_status() . "\n";
echo "Стоимость доставки: " . $order->get_shipping_total() . "\n\n";

// Проверяем мета-поля даты доставки
echo "=== ПРОВЕРКА МЕТА-ПОЛЕЙ ДАТЫ ДОСТАВКИ ===\n";

$delivery_date_fields = array(
    '_delivery_date',
    'delivery_date', 
    '_orddd_timestamp',
    '_delivery_time',
    'delivery_time',
    '_orddd_time_slot'
);

foreach ($delivery_date_fields as $field) {
    $value = $order->get_meta($field);
    echo "Поле '$field': " . (empty($value) ? "(пусто)" : "'$value'") . "\n";
}

// Функция для проверки валидности даты (как в исправленном коде)
$is_valid_date = function($date) {
    if (empty($date)) return false;
    if ($date === '0' || $date === 0) return false;
    if ($date === '1970-01-01 00:00:00+00') return false;
    return true;
};

echo "\n=== ТЕСТ ЛОГИКИ ДАТЫ ДОСТАВКИ ===\n";

// Симулируем логику из исправленного кода
$delivery_data = array();

// Получаем дату доставки
$delivery_date = $order->get_meta('_delivery_date');
if (empty($delivery_date)) {
    $delivery_date = $order->get_meta('delivery_date');
}
if (empty($delivery_date)) {
    $delivery_date = $order->get_meta('_orddd_timestamp');
}

echo "Найденная дата доставки: " . (empty($delivery_date) ? "(не найдена)" : "'$delivery_date'") . "\n";

if (!empty($delivery_date)) {
    if ($is_valid_date($delivery_date)) {
        $delivery_data['delivery_date'] = $delivery_date;
        echo "✅ Дата доставки будет отправлена: '$delivery_date'\n";
    } else {
        echo "❌ Дата доставки невалидна, поле не будет отправлено\n";
    }
} else {
    echo "ℹ️ Дата доставки не найдена, поле не будет отправлено\n";
}

// Формируем остальные данные доставки для демонстрации
$first_name = $order->get_shipping_first_name() ?: $order->get_billing_first_name();
$last_name = $order->get_shipping_last_name() ?: $order->get_billing_last_name();
$phone = $order->get_shipping_phone() ?: $order->get_billing_phone();

// Функция для проверки телефона
$is_phone = function($str) {
    if (empty($str)) return false;
    return preg_match('/^[\+]?[0-9\(\)\-\s]{7,}$/', $str) || 
           strpos($str, '+7') === 0 || 
           strpos($str, '8(') === 0 ||
           preg_match('/\d{3}.*\d{3}.*\d{2}.*\d{2}/', $str);
};

$clean_first_name = (!empty($first_name) && !$is_phone($first_name)) ? $first_name : '';
$clean_last_name = (!empty($last_name) && !$is_phone($last_name)) ? $last_name : '';

$recipient_parts = array();
if (!empty($clean_first_name)) {
    $recipient_parts[] = $clean_first_name;
}
if (!empty($clean_last_name)) {
    $recipient_parts[] = $clean_last_name;
}

$recipient_name = implode(' ', $recipient_parts);

if (!empty($recipient_name)) {
    $delivery_data['recipient'] = $recipient_name;
}

// Адрес
$address_parts = array();
if ($order->get_billing_address_1()) {
    $address_parts[] = $order->get_billing_address_1();
}
if ($order->get_billing_city()) {
    $address_parts[] = $order->get_billing_city();
}

if (!empty($address_parts)) {
    $delivery_data['address'] = implode(', ', $address_parts);
}

// Примечание с телефоном
$note_parts = array();
$shipping_method = $order->get_shipping_method();
if ($shipping_method) {
    $note_parts[] = "Способ доставки: " . $shipping_method;
}

$shipping_total = $order->get_shipping_total();
if ($shipping_total > 0) {
    $note_parts[] = "Стоимость доставки: " . $shipping_total . " руб.";
}

if (!empty($phone)) {
    $note_parts[] = "Телефон: " . $phone;
}

if (!empty($note_parts)) {
    $delivery_data['note'] = implode('. ', $note_parts);
}

echo "\n=== ИТОГОВЫЕ ДАННЫЕ ДОСТАВКИ ===\n";
echo json_encode($delivery_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";

echo "\n=== КЛЮЧЕВЫЕ ИЗМЕНЕНИЯ ===\n";
echo "✅ Поле delivery_date отправляется ТОЛЬКО если есть реальная дата\n";
echo "✅ Значения 0, '0', '1970-01-01 00:00:00+00' игнорируются\n";
echo "✅ Если даты нет - поле вообще не включается в запрос\n";
echo "✅ Recipient отправляется как строка, а не JSON объект\n";
echo "✅ Телефон перенесен в поле note\n";

echo "\n=== ТЕСТ ЗАВЕРШЕН ===\n";
?> 