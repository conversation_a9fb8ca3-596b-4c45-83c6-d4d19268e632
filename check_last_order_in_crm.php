<?php
/**
 * Скрипт для проверки последнего заказа в TableCRM через API
 * Запуск из командной строки: php check_last_order_in_crm.php
 */

// Подключение к WordPress
require_once('../../wp-config.php');
require_once('../../wp-load.php');

echo "🔍 Проверка последнего заказа в TableCRM\n";
echo "=========================================\n\n";

// Получаем настройки плагина
$api_url = get_option('tablecrm_api_url', 'https://app.tablecrm.com/api/v1/');
$api_key = get_option('tablecrm_api_key');
$organization_id = get_option('tablecrm_organization_id', 38);

if (empty($api_key)) {
    echo "❌ API ключ не настроен в плагине!\n";
    echo "Настройте плагин в WordPress админке.\n";
    exit(1);
}

// Функция для расшифровки API ключа (если зашифрован)
function get_decrypted_api_key() {
    $stored = get_option("tablecrm_api_key");
    if (!$stored) {
        return '';
    }
    
    // Если ключ зашифрован
    if (strpos($stored, 'enc:') === 0) {
        // Простая расшифровка для тестирования
        if (function_exists('tablecrm_decrypt_value')) {
            return tablecrm_decrypt_value($stored);
        }
        echo "⚠️  API ключ зашифрован, используем как есть\n";
        return $stored;
    }
    
    return $stored;
}

$api_key_decrypted = get_decrypted_api_key();

echo "📋 Настройки подключения:\n";
echo "API URL: $api_url\n";
echo "API Key: " . substr($api_key_decrypted, 0, 10) . "...\n";
echo "ID Организации: $organization_id\n\n";

// Функция для выполнения API запроса
function make_tablecrm_request($endpoint, $params = array()) {
    global $api_url, $api_key_decrypted;
    
    $url = rtrim($api_url, '/') . '/' . ltrim($endpoint, '/');
    
    // Добавляем токен и параметры
    $query_params = array_merge($params, array('token' => $api_key_decrypted));
    $url .= '?' . http_build_query($query_params);
    
    echo "🔗 Запрос к: " . rtrim($url, '?token=' . $api_key_decrypted) . "?token=***\n";
    
    $response = wp_remote_get($url, array(
        'timeout' => 30,
        'sslverify' => true,
        'headers' => array(
            'User-Agent' => 'WordPress-TableCRM-Integration/1.0'
        )
    ));
    
    if (is_wp_error($response)) {
        echo "❌ Ошибка запроса: " . $response->get_error_message() . "\n";
        return false;
    }
    
    $status_code = wp_remote_retrieve_response_code($response);
    $body = wp_remote_retrieve_body($response);
    
    echo "📊 HTTP статус: $status_code\n";
    
    if ($status_code !== 200) {
        echo "❌ Ошибка API: HTTP $status_code\n";
        echo "Ответ: " . substr($body, 0, 500) . "\n";
        return false;
    }
    
    $decoded = json_decode($body, true);
    if (!$decoded) {
        echo "❌ Ошибка декодирования JSON\n";
        echo "Ответ: " . substr($body, 0, 500) . "\n";
        return false;
    }
    
    return $decoded;
}

// 1. Получаем последние документы продаж
echo "1️⃣ Получение последних документов продаж...\n";
echo "============================================\n";

$docs_params = array(
    'limit' => 5,
    'offset' => 0,
    'sort' => 'created_at:desc'
);

$docs_response = make_tablecrm_request('docs_sales/', $docs_params);

if (!$docs_response) {
    echo "❌ Не удалось получить документы продаж\n";
    exit(1);
}

echo "✅ Получен ответ от API\n\n";

if (isset($docs_response['results']) && !empty($docs_response['results'])) {
    echo "📋 Найдено документов: " . count($docs_response['results']) . "\n\n";
    
    foreach ($docs_response['results'] as $index => $doc) {
        echo "📄 Документ #" . ($index + 1) . ":\n";
        echo "   ID: " . ($doc['id'] ?? 'не указан') . "\n";
        echo "   Номер: " . ($doc['number'] ?? 'не указан') . "\n";
        echo "   Дата: " . ($doc['dated'] ?? 'не указана') . "\n";
        echo "   Операция: " . ($doc['operation'] ?? 'не указана') . "\n";
        echo "   Сумма: " . ($doc['paid_rubles'] ?? 'не указана') . "\n";
        echo "   Статус оплаты: " . ($doc['status'] ? 'Оплачен' : 'Не оплачен') . "\n";
        echo "   Контрагент ID: " . ($doc['contragent'] ?? 'не указан') . "\n";
        echo "   Организация ID: " . ($doc['organization'] ?? 'не указана') . "\n";
        echo "   Комментарий: " . (isset($doc['comment']) ? substr($doc['comment'], 0, 100) . '...' : 'нет') . "\n";
        
        // Если есть товары
        if (isset($doc['goods']) && !empty($doc['goods'])) {
            echo "   Товары (" . count($doc['goods']) . "):\n";
            foreach ($doc['goods'] as $good) {
                echo "     - Номенклатура ID: " . ($good['nomenclature'] ?? 'не указана') . "\n";
                echo "       Количество: " . ($good['quantity'] ?? 'не указано') . "\n";
                echo "       Цена: " . ($good['price'] ?? 'не указана') . "\n";
            }
        }
        echo "\n";
    }
    
    // Берем первый (самый последний) документ для детальной проверки
    $last_doc = $docs_response['results'][0];
    $last_doc_id = $last_doc['id'];
    $contragent_id = $last_doc['contragent'] ?? null;
    
    echo "\n2️⃣ Детальная информация о последнем документе (ID: $last_doc_id)...\n";
    echo "================================================================\n";
    
    // Получаем полную информацию о контрагенте
    if ($contragent_id) {
        echo "\n👤 Информация о контрагенте (ID: $contragent_id):\n";
        echo "===============================================\n";
        
        $contragent_response = make_tablecrm_request("contragents/$contragent_id/");
        
        if ($contragent_response) {
            echo "✅ Данные контрагента получены:\n";
            echo "   Имя: " . ($contragent_response['name'] ?? 'не указано') . "\n";
            echo "   Email: " . ($contragent_response['email'] ?? 'не указан') . "\n";
            echo "   Телефон: " . ($contragent_response['phone'] ?? 'не указан') . "\n";
            echo "   Тип: " . ($contragent_response['type'] ?? 'не указан') . "\n";
            echo "   External ID: " . ($contragent_response['external_id'] ?? 'не указан') . "\n";
            echo "   Комментарий: " . ($contragent_response['comment'] ?? 'нет') . "\n";
            
            // Проверяем, не является ли это контрагентом "Айгуль"
            $name = $contragent_response['name'] ?? '';
            if (stripos($name, 'Айгуль') !== false || stripos($name, 'Aigul') !== false) {
                echo "\n🚨 НАЙДЕН КОНТРАГЕНТ С ИМЕНЕМ 'АЙГУЛЬ'!\n";
                echo "🚨 Это может быть источником проблемы!\n";
            }
        } else {
            echo "❌ Не удалось получить данные контрагента\n";
        }
    }
    
    // Получаем информацию о номенклатурах из товаров
    if (isset($last_doc['goods']) && !empty($last_doc['goods'])) {
        echo "\n📦 Информация о товарах:\n";
        echo "========================\n";
        
        foreach ($last_doc['goods'] as $index => $good) {
            $nomenclature_id = $good['nomenclature'] ?? null;
            if ($nomenclature_id) {
                echo "\n📋 Товар #" . ($index + 1) . " (Номенклатура ID: $nomenclature_id):\n";
                
                $nomenclature_response = make_tablecrm_request("nomenclature/$nomenclature_id/");
                
                if ($nomenclature_response) {
                    echo "   Название: " . ($nomenclature_response['name'] ?? 'не указано') . "\n";
                    echo "   Код: " . ($nomenclature_response['code'] ?? 'не указан') . "\n";
                    echo "   External ID: " . ($nomenclature_response['external_id'] ?? 'не указан') . "\n";
                    echo "   Активен: " . ($nomenclature_response['is_active'] ? 'Да' : 'Нет') . "\n";
                } else {
                    echo "   ❌ Не удалось получить данные номенклатуры\n";
                }
            }
        }
    }
    
} else {
    echo "❌ Документы продаж не найдены\n";
    echo "Проверьте:\n";
    echo "- Правильность API ключа\n";
    echo "- ID организации\n";
    echo "- Наличие документов в TableCRM\n";
}

// 3. Дополнительная диагностика - поиск контрагентов с именем "Айгуль"
echo "\n\n3️⃣ Поиск контрагентов с именем 'Айгуль'...\n";
echo "========================================\n";

$search_params = array(
    'limit' => 50,
    'offset' => 0,
    'sort' => 'created_at:desc'
);

$contragents_response = make_tablecrm_request('contragents/', $search_params);

if ($contragents_response && isset($contragents_response['results'])) {
    $found_aigul = false;
    
    foreach ($contragents_response['results'] as $contragent) {
        $name = $contragent['name'] ?? '';
        if (stripos($name, 'Айгуль') !== false || stripos($name, 'Aigul') !== false) {
            if (!$found_aigul) {
                echo "🚨 НАЙДЕНЫ КОНТРАГЕНТЫ С ИМЕНЕМ 'АЙГУЛЬ':\n";
                $found_aigul = true;
            }
            
            echo "\n👤 Контрагент:\n";
            echo "   ID: " . ($contragent['id'] ?? 'не указан') . "\n";
            echo "   Имя: " . $name . "\n";
            echo "   Email: " . ($contragent['email'] ?? 'не указан') . "\n";
            echo "   Телефон: " . ($contragent['phone'] ?? 'не указан') . "\n";
            echo "   External ID: " . ($contragent['external_id'] ?? 'не указан') . "\n";
        }
    }
    
    if (!$found_aigul) {
        echo "✅ Контрагенты с именем 'Айгуль' не найдены в последних 50 записях\n";
    }
} else {
    echo "❌ Не удалось получить список контрагентов\n";
}

echo "\n\n✅ Проверка завершена!\n";
echo "======================\n";
echo "Время выполнения: " . date('Y-m-d H:i:s') . "\n";
?> 