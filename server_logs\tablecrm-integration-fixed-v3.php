<?php
/**
 * Plugin Name: TableCRM Integration (Fixed v3 - Security Enhanced)
 * Description: Интеграция WordPress/WooCommerce с TableCRM для отправки заявок через API - версия с полным устранением логических рисков безопасности
 * Version: 1.0.12
 * Author: Your Name
 * Text Domain: tablecrm-integration-fixed-v3
 */

// Предотвращение прямого доступа
if (!defined('ABSPATH')) {
    exit;
}

// Определение констант плагина
define('TABLECRM_FIXED_V3_PLUGIN_URL', plugin_dir_url(__FILE__));
define('TABLECRM_FIXED_V3_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('TABLECRM_FIXED_V3_VERSION', '1.0.12');

class TableCRM_Integration_Fixed_V3 {
    
    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'register_settings'));
        
        // Хук валидации настроек при сохранении
        add_action('update_option_tablecrm_fixed_v3_api_url', array($this, 'validate_and_log_settings'));
        add_action('update_option_tablecrm_fixed_v3_api_key', array($this, 'validate_and_log_settings'));
        
        // Хуки для WooCommerce - С ACTION SCHEDULER
        add_action('woocommerce_new_order', array($this, 'queue_order_sync'), 20);
        add_action('woocommerce_order_status_completed', array($this, 'queue_order_sync'));
        add_action('woocommerce_order_status_processing', array($this, 'queue_order_sync'));
        add_action('woocommerce_order_status_cancelled', array($this, 'queue_order_sync'));
        add_action('woocommerce_order_status_refunded', array($this, 'queue_order_sync'));
        
        // Action Scheduler хуки
        add_action('tablecrm_process_order', array($this, 'process_order_async'));
        add_action('tablecrm_process_cf7_lead', array($this, 'process_cf7_lead_async'));
        add_action('tablecrm_update_order', array($this, 'process_order_update_async'));
        
        // Хуки для Contact Form 7
        add_action('wpcf7_mail_sent', array($this, 'send_cf7_to_tablecrm'));
        
        // AJAX хуки
        add_action('wp_ajax_test_tablecrm_connection_fixed_v3', array($this, 'test_connection'));
        add_action('wp_ajax_nopriv_test_tablecrm_connection_fixed_v3', array($this, 'test_connection'));
        
        // Хук для сохранения UTM данных
        add_action('woocommerce_checkout_create_order', array($this, 'save_utm_data_to_order'), 10, 2);
        
        // Хук очистки старых задач
        add_action('wp_loaded', array($this, 'schedule_cleanup'));
    }
    
    public function init() {
        load_plugin_textdomain('tablecrm-integration-fixed-v3', false, dirname(plugin_basename(__FILE__)) . '/languages');
        
        // Проверяем наличие Action Scheduler
        if (!function_exists('as_schedule_single_action')) {
            add_action('admin_notices', array($this, 'missing_action_scheduler_notice'));
            return;
        }
        
        // Добавляем скрипт для отслеживания UTM на фронтенде
        add_action('wp_enqueue_scripts', array($this, 'enqueue_utm_tracking_script'));
    }
    
    // Уведомление об отсутствии Action Scheduler
    public function missing_action_scheduler_notice() {
        ?>
        <div class="notice notice-error">
            <p><strong>TableCRM Integration v3:</strong> Требуется WooCommerce ≥ 8.5 для работы Action Scheduler. Пожалуйста, обновите WooCommerce.</p>
        </div>
        <?php
    }
    
    // Подключение скрипта отслеживания UTM
    public function enqueue_utm_tracking_script() {
        wp_enqueue_script('tablecrm-utm-tracking-fixed-v3', TABLECRM_FIXED_V3_PLUGIN_URL . 'utm-tracking.js', array('jquery'), TABLECRM_FIXED_V3_VERSION, true);
    }
    
    // Добавление меню в админку
    public function add_admin_menu() {
        add_options_page(
            'TableCRM Integration Settings (Fixed v3)',
            'TableCRM Integration (Fixed v3)',
            'manage_options',
            'tablecrm-fixed-v3-settings',
            array($this, 'admin_page')
        );
    }
    
    // Регистрация настроек с санитизацией
    public function register_settings() {
        // API URL - санитизация URL
        register_setting('tablecrm_fixed_v3_settings', 'tablecrm_fixed_v3_api_url', array(
            'type' => 'string',
            'sanitize_callback' => array($this, 'sanitize_api_url'),
            'default' => 'https://app.tablecrm.com/api/v1/'
        ));
        
        // API Key - санитизация текстового поля (ключ)
        register_setting('tablecrm_fixed_v3_settings', 'tablecrm_fixed_v3_api_key', array(
            'type' => 'string',
            'sanitize_callback' => array($this, 'sanitize_api_key'),
            'default' => ''
        ));
        
        // Project ID - санитизация числа
        register_setting('tablecrm_fixed_v3_settings', 'tablecrm_fixed_v3_project_id', array(
            'type' => 'integer',
            'sanitize_callback' => 'absint',
            'default' => 0
        ));
        
        // Organization ID - санитизация числа
        register_setting('tablecrm_fixed_v3_settings', 'tablecrm_fixed_v3_organization_id', array(
            'type' => 'integer',
            'sanitize_callback' => 'absint',
            'default' => 38
        ));
        
        // Cashbox ID - санитизация числа
        register_setting('tablecrm_fixed_v3_settings', 'tablecrm_fixed_v3_cashbox_id', array(
            'type' => 'integer',
            'sanitize_callback' => 'absint',
            'default' => 218
        ));
        
        // Warehouse ID - санитизация числа
        register_setting('tablecrm_fixed_v3_settings', 'tablecrm_fixed_v3_warehouse_id', array(
            'type' => 'integer',
            'sanitize_callback' => 'absint',
            'default' => 39
        ));
        
        // Unit ID - санитизация числа
        register_setting('tablecrm_fixed_v3_settings', 'tablecrm_fixed_v3_unit_id', array(
            'type' => 'integer',
            'sanitize_callback' => 'absint',
            'default' => 116
        ));
        
        // Булевы настройки - санитизация чекбоксов
        register_setting('tablecrm_fixed_v3_settings', 'tablecrm_fixed_v3_enable_orders', array(
            'type' => 'boolean',
            'sanitize_callback' => array($this, 'sanitize_checkbox'),
            'default' => true
        ));
        
        register_setting('tablecrm_fixed_v3_settings', 'tablecrm_fixed_v3_enable_cf7', array(
            'type' => 'boolean',
            'sanitize_callback' => array($this, 'sanitize_checkbox'),
            'default' => false
        ));
        
        register_setting('tablecrm_fixed_v3_settings', 'tablecrm_fixed_v3_check_duplicates', array(
            'type' => 'boolean',
            'sanitize_callback' => array($this, 'sanitize_checkbox'),
            'default' => true
        ));
        
        register_setting('tablecrm_fixed_v3_settings', 'tablecrm_fixed_v3_debug_mode', array(
            'type' => 'boolean',
            'sanitize_callback' => array($this, 'sanitize_checkbox'),
            'default' => true
        ));
        
        register_setting('tablecrm_fixed_v3_settings', 'tablecrm_fixed_v3_send_real_data_only', array(
            'type' => 'boolean',
            'sanitize_callback' => array($this, 'sanitize_checkbox'),
            'default' => true
        ));
        
        register_setting('tablecrm_fixed_v3_settings', 'tablecrm_fixed_v3_retry_failed', array(
            'type' => 'boolean',
            'sanitize_callback' => array($this, 'sanitize_checkbox'),
            'default' => true
        ));
        
        // Массив статусов заказов - санитизация массива
        register_setting('tablecrm_fixed_v3_settings', 'tablecrm_fixed_v3_order_statuses', array(
            'type' => 'array',
            'sanitize_callback' => array($this, 'sanitize_order_statuses'),
            'default' => array('completed', 'processing')
        ));
    }
    
    // Страница настроек
    public function admin_page() {
        ?>
        <div class="wrap">
            <h1>TableCRM Integration Settings (Fixed v3 - v1.0.11 Enhanced Deduplication)</h1>
            <div class="notice notice-success">
                <p><strong>🚀 Версия с Action Scheduler!</strong></p>
                <p>✅ Надежная защита от дублирования заказов</p>
                <p>✅ Идемпотентность через уникальные action_id</p>
                <p>✅ Автоматические повторы при ошибках 5xx</p>
                <p>✅ Хранение очереди в базе данных</p>
            </div>
            <form method="post" action="options.php">
                <?php
                settings_fields('tablecrm_fixed_v3_settings');
                do_settings_sections('tablecrm_fixed_v3_settings');
                ?>
                <table class="form-table">
                    <tr>
                        <th scope="row">API URL</th>
                        <td>
                            <input type="url" name="tablecrm_fixed_v3_api_url" value="<?php echo esc_attr(get_option('tablecrm_fixed_v3_api_url', 'https://app.tablecrm.com/api/v1/')); ?>" class="regular-text" />
                            <p class="description">URL API TableCRM</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">API Key</th>
                        <td>
                            <input type="password" name="tablecrm_fixed_v3_api_key" value="<?php echo esc_attr(get_option('tablecrm_fixed_v3_api_key')); ?>" class="regular-text" />
                            <p class="description">Ваш API ключ TableCRM</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Organization ID</th>
                        <td>
                            <input type="number" name="tablecrm_fixed_v3_organization_id" value="<?php echo esc_attr(get_option('tablecrm_fixed_v3_organization_id', 38)); ?>" class="regular-text" />
                            <p class="description">ID организации в TableCRM</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Cashbox ID</th>
                        <td>
                            <input type="number" name="tablecrm_fixed_v3_cashbox_id" value="<?php echo esc_attr(get_option('tablecrm_fixed_v3_cashbox_id', 218)); ?>" class="regular-text" />
                            <p class="description">ID кассы в TableCRM</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Warehouse ID</th>
                        <td>
                            <input type="number" name="tablecrm_fixed_v3_warehouse_id" value="<?php echo esc_attr(get_option('tablecrm_fixed_v3_warehouse_id', 39)); ?>" class="regular-text" />
                            <p class="description">ID склада в TableCRM</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Unit ID</th>
                        <td>
                            <input type="number" name="tablecrm_fixed_v3_unit_id" value="<?php echo esc_attr(get_option('tablecrm_fixed_v3_unit_id', 116)); ?>" class="regular-text" />
                            <p class="description">ID единицы измерения в TableCRM</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row" style="background-color: #ffffcc;">Отправлять только реальные данные</th>
                        <td style="background-color: #ffffcc;">
                            <input type="checkbox" name="tablecrm_fixed_v3_send_real_data_only" value="1" <?php checked(1, get_option('tablecrm_fixed_v3_send_real_data_only', 1)); ?> />
                            <label><strong>ВКЛЮЧЕНО: Отправлять только заказы с полными данными (имя, email, сумма > 0)</strong></label>
                            <p class="description" style="color: red;">⚠️ Это предотвращает отправку пустых/тестовых заказов в CRM</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Отправлять заказы WooCommerce</th>
                        <td>
                            <input type="checkbox" name="tablecrm_fixed_v3_enable_orders" value="1" <?php checked(1, get_option('tablecrm_fixed_v3_enable_orders', 1)); ?> />
                            <label>Включить отправку заказов</label>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row" style="background-color: #e8f5e8;">Повторять неудачные попытки</th>
                        <td style="background-color: #e8f5e8;">
                            <input type="checkbox" name="tablecrm_fixed_v3_retry_failed" value="1" <?php checked(1, get_option('tablecrm_fixed_v3_retry_failed', 1)); ?> />
                            <label><strong>ВКЛЮЧЕНО: Автоматически повторять отправку при ошибках 5xx</strong></label>
                            <p class="description">Action Scheduler будет повторять отправку до 3 раз с интервалами</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Режим отладки</th>
                        <td>
                            <input type="checkbox" name="tablecrm_fixed_v3_debug_mode" value="1" <?php checked(1, get_option('tablecrm_fixed_v3_debug_mode', 1)); ?> />
                            <label>Включить подробное логирование</label>
                        </td>
                    </tr>
                </table>
                
                <p class="submit">
                    <input type="submit" name="submit" id="submit" class="button button-primary" value="Сохранить настройки" />
                    <button type="button" id="test-connection-fixed-v3" class="button">Тестировать соединение (Fixed v3)</button>
                </p>
            </form>
            
            <div id="test-result-fixed-v3" style="margin-top: 20px;"></div>
            
            <?php if (function_exists('as_get_scheduled_actions')): ?>
            <h2>Статус очереди Action Scheduler</h2>
            <?php
            $pending_actions = as_get_scheduled_actions(array(
                'hook' => 'tablecrm_process_order',
                'status' => 'pending',
                'per_page' => 10
            ));
            $failed_actions = as_get_scheduled_actions(array(
                'hook' => 'tablecrm_process_order', 
                'status' => 'failed',
                'per_page' => 5
            ));
            ?>
            <p><strong>Задач в очереди:</strong> <?php echo count($pending_actions); ?></p>
            <p><strong>Неудачных задач:</strong> <?php echo count($failed_actions); ?></p>
            <?php endif; ?>
            
            <script>
            jQuery(document).ready(function($) {
                $('#test-connection-fixed-v3').click(function() {
                    var button = $(this);
                    var result = $('#test-result-fixed-v3');
                    
                    button.prop('disabled', true).text('Тестируем...');
                    result.html('<p>Проверяем соединение с TableCRM API...</p>');
                    
                    $.ajax({
                        url: ajaxurl,
                        method: 'POST',
                        data: {
                            action: 'test_tablecrm_connection_fixed_v3',
                            nonce: '<?php echo wp_create_nonce("test_tablecrm_fixed_v3"); ?>'
                        },
                        success: function(response) {
                            if(response.success) {
                                result.html('<div class="notice notice-success"><p><strong>✅ Соединение успешно!</strong><br>' + response.data.message + '</p></div>');
                            } else {
                                result.html('<div class="notice notice-error"><p><strong>❌ Ошибка соединения:</strong><br>' + response.data.message + '</p></div>');
                            }
                        },
                        error: function() {
                            result.html('<div class="notice notice-error"><p><strong>❌ Ошибка AJAX запроса</strong></p></div>');
                        },
                        complete: function() {
                            button.prop('disabled', false).text('Тестировать соединение (Fixed v3)');
                        }
                    });
                });
            });
            </script>
        </div>
        <?php
    }
    
    // Тестирование соединения
    public function test_connection() {
        // КРИТИЧЕСКАЯ ЗАЩИТА: Проверяем права доступа и nonce
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Недостаточно прав доступа');
            return;
        }
        
        if (!isset($_POST['nonce']) || !wp_verify_nonce(sanitize_text_field($_POST['nonce']), 'test_tablecrm_fixed_v3')) {
            wp_send_json_error('Неверный токен безопасности');
            return;
        }
        
        // УЛУЧШЕННАЯ САНИТИЗАЦИЯ: Получаем настройки с дополнительной валидацией
        $api_url = get_option('tablecrm_fixed_v3_api_url', 'https://app.tablecrm.com/api/v1/');
        $api_key = get_option('tablecrm_fixed_v3_api_key');
        
        // ЗАЩИТА ОТ XSS/SQLi: Дополнительная валидация длины и формата
        if (empty($api_url) || empty($api_key)) {
            wp_send_json_error('Пожалуйста, заполните обязательные поля (API URL и API Key)');
            return;
        }
        
        // ВАЛИДАЦИЯ API KEY: Проверяем длину и формат (минимум 32 символа, только hex)
        if (strlen($api_key) < 32) {
            wp_send_json_error('API Key должен содержать минимум 32 символа');
            return;
        }
        
        // ВАЛИДАЦИЯ URL: Проверяем корректность формата URL
        if (!filter_var($api_url, FILTER_VALIDATE_URL)) {
            wp_send_json_error('Некорректный формат API URL');
            return;
        }
        
        // ПРОВЕРКА HTTPS: Убеждаемся что используется защищенное соединение
        if (substr($api_url, 0, 8) !== 'https://') {
            wp_send_json_error('API URL должен использовать HTTPS протокол');
            return;
        }

        $response = $this->make_api_request('health', array());
        
        if ($response['success']) {
            wp_send_json_success('Соединение установлено успешно! Action Scheduler готов к работе.');
        } else {
            wp_send_json_error('Ошибка соединения: ' . $response['message']);
        }
    }
    
    // ОСНОВНАЯ ФУНКЦИЯ: Постановка заказа в очередь (ОПТИМИЗИРОВАНО)
    public function queue_order_sync($order_id) {
        if (!function_exists('as_schedule_single_action')) {
            $this->log_error("Action Scheduler недоступен. Заказ $order_id не поставлен в очередь.");
            return;
        }
        
        if (!get_option('tablecrm_fixed_v3_enable_orders', 1)) {
            return;
        }
        
        // КРИТИЧЕСКАЯ ЗАЩИТА: Проверяем, был ли заказ уже обработан
        $existing_doc_id = get_post_meta($order_id, '_tablecrm_v3_document_id', true);
        if (!empty($existing_doc_id)) {
            $this->log_info("Заказ $order_id уже обработан (Document ID: $existing_doc_id). Пропускаем.");
            return;
        }
        
        // КРИТИЧЕСКАЯ ЗАЩИТА: Быстрая проверка очереди (ОПТИМИЗИРОВАНО)
        if (as_has_scheduled_action('tablecrm_process_order', array('order_id' => $order_id))) {
            $this->log_info("Задача для заказа $order_id уже в очереди. Пропускаем дублирование.");
            return;
        }
        
        // КРИТИЧЕСКАЯ ЗАЩИТА: Атомарное создание маркера (ИСПРАВЛЕНО ОКОНЧАТЕЛЬНО)
        $processing_key = "_tablecrm_v3_processing";
        $current_time = time();
        
        // УЛУЧШЕНИЕ: Проверяем существующий маркер на предмет зависания
        $existing_processing_time = get_post_meta($order_id, $processing_key, true);
        if (!empty($existing_processing_time)) {
            $time_diff = $current_time - intval($existing_processing_time);
            // Если маркер старше 5 минут - считаем процесс зависшим и очищаем
            if ($time_diff > 300) { // 5 минут = 300 секунд
                $this->log_warning("Найден зависший маркер обработки для заказа $order_id (возраст: {$time_diff}с). Очищаем.");
                delete_post_meta($order_id, $processing_key);
            } else {
                $this->log_warning("Заказ $order_id уже обрабатывается другим процессом (возраст маркера: {$time_diff}с). Пропускаем.");
                return;
            }
        }
        
        // АТОМАРНАЯ УСТАНОВКА МАРКЕРА: только один процесс может установить маркер
        if (!add_post_meta($order_id, $processing_key, $current_time, true)) {
            $this->log_warning("Заказ $order_id уже обрабатывается другим процессом. Пропускаем.");
            return;
        }
        
        // Создаем уникальный идентификатор группы для идемпотентности
        $group_id = "tablecrm_order_{$order_id}_" . md5($order_id . time());
        
        // Ставим задачу в очередь Action Scheduler
        $action_id = as_enqueue_async_action(
            'tablecrm_process_order',
            array('order_id' => $order_id),
            $group_id // Уникальная группа для идемпотентности
        );
        
        if ($action_id) {
            update_post_meta($order_id, '_tablecrm_v3_action_id', $action_id);
            $this->log_success("Заказ $order_id поставлен в очередь Action Scheduler. Action ID: $action_id");
        } else {
            delete_post_meta($order_id, $processing_key);
            $this->log_error("Не удалось поставить заказ $order_id в очередь Action Scheduler");
        }
    }
    
    // ОБРАБОТЧИК АСИНХРОННОЙ ЗАДАЧИ
    public function process_order_async($order_id) {
        $this->log_info("=== НАЧАЛО АСИНХРОННОЙ ОБРАБОТКИ ЗАКАЗА $order_id ===");
        
        // Проверяем, не был ли заказ уже обработан за время ожидания в очереди
        $existing_doc_id = get_post_meta($order_id, '_tablecrm_v3_document_id', true);
        if (!empty($existing_doc_id)) {
            $this->log_info("Заказ $order_id уже обработан (Document ID: $existing_doc_id). Завершаем задачу.");
            $this->cleanup_processing_meta($order_id);
            return;
        }
        
        $order = wc_get_order($order_id);
        if (!$order) {
            $this->log_error("Заказ $order_id не найден в WooCommerce");
            $this->cleanup_processing_meta($order_id);
            return;
        }
        
        try {
            $result = $this->send_order_to_tablecrm($order_id);
            
            if ($result) {
                $this->log_success("Заказ $order_id успешно обработан через Action Scheduler");
            } else {
                // Если включены повторы, генерируем ошибку для Action Scheduler
                if (get_option('tablecrm_fixed_v3_retry_failed', 1)) {
                    throw new Exception("Не удалось отправить заказ $order_id в TableCRM");
                }
            }
        } catch (Exception $e) {
            $this->log_error("Ошибка при обработке заказа $order_id: " . $e->getMessage());
            $this->cleanup_processing_meta($order_id);
            throw $e; // Передаем ошибку Action Scheduler для повтора
        }
        
        $this->cleanup_processing_meta($order_id);
        $this->log_info("=== ЗАВЕРШЕНИЕ АСИНХРОННОЙ ОБРАБОТКИ ЗАКАЗА $order_id ===");
    }
    
    // ОБРАБОТЧИК АСИНХРОННОЙ ЗАДАЧИ ДЛЯ ЛИДОВ CF7
    public function process_cf7_lead_async($lead_data, $lead_hash) {
        $this->log_info("=== НАЧАЛО АСИНХРОННОЙ ОБРАБОТКИ CF7 ЛИДА (lead_hash: $lead_hash) ===");
        
        try {
            $result = $this->send_lead_to_tablecrm($lead_data);
            
            if ($result) {
                $this->log_success("CF7 лид успешно обработан через Action Scheduler (lead_hash: $lead_hash)");
                // Очищаем блокировку после успешной обработки
                delete_transient("tablecrm_cf7_processing_{$lead_hash}");
            } else {
                // Если включены повторы, генерируем ошибку для Action Scheduler
                if (get_option('tablecrm_fixed_v3_retry_failed', 1)) {
                    throw new Exception("Не удалось отправить CF7 лид в TableCRM (lead_hash: $lead_hash)");
                }
                delete_transient("tablecrm_cf7_processing_{$lead_hash}");
            }
        } catch (Exception $e) {
            $this->log_error("Ошибка при обработке CF7 лида (lead_hash: $lead_hash): " . $e->getMessage());
            delete_transient("tablecrm_cf7_processing_{$lead_hash}");
            throw $e; // Передаем ошибку Action Scheduler для повтора
        }
        
        $this->log_info("=== ЗАВЕРШЕНИЕ АСИНХРОННОЙ ОБРАБОТКИ CF7 ЛИДА (lead_hash: $lead_hash) ===");
    }
    
    // ОБРАБОТЧИК АСИНХРОННОЙ ЗАДАЧИ ДЛЯ ОБНОВЛЕНИЯ ЗАКАЗОВ
    public function process_order_update_async($order_id, $update_data, $status) {
        $this->log_info("=== НАЧАЛО АСИНХРОННОГО ОБНОВЛЕНИЯ ЗАКАЗА $order_id (статус: $status) ===");
        
        try {
            $result = $this->send_order_update_to_tablecrm($order_id, $update_data);
            
            if ($result) {
                $this->log_success("Заказ $order_id успешно обновлен через Action Scheduler (статус: $status)");
                // Очищаем маркер обработки после успешного обновления
                delete_post_meta($order_id, '_tablecrm_v3_update_processing');
            } else {
                // Если включены повторы, генерируем ошибку для Action Scheduler
                if (get_option('tablecrm_fixed_v3_retry_failed', 1)) {
                    throw new Exception("Не удалось обновить заказ $order_id в TableCRM (статус: $status)");
                }
                delete_post_meta($order_id, '_tablecrm_v3_update_processing');
            }
        } catch (Exception $e) {
            $this->log_error("Ошибка при обновлении заказа $order_id (статус: $status): " . $e->getMessage());
            delete_post_meta($order_id, '_tablecrm_v3_update_processing');
            throw $e; // Передаем ошибку Action Scheduler для повтора
        }
        
        $this->log_info("=== ЗАВЕРШЕНИЕ АСИНХРОННОГО ОБНОВЛЕНИЯ ЗАКАЗА $order_id (статус: $status) ===");
    }
    
    // Очистка временных мета-данных (БЕЗОПАСНАЯ ВЕРСИЯ)
    private function cleanup_processing_meta($order_id) {
        // Удаляем маркер обработки создания заказа
        delete_post_meta($order_id, '_tablecrm_v3_processing');
        
        // Удаляем маркер обработки обновления заказа
        delete_post_meta($order_id, '_tablecrm_v3_update_processing');
        
        // БЕЗОПАСНАЯ ОЧИСТКА: Удаляем старые маркеры через прямой prepare() запрос
        global $wpdb;
        
        // Удаляем старые маркеры с timestamp (для совместимости) - HPOS-безопасно
        $deleted_legacy = $wpdb->query(
            $wpdb->prepare(
                "DELETE FROM {$wpdb->postmeta} 
                 WHERE post_id = %d 
                 AND meta_key REGEXP %s",
                $order_id,
                '^_tablecrm_v3_processing_[0-9]+$'
            )
        );
        
        if ($deleted_legacy > 0) {
            $this->log_info("Очищено $deleted_legacy старых маркеров обработки для заказа $order_id");
        }
    }
    
    // Отправка заказа WooCommerce в TableCRM (УЛУЧШЕННАЯ ВЕРСИЯ)
    public function send_order_to_tablecrm($order_id) {
        $this->log_info("=== ОТПРАВКА ЗАКАЗА $order_id В TABLECRM (v3) ===");
        
        $order = wc_get_order($order_id);
        if (!$order) {
            $this->log_error("Заказ $order_id не найден");
            return false;
        }
        
        // ФИНАЛЬНАЯ ПРОВЕРКА НА ДУБЛИРОВАНИЕ
        $existing_doc_id = get_post_meta($order_id, '_tablecrm_v3_document_id', true);
        if (!empty($existing_doc_id)) {
            $this->log_info("Заказ $order_id уже обработан. Document ID: $existing_doc_id");
            return true; // Считаем успехом, так как заказ уже отправлен
        }
        
        // Проверка на черновики и мусор
        if (in_array($order->get_status(), array('auto-draft', 'draft', 'trash'))) {
            $this->log_warning("Заказ $order_id пропущен - статус '" . $order->get_status() . "' (системный)");
            return true; // Не считаем ошибкой
        }
        
        // Собираем данные заказа
        $billing_first_name = $order->get_billing_first_name();
        $billing_last_name = $order->get_billing_last_name();
        $billing_email = $order->get_billing_email();
        $billing_phone = $order->get_billing_phone();
        $order_total = $order->get_total();
        
        // ВАЛИДАЦИЯ ДАННЫХ
        $validation_errors = array();
        
        if (empty($billing_first_name) && empty($billing_last_name)) {
            $validation_errors[] = "Отсутствует имя клиента";
        }
        
        if (empty($billing_email)) {
            $validation_errors[] = "Отсутствует email клиента";
        }
        
        if ($order_total <= 0) {
            $validation_errors[] = "Сумма заказа равна нулю или отрицательная ($order_total)";
        }
        
        // Проверка настройки валидации
        if (get_option('tablecrm_fixed_v3_send_real_data_only', 1) && !empty($validation_errors)) {
            $this->log_error("Заказ $order_id НЕ ОТПРАВЛЕН - ошибки валидации:");
            foreach ($validation_errors as $error) {
                $this->log_error("  - $error");
            }
            return true; // Не считаем ошибкой, просто не отправляем
        }
        
        // Формируем данные
        $data = array(
            'name' => trim($billing_first_name . ' ' . $billing_last_name),
            'email' => $billing_email,
            'phone' => $billing_phone,
            'order_id' => $order_id,
            'order_total' => $order_total,
            'order_status' => $order->get_status(),
            'order_date' => $order->get_date_created() ? $order->get_date_created()->format('Y-m-d H:i:s') : current_time('mysql'),
            'source' => 'WooCommerce Order (Fixed Plugin v3 Action Scheduler)',
            'payment_method' => $order->get_payment_method_title(),
            'payment_status' => $order->is_paid() ? 'paid' : 'unpaid',
            'domain' => isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : '',
            'items' => array()
        );
        
        // Добавляем товары
        foreach ($order->get_items() as $item_id => $item) {
            $product = $item->get_product();
            if ($product) {
                $data['items'][] = array(
                    'name' => $item->get_name(),
                    'quantity' => $item->get_quantity(),
                    'price' => $item->get_total(),
                    'unit_price' => ($item->get_quantity() > 0) ? ($item->get_total() / $item->get_quantity()) : 0,
                    'sku' => $product->get_sku() ?: '',
                    'product_id' => $product->get_id()
                );
            }
        }
        
        $this->log_info("Отправляем заказ $order_id в TableCRM API...");
        $response = $this->make_api_request('docs_sales/', $data, 'AUTO', $order_id);
        
        if ($response['success']) {
            $document_id = $this->extract_document_id($response);
            
            if ($document_id !== 'unknown' && !empty($document_id)) {
                // АТОМАРНОЕ СОХРАНЕНИЕ МАРКЕРА УСПЕШНОЙ ОТПРАВКИ
                update_post_meta($order_id, '_tablecrm_v3_document_id', $document_id);
                update_post_meta($order_id, '_tablecrm_v3_sent_at', current_time('mysql'));
                
                $this->log_success("Заказ $order_id успешно отправлен. Document ID: $document_id");
                return true;
            } else {
                $this->log_error("Заказ $order_id отправлен, но не удалось получить Document ID");
                return false;
            }
        } else {
            $this->log_error("Ошибка отправки заказа $order_id: " . $response['message']);
            return false;
        }
    }
    
    // Извлечение Document ID из ответа
    private function extract_document_id($response) {
        if (isset($response['document_id'])) {
            return $response['document_id'];
        } elseif (isset($response['lead_id'])) {
            return $response['lead_id'];
        } elseif (isset($response['data']) && is_array($response['data']) && !empty($response['data'][0]['id'])) {
            return $response['data'][0]['id'];
        }
        return 'unknown';
    }
    
    // API запросы с улучшенной идемпотентностью (ИСПРАВЛЕНО)
    private function make_api_request($endpoint, $data = array(), $method = 'AUTO', $order_id = null, $lead_hash = null) {
        $api_url = get_option('tablecrm_fixed_v3_api_url', 'https://app.tablecrm.com/api/v1/');
        $api_key = get_option('tablecrm_fixed_v3_api_key');
        
        if (empty($api_url) || empty($api_key)) {
            return array('success' => false, 'message' => 'Не настроены API параметры');
        }
        
        $url = rtrim($api_url, '/') . '/' . ltrim($endpoint, '/') . '?token=' . $api_key;
        
        // Автоопределение HTTP метода
        if ($method === 'AUTO') {
            if ($endpoint === 'health') {
                $method = 'GET';
            } elseif (strpos($endpoint, 'documents/') === 0 && !empty($data)) {
                $method = 'PUT'; // Обновление существующего документа
            } elseif ($endpoint === 'leads') {
                $method = 'POST'; // Создание лида
            } else {
                $method = 'POST'; // По умолчанию POST для создания
            }
        }
        
        $headers = array(
            'Content-Type' => 'application/json',
            'User-Agent' => 'WordPress-TableCRM-Integration-v3/1.0.12'
        );
        
        // УЛУЧШЕННАЯ ИДЕМПОТЕНТНОСТЬ: Добавляем Idempotency-Key для всех типов запросов
        if (!empty($order_id)) {
            $headers['Idempotency-Key'] = 'order-' . $order_id;
            $this->log_info("Добавлен Idempotency-Key: order-$order_id");
        } elseif (!empty($lead_hash)) {
            $headers['Idempotency-Key'] = 'lead-' . $lead_hash;
            $this->log_info("Добавлен Idempotency-Key: lead-$lead_hash");
        }
        
        $args = array(
            'method' => $method,
            'headers' => $headers,
            'timeout' => 30,
            'sslverify' => true
        );
        
        // Добавляем тело запроса для POST и PUT
        if (in_array($method, array('POST', 'PUT')) && !empty($data)) {
            if ($endpoint === 'docs_sales/' || $endpoint === 'docs_sales') {
                // Для создания заказов используем специальную адаптацию
                $args['body'] = json_encode($this->adapt_data_format($endpoint, $data));
            } else {
                // Для остальных API (лиды, обновления) отправляем как есть
                $args['body'] = json_encode($data);
            }
        }
        
        $this->log_info("API запрос: $method $url");
        if (!empty($args['body'])) {
            $this->log_info("Данные запроса: " . substr($args['body'], 0, 500) . (strlen($args['body']) > 500 ? '...' : ''));
        }
        
        $response = wp_remote_request($url, $args);
        
        if (is_wp_error($response)) {
            $error_message = $response->get_error_message();
            $this->log_error("Ошибка HTTP запроса: $error_message");
            return array('success' => false, 'message' => $error_message);
        }
        
        $status_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        
        $this->log_info("API ответ: HTTP $status_code");
        if (!empty($body)) {
            $this->log_info("Тело ответа: " . substr($body, 0, 500) . (strlen($body) > 500 ? '...' : ''));
        }
        
        if ($status_code >= 200 && $status_code < 300) {
            $decoded = json_decode($body, true);
            return array(
                'success' => true, 
                'data' => $decoded, 
                'document_id' => $this->extract_document_id(array('data' => $decoded)),
                'status_code' => $status_code
            );
        } else {
            $error_message = "HTTP $status_code: " . substr($body, 0, 200);
            $this->log_error("API ошибка: $error_message");
            return array('success' => false, 'message' => $error_message, 'status_code' => $status_code);
        }
    }
    
    // Адаптация данных (улучшенная версия с созданием контрагентов)
    private function adapt_data_format($endpoint, $data) {
        if ($endpoint !== 'docs_sales/') {
            return array();
        }
        
        $organization_id = get_option('tablecrm_fixed_v3_organization_id', 38);
        $cashbox_id = get_option('tablecrm_fixed_v3_cashbox_id', 218);
        $warehouse_id = get_option('tablecrm_fixed_v3_warehouse_id', 39);
        $unit_id = get_option('tablecrm_fixed_v3_unit_id', 116);
        
        // ИСПРАВЛЕНИЕ: Получаем или создаем контрагента для каждого клиента
        $contragent_id = $this->get_or_create_contragent($data);
        if (!$contragent_id) {
            $contragent_id = 330985; // Fallback контрагент только если создание не удалось
            $this->log_warning("Не удалось создать контрагента для клиента {$data['name']}, используется fallback ID: $contragent_id");
        }
        
        // Формируем товары
        $goods = array();
        if (!empty($data['items'])) {
            foreach ($data['items'] as $item) {
                $goods[] = array(
                    'price' => floatval($item['unit_price']),
                    'quantity' => intval($item['quantity']),
                    'unit' => intval($unit_id),
                    'discount' => 0,
                    'sum_discounted' => 0,
                    'nomenclature' => 39697 // Стандартная номенклатура
                );
            }
        } else {
            $goods[] = array(
                'price' => floatval($data['order_total']),
                'quantity' => 1,
                'unit' => intval($unit_id),
                'discount' => 0,
                'sum_discounted' => 0,
                'nomenclature' => 39697
            );
        }
        
        return array(array(
            'dated' => time(),
            'operation' => 'Заказ',
            'comment' => "Заказ #{$data['order_id']} с сайта (Action Scheduler)\nКлиент: {$data['name']}\nEmail: {$data['email']}\nТелефон: {$data['phone']}",
            'tax_included' => true,
            'tax_active' => true,
            'goods' => $goods,
            'settings' => (object)array(),
            'warehouse' => intval($warehouse_id),
            'contragent' => intval($contragent_id), // ИСПРАВЛЕНО: Динамический контрагент
            'paybox' => intval($cashbox_id),
            'organization' => intval($organization_id),
            'status' => false,
            'paid_rubles' => number_format(floatval($data['order_total']), 2, '.', ''),
            'paid_lt' => 0
        ));
    }
    
    // ========================================
    // МЕТОДЫ РАБОТЫ С КОНТРАГЕНТАМИ
    // ========================================
    
    /**
     * Получение или создание контрагента для клиента
     * Сначала ищет существующего контрагента по телефону/email, 
     * если не найден - создает нового
     */
    private function get_or_create_contragent($data) {
        if (empty($data['phone']) && empty($data['email'])) {
            $this->log_warning("Нет телефона и email для поиска/создания контрагента");
            return null;
        }
        
        // Сначала ищем контрагента по телефону
        if (!empty($data['phone'])) {
            $phone = preg_replace('/[^0-9]/', '', $data['phone']); // Убираем все кроме цифр
            $contragent_id = $this->search_contragent_by_phone($phone);
            if ($contragent_id) {
                $this->log_success("Найден существующий контрагент по телефону: $contragent_id");
                return $contragent_id;
            }
        }
        
        // Если не найден по телефону, ищем по email
        if (!empty($data['email'])) {
            $contragent_id = $this->search_contragent_by_email($data['email']);
            if ($contragent_id) {
                $this->log_success("Найден существующий контрагент по email: $contragent_id");
                return $contragent_id;
            }
        }
        
        // Если не найден, создаем нового
        $this->log_info("Контрагент не найден, создаем нового для клиента: {$data['name']}");
        return $this->create_new_contragent($data);
    }
    
    /**
     * Поиск контрагента по телефону через TableCRM API
     */
    private function search_contragent_by_phone($phone) {
        $api_url = get_option('tablecrm_fixed_v3_api_url', 'https://app.tablecrm.com/api/v1/');
        $api_key = get_option('tablecrm_fixed_v3_api_key');
        
        if (empty($api_key)) {
            $this->log_error("API ключ не настроен для поиска контрагента");
            return null;
        }
        
        $search_url = rtrim($api_url, '/') . '/contragents/?token=' . $api_key . 
                     '&limit=100&offset=0&sort=created_at:desc&phone=' . $phone;
        
        $this->log_info("Поиск контрагента по телефону: $phone");
        
        $response = wp_remote_get($search_url, array(
            'timeout' => 30,
            'sslverify' => true,
            'headers' => array(
                'User-Agent' => 'WordPress-TableCRM-Integration-v3/1.0.12'
            )
        ));
        
        if (is_wp_error($response)) {
            $error_message = $response->get_error_message();
            $this->log_error("Ошибка поиска контрагента по телефону: $error_message");
            return null;
        }
        
        $body = wp_remote_retrieve_body($response);
        $status_code = wp_remote_retrieve_response_code($response);
        
        if ($status_code === 200) {
            $decoded = json_decode($body, true);
            if (isset($decoded['results']) && count($decoded['results']) > 0) {
                $contragent_id = intval($decoded['results'][0]['id']);
                $this->log_success("Найден контрагент по телефону $phone: ID $contragent_id");
                return $contragent_id;
            }
        } else {
            $this->log_warning("API ответ при поиске контрагента: HTTP $status_code");
        }
        
        return null;
    }
    
    /**
     * Поиск контрагента по email через TableCRM API
     */
    private function search_contragent_by_email($email) {
        $api_url = get_option('tablecrm_fixed_v3_api_url', 'https://app.tablecrm.com/api/v1/');
        $api_key = get_option('tablecrm_fixed_v3_api_key');
        
        if (empty($api_key)) {
            $this->log_error("API ключ не настроен для поиска контрагента");
            return null;
        }
        
        $search_url = rtrim($api_url, '/') . '/contragents/?token=' . $api_key . 
                     '&limit=100&offset=0&sort=created_at:desc&email=' . urlencode($email);
        
        $this->log_info("Поиск контрагента по email: $email");
        
        $response = wp_remote_get($search_url, array(
            'timeout' => 30,
            'sslverify' => true,
            'headers' => array(
                'User-Agent' => 'WordPress-TableCRM-Integration-v3/1.0.12'
            )
        ));
        
        if (is_wp_error($response)) {
            $error_message = $response->get_error_message();
            $this->log_error("Ошибка поиска контрагента по email: $error_message");
            return null;
        }
        
        $body = wp_remote_retrieve_body($response);
        $status_code = wp_remote_retrieve_response_code($response);
        
        if ($status_code === 200) {
            $decoded = json_decode($body, true);
            if (isset($decoded['results']) && count($decoded['results']) > 0) {
                $contragent_id = intval($decoded['results'][0]['id']);
                $this->log_success("Найден контрагент по email $email: ID $contragent_id");
                return $contragent_id;
            }
        } else {
            $this->log_warning("API ответ при поиске контрагента: HTTP $status_code");
        }
        
        return null;
    }
    
    /**
     * Создание нового контрагента через TableCRM API
     */
    private function create_new_contragent($data) {
        // Подготавливаем данные для создания контрагента
        $contragent_data = array(
            'name' => isset($data['name']) ? sanitize_text_field($data['name']) : 'Клиент с сайта',
            'email' => isset($data['email']) ? sanitize_email($data['email']) : '',
            'phone' => isset($data['phone']) ? preg_replace('/[^0-9]/', '', $data['phone']) : '',
            'external_id' => isset($data['order_id']) ? 'order_' . $data['order_id'] : '',
            'type' => 'individual', // Тип контрагента - физическое лицо
            'comment' => $this->format_contragent_comment($data)
        );
        
        $this->log_info("Создание нового контрагента: {$contragent_data['name']} ({$contragent_data['email']})");
        
        // Отправляем запрос на создание контрагента
        $response = $this->make_api_request('contragents/', array($contragent_data), 'POST');
        
        if ($response['success'] && isset($response['data']) && is_array($response['data'])) {
            if (isset($response['data'][0]['id'])) {
                $contragent_id = intval($response['data'][0]['id']);
                $this->log_success("Создан новый контрагент: ID $contragent_id для клиента {$contragent_data['name']}");
                return $contragent_id;
            }
        }
        
        $error_message = isset($response['message']) ? $response['message'] : 'Неизвестная ошибка';
        $this->log_error("Ошибка создания контрагента: $error_message");
        return null;
    }
    
    /**
     * Форматирование комментария для контрагента
     */
    private function format_contragent_comment($data) {
        $comment = "Клиент с сайта WordPress\n";
        
        if (isset($data['order_id'])) {
            $comment .= "Первый заказ: #{$data['order_id']}\n";
        }
        
        if (isset($data['phone'])) {
            $comment .= "Телефон: {$data['phone']}\n";
        }
        
        if (isset($data['email'])) {
            $comment .= "Email: {$data['email']}\n";
        }
        
        if (isset($data['shipping_address_1']) || isset($data['shipping_city'])) {
            $comment .= "Адрес: ";
            if (isset($data['shipping_address_1'])) {
                $comment .= $data['shipping_address_1'];
            }
            if (isset($data['shipping_city'])) {
                $comment .= ", " . $data['shipping_city'];
            }
            $comment .= "\n";
        }
        
        $comment .= "Дата регистрации: " . date('Y-m-d H:i:s');
        
        return $comment;
    }
    
    // Планирование очистки старых задач
    public function schedule_cleanup() {
        if (!wp_next_scheduled('tablecrm_cleanup_old_actions')) {
            wp_schedule_event(time(), 'daily', 'tablecrm_cleanup_old_actions');
        }
    }
    
    // Логирование
    private function log($message, $level = 'INFO') {
        if (get_option('tablecrm_fixed_v3_debug_mode', 1)) {
            $timestamp = current_time('Y-m-d H:i:s');
            error_log("[$timestamp] [TableCRM v3] [$level] $message");
        }
    }
    
    private function log_info($message) { $this->log($message, 'INFO'); }
    private function log_success($message) { $this->log($message, 'SUCCESS'); }
    private function log_warning($message) { $this->log($message, 'WARNING'); }
    private function log_error($message) { $this->log($message, 'ERROR'); }
    
    // ========================================
    // МЕТОДЫ САНИТИЗАЦИИ НАСТРОЕК
    // ========================================
    
    /**
     * Санитизация API URL
     * Проверяет корректность URL и защищает от XSS
     */
    public function sanitize_api_url($value) {
        if (empty($value)) {
            return 'https://app.tablecrm.com/api/v1/';
        }
        
        // Убираем все HTML теги и санитизируем как URL
        $value = wp_strip_all_tags($value);
        $value = esc_url_raw($value);
        
        // Проверяем что URL начинается с https://
        if (!filter_var($value, FILTER_VALIDATE_URL) || 
            (substr($value, 0, 8) !== 'https://' && substr($value, 0, 7) !== 'http://')) {
            
            $this->log_warning("Некорректный API URL: $value. Используется значение по умолчанию.");
            return 'https://app.tablecrm.com/api/v1/';
        }
        
        // Добавляем / в конец если его нет
        return rtrim($value, '/') . '/';
    }
    
    /**
     * Санитизация API ключа с улучшенной валидацией (ИСПРАВЛЕНО)
     * Защищает от SQL инъекций и XSS
     */
    public function sanitize_api_key($value) {
        if (empty($value)) {
            return '';
        }
        
        // Убираем все HTML теги и пробелы
        $value = wp_strip_all_tags(trim($value));
        
        // ДОПОЛНИТЕЛЬНАЯ ВАЛИДАЦИЯ: Проверяем длину токена (минимум 32 символа)
        if (strlen($value) < 32) {
            $this->log_warning("API Key слишком короткий (требуется минимум 32 символа). Используется предыдущее значение.");
            return get_option('tablecrm_fixed_v3_api_key', ''); // Возвращаем предыдущее значение
        }
        
        // ЗАЩИТА ОТ INJECTION: Санитизируем как текстовое поле с alphanumeric символами
        $value = sanitize_text_field($value);
        
        // ДОПОЛНИТЕЛЬНАЯ ФИЛЬТРАЦИЯ: Убираем всё кроме букв, цифр и базовых символов
        $value = preg_replace('/[^a-zA-Z0-9\-_]/', '', $value);
        
        return $value;
    }
    
    /**
     * Санитизация чекбоксов
     * Гарантирует только boolean значения
     */
    public function sanitize_checkbox($value) {
        return !empty($value) ? 1 : 0;
    }
    
    /**
     * Санитизация массива статусов заказов
     * Защищает от инъекций в массиве
     */
    public function sanitize_order_statuses($value) {
        if (!is_array($value)) {
            return array('completed', 'processing');
        }
        
        $sanitized = array();
        $allowed_statuses = array(
            'pending', 'processing', 'on-hold', 'completed', 
            'cancelled', 'refunded', 'failed', 'checkout-draft'
        );
        
        foreach ($value as $status) {
            $status = sanitize_key($status);
            if (in_array($status, $allowed_statuses)) {
                $sanitized[] = $status;
            }
        }
        
        return empty($sanitized) ? array('completed', 'processing') : $sanitized;
    }
    
    /**
     * Дополнительная валидация при сохранении
     * Запускается при каждом сохранении настроек
     */
    public function validate_and_log_settings() {
        $api_url = get_option('tablecrm_fixed_v3_api_url');
        $api_key = get_option('tablecrm_fixed_v3_api_key');
        
        // Логируем важные изменения для безопасности
        if (!empty($api_key) && strlen($api_key) > 10) {
            $this->log_info("Настройки обновлены. API URL: $api_url");
            
            // Маскируем API ключ в логах
            $masked_key = substr($api_key, 0, 4) . '***' . substr($api_key, -4);
            $this->log_info("API ключ обновлен: $masked_key");
        }
        
        // Проверяем критические ID на корректность
        $organization_id = get_option('tablecrm_fixed_v3_organization_id');
        $cashbox_id = get_option('tablecrm_fixed_v3_cashbox_id');
        
        if ($organization_id <= 0) {
            $this->log_warning("Некорректный Organization ID: $organization_id");
        }
        if ($cashbox_id <= 0) {
            $this->log_warning("Некорректный Cashbox ID: $cashbox_id");
        }
    }
    
    // ========================================
    // МЕТОДЫ ДЛЯ РАСШИРЕННОЙ ФУНКЦИОНАЛЬНОСТИ
    // ========================================
    
    /**
     * Отправка лида из Contact Form 7 в TableCRM через Action Scheduler
     * С защитой от дублирования через атомарный маркер
     */
    public function send_cf7_to_tablecrm($contact_form) {
        // Проверяем включена ли интеграция CF7
        if (!get_option('tablecrm_fixed_v3_enable_cf7', false)) {
            $this->log_info("Contact Form 7 интеграция отключена в настройках");
            return false;
        }
        
        // Получаем данные из формы
        $submission = WPCF7_Submission::get_instance();
        if (!$submission) {
            $this->log_error("Не удалось получить данные формы CF7");
            return false;
        }
        
        $posted_data = $submission->get_posted_data();
        $this->log_info("CF7 форма отправлена, данные: " . print_r($posted_data, true));
        
        // Создаем уникальный идентификатор лида на основе данных формы и времени
        $email = isset($posted_data['your-email']) ? sanitize_email($posted_data['your-email']) : '';
        $phone = isset($posted_data['your-phone']) ? sanitize_text_field($posted_data['your-phone']) : '';
        $submission_timestamp = time();
        
        // Создаем хэш на основе данных формы для определения дублей
        $lead_hash = md5($contact_form->id() . $email . $phone . date('Y-m-d H:i', $submission_timestamp));
        
        // Подготавливаем данные для отправки в TableCRM (перенесено вверх для проверки дубликатов)
        $lead_data = array(
            'name' => isset($posted_data['your-name']) ? sanitize_text_field($posted_data['your-name']) : 'Лид с сайта',
            'email' => $email,
            'phone' => $phone,
            'message' => isset($posted_data['your-message']) ? sanitize_textarea_field($posted_data['your-message']) : '',
            'source' => 'Contact Form 7',
            'form_id' => $contact_form->id(),
            'form_title' => $contact_form->title(),
            'utm_source' => isset($_COOKIE['utm_source']) ? sanitize_text_field($_COOKIE['utm_source']) : '',
            'utm_medium' => isset($_COOKIE['utm_medium']) ? sanitize_text_field($_COOKIE['utm_medium']) : '',
            'utm_campaign' => isset($_COOKIE['utm_campaign']) ? sanitize_text_field($_COOKIE['utm_campaign']) : '',
            'utm_term' => isset($_COOKIE['utm_term']) ? sanitize_text_field($_COOKIE['utm_term']) : '',
            'utm_content' => isset($_COOKIE['utm_content']) ? sanitize_text_field($_COOKIE['utm_content']) : '',
            'created_at' => current_time('Y-m-d H:i:s'),
            'lead_hash' => $lead_hash
        );
        
        // КРИТИЧЕСКАЯ ЗАЩИТА: Проверяем, есть ли уже задача в очереди для CF7 лида (ИСПРАВЛЕНО)
        if (as_has_scheduled_action('tablecrm_process_cf7_lead', array($lead_data, $lead_hash))) {
            $this->log_info("CF7 лид с hash $lead_hash уже в очереди. Пропускаем дублирование.");
            return true;
        }
        
        // КРИТИЧЕСКАЯ ЗАЩИТА: Устанавливаем временный маркер обработки CF7 лида
        $option_key = "tablecrm_cf7_processing_{$lead_hash}";
        if (get_transient($option_key)) {
            $this->log_warning("CF7 лид с hash $lead_hash уже обрабатывается. Пропускаем дублирование.");
            return true;
        }
        
        // Устанавливаем блокировку на 5 минут
        set_transient($option_key, time(), 300);
        
        // Убираем пустые поля
        $lead_data = array_filter($lead_data, function($value) {
            return !empty($value);
        });
        
        // Отправляем лид через Action Scheduler
        if (function_exists('as_enqueue_async_action')) {
            // Создаем уникальный идентификатор группы для идемпотентности
            $group_id = "tablecrm_cf7_lead_{$lead_hash}";
            
            $action_id = as_enqueue_async_action(
                'tablecrm_process_cf7_lead',
                array($lead_data, $lead_hash),
                $group_id // Уникальная группа для идемпотентности
            );
            
            if ($action_id) {
                $this->log_success("CF7 лид поставлен в очередь с lead_hash: $lead_hash, action_id: $action_id");
                return true;
            } else {
                delete_transient($option_key);
                $this->log_error("Не удалось поставить CF7 лид в очередь Action Scheduler");
                return false;
            }
        } else {
            // Fallback - отправляем сразу
            $result = $this->send_lead_to_tablecrm($lead_data);
            if (!$result) {
                delete_transient($option_key);
            }
            return $result;
        }
    }
    
    /**
     * Сохранение UTM данных в заказ WooCommerce
     * Сохраняет UTM метки в мета-поля заказа
     */
    public function save_utm_data_to_order($order, $data) {
        if (!is_a($order, 'WC_Order')) {
            $this->log_error("save_utm_data_to_order: Передан некорректный объект заказа");
            return false;
        }
        
        $order_id = $order->get_id();
        $this->log_info("Сохранение UTM данных для заказа #$order_id");
        
        // Получаем UTM данные из cookies или POST данных
        $utm_data = array();
        
        // Проверяем cookies (установленные через utm-tracking.js)
        $utm_fields = array('utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content');
        
        foreach ($utm_fields as $field) {
            $value = '';
            
            // Сначала проверяем POST данные
            if (isset($_POST[$field]) && !empty($_POST[$field])) {
                $value = sanitize_text_field($_POST[$field]);
            }
            // Затем проверяем cookies
            elseif (isset($_COOKIE[$field]) && !empty($_COOKIE[$field])) {
                $value = sanitize_text_field($_COOKIE[$field]);
            }
            // Проверяем $_GET параметры (для прямых ссылок)
            elseif (isset($_GET[$field]) && !empty($_GET[$field])) {
                $value = sanitize_text_field($_GET[$field]);
            }
            
            if (!empty($value)) {
                $utm_data[$field] = $value;
            }
        }
        
        // Дополнительные данные для аналитики
        $utm_data['referrer'] = isset($_SERVER['HTTP_REFERER']) ? esc_url_raw($_SERVER['HTTP_REFERER']) : '';
        $utm_data['landing_page'] = isset($_POST['landing_page']) ? esc_url_raw($_POST['landing_page']) : '';
        $utm_data['user_agent'] = isset($_SERVER['HTTP_USER_AGENT']) ? sanitize_text_field($_SERVER['HTTP_USER_AGENT']) : '';
        $utm_data['ip_address'] = $this->get_client_ip();
        $utm_data['timestamp'] = current_time('Y-m-d H:i:s');
        
        // Сохраняем данные в мета-поля заказа
        $saved_count = 0;
        foreach ($utm_data as $key => $value) {
            if (!empty($value)) {
                $meta_key = '_tablecrm_' . $key;
                $result = update_post_meta($order_id, $meta_key, $value);
                
                if ($result !== false) {
                    $saved_count++;
                    $this->log_info("UTM данные сохранены: $meta_key = $value");
                } else {
                    $this->log_warning("Не удалось сохранить UTM данные: $meta_key");
                }
            }
        }
        
        // Сохраняем общий маркер наличия UTM данных
        if ($saved_count > 0) {
            update_post_meta($order_id, '_tablecrm_utm_data_saved', current_time('Y-m-d H:i:s'));
            $this->log_success("UTM данные сохранены для заказа #$order_id ($saved_count полей)");
            return true;
        } else {
            $this->log_info("Нет UTM данных для сохранения в заказе #$order_id");
            return false;
        }
    }
    
    /**
     * Обновление заказа в TableCRM при изменении статуса
     * С защитой от дублирования через атомарный маркер
     */
    public function update_order_in_tablecrm($order_id) {
        if (empty($order_id) || !is_numeric($order_id)) {
            $this->log_error("update_order_in_tablecrm: Некорректный ID заказа");
            return false;
        }
        
        $order = wc_get_order($order_id);
        if (!$order) {
            $this->log_error("update_order_in_tablecrm: Заказ #$order_id не найден");
            return false;
        }
        
        $this->log_info("Обновление заказа #$order_id в TableCRM, статус: " . $order->get_status());
        
        // КРИТИЧЕСКАЯ ЗАЩИТА: Устанавливаем временный маркер обработки обновления заказа
        $processing_key = "_tablecrm_v3_update_processing";
        $current_time = time();
        
        // УЛУЧШЕНИЕ: Проверяем существующий маркер на предмет зависания
        $existing_processing_time = get_post_meta($order_id, $processing_key, true);
        if (!empty($existing_processing_time)) {
            $time_diff = $current_time - intval($existing_processing_time);
            // Если маркер старше 5 минут - считаем процесс зависшим и очищаем
            if ($time_diff > 300) { // 5 минут = 300 секунд
                $this->log_warning("Найден зависший маркер обновления для заказа $order_id (возраст: {$time_diff}с). Очищаем.");
                delete_post_meta($order_id, $processing_key);
            } else {
                $this->log_warning("Заказ $order_id уже обновляется другим процессом (возраст маркера: {$time_diff}с). Пропускаем.");
                return true;
            }
        }
        
        // АТОМАРНАЯ УСТАНОВКА МАРКЕРА: только один процесс может установить маркер
        if (!add_post_meta($order_id, $processing_key, $current_time, true)) {
            $this->log_warning("Заказ $order_id уже обновляется другим процессом. Пропускаем.");
            return true;
        }
        
        // Проверяем есть ли уже document_id для этого заказа
        $document_id = get_post_meta($order_id, '_tablecrm_v3_document_id', true);
        
        if (empty($document_id)) {
            // Если document_id нет, создаем новый заказ вместо обновления
            delete_post_meta($order_id, $processing_key);
            $this->log_info("Document ID не найден, создаем новый заказ в TableCRM");
            return $this->queue_order_sync($order_id);
        }
        
        // Проверяем, что статус действительно изменился
        $last_status = get_post_meta($order_id, '_tablecrm_v3_last_status', true);
        $current_status = $order->get_status();
        
        if ($last_status === $current_status) {
            delete_post_meta($order_id, $processing_key);
            $this->log_info("Статус заказа #$order_id не изменился ($current_status), обновление не требуется");
            return true;
        }
        
        // Подготавливаем данные для обновления
        $update_data = array(
            'id' => $document_id,
            'status' => $this->map_wc_status_to_tablecrm($current_status),
            'updated_at' => current_time('c'), // ISO 8601 формат
            'notes' => "Обновлено из WooCommerce: статус изменен с '$last_status' на '$current_status'"
        );
        
        // Добавляем данные оплаты если заказ оплачен
        if ($order->is_paid()) {
            $update_data['payment_status'] = 'paid';
            $update_data['payment_method'] = $order->get_payment_method_title();
            $update_data['payment_date'] = $order->get_date_paid() ? $order->get_date_paid()->format('c') : null;
        }
        
        // Отправляем обновление через Action Scheduler
        if (function_exists('as_enqueue_async_action')) {
            // Создаем уникальный идентификатор группы для идемпотентности обновлений
            $group_id = "tablecrm_update_order_{$order_id}_{$current_status}";
            
            $action_id = as_enqueue_async_action(
                'tablecrm_update_order',
                array($order_id, $update_data, $current_status),
                $group_id // Уникальная группа для идемпотентности
            );
            
            if ($action_id) {
                // Сохраняем текущий статус для предотвращения повторных обновлений
                update_post_meta($order_id, '_tablecrm_v3_last_status', $current_status);
                update_post_meta($order_id, '_tablecrm_v3_update_action_id', $action_id);
                $this->log_success("Обновление заказа #$order_id поставлено в очередь с action_id: $action_id");
                return true;
            } else {
                delete_post_meta($order_id, $processing_key);
                $this->log_error("Не удалось поставить обновление заказа #$order_id в очередь Action Scheduler");
                return false;
            }
        } else {
            // Fallback - обновляем сразу
            $result = $this->send_order_update_to_tablecrm($order_id, $update_data);
            if (!$result) {
                delete_post_meta($order_id, $processing_key);
            } else {
                update_post_meta($order_id, '_tablecrm_v3_last_status', $current_status);
                delete_post_meta($order_id, $processing_key);
            }
            return $result;
        }
    }
    
    // ========================================
    // ВСПОМОГАТЕЛЬНЫЕ МЕТОДЫ
    // ========================================
    
    /**
     * Отправка лида в TableCRM
     */
    private function send_lead_to_tablecrm($lead_data) {
        $this->log_info("=== ОТПРАВКА CF7 ЛИДА В TABLECRM ===");
        
        // Создаем хеш для идемпотентности
        $lead_hash = md5(serialize($lead_data) . time());
        
        $response = $this->make_api_request('leads', $lead_data, 'POST', null, $lead_hash);
        
        if ($response['success']) {
            $this->log_success("CF7 лид успешно отправлен в TableCRM (lead_hash: $lead_hash)");
            return true;
        } else {
            $this->log_error("Ошибка отправки CF7 лида: " . $response['message']);
            return false;
        }
    }
    
    /**
     * Отправка обновления заказа в TableCRM
     */
    private function send_order_update_to_tablecrm($order_id, $update_data) {
        $this->log_info("Отправка обновления заказа #$order_id в TableCRM");
        
        $endpoint = 'documents/' . $update_data['id'];
        $response = $this->make_api_request($endpoint, $update_data, 'PUT', $order_id);
        
        if ($response && isset($response['success']) && $response['success']) {
            $this->log_success("Заказ #$order_id успешно обновлен в TableCRM");
            
            // Сохраняем время последнего обновления
            update_post_meta($order_id, '_tablecrm_v3_last_update', current_time('Y-m-d H:i:s'));
            
            return true;
        } else {
            $this->log_error("Ошибка обновления заказа #$order_id в TableCRM: " . print_r($response, true));
            return false;
        }
    }
    
    /**
     * Маппинг статусов WooCommerce в статусы TableCRM
     */
    private function map_wc_status_to_tablecrm($wc_status) {
        $status_map = array(
            'pending' => 'new',
            'processing' => 'processing',
            'completed' => 'completed',
            'cancelled' => 'cancelled',
            'refunded' => 'refunded',
            'failed' => 'failed',
            'on-hold' => 'on_hold'
        );
        
        return isset($status_map[$wc_status]) ? $status_map[$wc_status] : 'unknown';
    }
    
    /**
     * Получение IP адреса клиента с учетом прокси
     */
    private function get_client_ip() {
        $ip_keys = array('HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR');
        
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = explode(',', $ip)[0];
                }
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '127.0.0.1';
    }
}

// Очистка старых задач
add_action('tablecrm_cleanup_old_actions', function() {
    if (function_exists('as_get_scheduled_actions')) {
        $hooks_to_clean = array(
            'tablecrm_process_order',
            'tablecrm_process_cf7_lead', 
            'tablecrm_update_order'
        );
        
        foreach ($hooks_to_clean as $hook) {
            // Удаляем завершенные задачи старше 7 дней
            $old_actions = as_get_scheduled_actions(array(
                'hook' => $hook,
                'status' => 'complete',
                'date_query' => array(
                    'before' => date('Y-m-d H:i:s', strtotime('-7 days'))
                ),
                'per_page' => 100
            ));
            
            foreach ($old_actions as $action) {
                if (function_exists('as_unschedule_action')) {
                    as_unschedule_action($hook, $action->get_args(), $action->get_group());
                }
            }
            
            error_log("[TableCRM v3] [INFO] Очищено " . count($old_actions) . " старых задач для хука: $hook");
        }
    }
});

// Инициализация плагина
function initialize_tablecrm_integration_fixed_v3() {
    global $tablecrm_integration_fixed_v3;
    $tablecrm_integration_fixed_v3 = new TableCRM_Integration_Fixed_V3();
}
add_action('plugins_loaded', 'initialize_tablecrm_integration_fixed_v3');

// Хуки активации/деактивации
register_activation_hook(__FILE__, 'tablecrm_fixed_v3_activation');
register_deactivation_hook(__FILE__, 'tablecrm_fixed_v3_deactivation');

function tablecrm_fixed_v3_activation() {
    // Устанавливаем настройки по умолчанию
    if (!get_option('tablecrm_fixed_v3_api_url')) {
        update_option('tablecrm_fixed_v3_api_url', 'https://app.tablecrm.com/api/v1/');
    }
    if (!get_option('tablecrm_fixed_v3_enable_orders')) {
        update_option('tablecrm_fixed_v3_enable_orders', 1);
    }
    if (!get_option('tablecrm_fixed_v3_send_real_data_only')) {
        update_option('tablecrm_fixed_v3_send_real_data_only', 1);
    }
    if (!get_option('tablecrm_fixed_v3_retry_failed')) {
        update_option('tablecrm_fixed_v3_retry_failed', 1);
    }
    if (!get_option('tablecrm_fixed_v3_debug_mode')) {
        update_option('tablecrm_fixed_v3_debug_mode', 1);
    }
    
    error_log('[TableCRM Integration v3] [SUCCESS] Плагин активирован с Action Scheduler');
}

function tablecrm_fixed_v3_deactivation() {
    // Очищаем запланированные задачи
    wp_clear_scheduled_hook('tablecrm_cleanup_old_actions');
    
    error_log('[TableCRM Integration v3] [INFO] Плагин деактивирован');
}