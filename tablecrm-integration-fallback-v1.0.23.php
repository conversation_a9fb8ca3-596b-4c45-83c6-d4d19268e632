<?php
/**
 * Plugin Name: TableCRM Integration (Fallback v1.0.23 - Dynamic Nomenclature)
 * Description: A fallback version of the TableCRM integration plugin for WordPress and WooCommerce with dynamic nomenclature and contragent creation.
 * Version: 1.0.23
 * Author: Your Name
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

function tablecrm_log_message($message) {
    $log_file = WP_CONTENT_DIR . '/tablecrm_integration.log';
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[{$timestamp}] " . $message . "\n";
    error_log($log_entry, 3, $log_file);
}

// Helper function to get or create Nomenclature ID
if (!function_exists('get_or_create_tablecrm_nomenclature_id')) {
    function get_or_create_tablecrm_nomenclature_id($item_name, $api_url, $api_token) {
        tablecrm_log_message("Начинаем поиск/создание номенклатуры для товара: '{$item_name}'");

        // 1. Search for nomenclature by name
        $search_url = $api_url . 'nomenclature/?token=' . $api_token . '&name=' . urlencode($item_name);
        tablecrm_log_message("URL для поиска номенклатуры: {$search_url}");

        $search_response = wp_remote_get($search_url);

        if (is_wp_error($search_response)) {
            tablecrm_log_message("WP_Error при поиске номенклатуры '{$item_name}': " . $search_response->get_error_message());
            return null;
        }

        $search_body = wp_remote_retrieve_body($search_response);
        $search_data = json_decode($search_body, true);
        
        tablecrm_log_message("Ответ от API при поиске номенклатуры '{$item_name}': " . $search_body);

        if (!empty($search_data) && is_array($search_data) && isset($search_data[0]['id'])) {
            $nomenclature_id = $search_data[0]['id'];
            tablecrm_log_message("Номенклатура '{$item_name}' найдена. ID: {$nomenclature_id}");
            return $nomenclature_id;
        }

        // 2. Create nomenclature if not found
        tablecrm_log_message("Номенклатура '{$item_name}' не найдена. Создаем новую.");
        
        $create_url = $api_url . 'nomenclatures/?token=' . $api_token;
        tablecrm_log_message("URL для создания номенклатуры: {$create_url}");

        $create_body = ['name' => $item_name, 'unit' => 116]; // unit 116 = шт. (default)
        
        $create_response = wp_remote_post($create_url, [
            'method'  => 'POST',
            'headers' => ['Content-Type' => 'application/json; charset=utf-8'],
            'body'    => json_encode($create_body, JSON_UNESCAPED_UNICODE),
        ]);

        if (is_wp_error($create_response)) {
            tablecrm_log_message("WP_Error при создании номенклатуры '{$item_name}': " . $create_response->get_error_message());
            return null;
        }
        
        $create_response_code = wp_remote_retrieve_response_code($create_response);
        $create_response_body = wp_remote_retrieve_body($create_response);
        $create_data = json_decode($create_response_body, true);

        tablecrm_log_message("Ответ от API при создании номенклатуры '{$item_name}' (Code: {$create_response_code}): " . $create_response_body);

        if ($create_response_code >= 200 && $create_response_code < 300 && isset($create_data['id'])) {
            $new_nomenclature_id = $create_data['id'];
            tablecrm_log_message("Новая номенклатура '{$item_name}' успешно создана. ID: {$new_nomenclature_id}");
            return $new_nomenclature_id;
        } else if (is_array($create_data) && isset($create_data[0]['id'])) {
            $new_nomenclature_id = $create_data[0]['id'];
            tablecrm_log_message("Новая номенклатура '{$item_name}' успешно создана (в массиве). ID: {$new_nomenclature_id}");
            return $new_nomenclature_id;
        }

        tablecrm_log_message("Не удалось создать номенклатуру '{$item_name}'. Ответ API: " . $create_response_body);
        return null;
    }
}

// Helper function to get or create Contragent ID
if (!function_exists('get_or_create_tablecrm_contragent_id')) {
    function get_or_create_tablecrm_contragent_id($order, $api_url, $api_token) {
        $phone = $order->get_billing_phone();
        if (empty($phone)) {
            tablecrm_log_message("Телефон клиента в заказе #{$order->get_id()} не найден. Невозможно найти или создать контрагента.");
            return null;
        }

        tablecrm_log_message("Начинаем поиск/создание контрагента для телефона: {$phone}");

        // 1. Search for contragent by phone
        $search_url = $api_url . 'contragents/?token=' . $api_token . '&phone=' . urlencode($phone);
        tablecrm_log_message("URL для поиска контрагента: {$search_url}");

        $search_response = wp_remote_get($search_url);

        if (is_wp_error($search_response)) {
            tablecrm_log_message("WP_Error при поиске контрагента: " . $search_response->get_error_message());
            return null;
        }

        $search_body = wp_remote_retrieve_body($search_response);
        $search_data = json_decode($search_body, true);
        tablecrm_log_message("Ответ от API при поиске контрагента: " . $search_body);

        if (!empty($search_data) && is_array($search_data) && isset($search_data[0]['id'])) {
            $contragent_id = $search_data[0]['id'];
            tablecrm_log_message("Контрагент для телефона {$phone} найден. ID: {$contragent_id}");
            return $contragent_id;
        }

        // 2. Create contragent if not found
        tablecrm_log_message("Контрагент для телефона {$phone} не найден. Создаем нового.");
        $create_url = $api_url . 'contragents/?token=' . $api_token;
        tablecrm_log_message("URL для создания контрагента: {$create_url}");

        $full_name = $order->get_formatted_billing_full_name();
        $email = $order->get_billing_email();

        $create_body_data = [ 'name' => $full_name, 'phone' => $phone, 'email' => $email, 'our_cost' => false ];
        
        $create_response = wp_remote_post($create_url, [
            'method' => 'POST',
            'headers' => ['Content-Type' => 'application/json; charset=utf-8'],
            'body'    => json_encode($create_body_data, JSON_UNESCAPED_UNICODE)
        ]);

        if (is_wp_error($create_response)) {
            tablecrm_log_message("WP_Error при создании контрагента: " . $create_response->get_error_message());
            return null;
        }
        
        $create_response_code = wp_remote_retrieve_response_code($create_response);
        $create_response_body = wp_remote_retrieve_body($create_response);
        $create_data = json_decode($create_response_body, true);

        tablecrm_log_message("Ответ от API при создании контрагента (Code: {$create_response_code}): " . $create_response_body);
        
        if ($create_response_code >= 200 && $create_response_code < 300 && isset($create_data['id'])) {
            $new_contragent_id = $create_data['id'];
            tablecrm_log_message("Новый контрагент '{$full_name}' успешно создан. ID: {$new_contragent_id}");
            return $new_contragent_id;
        } else if (is_array($create_data) && isset($create_data[0]['id'])) {
            $new_contragent_id = $create_data[0]['id'];
            tablecrm_log_message("Новый контрагент '{$full_name}' успешно создан (в массиве). ID: {$new_contragent_id}");
            return $new_contragent_id;
        }

        tablecrm_log_message("Не удалось создать контрагента. Ответ API: " . $create_response_body);
        return null;
    }
}

if (!function_exists('adapt_data_format')) {
    function adapt_data_format($order, $warehouse_id, $organization_id, $paybox_id, $contragent_id, $nomenclature_ids_map) {
        $order_id = $order->get_id();
        $items = $order->get_items();
        $goods = [];

        foreach ($items as $item_id => $item) {
            $product = $item->get_product();
            $item_name = $item->get_name();

            $nomenclature_id = isset($nomenclature_ids_map[$item_id]) ? $nomenclature_ids_map[$item_id] : null;
            if (!$nomenclature_id) {
                tablecrm_log_message("Ошибка в adapt_data_format: ID номенклатуры для товара '{$item_name}' (Item ID: {$item_id}) не найден. Пропуск товара.");
                continue;
            }
            
            $price = (float) $order->get_item_total($item, false, false);

            $goods[] = [
                'price'          => $price,
                'quantity'       => $item->get_quantity(),
                'unit'           => 116,
                'discount'       => 0,
                'sum_discounted' => 0,
                'nomenclature'   => (int) $nomenclature_id,
                'name'           => $item_name,
                'sku'            => $product ? $product->get_sku() : '',
            ];
        }

        if (empty($goods)) {
            tablecrm_log_message("В заказе #{$order_id} нет товаров для отправки после обработки.");
            return null;
        }
        
        $comment_text = "Заказ с сайта №{$order_id}.";
        if ($note = $order->get_customer_note()) {
            $comment_text .= " Комментарий клиента: " . $note;
        }
        
        return [[
            'dated'          => time(),
            'operation'      => 'Заказ',
            'comment'        => $comment_text,
            'tax_included'   => true,
            'tax_active'     => true,
            'goods'          => $goods,
            'settings'       => new stdClass(),
            'warehouse'      => (int) $warehouse_id,
            'contragent'     => (int) $contragent_id,
            'paybox'         => (int) $paybox_id,
            'organization'   => (int) $organization_id,
            'status'         => false,
            'paid_rubles'    => (string) $order->get_total(),
            'paid_lt'        => 0,
        ]];
    }
}

if (!function_exists('tablecrm_send_order_to_api')) {
    function tablecrm_send_order_to_api($order_id) {
        $order = wc_get_order($order_id);
        if (!$order) {
            tablecrm_log_message("Ошибка: Не удалось получить заказ с ID: $order_id");
            return;
        }

        $order_status = $order->get_status();
        $trigger_statuses = get_option('tablecrm_trigger_statuses', ['processing']);
        if (!in_array($order_status, $trigger_statuses)) {
            return;
        }
        
        if (get_post_meta($order_id, '_tablecrm_sync_status', true) === 'success') {
            tablecrm_log_message("Заказ #{$order_id} уже был успешно отправлен. Повторная отправка отменена.");
            return;
        }
        
        tablecrm_log_message("Начало обработки заказа #{$order_id} для отправки в TableCRM.");

        $api_url = get_option('tablecrm_api_url');
        $api_token = get_option('tablecrm_api_token');
        $warehouse_id = get_option('tablecrm_warehouse_id');
        $organization_id = get_option('tablecrm_organization_id');
        $paybox_id = get_option('tablecrm_paybox_id');

        if (empty($api_url) || empty($api_token) || empty($warehouse_id) || empty($organization_id) || empty($paybox_id)) {
            tablecrm_log_message("Ошибка: Один или несколько параметров API не настроены.");
            return;
        }
        
        $api_url = trailingslashit($api_url);

        // 1. Get Contragent ID
        $contragent_id = get_or_create_tablecrm_contragent_id($order, $api_url, $api_token);
        if (!$contragent_id) {
            $error_message = "Критическая ошибка: не удалось получить или создать ID контрагента для заказа #{$order_id}.";
            tablecrm_log_message($error_message);
            update_post_meta($order_id, '_tablecrm_sync_status', 'failed');
            update_post_meta($order_id, '_tablecrm_sync_error', 'Не удалось получить/создать контрагента.');
            tablecrm_add_order_note($order_id, "Ошибка TableCRM: не удалось обработать клиента.");
            return;
        }

        // 2. Get Nomenclature IDs for all items
        $nomenclature_ids = [];
        $items = $order->get_items();
        foreach ($items as $item_id => $item) {
            $product_name = $item->get_name();
            $nomenclature_id = get_or_create_tablecrm_nomenclature_id($product_name, $api_url, $api_token);
            if (!$nomenclature_id) {
                $error_message = "Критическая ошибка: не удалось получить или создать ID номенклатуры для товара '{$product_name}' в заказе #{$order_id}.";
                tablecrm_log_message($error_message);
                update_post_meta($order_id, '_tablecrm_sync_status', 'failed');
                update_post_meta($order_id, '_tablecrm_sync_error', "Не удалось получить/создать номенклатуру: {$product_name}.");
                tablecrm_add_order_note($order_id, "Ошибка TableCRM: не удалось обработать товар '{$product_name}'.");
                return;
            }
            $nomenclature_ids[$item_id] = $nomenclature_id;
        }

        // 3. Adapt data format with the new IDs
        $adapted_data = adapt_data_format($order, $warehouse_id, $organization_id, $paybox_id, $contragent_id, $nomenclature_ids);

        if (!$adapted_data) {
            tablecrm_log_message("Не удалось адаптировать данные заказа #{$order_id}. Процесс остановлен.");
            return;
        }

        $json_data = json_encode($adapted_data, JSON_UNESCAPED_UNICODE);
        
        tablecrm_log_message("Подготовленные данные для заказа #{$order_id}: {$json_data}");

        $api_endpoint = $api_url . 'docs_sales/?token=' . $api_token;

        $response = wp_remote_post($api_endpoint, [
            'method'    => 'POST',
            'headers'   => ['Content-Type' => 'application/json; charset=utf-8'],
            'body'      => $json_data,
            'timeout'   => 45,
        ]);

        if (is_wp_error($response)) {
            $error_message = $response->get_error_message();
            tablecrm_log_message("Ошибка отправки заказа #{$order_id}: " . $error_message);
            update_post_meta($order_id, '_tablecrm_sync_status', 'failed');
            update_post_meta($order_id, '_tablecrm_sync_error', $error_message);
            tablecrm_add_order_note($order_id, "Ошибка TableCRM: " . $error_message);
        } else {
            $response_code = wp_remote_retrieve_response_code($response);
            $response_body = wp_remote_retrieve_body($response);
            tablecrm_log_message("Ответ от TableCRM для заказа #{$order_id} (Code: {$response_code}): " . $response_body);

            if ($response_code >= 200 && $response_code < 300) {
                update_post_meta($order_id, '_tablecrm_sync_status', 'success');
                delete_post_meta($order_id, '_tablecrm_sync_error');
                tablecrm_add_order_note($order_id, 'Заказ успешно отправлен в TableCRM.');
            } else {
                update_post_meta($order_id, '_tablecrm_sync_status', 'failed');
                update_post_meta($order_id, '_tablecrm_sync_error', "Error {$response_code}: " . $response_body);
                tablecrm_add_order_note($order_id, "Ошибка TableCRM {$response_code}: " . $response_body);
            }
        }
    }
}

if (!function_exists('tablecrm_add_order_note')) {
    function tablecrm_add_order_note($order_id, $note, $is_customer_note = 0) {
        $order = wc_get_order($order_id);
        if ($order) {
            $order->add_order_note($note, $is_customer_note);
            $order->save();
        }
    }
}

// Hook для обработки заказов
add_action('woocommerce_order_status_changed', 'tablecrm_send_order_to_api', 10, 1);

// Добавление административной страницы
add_action('admin_menu', 'tablecrm_admin_menu');

function tablecrm_admin_menu() {
    add_options_page(
        'TableCRM Integration Settings',
        'TableCRM Integration',
        'manage_options',
        'tablecrm-integration',
        'tablecrm_admin_page'
    );
}

function tablecrm_admin_page() {
    if (isset($_POST['submit'])) {
        update_option('tablecrm_api_url', sanitize_text_field($_POST['tablecrm_api_url']));
        update_option('tablecrm_api_token', sanitize_text_field($_POST['tablecrm_api_token']));
        update_option('tablecrm_warehouse_id', intval($_POST['tablecrm_warehouse_id']));
        update_option('tablecrm_organization_id', intval($_POST['tablecrm_organization_id']));
        update_option('tablecrm_paybox_id', intval($_POST['tablecrm_paybox_id']));
        update_option('tablecrm_trigger_statuses', array_map('sanitize_text_field', $_POST['tablecrm_trigger_statuses'] ?? []));
        echo '<div class="notice notice-success"><p>Настройки сохранены!</p></div>';
    }

    $api_url = get_option('tablecrm_api_url', '');
    $api_token = get_option('tablecrm_api_token', '');
    $warehouse_id = get_option('tablecrm_warehouse_id', '');
    $organization_id = get_option('tablecrm_organization_id', '');
    $paybox_id = get_option('tablecrm_paybox_id', '');
    $trigger_statuses = get_option('tablecrm_trigger_statuses', ['processing']);

    ?>
    <div class="wrap">
        <h1>TableCRM Integration Settings</h1>
        <form method="post" action="">
            <table class="form-table">
                <tr>
                    <th scope="row">API URL</th>
                    <td><input type="url" name="tablecrm_api_url" value="<?php echo esc_attr($api_url); ?>" class="regular-text" /></td>
                </tr>
                <tr>
                    <th scope="row">API Token</th>
                    <td><input type="text" name="tablecrm_api_token" value="<?php echo esc_attr($api_token); ?>" class="regular-text" /></td>
                </tr>
                <tr>
                    <th scope="row">Warehouse ID</th>
                    <td><input type="number" name="tablecrm_warehouse_id" value="<?php echo esc_attr($warehouse_id); ?>" /></td>
                </tr>
                <tr>
                    <th scope="row">Organization ID</th>
                    <td><input type="number" name="tablecrm_organization_id" value="<?php echo esc_attr($organization_id); ?>" /></td>
                </tr>
                <tr>
                    <th scope="row">Paybox ID</th>
                    <td><input type="number" name="tablecrm_paybox_id" value="<?php echo esc_attr($paybox_id); ?>" /></td>
                </tr>
                <tr>
                    <th scope="row">Trigger Statuses</th>
                    <td>
                        <label><input type="checkbox" name="tablecrm_trigger_statuses[]" value="processing" <?php checked(in_array('processing', $trigger_statuses)); ?> /> Processing</label><br>
                        <label><input type="checkbox" name="tablecrm_trigger_statuses[]" value="completed" <?php checked(in_array('completed', $trigger_statuses)); ?> /> Completed</label><br>
                        <label><input type="checkbox" name="tablecrm_trigger_statuses[]" value="on-hold" <?php checked(in_array('on-hold', $trigger_statuses)); ?> /> On Hold</label>
                    </td>
                </tr>
            </table>
            <?php submit_button(); ?>
        </form>
    </div>
    <?php
}
?> 