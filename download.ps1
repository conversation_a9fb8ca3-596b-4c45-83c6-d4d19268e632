Write-Host "Downloading logs from server..." -ForegroundColor Green

# Create directory
if (!(Test-Path "server_logs_latest")) {
    New-Item -ItemType Directory -Path "server_logs_latest"
}

# FTP parameters
$server = "bekflom3.beget.tech"
$user = "bekflom3_wz"
$pass = 'zl2Shj!e&6ng'

try {
    # Download debug.log
    Write-Host "Downloading debug.log..." -ForegroundColor Yellow
    $uri1 = "ftp://$server/public_html/wp-content/debug.log"
    $webclient = New-Object System.Net.WebClient
    $webclient.Credentials = New-Object System.Net.NetworkCredential($user, $pass)
    $webclient.DownloadFile($uri1, "server_logs_latest/debug_latest.log")
    Write-Host "debug.log downloaded" -ForegroundColor Green
    
    # Download tablecrm_complete.log
    Write-Host "Downloading tablecrm_complete.log..." -ForegroundColor Yellow
    $uri2 = "ftp://$server/public_html/wp-content/tablecrm_complete.log"
    $webclient.DownloadFile($uri2, "server_logs_latest/tablecrm_complete_latest.log")
    Write-Host "tablecrm_complete.log downloaded" -ForegroundColor Green
    
    $webclient.Dispose()
}
catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Show latest entries
Write-Host ""
Write-Host "LATEST LOG ENTRIES:" -ForegroundColor Cyan

if (Test-Path "server_logs_latest/debug_latest.log") {
    Write-Host ""
    Write-Host "=== DEBUG.LOG (last 15 lines) ===" -ForegroundColor Yellow
    Get-Content "server_logs_latest/debug_latest.log" | Select-Object -Last 15
}

if (Test-Path "server_logs_latest/tablecrm_complete_latest.log") {
    Write-Host ""
    Write-Host "=== TABLECRM_COMPLETE.LOG (last 15 lines) ===" -ForegroundColor Yellow
    Get-Content "server_logs_latest/tablecrm_complete_latest.log" | Select-Object -Last 15
}

Write-Host ""
Write-Host "DONE!" -ForegroundColor Green 