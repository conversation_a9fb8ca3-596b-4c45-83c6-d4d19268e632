# TableCRM Integration Plugin

Плагин для интеграции WordPress/WooCommerce с TableCRM для автоматической отправки заявок через API.

## Возможности

- ✅ Автоматическая отправка заказов WooCommerce в TableCRM
- ✅ Поддержка форм Contact Form 7
- ✅ Проверка дубликатов по email и телефону
- ✅ Тестирование соединения с API
- ✅ Подробное логирование для отладки
- ✅ Настраиваемые параметры через админ-панель
- ✅ Отправка полной информации о заказе (товары, адрес, сумма)
- ✅ Поддержка кастомных полей форм

## Установка

### Способ 1: Загрузка через SFTP

1. Подключитесь к серверу по SFTP
2. Перейдите в папку `/public_html/wp-content/plugins/`
3. Создайте папку `tablecrm-integration`
4. Загрузите файл `tablecrm-integration.php` в эту папку
5. Активируйте плагин в админ-панели WordPress

### Способ 2: Через скрипт установки

```bash
# Запустите скрипт установки плагина
./install_plugin.sh
```

## Настройка

1. Перейдите в **Настройки → TableCRM Integration** в админ-панели WordPress

2. Заполните обязательные поля:
   - **API URL**: URL API TableCRM (обычно `https://api.tablecrm.com`)
   - **API Key**: Ваш API ключ (получите в личном кабинете TableCRM)
   - **Project ID**: ID вашего проекта в TableCRM

3. Настройте дополнительные параметры:
   - **Отправлять заказы WooCommerce**: включить отправку заказов
   - **Отправлять формы Contact Form 7**: включить отправку форм CF7
   - **Проверять дубликаты**: предотвращает создание дублей
   - **Режим отладки**: включает подробное логирование

4. Нажмите **"Тестировать соединение"** для проверки настроек

## Получение API ключа TableCRM

1. Войдите в личный кабинет TableCRM
2. Перейдите в раздел **API & Интеграции**
3. Создайте новый API ключ или используйте существующий
4. Скопируйте API ключ и Project ID

## Поддерживаемые данные

### Заказы WooCommerce
- **От кого:** Имя и фамилия клиента, Email и телефон
- **Кому:** Полный адрес доставки, город, индекс, страна
- **Время:** Дата и время доставки (если настроено)
- **Товары:** Список товаров с количеством, ценой и SKU
- **Оплата:** Способ оплаты и статус (оплачено/не оплачено)
- **Аналитика:** UTM-метки, referrer, landing page, домен
- **Дубликаты:** Автоматическое объединение одинаковых товаров

### Формы Contact Form 7
- Все поля формы
- Метаданные формы (ID, название)
- Дата отправки
- Кастомные поля

## Проверка дубликатов

Плагин может проверять дубликаты перед отправкой заявки:
- По email адресу (обязательно)
- По номеру телефона (если указан)

Если дубликат найден, заявка не отправляется и записывается в лог.

## Логирование

При включенном режиме отладки все операции записываются в лог:
- Успешные отправки
- Ошибки API
- Найденные дубликаты

Логи можно найти в файле логов WordPress (`wp-content/debug.log`).

## Структура API запросов

### Создание лида
```json
POST /leads
{
  "name": "Иван Петров",
  "email": "<EMAIL>", 
  "phone": "+7(900)123-45-67",
  "source": "WooCommerce Order",
  "order_id": "12345",
  "order_total": "2500.00",
  "order_status": "processing",
  "order_date": "2024-06-02 21:30:00",
  "address": "ул. Ленина, 10",
  "city": "Москва",
  "delivery_date": "2024-06-03",
  "delivery_time": "14:00-18:00",
  "payment_method": "Оплата при получении",
  "payment_status": "unpaid",
  "domain": "mosbuketik.ru",
  "utm_data": {
    "utm_source": "google",
    "utm_medium": "cpc",
    "utm_campaign": "summer_sale",
    "referrer": "https://google.com"
  },
  "items": [
    {
      "name": "Товар 1",
      "quantity": 2,
      "price": "2500.00",
      "unit_price": "1250.00",
      "sku": "PROD-001",
      "category": ["Одежда", "Летняя коллекция"]
    }
  ]
}
```

### Проверка дубликатов
```json
POST /leads/check-duplicate
{
  "email": "<EMAIL>",
  "phone": "+7(900)123-45-67"
}
```

## Хуки WordPress

Плагин использует следующие хуки:
- `woocommerce_new_order` - новый заказ WooCommerce
- `woocommerce_order_status_completed` - завершенный заказ
- `wpcf7_mail_sent` - отправленная форма CF7

## Кастомизация

Вы можете дополнить плагин своими хуками и фильтрами:

```php
// Добавить дополнительные данные к заказу
add_filter('tablecrm_order_data', function($data, $order) {
    $data['custom_field'] = get_post_meta($order->get_id(), '_custom_field', true);
    return $data;
}, 10, 2);

// Изменить endpoint API
add_filter('tablecrm_api_endpoint', function($endpoint, $type) {
    if ($type === 'order') {
        return 'custom/orders';
    }
    return $endpoint;
}, 10, 2);
```

## Устранение неполадок

### Заявки не отправляются
1. Проверьте настройки API (URL, ключ, Project ID)
2. Включите режим отладки и проверьте логи
3. Протестируйте соединение через кнопку в настройках

### Ошибки авторизации
1. Убедитесь, что API ключ действителен
2. Проверьте правильность Project ID
3. Убедитесь, что у API ключа есть права на создание лидов

### Дубликаты не проверяются
1. Убедитесь, что включена опция "Проверять дубликаты"
2. Проверьте, что API поддерживает endpoint `/leads/check-duplicate`

### Ошибка 500 на Checkout / жёлтый баннер WooCommerce
1. **Симптомы:** при оформлении заказа WooCommerce показывает жёлтое всплывающее уведомление об ошибке, а AJAX‐запрос `?wc-ajax=checkout` отвечает кодом **500**; заказ в админ-панели не создаётся.
2. **Типичная причина:** в каталоге `wp-content/plugins` лежит старая копия плагина или происходит PHP-фатал в хуке `woocommerce_checkout_order_processed` (например `Call to undefined method TableCRM_Integration_Complete::log_info()`).
3. **Проверка:** включите `WP_DEBUG` и изучите `wp-content/debug.log` и `wp-content/uploads/tablecrm_integration_complete.log`.
4. **Решение:**
   * Оставьте в папке *только одну* актуальную версию `tablecrm-integration-complete-v1.0.24.php`.
   * Начиная с v1.0.24 отправка заказа выполняется асинхронно через WP-Cron (`tablecrm_complete_schedule_send`). Убедитесь, что событие запланировано и WP-Cron работает.
   * Если в `debug.log` много записей вида `load_textdomain_just_in_time was called too early`, можно временно установить MU-плагин-«санитайзер» (см. `checkout-clean-buffer.php`), который удалит лишний HTML перед JSON-ответом.
   * После внесения изменений сбросьте **OPcache** и повторите попытку.
5. **Если ошибка не исчезает** после деактивации плагина — пришлите логи разработчику.

## Механизм фоновой отправки (v1.0.25+)

Начиная с версии **1.0.25**, плагин использует хук `shutdown` для асинхронной отправки заказов. Это решает проблему блокировки и совместимо с большинством серверных конфигураций, включая Nginx.

**Как это работает:**
1. При создании/изменении статуса заказа его ID добавляется во внутреннюю очередь `orders_to_send_on_shutdown`.
2. PHP-скрипт завершает свою основную работу и отправляет ответ браузеру покупателя. Оформление заказа происходит **мгновенно**.
3. Сразу после этого срабатывает хук `shutdown`. Плагин пытается вызвать `fastcgi_finish_request()`, чтобы принудительно закрыть соединение с пользователем, если это еще не произошло.
4. В фоновом режиме, когда пользователь уже получил страницу, плагин перебирает заказы из очереди и отправляет их в TableCRM.

### Преимущества

*   🚀 **Мгновенный UX**: Покупатель не ждет ответа от API CRM.
*   ⚙️ **Высокая совместимость**: Не зависит от REST API и loopback-запросов, что решает конфликты с Nginx. Отлично работает на серверах с PHP-FPM.
*   🛡️ **Надежность**: Менее подвержен проблемам с WP-Cron или Action Scheduler.

> Для максимальной эффективности рекомендуется использовать PHP-FPM, так как функция `fastcgi_finish_request()` доступна именно в этой среде. На других конфигурациях отправка все равно произойдет в конце выполнения скрипта, но может быть небольшая задержка перед тем, как браузер получит полный ответ.

### Требования

* WP-Cron должен работать (или заменён внешним cron-заданием `wp-cron.php`).
* Сервер допускает фоновое выполнение скриптов минимум 30 сек.

### Как убедиться, что событие выполнено

1. Включите режим отладки плагина.  
2. В файле `wp-content/uploads/tablecrm_integration_complete.log` появятся строки:

```
[2025-06-17 12:34:56] [INFO] Отправка нового заказа 12345 запланирована через cron.
[2025-06-17 12:35:06] [INFO] Начинаем отправку заказа 12345 в TableCRM
```

Если второй строки нет — проверьте работу WP-Cron.

---

## Системные требования

- WordPress 5.0+
- PHP 7.4+
- WooCommerce 4.0+ (для заказов)
- Contact Form 7 (для форм)
- Активный SSL сертификат для HTTPS

## Поддержка

При возникновении проблем:
1. Включите режим отладки
2. Проверьте логи WordPress
3. Протестируйте API соединение
4. Обратитесь в поддержку TableCRM с логами

## Безопасность

Плагин соблюдает стандарты безопасности WordPress:
- Экранирование всех пользовательских данных
- Проверка nonce для AJAX запросов
- Валидация и санитизация входных данных
- Использование WordPress HTTP API 

## Асинхронная отправка через REST (v1.0.25+)

Начиная с версии **1.0.25** заказ отправляется в TableCRM через внутренний loopback-REST-эндпоинт `POST /wp-json/tablecrm/v1/async-send`.

1. При создании или изменении статуса заказа плагин выполняет `wp_remote_post()` с параметрами `blocking=false` и `timeout=0.01` — оформление для покупателя завершается мгновенно.
2. Отдельный PHP-процесс обрабатывает REST-запрос и вызывает `send_order_to_tablecrm()`.
3. В случае ошибки CRM (HTTP 4xx/5xx) REST возвращает 500, а заказ остаётся без меты `_tablecrm_complete_document_id` — такой заказ можно повторно отправить.

### Преимущества
* 🚀 Мгновенный UX — никаких задержек на checkout даже при медленном API.
* 🛡 Безопасность — loopback-запрос выполняется внутри того же сайта, без внешних вебхуков.
* 🛠 Совместимость — не требует WP-Cron, Action Scheduler или FastCGI-трюков.

> Убедитесь, что сервер разрешает HTTPS-loopback (не блокируется фаерволом). Если loopback недоступен, вернитесь к WP-Cron или настройте внешний cron. 

## Особенности ответа API TableCRM

TableCRM может возвращать `id` созданного документа в разных форматах, зависящих от версии бекенда и настроек «облегчённого»/«расширенного» ответа.

Плагин учитывает следующие варианты:

| Формат | Пример |
|--------|--------|
| 1 | `{ "document_id": 123 }` |
| 2 | `{ "data": 123 }` |
| 3 | `{ "data": { "id": 123 } }` |
| 4 | `{ "data": [ { "id": 123, ... } ] }` |
| 5 | `{ "data": { "result": [ { "id": 123 } ] } }` |
| 6 | `{ "data": { "results": [ { "id": 123 } ] } }` |

Функция `extract_document_id()` проходит по этим вариантам в указанном порядке и пишет предупреждение в лог, если `id` не найден. Благодаря этому плагин остаётся совместимым при будущих изменениях API.

> Если вы знаете, что ваш аккаунт TableCRM всегда возвращает один конкретный формат, можете упростить функцию, оставив только нужную ветку. Однако текущая универсальная реализация безопаснее при обновлениях CRM. 