# ДИАГНОСТИКА ПРОБЛЕМЫ С ОТПРАВКОЙ ИНФОРМАЦИИ О ДОСТАВКЕ

## 🔍 АНАЛИЗ КОРНЕВОЙ ПРИЧИНЫ

### Почему до правок информация о доставке НЕ отправлялась?

Функция `send_delivery_info()` **существовала и вызывалась**, но данные не достигали TableCRM из-за критической ошибки в функции `adapt_data_format()`.

## 🐛 ДЕТАЛЬНЫЙ РАЗБОР ПРОБЛЕМЫ

### Проблема #1: Неправильная логика adapt_data_format()

**В старой версии (до исправления):**

```php
private function adapt_data_format($endpoint, $data) {
    if ($endpoint === 'docs_sales/') {
        // Только для docs_sales/ - возвращаем адаптированные данные
        $organization_id = get_option('tablecrm_organization_id', 38);
        // ... формирование документа продаж ...
        return array($document_data);
    }
    
    // ❌ КРИТИЧЕСКАЯ ОШИБКА: для всех остальных эндпоинтов возвращался пустой массив!
    return array();
}
```

**Что происходило пошагово:**

1. ✅ **WooCommerce создавал заказ**
2. ✅ **Плагин отправлял документ продаж** → `POST /docs_sales/`
   - `adapt_data_format('docs_sales/', $order_data)` возвращал корректные данные
   - TableCRM создавал документ и возвращал `document_id`
3. ✅ **Автоматически вызывался** `send_delivery_info($document_id, $order)`
4. ❌ **ЗДЕСЬ ПРОИСХОДИЛА ОШИБКА:**
   - `make_api_request("docs_sales/{id}/delivery_info/", $delivery_data)`
   - Внутри `make_api_request()` вызывался `adapt_data_format("docs_sales/{id}/delivery_info/", $delivery_data)`
   - Поскольку эндпоинт НЕ равен `'docs_sales/'`, функция возвращала `array()`
   - В TableCRM отправлялся пустой JSON: `{}`
5. ❌ **TableCRM отвергал запрос** с ошибкой: `"HTTP 422 Validation Error: field required"`

### Проблема #2: Потеря данных в цепочке вызовов

**Поток данных в старой версии:**

```
send_delivery_info($document_id, $order)
   ↓
$delivery_data = [
    'recipient' => ['name' => '...', 'phone' => '...'],
    'address' => '...',
    'note' => '...'
]
   ↓
make_api_request("docs_sales/{id}/delivery_info/", $delivery_data)
   ↓
adapt_data_format("docs_sales/{id}/delivery_info/", $delivery_data)
   ↓
return array(); // ❌ ДАННЫЕ ПОТЕРЯНЫ!
   ↓
wp_remote_post($url, ['body' => json_encode(array())]) // ❌ ПУСТОЙ JSON!
   ↓
TableCRM: HTTP 422 Validation Error
```

### Проблема #3: Отсутствие детального логирования

В старых версиях логирование было минимальным:

```php
// Старое логирование
$this->log_info("Запуск функции send_delivery_info");
// Отсутствовало логирование подготовленных данных!
// Отсутствовало логирование ответа API!
```

Из-за этого проблему было сложно диагностировать.

## ✅ ИСПРАВЛЕНИЯ В v1.0.24

### 1. Исправлена логика adapt_data_format()

**После исправления:**

```php
private function adapt_data_format($endpoint, $data) {
    // Если это основной эндпоинт создания документа – адаптируем под docs_sales
    if ($endpoint === 'docs_sales/') {
        $organization_id = get_option('tablecrm_fixed_v3_organization_id', 38);
        // ... полная адаптация для создания документа ...
        return array($document_data);
    }

    // ✅ ИСПРАВЛЕНО: для всех остальных эндпоинтов возвращаем данные БЕЗ ИЗМЕНЕНИЙ
    return $data;
}
```

### 2. Улучшена функция send_delivery_info()

**Новая версия с детальным логированием:**

```php
private function send_delivery_info($document_id, $order) {
    $this->log_info("Запуск функции send_delivery_info для заказа ID: " . $order->get_id());

    // ✅ Получение даты доставки с поддержкой популярных плагинов
    $delivery_timestamp = $this->get_delivery_date($order);

    // ✅ Формирование корректной структуры данных
    $delivery_data = [
        'recipient' => [
            'name'  => trim($order->get_shipping_first_name() . ' ' . $order->get_shipping_last_name()) ?: trim($order->get_billing_first_name() . ' ' . $order->get_billing_last_name()),
            'phone' => $this->get_recipient_phone($order),
        ],
        'address' => $this->get_recipient_address($order),
        'note'    => $order->get_customer_note(),
    ];

    // ✅ Автоматическое добавление delivery_date если найдена
    if ($delivery_timestamp) {
        $delivery_data['delivery_date'] = $delivery_timestamp;
    }

    // ✅ КРИТИЧЕСКИ ВАЖНО: детальное логирование подготовленных данных
    $this->log_info("Подготовленные данные для delivery_info: " . json_encode($delivery_data, JSON_UNESCAPED_UNICODE));

    // ✅ Правильная отправка через единый API метод
    $response = $this->make_api_request("docs_sales/{$document_id}/delivery_info/", $delivery_data, 'POST');

    // ✅ Детальная обработка результата
    if ($response && isset($response['success']) && $response['success']) {
        $this->log_success("Информация о доставке для заказа ID " . $order->get_id() . " успешно отправлена в TableCRM. Document ID: {$document_id}");
        $this->add_order_note($order->get_id(), 'Информация о доставке успешно отправлена в TableCRM.');
    } else {
        $error_message = 'Не удалось отправить информацию о доставке в TableCRM.';
        if (isset($response['message'])) {
            $error_message .= ' Причина: ' . $response['message'];
        }
        if(isset($response['data']['errors'])) {
            $error_message .= ' Детали ошибки: ' . json_encode($response['data']['errors'], JSON_UNESCAPED_UNICODE);
        }
        
        $this->log_error("Ошибка отправки информации о доставке для заказа ID " . $order->get_id() . ". $error_message");
        $this->add_order_note($order->get_id(), "❌ $error_message");
    }
}
```

### 3. Добавлены вспомогательные методы

```php
// ✅ Поддержка популярных плагинов доставки
private function get_delivery_date($order) {
    // CodeRockz, Order Delivery Date Pro, WooCommerce Delivery Time, etc.
}

// ✅ Нормализация телефона к формату +7XXXXXXXXXX
private function get_recipient_phone($order) {
    // Приведение к единому формату TableCRM
}

// ✅ Сборка полного адреса с fallback
private function get_recipient_address($order) {
    // shipping → billing fallback
}

// ✅ HPOS совместимость
private function get_order_meta($order, $key) {
    // Поддержка новой системы хранения заказов
}
```

## 📊 СРАВНЕНИЕ ЛОГОВ ДО/ПОСЛЕ

### ❌ Логи старой версии (проблемная)

```
[2024-01-15 10:30:15] [INFO] Запуск функции send_delivery_info для заказа ID: 12345
[2024-01-15 10:30:15] [DEBUG] Отправка запроса к: https://app.tablecrm.com/api/v1/docs_sales/67890/delivery_info/
[2024-01-15 10:30:15] [DEBUG] Данные: [] // ❌ ПУСТОЙ МАССИВ ИЗ-ЗА adapt_data_format!
[2024-01-15 10:30:16] [ERROR] Ошибка API (HTTP 422): {"message":"Validation failed","errors":{"recipient.name":["The recipient.name field is required."],"recipient.phone":["The recipient.phone field is required."]}}
```

### ✅ Логи исправленной версии

```
[2024-01-15 10:30:15] [INFO] Запуск функции send_delivery_info для заказа ID: 12345
[2024-01-15 10:30:15] [INFO] Найдена дата от CodeRockz: '15/01/2024'
[2024-01-15 10:30:15] [INFO] Найдено время от CodeRockz: '14:00 - 16:00'
[2024-01-15 10:30:15] [SUCCESS] Дата и время от CodeRockz ('15-01-2024 14:00') успешно преобразованы в timestamp: 1705320000
[2024-01-15 10:30:15] [INFO] Подготовленные данные для delivery_info: {"recipient":{"name":"Иван Петров","phone":"+79123456789"},"address":"ул. Ленина, 1, Москва, 101000","note":"Доставить после 18:00","delivery_date":1705320000}
[2024-01-15 10:30:16] [SUCCESS] Информация о доставке для заказа ID 12345 успешно отправлена в TableCRM. Document ID: 67890
```

## 🔧 ДИАГНОСТИКА ПРОБЛЕМЫ

### Признаки старой проблемной версии

1. **В логах есть вызовы send_delivery_info, но нет успешных отправок:**
   ```bash
   grep "send_delivery_info" /wp-content/uploads/tablecrm_integration_complete.log
   ```

2. **Есть ошибки 422 Validation с полями recipient:**
   ```bash
   grep "HTTP 422.*recipient.*required" /wp-content/uploads/tablecrm_integration_complete.log
   ```

3. **В логах видны пустые данные для отправки:**
   ```bash
   grep "Данные: \[\]" /wp-content/uploads/tablecrm_integration_complete.log
   ```

4. **В TableCRM секция "Доставка" остается пустой**

### Признаки исправленной версии

1. **В логах есть детализация подготовленных данных:**
   ```bash
   grep "Подготовленные данные для delivery_info" /wp-content/uploads/tablecrm_integration_complete.log
   ```

2. **Есть успешные сообщения об отправке:**
   ```bash
   grep "Информация о доставке.*успешно отправлена" /wp-content/uploads/tablecrm_integration_complete.log
   ```

3. **В WooCommerce заметках появляется подтверждение:**
   - "Информация о доставке успешно отправлена в TableCRM."

4. **В TableCRM секция "Доставка" заполняется корректными данными**

## 📁 ФАЙЛЫ И ВЕРСИИ

### ✅ Исправленные версии (v1.0.24)

- `tablecrm-integration-complete-v1.0.24.php`
- `tablecrm-integration-complete-v1.0.24-FIXED.php`  
- `tablecrm-integration-fixed-v3-secure.php`

### ❌ Проблемные версии (до v1.0.24)

- `backup/$(date/tablecrm-integration-fixed.php`
- `backup/$(date/tablecrm-integration.php`
- Все версии без исправления adapt_data_format

## 🎯 ИТОГОВОЕ РЕШЕНИЕ

### Алгоритм работы после исправления

```
1. WooCommerce создает заказ
   ↓
2. send_order_to_tablecrm($order_id)
   ↓
3. POST /docs_sales/ → получение document_id
   ↓
4. send_delivery_info($document_id, $order)
   ↓
5. Формирование корректных данных доставки
   ↓
6. adapt_data_format() ТЕПЕРЬ НЕ ОБНУЛЯЕТ данные
   ↓
7. POST /docs_sales/{id}/delivery_info/ → SUCCESS
   ↓
8. TableCRM заполняет секцию "Доставка"
```

## 🚀 ЗАКЛЮЧЕНИЕ

Проблема была **классической "скрытой ошибкой"**:
- ✅ Функциональность формально существовала
- ✅ Код выполнялся без критических ошибок
- ❌ Данные "терялись" в цепочке обработки
- ❌ Результат: пустые запросы в API

**Исправление потребовало:**
1. Изменения логики `adapt_data_format()` 
2. Улучшения `send_delivery_info()` с детальным логированием
3. Добавления поддержки популярных плагинов доставки
4. Обеспечения HPOS совместимости

**Теперь интеграция API доставки работает корректно!** ✅ 