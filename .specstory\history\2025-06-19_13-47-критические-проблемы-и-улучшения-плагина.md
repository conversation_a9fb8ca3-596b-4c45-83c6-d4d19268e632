<!-- Generated by SpecStory -->

# Критические проблемы и улучшения плагина (2025-06-19 16:47:50)

_**User**_

Критические проблемы и улучшения для плагина TableCRM Integration
Критические проблемы
Состояние гонки при обработке заказов
Используется Action Scheduler для асинхронной обработки, но отсутствует управление транзакциями
Возможны дублирующиеся заказы при одновременной обработке
Недостаточная обработка ошибок API
Некоторые вызовы API не имеют механизма повторных попыток
Отсутствует паттерн "circuit breaker" при сбоях API
Проблемы безопасности
API-ключи хранятся в открытом виде в базе данных
Некоторые функции админки не проверяют права доступа

---

_**Assistant**_



---

Ниже приведён перечень основных рисков, подтверждение их наличия в текущем коде и конкретные рекомендации, как устранить каждую из проблем. Все предложения совместимы с WordPress-стеком и придерживаются хороших практик PHP.

────────────────────────────────────────
1. Состояние гонки при обработке заказов
────────────────────────────────────────
Проблема  
• Action Scheduler запускает несколько «job’ов» параллельно, из-за чего один и тот же заказ может быть отправлен в TableCRM дважды.

Подтверждение  
• В большинстве скриптов (например, `tablecrm-integration-fixed-v3.php`) обработчик вызывает `TableCRM_API::send_order()` без предварительных блокировок или idempotency-ключей.

Решение  
1. Ввести «идемпотентный ключ» (order_id + site_url + версия) и передавать его в каждом API-запросе.  
2. В плагине добавить таблицу `wp_tablecrm_jobs` с колонками `order_id`, `hash`, `status (processing|done)` и индексом `UNIQUE(order_id)`.  
3. Использовать транзакцию:
   ```php
   $wpdb->query('START TRANSACTION');
   $done = $wpdb->get_var(
       $wpdb->prepare('SELECT status FROM wp_tablecrm_jobs WHERE order_id = %d FOR UPDATE', $order_id)
   );
   if ($done === 'done') { $wpdb->query('ROLLBACK'); return; }
   // ...отправка...
   $wpdb->update('wp_tablecrm_jobs', ['status'=>'done'], ['order_id'=>$order_id]);
   $wpdb->query('COMMIT');
   ```
4. Либо заменить Action Scheduler на WooCommerce Queue с поддержкой `unique`-jobs.

────────────────────────────────────────
2. Недостаточная обработка ошибок API
────────────────────────────────────────
Проблема  
• Отсутствуют повторные попытки (retry) при 5xx/429, нет экспоненциального backoff, возможна лавинообразная нагрузка.

Решение  
1. В качестве транспорта применить Guzzle 7 с middleware `RetrySubscriber`.  
2. Алгоритм:  
   • повторять до 3 раз, backoff = 2^n сек;  
   • после N неудач писать в дополнительный лог + создавать оповещение admin_notice.  
3. Для статусов 429/503 внедрить Circuit Breaker (можно библиотеку `php-circuit-breaker` или простой класс, который на 1 мин снимает сервис «с оборота»).

────────────────────────────────────────
3. Неупорядоченное хранение логики работы с API
────────────────────────────────────────
• Сейчас во многих файлах идёт прямой вызов `wp_remote_post()`, что усложняет поддержку.

Решение  
1. Выделить слой `src/TableCRM/Client.php`, инкапсулирующий:  
   • авторизацию,  
   • retries, circuit-breaker,  
   • логирование.  
2. Все существующие вызовы заменить на `$client->orders()->create($payload)`.

────────────────────────────────────────
4. Проблемы безопасности
────────────────────────────────────────
4.1 Хранение API-ключа в БД «как есть»  
Решение  
• Шифровать ключ посредством `openssl_encrypt()` с солью из `AUTH_SALT`, сохранять в `wp_options`.  
• При чтении – дешифровать; при сохранении – проверять текущую роль (`manage_options`).

4.2 Отсутствие проверки прав в admin-функциях  
• В файлах с `add_menu_page()`/`add_submenu_page()` добавить 3-й параметр `'manage_woocommerce'`.  
• В AJAX-обработчиках первым делом вызывать `check_ajax_referer()` и `current_user_can()`.

────────────────────────────────────────
5. Рекомендации по улучшениям «в один клик»
────────────────────────────────────────
• Установить PHPUnit + WooCommerce 5 testutils → покрыть ключевые сценарии (отправка, дублирование, fallback).  
• Ввести GitHub Actions:  
  – job для `composer validate && composer test`,  
  – job для WordPress Coding Standards `--standard=WordPress` (PHPCS).  
• Добавить флаг «Песочница» в настройках: перенаправляет запросы на staging-URL TableCRM.  
• Расширить инструмент диагностики (у вас уже есть `diagnose_*`-скрипты): выводить результат проверки SSL, версий PHP-расширений и разрешений файлов.

Таким образом вы получите:
• ​устранение дублей заказов,  
• ​устойчивость к сбоям API,  
• ​значительно повышенную безопасность,  
• ​облегчённую поддержку и контроль качества.

---

