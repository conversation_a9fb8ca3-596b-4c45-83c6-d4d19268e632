# 🔍 ПОЛНЫЙ АНАЛИЗ ВСЕХ ДАННЫХ В ПЛАГИНЕ

## 📋 ЧТО СОБИРАЕТСЯ ИЗ WOOCOMMERCE (строки 647-673)

### **Основные данные заказа:**
```php
$data = array(
    'name' => trim($billing_first_name . ' ' . $billing_last_name),        // ✅ ИМЯ КЛИЕНТА
    'email' => $billing_email,                                            // ✅ EMAIL 
    'phone' => $billing_phone,                                            // ✅ ТЕЛЕФОН
    'order_id' => $order_id,                                              // ✅ ID ЗАКАЗА
    'order_total' => $order_total,                                        // ✅ СУММА ЗАКАЗА
    'order_status' => $order->get_status(),                               // ✅ СТАТУС ЗАКАЗА
    'order_date' => $order->get_date_created()->format('Y-m-d H:i:s'),    // ✅ ДАТА ЗАКАЗА
    'source' => 'WooCommerce Order (Fixed Plugin v3 Action Scheduler)',   // ✅ ИСТОЧНИК
    'payment_method' => $order->get_payment_method_title(),               // ✅ СПОСОБ ОПЛАТЫ
    'payment_status' => $order->is_paid() ? 'paid' : 'unpaid',            // ✅ СТАТУС ОПЛАТЫ
    'domain' => $_SERVER['HTTP_HOST'],                                     // ✅ ДОМЕН
    'items' => array()                                                     // ✅ ТОВАРЫ
);
```

### **Данные товаров (строки 660-672):**
```php
foreach ($order->get_items() as $item_id => $item) {
    $product = $item->get_product();
    if ($product) {
        $data['items'][] = array(
            'name' => $item->get_name(),                    // ✅ НАЗВАНИЕ ТОВАРА
            'quantity' => $item->get_quantity(),            // ✅ КОЛИЧЕСТВО
            'price' => $item->get_total(),                  // ✅ ОБЩАЯ СТОИМОСТЬ
            'unit_price' => $item->get_total() / $item->get_quantity(), // ✅ ЦЕНА ЗА ЕДИНИЦУ
            'sku' => $product->get_sku() ?: '',             // ✅ АРТИКУЛ
            'product_id' => $product->get_id()              // ✅ ID ТОВАРА
        );
    }
}
```

## 🔄 ЧТО ПЕРЕДАЕТСЯ В TABLECRM API (строки 848-862)

### **Документ продаж:**
```php
return array(array(
    'dated' => time(),                                      // ✅ ВРЕМЯ СОЗДАНИЯ
    'operation' => 'Заказ',                                // ✅ ТИП ОПЕРАЦИИ  
    'comment' => "Заказ #{$data['order_id']} с сайта...",  // ✅ КОММЕНТАРИЙ С ДАННЫМИ КЛИЕНТА
    'tax_included' => true,                                 // ✅ НДС ВКЛЮЧЕН
    'tax_active' => true,                                   // ✅ НДС АКТИВЕН
    'goods' => $goods,                                      // ✅ МАССИВ ТОВАРОВ
    'settings' => (object)array(),                          // ✅ НАСТРОЙКИ
    'warehouse' => intval($warehouse_id),                   // ✅ СКЛАД (39)
    'contragent' => intval($contragent_id),                // ✅ КОНТРАГЕНТ (ДИНАМИЧЕСКИЙ)
    'paybox' => intval($cashbox_id),                       // ✅ КАССА (218)
    'organization' => intval($organization_id),             // ✅ ОРГАНИЗАЦИЯ (38)
    'status' => false,                                      // ✅ НЕ ПРОВЕДЕН
    'paid_rubles' => number_format($data['order_total'], 2), // ✅ СУММА К ОПЛАТЕ
    'paid_lt' => 0                                          // ✅ ОПЛАЧЕНО (0)
));
```

### **Товары в API (строки 825-833):**
```php
foreach ($data['items'] as $item) {
    $goods[] = array(
        'name' => sanitize_text_field($item['name']),       // ✅ ИСПРАВЛЕНО: НАЗВАНИЕ ТОВАРА
        'price' => floatval($item['unit_price']),           // ✅ ЦЕНА ЗА ЕДИНИЦУ
        'quantity' => intval($item['quantity']),            // ✅ КОЛИЧЕСТВО
        'unit' => intval($unit_id),                        // ✅ ЕДИНИЦА ИЗМЕРЕНИЯ (116)
        'discount' => 0,                                    // ❌ СКИДКА НЕ ПЕРЕДАЕТСЯ
        'sum_discounted' => 0,                             // ❌ СУММА СО СКИДКОЙ НЕ ВЫЧИСЛЯЕТСЯ
        'nomenclature' => 39697                            // ⚠️ ФИКСИРОВАННАЯ НОМЕНКЛАТУРА
    );
}
```

## ❌ НАЙДЕННЫЕ ПРОБЛЕМЫ

### **1. ✅ ИСПРАВЛЕНО: Названия товаров**
- **Проблема:** Поле `name` отсутствовало в товарах
- **Исправление:** Добавлено `'name' => sanitize_text_field($item['name'])`

### **2. ⚠️ СКИДКИ НЕ ПЕРЕДАЮТСЯ**
```php
// СЕЙЧАС:
'discount' => 0,
'sum_discounted' => 0,

// НУЖНО:
'discount' => $item_discount,                              // Скидка товара
'sum_discounted' => $item_total_with_discount,            // Итоговая сумма со скидкой
```

### **3. ⚠️ ФИКСИРОВАННАЯ НОМЕНКЛАТУРА**
```php
// СЕЙЧАС:
'nomenclature' => 39697  // Всегда одна номенклатура

// ЛУЧШЕ БЫЛО БЫ:
'nomenclature' => $this->get_or_create_nomenclature($item['name'], $item['sku'])
```

### **4. ⚠️ АДРЕС ДОСТАВКИ НЕ ПЕРЕДАЕТСЯ**
Из заказа не извлекается:
- `$order->get_shipping_address_1()`
- `$order->get_shipping_address_2()` 
- `$order->get_shipping_city()`
- `$order->get_shipping_postcode()`

### **5. ⚠️ UTM ДАННЫЕ НЕ ИСПОЛЬЗУЮТСЯ**
UTM данные собираются скриптом `utm-tracking.js`, но не используются в API запросе

### **6. ⚠️ ВАРИАЦИИ ТОВАРОВ НЕ ДЕТАЛИЗИРУЮТСЯ**
Если товар имеет вариации (размер, цвет), они не передаются отдельно

## 🛠️ ДОПОЛНИТЕЛЬНЫЕ ИСПРАВЛЕНИЯ

### **Добавить скидки товаров:**
```php
// В цикле товаров добавить:
$item_discount = $item->get_total() - $item->get_subtotal(); // Размер скидки
$discount_percent = ($item->get_subtotal() > 0) ? 
    round(($item_discount / $item->get_subtotal()) * 100, 2) : 0;

$goods[] = array(
    'name' => sanitize_text_field($item['name']),
    'price' => floatval($item->get_subtotal() / $item->get_quantity()), // Цена БЕЗ скидки
    'quantity' => intval($item->get_quantity()),
    'unit' => intval($unit_id),
    'discount' => floatval($discount_percent),              // ИСПРАВЛЕНИЕ: Процент скидки
    'sum_discounted' => floatval($item->get_total()),      // ИСПРАВЛЕНИЕ: Сумма СО скидкой
    'nomenclature' => 39697
);
```

### **Добавить адрес доставки в комментарий:**
```php
$shipping_address = array();
if ($order->get_shipping_address_1()) $shipping_address[] = $order->get_shipping_address_1();
if ($order->get_shipping_address_2()) $shipping_address[] = $order->get_shipping_address_2();
if ($order->get_shipping_city()) $shipping_address[] = $order->get_shipping_city();
if ($order->get_shipping_postcode()) $shipping_address[] = $order->get_shipping_postcode();

$address_string = !empty($shipping_address) ? 
    "\nАдрес доставки: " . implode(', ', $shipping_address) : '';

'comment' => "Заказ #{$data['order_id']} с сайта\n" .
             "Клиент: {$data['name']}\n" .
             "Email: {$data['email']}\n" .
             "Телефон: {$data['phone']}\n" .
             "Способ оплаты: {$data['payment_method']}" .
             $address_string,
```

### **Добавить UTM данные:**
```php
// Получить UTM из метаданных заказа
$utm_source = get_post_meta($order_id, '_utm_source', true);
$utm_medium = get_post_meta($order_id, '_utm_medium', true);
$utm_campaign = get_post_meta($order_id, '_utm_campaign', true);

$utm_string = '';
if ($utm_source || $utm_medium || $utm_campaign) {
    $utm_string = "\nИсточник: $utm_source, $utm_medium, $utm_campaign";
}
```

## ✅ ИТОГОВЫЙ СТАТУС

### **Передается корректно:**
- ✅ Имя, email, телефон клиента
- ✅ ID заказа, сумма, статус, дата
- ✅ Способ оплаты, статус оплаты
- ✅ **НАЗВАНИЯ ТОВАРОВ (ИСПРАВЛЕНО)**
- ✅ Количество и цены товаров
- ✅ Контрагенты (уникальные для каждого клиента)

### **Можно улучшить:**
- ⚠️ Скидки товаров (сейчас всегда 0)
- ⚠️ Адрес доставки (не передается)
- ⚠️ UTM аналитика (собирается, но не используется)
- ⚠️ Номенклатура (всегда фиксированная)
- ⚠️ Вариации товаров (не детализируются)

### **Критично для исправления:**
- 🔧 **ТОЛЬКО названия товаров** (уже исправлено)

Остальные улучшения не критичны для базовой работы интеграции. 