#!/bin/bash

echo "==========================================
🔍 Проверка логов TableCRM Integration
=========================================="

SERVER="bekflom3.beget.tech"
USER="bekflom3_wz"
PASS="zl2Shj!e&6ng"

echo "📡 Подключение к серверу $SERVER..."

# Создаем временный файл команд для SFTP
cat > /tmp/sftp_commands.txt << 'EOF'
cd public_html

# Проверяем debug.log WordPress
ls -la wp-content/debug.log
get wp-content/debug.log /tmp/debug.log
tail -50 /tmp/debug.log

# Проверяем error.log
ls -la wp-content/error.log
get wp-content/error.log /tmp/error.log
tail -20 /tmp/error.log

# Проверяем настройки wp-config.php
get wp-config.php /tmp/wp-config.php
grep -i "debug\|log" /tmp/wp-config.php

# Проверяем статус плагина
ls -la wp-content/plugins/tablecrm-integration/

quit
EOF

echo "🔄 Выполняем проверку логов..."

# Выполняем SFTP команды
sftp -oStrictHostKeyChecking=no -oBatchMode=no $USER@$SERVER < /tmp/sftp_commands.txt

echo ""
echo "📋 Анализ загруженных логов:"

# Анализируем debug.log
if [ -f /tmp/debug.log ]; then
    echo ""
    echo "🔍 Последние записи TableCRM в debug.log:"
    grep -i "tablecrm" /tmp/debug.log | tail -10 || echo "❌ Записей TableCRM не найдено"
    
    echo ""
    echo "🔍 Ошибки PHP в debug.log:"
    grep -i "error\|fatal\|warning" /tmp/debug.log | tail -5 || echo "✅ PHP ошибок не найдено"
else
    echo "❌ debug.log не удалось загрузить"
fi

# Анализируем error.log  
if [ -f /tmp/error.log ]; then
    echo ""
    echo "🔍 Последние ошибки в error.log:"
    tail -10 /tmp/error.log || echo "✅ error.log пуст"
else
    echo "❌ error.log не удалось загрузить"
fi

# Анализируем wp-config.php
if [ -f /tmp/wp-config.php ]; then
    echo ""
    echo "🔍 Настройки отладки в wp-config.php:"
    grep -i "debug\|log" /tmp/wp-config.php || echo "❌ Настройки отладки не найдены"
else
    echo "❌ wp-config.php не удалось загрузить"
fi

# Очищаем временные файлы
rm -f /tmp/sftp_commands.txt /tmp/debug.log /tmp/error.log /tmp/wp-config.php

echo ""
echo "✅ Проверка логов завершена!"
echo ""
echo "📋 Рекомендации:"
echo "1. Если debug.log пуст - включите отладку в wp-config.php"
echo "2. Если нет записей TableCRM - плагин может быть неактивен"
echo "3. При наличии ошибок PHP - исправьте их в коде плагина" 