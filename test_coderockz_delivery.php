<?php
// Тест работы с CodeRockz плагином доставки
echo "=== ТЕСТ CODEROCKZ DELIVERY DATE TIME PRO ===\n";

$wp_path = '/home/<USER>/bekflom3/mosbuketik.ru/public_html';
require_once($wp_path . '/wp-config.php');
require_once($wp_path . '/wp-load.php');

echo "WordPress загружен успешно\n\n";

// Тестируем заказ
$order_id = 21780;
$order = wc_get_order($order_id);

if (!$order) {
    echo "❌ Заказ #$order_id не найден\n";
    exit;
}

echo "✅ Заказ #$order_id найден\n\n";

// Проверяем поля CodeRockz
echo "=== ПОЛЯ CODEROCKZ ПЛАГИНА ===\n";
$delivery_date = $order->get_meta('delivery_date');
$delivery_time = $order->get_meta('delivery_time');
$delivery_type = $order->get_meta('delivery_type');
$delivery_details_timestamp = $order->get_meta('delivery_details_timestamp');

echo "delivery_date: '$delivery_date'\n";
echo "delivery_time: '$delivery_time'\n";
echo "delivery_type: '$delivery_type'\n";
echo "delivery_details_timestamp: '$delivery_details_timestamp'\n\n";

// Тестируем обновленную логику получения даты
echo "=== ТЕСТ ОБНОВЛЕННОЙ ЛОГИКИ ===\n";

function get_proper_delivery_date_updated($order) {
    // HPOS-совместимое получение мета-данных
    $get_order_meta = function($order, $key) {
        if (method_exists($order, 'get_meta')) {
            return $order->get_meta($key);
        } else {
            return get_post_meta($order->get_id(), $key, true);
        }
    };
    
    // 1. Проверяем CodeRockz WooCommerce Delivery Date Time Pro
    $delivery_date = $get_order_meta($order, 'delivery_date');
    if (!empty($delivery_date) && $delivery_date !== '0' && strtotime($delivery_date) !== false) {
        // Если есть время доставки, добавляем его
        $delivery_time = $get_order_meta($order, 'delivery_time');
        if (!empty($delivery_time)) {
            // Парсим время (например "12:30 - 14:00" берем начальное время)
            $time_parts = explode(' - ', $delivery_time);
            if (!empty($time_parts[0])) {
                return $delivery_date . ' ' . trim($time_parts[0]) . ':00';
            }
        }
        // Если времени нет, возвращаем дату с временем по умолчанию
        return $delivery_date . ' 12:00:00';
    }
    
    // 2. Проверяем Order Delivery Date Pro
    $delivery_date = $get_order_meta($order, '_orddd_timestamp');
    if (!empty($delivery_date) && is_numeric($delivery_date) && $delivery_date > 1000000000) {
        return date('Y-m-d H:i:s', $delivery_date);
    }
    
    // 3. Проверяем WooCommerce Delivery Date
    $delivery_date = $get_order_meta($order, '_delivery_date');
    if (!empty($delivery_date) && $delivery_date !== '0' && strtotime($delivery_date) !== false) {
        return date('Y-m-d H:i:s', strtotime($delivery_date));
    }
    
    // 4. Проверяем expected delivery date
    $delivery_date = $get_order_meta($order, '_expected_delivery_date');
    if (!empty($delivery_date) && $delivery_date !== '0' && strtotime($delivery_date) !== false) {
        return date('Y-m-d H:i:s', strtotime($delivery_date));
    }
    
    // 5. Проверяем delivery_details_timestamp как fallback
    $delivery_timestamp = $get_order_meta($order, 'delivery_details_timestamp');
    if (!empty($delivery_timestamp) && is_numeric($delivery_timestamp) && $delivery_timestamp > 1000000000) {
        return date('Y-m-d H:i:s', $delivery_timestamp);
    }
    
    return ''; // Возвращаем пустую строку если ничего не найдено
}

$proper_delivery_date = get_proper_delivery_date_updated($order);
echo "Результат обновленной функции: '$proper_delivery_date'\n\n";

// Формируем полные данные доставки
echo "=== ПОЛНЫЕ ДАННЫЕ ДОСТАВКИ ===\n";

$delivery_data = array();

// Адрес
$address_parts = array();
if ($order->get_billing_address_1()) {
    $address_parts[] = $order->get_billing_address_1();
}
if ($order->get_billing_city()) {
    $address_parts[] = $order->get_billing_city();
}

if (!empty($address_parts)) {
    $delivery_data['address'] = implode(', ', $address_parts);
}

// Дата доставки (только если есть реальная дата)
if (!empty($proper_delivery_date)) {
    $delivery_data['delivery_date'] = $proper_delivery_date;
}

// Получатель как строка
$first_name = $order->get_shipping_first_name() ?: $order->get_billing_first_name();
$last_name = $order->get_shipping_last_name() ?: $order->get_billing_last_name();
$phone = $order->get_shipping_phone() ?: $order->get_billing_phone();

// Функция для проверки телефона
$is_phone = function($str) {
    if (empty($str)) return false;
    return preg_match('/^[\+]?[0-9\(\)\-\s]{7,}$/', $str) || 
           strpos($str, '+7') === 0 || 
           strpos($str, '8(') === 0 ||
           preg_match('/\d{3}.*\d{3}.*\d{2}.*\d{2}/', $str);
};

$clean_first_name = (!empty($first_name) && !$is_phone($first_name)) ? $first_name : '';
$clean_last_name = (!empty($last_name) && !$is_phone($last_name)) ? $last_name : '';

$recipient_parts = array();
if (!empty($clean_first_name)) {
    $recipient_parts[] = $clean_first_name;
}
if (!empty($clean_last_name)) {
    $recipient_parts[] = $clean_last_name;
}

$recipient_name = implode(' ', $recipient_parts);
if (!empty($recipient_name)) {
    $delivery_data['recipient'] = $recipient_name;
}

// Примечание с деталями доставки
$note_parts = array();

$shipping_method = $order->get_shipping_method();
if ($shipping_method) {
    $note_parts[] = "Способ доставки: " . $shipping_method;
}

$shipping_total = $order->get_shipping_total();
if ($shipping_total > 0) {
    $note_parts[] = "Стоимость доставки: " . $shipping_total . " руб.";
}

// Добавляем время доставки в примечание
if (!empty($delivery_time)) {
    $note_parts[] = "Время доставки: " . $delivery_time;
}

// Добавляем тип доставки
if (!empty($delivery_type)) {
    $note_parts[] = "Тип доставки: " . $delivery_type;
}

// Телефон
if (!empty($phone)) {
    $note_parts[] = "Телефон: " . $phone;
}

if (!empty($note_parts)) {
    $delivery_data['note'] = implode('. ', $note_parts);
}

echo json_encode($delivery_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";

echo "=== КЛЮЧЕВЫЕ УЛУЧШЕНИЯ ===\n";
echo "✅ Поддержка CodeRockz плагина (delivery_date + delivery_time)\n";
echo "✅ Правильное объединение даты и времени\n";
echo "✅ Время доставки добавлено в примечание\n";
echo "✅ Тип доставки добавлен в примечание\n";
echo "✅ Fallback на delivery_details_timestamp\n";
echo "✅ Не отправляем поле если даты нет\n";

echo "\n=== ТЕСТ ЗАВЕРШЕН ===\n";
?> 