# Upload CRITICAL FIX for TableCRM Payment Issue
Write-Host "=== КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ ПРОБЛЕМЫ ОПЛАТЫ ===" -ForegroundColor Red
Write-Host "Загружаем исправленную версию плагина на сервер..." -ForegroundColor Yellow

$ftpServer = "bekflom3.beget.tech"
$ftpUser = "bekflom3_wz"
$ftpPassword = '*rJ*a&7IF*gJ'
$localFile = "tablecrm-integration-complete-v1.0.24-FIXED.php"
$remotePath = "/public_html/wp-content/plugins/tablecrm-integration-complete/tablecrm-integration-complete.php"

if (!(Test-Path $localFile)) {
    Write-Host "ОШИБКА: Локальный файл $localFile не найден!" -ForegroundColor Red
    exit 1
}

try {
    Write-Host "Подключение к FTP серверу..." -ForegroundColor Cyan
    
    # Загружаем файл
    $uri = "ftp://$ftpServer$remotePath"
    $wc = New-Object System.Net.WebClient
    $wc.Credentials = New-Object System.Net.NetworkCredential($ftpUser, $ftpPassword)
    $wc.UploadFile($uri, $localFile)
    
    Write-Host "✅ ИСПРАВЛЕННЫЙ ПЛАГИН УСПЕШНО ЗАГРУЖЕН!" -ForegroundColor Green
    Write-Host "Файл: $remotePath" -ForegroundColor Green
    
    Write-Host "" 
    Write-Host "=== ЧТО ИСПРАВЛЕНО ===" -ForegroundColor Yellow
    Write-Host "❌ БЫЛО: paid_rubles = всегда полная сумма, status = всегда false" -ForegroundColor Red
    Write-Host "✅ СТАЛО: paid_rubles = 0 для неоплаченных / реальная сумма для оплаченных" -ForegroundColor Green
    Write-Host "✅ СТАЛО: status = зависит от статуса заказа WooCommerce" -ForegroundColor Green
    Write-Host ""
    Write-Host "=== СЛЕДУЮЩИЕ ШАГИ ===" -ForegroundColor Cyan
    Write-Host "1. Проверьте настройки плагина в админке WordPress" 
    Write-Host "2. Убедитесь, что настроены правильные 'Оплаченные статусы'"
    Write-Host "3. Создайте тестовый заказ для проверки"
    Write-Host "4. Проверьте логи: download_logs_fixed.ps1"
    
}
catch {
    Write-Host "ОШИБКА при загрузке: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
} 