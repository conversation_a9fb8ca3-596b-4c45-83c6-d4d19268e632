Write-Host "📥 СКАЧИВАЮ АКТУАЛЬНЫЕ ЛОГИ С СЕРВЕРА..." -ForegroundColor Green

# Создаем папку для логов
if (!(Test-Path "server_logs_latest")) {
    New-Item -ItemType Directory -Path "server_logs_latest" | Out-Null
}

# FTP параметры
$ftpServer = "bekflom3.beget.tech"
$ftpUser = "bekflom3_wz"
$ftpPassword = 'zl2Shj!e&6ng'

# Функция для скачивания файла через FTP
function Download-FtpFile {
    param(
        [string]$FtpUri,
        [string]$LocalPath,
        [string]$Username,
        [string]$Password
    )
    
    try {
        $request = [System.Net.FtpWebRequest]::Create($FtpUri)
        $request.Method = [System.Net.WebRequestMethods+Ftp]::DownloadFile
        $request.Credentials = New-Object System.Net.NetworkCredential($Username, $Password)
        $request.UseBinary = $true
        
        $response = $request.GetResponse()
        $responseStream = $response.GetResponseStream()
        
        $fileStream = [System.IO.File]::Create($LocalPath)
        $responseStream.CopyTo($fileStream)
        
        $fileStream.Close()
        $responseStream.Close()
        $response.Close()
        
        Write-Host "✅ Скачан: $LocalPath" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "❌ Ошибка скачивания $FtpUri : $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

Write-Host "🔄 Скачиваю debug.log..." -ForegroundColor Yellow
$debugLogUri = "ftp://$ftpServer/public_html/wp-content/debug.log"
$debugLogLocal = "server_logs_latest/debug_latest.log"
Download-FtpFile -FtpUri $debugLogUri -LocalPath $debugLogLocal -Username $ftpUser -Password $ftpPassword

Write-Host "🔄 Скачиваю tablecrm_debug.log..." -ForegroundColor Yellow
$tablecrmLogUri = "ftp://$ftpServer/public_html/wp-content/uploads/tablecrm_debug.log"
$tablecrmLogLocal = "server_logs_latest/tablecrm_debug_latest.log"
Download-FtpFile -FtpUri $tablecrmLogUri -LocalPath $tablecrmLogLocal -Username $ftpUser -Password $ftpPassword

Write-Host "🔄 Скачиваю tablecrm_complete.log..." -ForegroundColor Yellow
$completeLogUri = "ftp://$ftpServer/public_html/wp-content/tablecrm_complete.log"
$completeLogLocal = "server_logs_latest/tablecrm_complete_latest.log"
Download-FtpFile -FtpUri $completeLogUri -LocalPath $completeLogLocal -Username $ftpUser -Password $ftpPassword

Write-Host ""
Write-Host "📋 АНАЛИЗ ПОСЛЕДНИХ ЗАПИСЕЙ В ЛОГАХ:" -ForegroundColor Cyan
Write-Host ""

if (Test-Path $debugLogLocal) {
    Write-Host "=== ПОСЛЕДНИЕ 20 СТРОК DEBUG.LOG ===" -ForegroundColor Yellow
    Get-Content $debugLogLocal | Select-Object -Last 20
    Write-Host ""
}

if (Test-Path $tablecrmLogLocal) {
    Write-Host "=== ПОСЛЕДНИЕ 20 СТРОК TABLECRM DEBUG ===" -ForegroundColor Yellow
    Get-Content $tablecrmLogLocal | Select-Object -Last 20
    Write-Host ""
}

if (Test-Path $completeLogLocal) {
    Write-Host "=== ПОСЛЕДНИЕ 20 СТРОК TABLECRM COMPLETE ===" -ForegroundColor Yellow
    Get-Content $completeLogLocal | Select-Object -Last 20
    Write-Host ""
}

Write-Host "✅ ЛОГИ СКАЧАНЫ В ПАПКУ server_logs_latest/" -ForegroundColor Green

# Показываем размеры файлов
Write-Host ""
Write-Host "📊 РАЗМЕРЫ СКАЧАННЫХ ФАЙЛОВ:" -ForegroundColor Cyan
Get-ChildItem "server_logs_latest/*.log" | ForEach-Object {
    $size = if ($_.Length -gt 1MB) { "{0:N2} MB" -f ($_.Length / 1MB) } 
            elseif ($_.Length -gt 1KB) { "{0:N2} KB" -f ($_.Length / 1KB) }
            else { "$($_.Length) bytes" }
    Write-Host "  $($_.Name): $size" -ForegroundColor White
} 