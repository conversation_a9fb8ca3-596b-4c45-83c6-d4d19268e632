<?php
/**
 * Тестовый файл для проверки удаления имени из адреса доставки
 */

// Подключаем WordPress
require_once('wp-config.php');

echo "=== Тест удаления имени из адреса доставки ===\n\n";

// Получаем заказы для тестирования
$orders = wc_get_orders(array(
    'limit' => 5,
    'orderby' => 'date',
    'order' => 'DESC'
));

if (empty($orders)) {
    echo "❌ Нет заказов для тестирования\n";
    exit;
}

// Получаем экземпляр плагина
$plugin = TableCRM_Integration_Complete::get_instance();

foreach ($orders as $order) {
    $order_id = $order->get_id();
    
    echo "--- Заказ ID: {$order_id} ---\n";
    
    // Получаем исходные данные адреса
    $shipping_address_1 = $order->get_shipping_address_1();
    $shipping_address_2 = $order->get_shipping_address_2();
    $shipping_city = $order->get_shipping_city();
    $shipping_first_name = $order->get_shipping_first_name();
    $shipping_last_name = $order->get_shipping_last_name();
    
    echo "Исходные данные:\n";
    echo "  Имя: '{$shipping_first_name}'\n";
    echo "  Фамилия: '{$shipping_last_name}'\n";
    echo "  Адрес 1: '{$shipping_address_1}'\n";
    echo "  Адрес 2: '{$shipping_address_2}'\n";
    echo "  Город: '{$shipping_city}'\n";
    
    // Используем рефлексию для вызова приватного метода
    $reflection = new ReflectionClass($plugin);
    $method = $reflection->getMethod('get_recipient_address');
    $method->setAccessible(true);
    
    $cleaned_address = $method->invoke($plugin, $order);
    
    echo "Очищенный адрес: '{$cleaned_address}'\n";
    
    // Проверяем, содержит ли очищенный адрес имя
    $full_name = trim($shipping_first_name . ' ' . $shipping_last_name);
    if (!empty($full_name) && strpos($cleaned_address, $full_name) !== false) {
        echo "⚠️ ВНИМАНИЕ: Имя '{$full_name}' все еще присутствует в адресе!\n";
    } elseif (!empty($shipping_first_name) && strpos($cleaned_address, $shipping_first_name) !== false) {
        echo "⚠️ ВНИМАНИЕ: Имя '{$shipping_first_name}' все еще присутствует в адресе!\n";
    } else {
        echo "✅ Имя успешно удалено из адреса\n";
    }
    
    echo "\n";
}

echo "=== Тест завершен ===\n";
?>
