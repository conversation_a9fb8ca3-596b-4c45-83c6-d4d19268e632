# Find TableCRM log files on FTP server
Write-Host "Searching for TableCRM log files..." -ForegroundColor Green

$ftpServer = "bekflom3.beget.tech"
$ftpUser = "bekflom3_wz"
$ftpPassword = '*rJ*a&7IF*gJ'

# Possible paths to check
$paths = @(
    "/",
    "/public_html/",
    "/public_html/wp-content/",
    "/public_html/wp-content/uploads/",
    "/public_html/wp-content/plugins/",
    "/public_html/wp-content/debug.log",
    "/wp-content/uploads/",
    "/wp-content/plugins/"
)

foreach ($path in $paths) {
    try {
        Write-Host "Checking path: $path" -ForegroundColor Yellow
        $uri = "ftp://$ftpServer$path"
        $ftp = [System.Net.FtpWebRequest]::Create($uri)
        $ftp.Credentials = New-Object System.Net.NetworkCredential($ftpUser, $ftpPassword)
        $ftp.Method = [System.Net.WebRequestMethods+Ftp]::ListDirectory
        
        $response = $ftp.GetResponse()
        $stream = $response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($stream)
        $files = $reader.ReadToEnd()
        $reader.Close()
        $response.Close()
        
        if ($files -match "tablecrm|debug\.log") {
            Write-Host "Found files in $path :" -ForegroundColor Green
            $files -split "`n" | Where-Object { $_ -match "tablecrm|debug\.log|\.log" } | ForEach-Object { Write-Host "  $_" -ForegroundColor Cyan }
        }
    }
    catch {
        Write-Host "Cannot access $path : $($_.Exception.Message)" -ForegroundColor Red
    }
} 