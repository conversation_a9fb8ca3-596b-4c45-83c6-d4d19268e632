<?php
// Проверка логов TableCRM интеграции
echo "=== ПРОВЕРКА ЛОГОВ TABLECRM ИНТЕГРАЦИИ ===\n";

$wp_path = '/home/<USER>/bekflom3/mosbuketik.ru/public_html';
require_once($wp_path . '/wp-config.php');
require_once($wp_path . '/wp-load.php');

echo "WordPress загружен успешно\n\n";

// 1. Проверяем лог файл плагина
$log_file = WP_CONTENT_DIR . '/uploads/tablecrm_integration_complete.log';
echo "=== ЛОГ ФАЙЛ ПЛАГИНА ===\n";
echo "Путь к логу: $log_file\n";

if (file_exists($log_file)) {
    echo "✅ Лог файл существует\n";
    $log_size = filesize($log_file);
    echo "Размер файла: " . number_format($log_size) . " байт\n\n";
    
    // Читаем последние 50 строк лога
    echo "=== ПОСЛЕДНИЕ 50 СТРОК ЛОГА ===\n";
    $lines = file($log_file);
    $total_lines = count($lines);
    echo "Всего строк в логе: $total_lines\n\n";
    
    $start_line = max(0, $total_lines - 50);
    for ($i = $start_line; $i < $total_lines; $i++) {
        echo ($i + 1) . ": " . trim($lines[$i]) . "\n";
    }
} else {
    echo "❌ Лог файл не найден\n";
}

echo "\n=== ПОСЛЕДНИЕ ЗАКАЗЫ WOOCOMMERCE ===\n";

// 2. Проверяем последние заказы
$orders = wc_get_orders(array(
    'limit' => 5,
    'orderby' => 'date',
    'order' => 'DESC',
    'status' => array('processing', 'completed', 'pending')
));

foreach ($orders as $order) {
    $order_id = $order->get_id();
    $order_status = $order->get_status();
    $order_date = $order->get_date_created()->format('Y-m-d H:i:s');
    
    echo "\n--- ЗАКАЗ #$order_id ---\n";
    echo "Статус: $order_status\n";
    echo "Дата создания: $order_date\n";
    
    // Проверяем мета-данные TableCRM
    $tablecrm_doc_id = get_post_meta($order_id, '_tablecrm_complete_document_id', true);
    $tablecrm_sent_at = get_post_meta($order_id, '_tablecrm_complete_sent_at', true);
    
    if (!empty($tablecrm_doc_id)) {
        echo "✅ TableCRM Document ID: $tablecrm_doc_id\n";
        if (!empty($tablecrm_sent_at)) {
            echo "✅ Отправлен в TableCRM: $tablecrm_sent_at\n";
        }
    } else {
        echo "❌ НЕ отправлен в TableCRM\n";
    }
    
    // Проверяем данные доставки
    $delivery_date = $order->get_meta('delivery_date');
    $delivery_time = $order->get_meta('delivery_time');
    $delivery_type = $order->get_meta('delivery_type');
    
    if (!empty($delivery_date)) {
        echo "📅 Дата доставки: $delivery_date\n";
    }
    if (!empty($delivery_time)) {
        echo "⏰ Время доставки: $delivery_time\n";
    }
    if (!empty($delivery_type)) {
        echo "🚚 Тип доставки: $delivery_type\n";
    }
    
    // Проверяем стоимость доставки
    $shipping_total = $order->get_shipping_total();
    if ($shipping_total > 0) {
        echo "💰 Стоимость доставки: $shipping_total руб.\n";
    } else {
        echo "❌ Доставка бесплатная или не настроена\n";
    }
    
    // Проверяем заметки заказа
    $notes = wc_get_order_notes(array('order_id' => $order_id, 'limit' => 3));
    if (!empty($notes)) {
        echo "📝 Последние заметки:\n";
        foreach ($notes as $note) {
            echo "  - " . strip_tags($note->content) . " (" . $note->date_created->format('Y-m-d H:i:s') . ")\n";
        }
    }
}

echo "\n=== НАСТРОЙКИ ПЛАГИНА ===\n";
$enable_orders = get_option('tablecrm_complete_enable_orders', 1);
$api_key = get_option('tablecrm_complete_api_key');
$api_url = get_option('tablecrm_complete_api_url');
$trigger_statuses = get_option('tablecrm_complete_order_statuses', array('processing', 'completed'));

echo "Отправка заказов включена: " . ($enable_orders ? 'ДА' : 'НЕТ') . "\n";
echo "API URL: $api_url\n";
echo "API Key настроен: " . (!empty($api_key) ? 'ДА' : 'НЕТ') . "\n";
echo "Статусы для отправки: " . implode(', ', $trigger_statuses) . "\n";

echo "\n=== ПРОВЕРКА ХУКОВ WORDPRESS ===\n";

// Проверяем, зарегистрированы ли хуки
global $wp_filter;

$hooks_to_check = array(
    'woocommerce_order_status_changed',
    'woocommerce_new_order',
    'woocommerce_checkout_order_processed'
);

foreach ($hooks_to_check as $hook) {
    if (isset($wp_filter[$hook])) {
        echo "✅ Хук '$hook' зарегистрирован\n";
        
        // Проверяем наши колбэки
        foreach ($wp_filter[$hook]->callbacks as $priority => $callbacks) {
            foreach ($callbacks as $callback) {
                if (is_array($callback['function']) && 
                    is_object($callback['function'][0]) && 
                    get_class($callback['function'][0]) === 'TableCRM_Integration_Complete') {
                    echo "  - Наш колбэк найден с приоритетом $priority\n";
                }
            }
        }
    } else {
        echo "❌ Хук '$hook' НЕ зарегистрирован\n";
    }
}

echo "\n=== ПРОВЕРКА ЗАВЕРШЕНА ===\n";
?> 