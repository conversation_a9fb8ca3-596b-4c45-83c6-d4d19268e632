<?php
/**
 * Экстренное отключение плагина Yandex Pay
 * Этот скрипт временно деактивирует плагин, вызывающий ошибки checkout
 */

// Имитируем среду WordPress для деактивации плагина
if (!defined('ABSPATH')) {
    // Ищем wp-config.php в возможных местах
    $config_paths = [
        '/home/<USER>/bekflom3/mosbuketik.ru/public_html/wp-config.php',
        '../wp-config.php',
        '../../wp-config.php',
        '../../../wp-config.php'
    ];
    
    $config_found = false;
    foreach ($config_paths as $config_path) {
        if (file_exists($config_path)) {
            echo "Найден wp-config.php: $config_path\n";
            require_once($config_path);
            $config_found = true;
            break;
        }
    }
    
    if (!$config_found) {
        echo "❌ wp-config.php не найден. Выполняем ручное отключение...\n";
        
        // Ручное отключение через переименование папки плагина
        $plugin_paths = [
            '/home/<USER>/bekflom3/mosbuketik.ru/public_html/wp-content/plugins/yandex-pay-and-split',
            '../wp-content/plugins/yandex-pay-and-split',
            '../../wp-content/plugins/yandex-pay-and-split'
        ];
        
        foreach ($plugin_paths as $plugin_path) {
            if (is_dir($plugin_path)) {
                $backup_path = $plugin_path . '_DISABLED_' . date('Y-m-d_H-i-s');
                if (rename($plugin_path, $backup_path)) {
                    echo "✅ Плагин Yandex Pay отключен: $plugin_path → $backup_path\n";
                    echo "📝 Для восстановления переименуйте папку обратно\n";
                    exit(0);
                } else {
                    echo "❌ Ошибка переименования: $plugin_path\n";
                }
            }
        }
        
        echo "❌ Папка плагина Yandex Pay не найдена\n";
        exit(1);
    }
}

// Если WordPress загружен, используем стандартные функции
if (function_exists('deactivate_plugins')) {
    echo "Отключаем плагин Yandex Pay через WordPress API...\n";
    
    $yandex_plugins = [
        'yandex-pay-and-split/yandex-pay-and-split.php',
        'yandex-pay/yandex-pay.php',
        'yandex-checkout-for-woocommerce/yandex-checkout-for-woocommerce.php'
    ];
    
    foreach ($yandex_plugins as $plugin) {
        if (is_plugin_active($plugin)) {
            deactivate_plugins($plugin);
            echo "✅ Плагин отключен: $plugin\n";
        }
    }
    
    echo "✅ Все плагины Yandex отключены\n";
} else {
    echo "❌ Функции WordPress недоступны\n";
}

echo "\n=== ИНСТРУКЦИИ ПО ВОССТАНОВЛЕНИЮ ===\n";
echo "1. Проверьте работу checkout без Yandex Pay\n";
echo "2. Если checkout работает - проблема была в Yandex Pay\n";
echo "3. Обновите плагин Yandex Pay до последней версии\n";
echo "4. Проверьте совместимость с версией WooCommerce\n";
echo "5. Включите плагин обратно после обновления\n";

echo "\n=== АЛЬТЕРНАТИВНЫЕ СПОСОБЫ ОПЛАТЫ ===\n";
echo "Пока Yandex Pay отключен, доступны:\n";
echo "- Банковские карты через другие платежные системы\n";
echo "- Наложенный платеж\n";
echo "- Банковский перевод\n";
echo "- СБП (если настроен)\n";
?> 