# Upload and run deep debug script
$server = "bekflom3.beget.tech"
$user = "bekflom3_wz"
$pass = "zl2Shj!e&6ng"

Write-Host "Uploading debug script..." -ForegroundColor Green

try {
    $uri = "ftp://$server/public_html/debug_plugin_issue.php"
    $webclient = New-Object System.Net.WebClient
    $webclient.Credentials = New-Object System.Net.NetworkCredential($user, $pass)
    $webclient.UploadFile($uri, "debug_plugin_issue.php")
    Write-Host "Debug script uploaded!" -ForegroundColor Green
    
    # Run the debug script
    Write-Host "Running deep debug..." -ForegroundColor Yellow
    $result = Invoke-WebRequest -Uri "https://mosbuketik.ru/debug_plugin_issue.php" -UseBasicParsing
    Write-Host $result.Content -ForegroundColor White
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

$webclient.Dispose()
Write-Host "Debug completed!" -ForegroundColor Green 