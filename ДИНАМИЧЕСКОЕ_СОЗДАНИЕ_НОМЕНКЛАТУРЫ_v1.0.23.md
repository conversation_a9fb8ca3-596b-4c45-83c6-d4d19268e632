# Динамическое создание номенклатуры и контрагентов в TableCRM Integration v1.0.23

## Обзор критических изменений

Версия 1.0.23 представляет кардинальное изменение в архитектуре плагина. Теперь он использует правильный многошаговый алгоритм работы с API TableCRM, основанный на официальной документации.

## Ключевые изменения

### 1. Правильный алгоритм работы с API

**Ранее (некорректно):**
- Плагин отправлял один запрос с жестко заданными ID
- Использовал фиксированный `nomenclature: 39697`
- В результате все товары отображались как "Testing"

**Теперь (корректно):**
- Поиск/создание контрагента по номеру телефона
- Поиск/создание номенклатуры для каждого товара
- Отправка заказа с корректными динамическими ID

### 2. Новые функции

#### `get_or_create_tablecrm_contragent_id()`
```php
// Поиск клиента по телефону
GET /api/v1/contragents/?token={token}&phone={phone}

// Создание нового клиента (если не найден)
POST /api/v1/contragents/?token={token}
{
    "name": "Имя Фамилия",
    "phone": "+79377799906",
    "email": "<EMAIL>",
    "our_cost": false
}
```

#### `get_or_create_tablecrm_nomenclature_id()`
```php
// Поиск товара по названию
GET /api/v1/nomenclature/?token={token}&name={product_name}

// Создание нового товара (если не найден)
POST /api/v1/nomenclatures/?token={token}
{
    "name": "Букет из 39 альстромерий",
    "unit": 116
}
```

### 3. Последовательность выполнения

1. **Проверка настроек плагина**
2. **Получение/создание контрагента:**
   - Извлечение телефона из заказа
   - Поиск существующего контрагента
   - Создание нового при отсутствии
3. **Получение/создание номенклатуры:**
   - Для каждого товара в заказе
   - Поиск по точному названию
   - Создание новой позиции при отсутствии
4. **Формирование финального JSON:**
   - Использование полученных динамических ID
   - Отправка заказа в TableCRM

## Логирование

Плагин теперь записывает подробные логи каждого шага: 

# ДИНАМИЧЕСКОЕ СОЗДАНИЕ НОМЕНКЛАТУРЫ + API ДОСТАВКИ v1.0.24

## Обзор обновлений

✅ **Динамическое создание номенклатуры** - автоматическое создание товаров в TableCRM  
✅ **Улучшенный поиск контрагентов** по телефону и email  
✅ **Защита от дублирования** заказов и данных  
✅ **UTM-аналитика** с расширенными параметрами  
✅ **🆕 Автоматическая отправка информации о доставке** - POST `/docs_sales/{id}/delivery_info/`

## 🚚 НОВОЕ: Интеграция API доставки

### Описание

В версии 1.0.24 добавлена полная поддержка API доставки TableCRM. После создания документа продаж автоматически отправляется детальная информация о доставке через эндпоинт `POST /docs_sales/{id}/delivery_info/`.

### Структура данных доставки

```json
{
  "recipient": {
    "name": "Иван Петров",
    "phone": "+79123456789"
  },
  "address": "ул. Ленина, 1, кв. 5, г. Москва, 101000",
  "note": "Доставить после 18:00",
  "delivery_date": 1672531200
}
```

### Поддерживаемые плагины доставки

| Плагин доставки | Поддержка | Источник даты |
|-----------------|-----------|---------------|
| **CodeRockz WooCommerce Delivery Date Time Pro** | ✅ Полная | `delivery_date` + `delivery_time` |
| **Order Delivery Date Pro** | ✅ Базовая | `_orddd_time_slot` |
| **WooCommerce Delivery Time** | ✅ Базовая | `_delivery_time` |
| **Custom fields** | ✅ Автоматическая | `_delivery_date` |

### Алгоритм работы

1. **WooCommerce создаёт заказ**
2. **Динамическое создание контрагента** (по телефону/email)
3. **Динамическое создание номенклатуры** для каждого товара
4. **Отправка документа продаж** → `POST /docs_sales/`
5. **🆕 Автоматическая отправка доставки** → `POST /docs_sales/{document_id}/delivery_info/`
6. **Отправка UTM-данных** → `POST /docs_sales/{document_id}/utm/`

### Логирование доставки

```
[2024-01-15 10:30:15] [INFO] Запуск функции send_delivery_info для заказа ID: 12345
[2024-01-15 10:30:15] [INFO] Найдена дата от CodeRockz: '15/01/2024'
[2024-01-15 10:30:15] [INFO] Найдено время от CodeRockz: '14:00 - 16:00'
[2024-01-15 10:30:15] [SUCCESS] Дата и время от CodeRockz ('15-01-2024 14:00') успешно преобразованы в timestamp: 1705320000
[2024-01-15 10:30:15] [INFO] Подготовленные данные для delivery_info: {"recipient":{"name":"Иван Петров","phone":"+79123456789"},"address":"ул. Ленина, 1, Москва, 101000","note":"Доставить после 18:00","delivery_date":1705320000}
[2024-01-15 10:30:16] [SUCCESS] Информация о доставке успешно отправлена для документа 67890
```

### Результат в TableCRM

В карточке документа появляется заполненная секция **"Доставка"**:
- 👤 **Получатель**: Иван Петров, +79123456789
- 📍 **Адрес**: ул. Ленина, 1, кв. 5, г. Москва, 101000
- 📅 **Дата доставки**: 15 января 2024, 14:00-16:00
- 💬 **Комментарий**: Доставить после 18:00

## Динамическое создание номенклатуры

### Как работает

1. **Анализ товаров** в заказе WooCommerce
2. **Поиск существующей номенклатуры** по названию через API
3. **Создание новой номенклатуры** если не найдена
4. **Привязка к документу** с корректными ID

### Алгоритм поиска/создания

```php
private function get_or_create_nomenclature($item_name, $api_url, $api_token) {
    // 1. Поиск существующей номенклатуры
    $search_url = $api_url . 'nomenclature/?token=' . $api_token . 
                  '&name=' . urlencode($item_name) . 
                  '&limit=100&offset=0';
    
    $search_response = wp_remote_get($search_url);
    
    // Проверка разных форматов ответа API
    if (isset($search_data['result']) && !empty($search_data['result'])) {
        return $search_data['result'][0]['id']; // Найдена существующая
    }
    
    // 2. Создание новой номенклатуры
    $create_data = array(array(
        'name' => $item_name, 
        'unit' => $unit_id
    ));
    
    $create_response = wp_remote_post($create_url, array(
        'body' => json_encode($create_data, JSON_UNESCAPED_UNICODE)
    ));
    
    return $create_result['id']; // Возвращаем ID новой номенклатуры
}
```

### Обработка ошибок

- **404 Not Found**: номенклатура не найдена → создаём новую
- **409 Conflict**: номенклатура уже существует → повторный поиск
- **422 Validation Error**: некорректные данные → логирование и пропуск товара
- **500 Server Error**: проблемы API → повторная попытка с backoff

### Логи создания номенклатуры

```
[2024-01-15 10:25:10] [INFO] Поиск/создание номенклатуры для товара: 'iPhone 14 Pro 128GB'
[2024-01-15 10:25:11] [INFO] Поиск номенклатуры - Status: 200, Response: {"result":[],"count":0}
[2024-01-15 10:25:11] [INFO] Номенклатура не найдена, создаем новую
[2024-01-15 10:25:12] [INFO] Создание номенклатуры - Status: 201, Response: {"id":45678,"name":"iPhone 14 Pro 128GB"}
[2024-01-15 10:25:12] [SUCCESS] Новая номенклатура создана. ID: 45678
```

## Улучшенный поиск контрагентов

### Нормализация телефонов

```php
// Приведение к единому формату +7XXXXXXXXXX
$normalized_phone = preg_replace('/[^0-9]/', '', $phone);
if (strlen($normalized_phone) == 11 && substr($normalized_phone, 0, 1) == '8') {
    $normalized_phone = '7' . substr($normalized_phone, 1);
}
if (strlen($normalized_phone) == 10) {
    $normalized_phone = '7' . $normalized_phone;
}
$search_phone = '+' . $normalized_phone;
```

### Алгоритм поиска

1. **Поиск по телефону** с параметром `phone`
2. **Поиск по email** если телефон не найден  
3. **Создание нового контрагента** с валидацией данных
4. **Повторный поиск** при конфликте создания

### Очистка имён от телефонов

```php
$is_phone = function($str) {
    return preg_match('/^[\+]?[0-9\(\)\-\s]{7,}$/', $str) || 
           strpos($str, '+7') === 0 || 
           strpos($str, '8(') === 0;
};

$clean_first_name = (!empty($first_name) && !$is_phone($first_name)) ? $first_name : '';
$clean_last_name = (!empty($last_name) && !$is_phone($last_name)) ? $last_name : '';
```

## Защита от дублирования

### Проверки дублирования

1. **Проверка document_id** в мета-полях заказа
2. **Блокировка повторной отправки** того же заказа
3. **Проверка существования контрагента** перед созданием
4. **Проверка существования номенклатуры** перед созданием

### Атомарные операции

```php
// Проверка на уже отправленный заказ
$existing_doc_id = get_post_meta($order_id, '_tablecrm_complete_document_id', true);
if (!empty($existing_doc_id)) {
    $this->log_info("Заказ $order_id уже был отправлен (Document ID: $existing_doc_id)");
    return true;
}

// Атомарное сохранение после успешной отправки
update_post_meta($order_id, '_tablecrm_complete_document_id', $document_id);
update_post_meta($order_id, '_tablecrm_complete_sent_at', current_time('mysql'));
```

## Расширенная UTM-аналитика

### Поддерживаемые параметры

#### Стандартные UTM
- `utm_source` - источник трафика
- `utm_medium` - канал/медиум  
- `utm_campaign` - кампания
- `utm_term` - ключевые слова (массив)
- `utm_content` - содержание объявления

#### Расширенные UTM (TableCRM)
- `utm_name` - имя клиента из UTM
- `utm_phone` - телефон клиента из UTM
- `utm_email` - email клиента из UTM
- `utm_leadid` - ID лида
- `utm_yclientid` - Yandex Client ID
- `utm_gaclientid` - Google Analytics Client ID

#### Click ID параметры
- `gclid` - Google Ads Click ID
- `fbclid` - Facebook Ads Click ID  
- `yclid` - Yandex Direct Click ID

### Источники аналитических данных

1. **WooCommerce Order Attribution** - встроенная аналитика WC
2. **UTM cookies** - сохранённые через utm-tracking.js
3. **POST/GET параметры** - прямые UTM из URL
4. **Meta-поля заказа** - сохранённые другими плагинами

### Формирование комментария аналитики

```php
private function format_analytics_comment($analytics) {
    $comment_parts = array();
    
    if (isset($analytics['utm_source'])) {
        $comment_parts[] = "Источник: " . $analytics['utm_source'];
    }
    if (isset($analytics['utm_campaign'])) {
        $comment_parts[] = "Кампания: " . $analytics['utm_campaign'];
    }
    if (isset($analytics['gclid'])) {
        $comment_parts[] = "Google Ads: " . substr($analytics['gclid'], 0, 20) . "...";
    }
    
    return empty($comment_parts) ? '' : '. Аналитика: ' . implode(', ', $comment_parts);
}
```

## Формирование итогового документа

### Структура документа продаж

```php
$final_data = array(array(
    'dated' => time(),
    'operation' => 'Заказ',
    'comment' => $comment_with_analytics,
    'tax_included' => true,
    'tax_active' => true,
    'goods' => $goods_with_dynamic_nomenclature,
    'settings' => new stdClass(),
    'warehouse' => $warehouse_id,
    'contragent' => $dynamic_contragent_id,
    'paybox' => $paybox_id,
    'organization' => $organization_id,
    'status' => $order->is_paid(),
    'paid_rubles' => $order->is_paid() ? $total_amount : '0.00',
    'paid_lt' => 0,
));
```

### Товары с динамической номенклатурой

```php
foreach ($items as $item_id => $item) {
    $nomenclature_id = $nomenclature_ids_map[$item_id]; // Динамически созданная
    
    $goods[] = array(
        'price' => $unit_price,
        'quantity' => $quantity,
        'unit' => $unit_id,
        'discount' => 0,
        'sum_discounted' => 0,
        'nomenclature' => $nomenclature_id, // ID новой или существующей номенклатуры
        'name' => $item_name,
        'sku' => $product_sku,
    );
}

// Добавление доставки как отдельного товара
if ($shipping_total > 0) {
    $delivery_nomenclature_id = $this->get_or_create_nomenclature('Доставка', $api_url, $api_token);
    $goods[] = array(
        'price' => $shipping_total,
        'quantity' => 1,
        'nomenclature' => $delivery_nomenclature_id,
        'name' => 'Доставка',
    );
}
```

## Полный пример успешной отправки

```
[2024-01-15 10:20:00] [INFO] ==> Начинаем отправку заказа 12345 в TableCRM
[2024-01-15 10:20:01] [INFO] Поиск/создание контрагента для телефона: 8(912)345-67-89 (нормализованный: +79123456789)
[2024-01-15 10:20:02] [SUCCESS] Контрагент найден в result. ID: 12345
[2024-01-15 10:20:03] [INFO] Поиск/создание номенклатуры для товара: 'iPhone 14 Pro 128GB'
[2024-01-15 10:20:04] [SUCCESS] Новая номенклатура создана. ID: 45678
[2024-01-15 10:20:05] [INFO] Поиск/создание номенклатуры для товара: 'Доставка'
[2024-01-15 10:20:06] [SUCCESS] Номенклатура найдена в result. ID: 98765
[2024-01-15 10:20:07] [INFO] Отправляем заказ 12345 в TableCRM API...
[2024-01-15 10:20:08] [SUCCESS] Заказ 12345 успешно отправлен. Document ID: 67890
[2024-01-15 10:20:09] [SUCCESS] UTM-данные успешно отправлены для документа 67890
[2024-01-15 10:20:10] [SUCCESS] Информация о доставке успешно отправлена для документа 67890
```

## Проверка работы

### 1. В логах WordPress
```bash
tail -f /wp-content/uploads/tablecrm_integration_complete.log
```

### 2. В админ-панели WooCommerce
Проверьте заметки заказа на наличие:
- "Заказ успешно отправлен в TableCRM. Document ID: XXXXX"
- "Информация о доставке успешно отправлена в TableCRM."

### 3. В TableCRM
- **Документы продаж**: новый документ с корректными товарами
- **Контрагенты**: автоматически созданный или найденный
- **Номенклатура**: новые товары с правильными названиями  
- **UTM-данные**: аналитическая информация
- **🆕 Доставка**: заполненная секция с получателем и адресом

## Диагностика проблем

### Проблемы с номенклатурой
```bash
grep "номенклатур" /wp-content/uploads/tablecrm_integration_complete.log
```

### Проблемы с контрагентами  
```bash
grep "контрагент" /wp-content/uploads/tablecrm_integration_complete.log
```

### Проблемы с доставкой
```bash
grep "delivery_info" /wp-content/uploads/tablecrm_integration_complete.log
```

### Общие ошибки API
```bash
grep "ERROR\|HTTP 4\|HTTP 5" /wp-content/uploads/tablecrm_integration_complete.log
```

**Интеграция полностью готова к работе с динамическим созданием всех сущностей и автоматической отправкой информации о доставке! 🚀** 