<?php
/**
 * Plugin Name: TableCRM Integration Complete
 * Description: Loader, подключающий актуальную версию плагина.
 * Version: 1.0.24-loader
 */



if (!defined('ABSPATH')) {
    exit;
}

// Определение констант плагина
define('TABLECRM_COMPLETE_PLUGIN_URL', plugin_dir_url(__FILE__));
define('TABLECRM_COMPLETE_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('TABLECRM_COMPLETE_VERSION', '1.0.24');

class TableCRM_Integration_Complete {
    private $orders_to_send_on_shutdown = array();
    
    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'register_settings'));
        
        // Хуки для WooCommerce
        add_action('woocommerce_order_status_changed', array($this, 'handle_order_status_change'), 10, 3);
        // add_action('woocommerce_new_order', array($this, 'handle_new_order'), 10, 1); // отключено, чтобы избежать дублей
        add_action('woocommerce_checkout_order_processed', array($this, 'handle_new_order'), 10, 1);
        // Action Scheduler async hook
        if (function_exists('as_enqueue_async_action')) {
            add_action('tablecrm_complete_async_send', array($this, 'send_order_to_tablecrm'), 10, 1);
        }
        
        // AJAX хуки
        add_action('wp_ajax_test_tablecrm_connection_complete', array($this, 'test_connection'));
        
        // Хук для фоновой отправки после ответа пользователю
        add_action('shutdown', array($this, 'send_orders_on_shutdown'));
        
        // Хук активации
        register_activation_hook(__FILE__, array($this, 'activate_plugin'));
    }
    
    public function init() {
        load_plugin_textdomain('tablecrm-integration-complete', false, dirname(plugin_basename(__FILE__)) . '/languages');
    }
    
    public function activate_plugin() {
        // Устанавливаем настройки по умолчанию
        if (!get_option('tablecrm_complete_api_url')) {
            update_option('tablecrm_complete_api_url', 'https://app.tablecrm.com/api/v1/');
        }
        if (!get_option('tablecrm_complete_organization_id')) {
            update_option('tablecrm_complete_organization_id', 38);
        }
        if (!get_option('tablecrm_complete_cashbox_id')) {
            update_option('tablecrm_complete_cashbox_id', 218);
        }
        if (!get_option('tablecrm_complete_warehouse_id')) {
            update_option('tablecrm_complete_warehouse_id', 39);
        }
        if (!get_option('tablecrm_complete_unit_id')) {
            update_option('tablecrm_complete_unit_id', 116);
        }
        if (!get_option('tablecrm_complete_enable_orders')) {
            update_option('tablecrm_complete_enable_orders', 1);
        }
        if (!get_option('tablecrm_complete_debug_mode')) {
            update_option('tablecrm_complete_debug_mode', 1);
        }
        if (!get_option('tablecrm_complete_order_statuses')) {
            update_option('tablecrm_complete_order_statuses', array('processing', 'completed'));
        }
        
        $this->log_success('Плагин TableCRM Integration Complete v1.0.24 активирован');
    }
    
    // Добавление меню в админку
    public function add_admin_menu() {
        add_options_page(
            'TableCRM Integration Complete Settings',
            'TableCRM Complete',
            'manage_options',
            'tablecrm-complete-settings',
            array($this, 'admin_page')
        );
        
        // Добавляем страницу для тестирования
        add_submenu_page(
            'options-general.php',
            'Тест TableCRM Доставка',
            'Тест TableCRM Доставка',
            'manage_options',
            'test-tablecrm-delivery',
            array($this, 'test_delivery_page')
        );
    }
    
    // Страница тестирования доставки
    public function test_delivery_page() {
        // Проверяем права доступа
        if (!current_user_can('manage_options')) {
            wp_die('Доступ запрещен');
        }
        
        // Создаем nonce для безопасности
        $nonce_url = wp_nonce_url(admin_url('admin.php?page=test-tablecrm-delivery'), 'test_tablecrm_delivery');
        
        // Выводим ссылку на тест
        echo '<div class="wrap">';
        echo '<h1>Тест интеграции TableCRM - Доставка</h1>';
        echo '<p>Нажмите кнопку ниже, чтобы протестировать отправку информации о доставке для последнего заказа:</p>';
        echo '<a href="' . esc_url($nonce_url) . '" class="button button-primary">Запустить тест</a>';
        echo '</div>';
        
        // Если есть nonce, запускаем тест
        if (isset($_GET['_wpnonce']) && wp_verify_nonce($_GET['_wpnonce'], 'test_tablecrm_delivery')) {
            include_once(plugin_dir_path(__FILE__) . 'test-tablecrm-delivery.php');
        }
    }
    
    // Регистрация настроек
    public function register_settings() {
        register_setting('tablecrm_complete_settings', 'tablecrm_complete_api_url', array(
            'type' => 'string',
            'sanitize_callback' => array($this, 'sanitize_api_url'),
            'default' => 'https://app.tablecrm.com/api/v1/'
        ));
        
        register_setting('tablecrm_complete_settings', 'tablecrm_complete_api_key', array(
            'type' => 'string',
            'sanitize_callback' => array($this, 'sanitize_api_key'),
            'default' => ''
        ));
        
        register_setting('tablecrm_complete_settings', 'tablecrm_complete_organization_id', array(
            'type' => 'integer',
            'sanitize_callback' => 'absint',
            'default' => 38
        ));
        
        register_setting('tablecrm_complete_settings', 'tablecrm_complete_cashbox_id', array(
            'type' => 'integer',
            'sanitize_callback' => 'absint',
            'default' => 218
        ));
        
        register_setting('tablecrm_complete_settings', 'tablecrm_complete_warehouse_id', array(
            'type' => 'integer',
            'sanitize_callback' => 'absint',
            'default' => 39
        ));
        
        register_setting('tablecrm_complete_settings', 'tablecrm_complete_unit_id', array(
            'type' => 'integer',
            'sanitize_callback' => 'absint',
            'default' => 116
        ));
        
        register_setting('tablecrm_complete_settings', 'tablecrm_complete_enable_orders', array(
            'type' => 'boolean',
            'sanitize_callback' => array($this, 'sanitize_checkbox'),
            'default' => true
        ));
        
        register_setting('tablecrm_complete_settings', 'tablecrm_complete_debug_mode', array(
            'type' => 'boolean',
            'sanitize_callback' => array($this, 'sanitize_checkbox'),
            'default' => true
        ));
        
        register_setting('tablecrm_complete_settings', 'tablecrm_complete_order_statuses', array(
            'type' => 'array',
            'sanitize_callback' => array($this, 'sanitize_order_statuses'),
            'default' => array('processing', 'completed')
        ));
    }
    
    // Страница настроек
    public function admin_page() {
        ?>
        <div class="wrap">
            <h1>TableCRM Integration Complete Settings v1.0.24</h1>
            <div class="notice notice-success">
                <p><strong>🚀 Полная версия с динамическим созданием номенклатуры!</strong></p>
                <p>✅ Автоматическое создание товаров в TableCRM</p>
                <p>✅ Автоматическое создание контрагентов</p>
                <p>✅ Защита от дублирования</p>
                <p>✅ Подробное логирование</p>
            </div>
            <form method="post" action="options.php">
                <?php
                settings_fields('tablecrm_complete_settings');
                do_settings_sections('tablecrm_complete_settings');
                ?>
                <table class="form-table">
                    <tr>
                        <th scope="row">API URL</th>
                        <td>
                            <input type="url" name="tablecrm_complete_api_url" value="<?php echo esc_attr(get_option('tablecrm_complete_api_url', 'https://app.tablecrm.com/api/v1/')); ?>" class="regular-text" />
                            <p class="description">URL API TableCRM</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">API Key</th>
                        <td>
                            <input type="password" name="tablecrm_complete_api_key" value="<?php echo esc_attr(get_option('tablecrm_complete_api_key')); ?>" class="regular-text" />
                            <p class="description">Ваш API токен TableCRM</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Organization ID</th>
                        <td>
                            <input type="number" name="tablecrm_complete_organization_id" value="<?php echo esc_attr(get_option('tablecrm_complete_organization_id', 38)); ?>" class="regular-text" />
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Cashbox ID</th>
                        <td>
                            <input type="number" name="tablecrm_complete_cashbox_id" value="<?php echo esc_attr(get_option('tablecrm_complete_cashbox_id', 218)); ?>" class="regular-text" />
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Warehouse ID</th>
                        <td>
                            <input type="number" name="tablecrm_complete_warehouse_id" value="<?php echo esc_attr(get_option('tablecrm_complete_warehouse_id', 39)); ?>" class="regular-text" />
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Unit ID (по умолчанию)</th>
                        <td>
                            <input type="number" name="tablecrm_complete_unit_id" value="<?php echo esc_attr(get_option('tablecrm_complete_unit_id', 116)); ?>" class="regular-text" />
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Отправлять заказы</th>
                        <td>
                            <input type="checkbox" name="tablecrm_complete_enable_orders" value="1" <?php checked(1, get_option('tablecrm_complete_enable_orders', 1)); ?> />
                            <label>Включить отправку заказов WooCommerce</label>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Режим отладки</th>
                        <td>
                            <input type="checkbox" name="tablecrm_complete_debug_mode" value="1" <?php checked(1, get_option('tablecrm_complete_debug_mode', 1)); ?> />
                            <label>Включить подробное логирование</label>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Статусы заказов</th>
                        <td>
                            <?php 
                            $selected_statuses = get_option('tablecrm_complete_order_statuses', array('processing', 'completed'));
                            $available_statuses = array(
                                'pending' => 'Ожидает оплаты',
                                'processing' => 'В обработке',
                                'on-hold' => 'На удержании',
                                'completed' => 'Выполнен',
                                'cancelled' => 'Отменен',
                                'refunded' => 'Возврат',
                                'failed' => 'Неудачен'
                            );
                            
                            foreach ($available_statuses as $status => $label) {
                                $checked = in_array($status, $selected_statuses) ? 'checked' : '';
                                echo "<label><input type='checkbox' name='tablecrm_complete_order_statuses[]' value='$status' $checked /> $label</label><br>";
                            }
                            ?>
                        </td>
                    </tr>
                </table>
                
                <p class="submit">
                    <input type="submit" name="submit" id="submit" class="button button-primary" value="Сохранить настройки" />
                    <button type="button" id="test-connection-complete" class="button">Тестировать соединение</button>
                </p>
            </form>
            
            <div id="test-result-complete" style="margin-top: 20px;"></div>
            
            <script>
            jQuery(document).ready(function($) {
                $('#test-connection-complete').click(function() {
                    var button = $(this);
                    var result = $('#test-result-complete');
                    
                    button.prop('disabled', true).text('Тестируем...');
                    result.html('<p>Проверяем соединение с TableCRM API...</p>');
                    
                    $.ajax({
                        url: ajaxurl,
                        method: 'POST',
                        data: {
                            action: 'test_tablecrm_connection_complete',
                            nonce: '<?php echo wp_create_nonce("test_tablecrm_complete"); ?>'
                        },
                        success: function(response) {
                            if(response.success) {
                                result.html('<div class="notice notice-success"><p><strong>✅ Соединение успешно!</strong><br>' + response.data.message + '</p></div>');
                            } else {
                                result.html('<div class="notice notice-error"><p><strong>❌ Ошибка соединения:</strong><br>' + response.data.message + '</p></div>');
                            }
                        },
                        error: function() {
                            result.html('<div class="notice notice-error"><p><strong>❌ Ошибка AJAX запроса</strong></p></div>');
                        },
                        complete: function() {
                            button.prop('disabled', false).text('Тестировать соединение');
                        }
                    });
                });
            });
            </script>
        </div>
        <?php
    }
    
    // Тестирование соединения
    public function test_connection() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Недостаточно прав доступа');
            return;
        }
        
        if (!isset($_POST['nonce']) || !wp_verify_nonce(sanitize_text_field($_POST['nonce']), 'test_tablecrm_complete')) {
            wp_send_json_error('Неверный токен безопасности');
            return;
        }
        
        $api_url = get_option('tablecrm_complete_api_url', 'https://app.tablecrm.com/api/v1/');
        $api_key = get_option('tablecrm_complete_api_key');
        
        if (empty($api_url) || empty($api_key)) {
            wp_send_json_error('Пожалуйста, заполните API URL и API Key');
            return;
        }
        
        $response = $this->make_api_request('health', array(), 'GET');
        
        if ($response['success']) {
            wp_send_json_success('Соединение установлено успешно! Динамическое создание номенклатуры работает.');
        } else {
            wp_send_json_error('Ошибка соединения: ' . $response['message']);
        }
    }
    
    // Обработка изменения статуса заказа
    public function handle_order_status_change($order_id, $old_status = '', $new_status = '') {
        try {
            if (!get_option('tablecrm_complete_enable_orders', 1)) {
                return;
            }

            $order = wc_get_order($order_id);
            if (!$order) { return; }

            // Пользовательские статусы, при которых всегда отправляем
            $trigger_statuses = get_option('tablecrm_complete_order_statuses', array('processing', 'completed'));

            // Был ли заказ оплачен ранее и оплачен ли сейчас
            $was_paid    = in_array($old_status, wc_get_is_paid_statuses(), true);
            $is_now_paid = $order->is_paid();

            // Нужно ли отправлять/обновлять
            $should_process = in_array($new_status, $trigger_statuses, true) || ($is_now_paid && !$was_paid);
            if (!$should_process) {
                $this->log_info("handle_order_status_change: статус {$new_status} не требует отправки (order {$order_id}).");
                return;
            }

            if (!in_array($order_id, $this->orders_to_send_on_shutdown, true)) {
                $reason = $is_now_paid && !$was_paid ? 'заказ оплачен' : "триггерный статус {$new_status}";
                $this->log_info("handle_order_status_change: заказ {$order_id} добавлен в очередь, причина: {$reason}.");
                $this->orders_to_send_on_shutdown[] = $order_id;
            }
        } catch (Exception $e) {
            $this->log_error('КРИТИЧЕСКАЯ ОШИБКА в handle_order_status_change: ' . $e->getMessage());
        }
    }
    
    // Обработка нового заказа
    public function handle_new_order($order_id) {
        try {
            if (!get_option('tablecrm_complete_enable_orders', 1)) {
                return;
            }

            $order = wc_get_order($order_id);
            if (!$order) { return; }

            // Всегда кладём в очередь — send_order_to_tablecrm сам решит, создать или обновить
            if (!in_array($order_id, $this->orders_to_send_on_shutdown, true)) {
                $this->orders_to_send_on_shutdown[] = $order_id;
                $this->log_info("handle_new_order: новый заказ {$order_id} добавлен в очередь на отправку.");
            }
        } catch (Exception $e) {
            $this->log_error('КРИТИЧЕСКАЯ ОШИБКА в handle_new_order: ' . $e->getMessage());
        }
    }

    // Фоновая отправка в конце выполнения PHP
    public function send_orders_on_shutdown() {
        if (empty($this->orders_to_send_on_shutdown)) {
            return;
        }

        // Пытаемся закрыть соединение с браузером
        if (function_exists('fastcgi_finish_request')) {
            fastcgi_finish_request();
        }

        // Игнорируем отключение пользователя
        ignore_user_abort(true);

        // Увеличиваем лимит времени выполнения
        if (function_exists('set_time_limit')) {
            set_time_limit(300);
        }

        foreach ($this->orders_to_send_on_shutdown as $order_id) {
            $this->log_info("Начало фоновой отправки заказа {$order_id} из shutdown-очереди.");
            $this->send_order_to_tablecrm($order_id);
        }
    }
    
    // Отправка заказа в TableCRM
    public function send_order_to_tablecrm($order_id) {
        $this->log_info("Начинаем отправку заказа $order_id в TableCRM");
        
        $order = wc_get_order($order_id);
        if (!$order) {
            $this->log_error("Заказ $order_id не найден");
            return false;
        }

        // Проверяем, был ли заказ уже отправлен — если да, будем ОТКОРРЕКТИРОВЫВАТЬ документ
        $existing_doc_id = get_post_meta($order_id, '_tablecrm_complete_document_id', true);
        $is_update = !empty($existing_doc_id);

        // Получаем настройки
        $api_url = get_option('tablecrm_complete_api_url', 'https://app.tablecrm.com/api/v1/');
        $api_token = get_option('tablecrm_complete_api_key');
        $warehouse_id = get_option('tablecrm_complete_warehouse_id');
        $organization_id = get_option('tablecrm_complete_organization_id');
        $paybox_id = get_option('tablecrm_complete_cashbox_id');

        if (empty($api_url) || empty($api_token) || empty($warehouse_id) || empty($organization_id) || empty($paybox_id)) {
            $this->log_error("Не все настройки API заполнены");
            return false;
        }
        
        $api_url = trailingslashit($api_url);

        // 1. Получаем или создаем контрагента
        $contragent_id = $this->get_or_create_contragent($order, $api_url, $api_token);
        if (!$contragent_id) {
            $this->log_error("Не удалось получить или создать контрагента для заказа $order_id");
            return false;
        }

        // 2. Получаем или создаем номенклатуры для всех товаров
        $nomenclature_ids = array();
        $items = $order->get_items();
        $failed_items = array();
        
        foreach ($items as $item_id => $item) {
            $product_name = $item->get_name();
            $nomenclature_id = $this->get_or_create_nomenclature($product_name, $api_url, $api_token);
            if (!$nomenclature_id) {
                $this->log_warning("Не удалось получить или создать номенклатуру для товара '$product_name' - пропускаем этот товар");
                $failed_items[] = $product_name;
                continue;
            }
            $nomenclature_ids[$item_id] = $nomenclature_id;
        }

        // Если не удалось создать номенклатуры ни для одного товара
        if (empty($nomenclature_ids)) {
            $this->log_error("Не удалось создать номенклатуры ни для одного товара в заказе $order_id");
            return false;
        }
        
        // Логируем информацию о пропущенных товарах
        if (!empty($failed_items)) {
            $this->log_warning("Пропущены товары: " . implode(', ', $failed_items));
        }

        // 3. Формируем данные заказа
        $adapted_data_array = $this->adapt_data_format($order, $warehouse_id, $organization_id, $paybox_id, $contragent_id, $nomenclature_ids);

        if (!$adapted_data_array) {
            $this->log_error("Не удалось адаптировать данные заказа $order_id");
            return false;
        }

        // 4. Отправляем заказ
        if ($is_update) {
            // Заказ уже существует в CRM, не пытаемся его обновлять
            $document_id = $existing_doc_id;
            $this->log_info("Заказ $order_id уже отправлен. Document ID: $document_id");

            // Отправляем только UTM-данные и информацию о доставке
            $this->send_utm_data($document_id, $this->get_analytics_data($order));
            $this->send_delivery_info($document_id, $order);

            return true;
        } else {
            // POST — создание нового документа (API требует массив)
            $response = $this->make_api_request('docs_sales/', $adapted_data_array, 'POST');

            if ($response['success']) {
                $document_id = $this->extract_document_id($response);

                if ($document_id && $document_id !== 'unknown') {
                    update_post_meta($order_id, '_tablecrm_complete_document_id', $document_id);
                    update_post_meta($order_id, '_tablecrm_complete_sent_at', current_time('mysql'));

                    $this->log_success("Заказ $order_id успешно отправлен. Document ID: $document_id");
                    $this->add_order_note($order_id, "Заказ успешно отправлен в TableCRM. Document ID: $document_id");

                    // Отправка UTM-данных через специальный API эндпоинт
                    $this->send_utm_data($document_id, $this->get_analytics_data($order));

                    // Отправка информации о доставке через специальный API эндпоинт
                    $this->send_delivery_info($document_id, $order);

                    return true;
                }
            }
        }
        
        $this->log_error("Ошибка " . ($is_update ? 'обновления' : 'отправки') . " заказа {$order_id}: " . $response['message']);
        $this->add_order_note($order_id, "Ошибка " . ($is_update ? 'обновления' : 'отправки') . " в TableCRM: " . $response['message']);
        return false;
    }
    
    // Получение или создание контрагента
    private function get_or_create_contragent($order, $api_url, $api_token) {
        $phone = $this->get_recipient_phone($order);
        if (empty($phone)) {
            $this->log_warning("Телефон клиента в заказе #{$order->get_id()} не найден");
            return null;
        }

        // Нормализуем номер телефона - убираем все кроме цифр и добавляем +7
        $normalized_phone = preg_replace('/[^0-9]/', '', $phone);
        if (strlen($normalized_phone) == 11 && substr($normalized_phone, 0, 1) == '8') {
            $normalized_phone = '7' . substr($normalized_phone, 1);
        }
        if (strlen($normalized_phone) == 10) {
            $normalized_phone = '7' . $normalized_phone;
        }
        $search_phone = '+' . $normalized_phone;

        $this->log_info("Поиск/создание контрагента для телефона: {$phone} (нормализованный: {$search_phone})");

        // 1. Поиск контрагента по телефону с правильными параметрами
        // Используем параметр phone для прямого поиска
        $search_url = $api_url . 'contragents/?token=' . $api_token . 
                      '&limit=100&offset=0&sort=created_at:desc&phone=' . urlencode($normalized_phone);
        $search_response = wp_remote_get($search_url);

        if (!is_wp_error($search_response)) {
            $search_response_code = wp_remote_retrieve_response_code($search_response);
            $search_body = wp_remote_retrieve_body($search_response);
            $this->log_info("Поиск контрагента - Status: {$search_response_code}, Response: " . substr($search_body, 0, 200));
            
            $search_data = json_decode($search_body, true);
            
            // Проверяем разные форматы ответа
            if (!empty($search_data)) {
                // Если ответ содержит result (правильный формат API)
                if (isset($search_data['result']) && is_array($search_data['result']) && !empty($search_data['result'])) {
                    // Берем первого найденного контрагента (API уже отфильтровал по номеру)
                    $contragent_id = $search_data['result'][0]['id'];
                    $this->log_success("Контрагент найден в result. ID: {$contragent_id}");
                    return $contragent_id;
                }
                // Если ответ содержит results (альтернативный формат)
                else if (isset($search_data['results']) && is_array($search_data['results']) && !empty($search_data['results'])) {
                    $contragent_id = $search_data['results'][0]['id'];
                    $this->log_success("Контрагент найден в results. ID: {$contragent_id}");
                    return $contragent_id;
                }
                // Если ответ - прямой массив (только если это действительно массив контрагентов)
                else if (is_array($search_data) && !isset($search_data['result']) && !isset($search_data['results']) && !empty($search_data) && isset($search_data[0]['id'])) {
                    $contragent_id = $search_data[0]['id'];
                    $this->log_success("Контрагент найден в массиве. ID: {$contragent_id}");
                    return $contragent_id;
                }
            }
        } else {
            $this->log_error("Ошибка поиска контрагента: " . $search_response->get_error_message());
        }

        // 2. Создание нового контрагента
        $this->log_info("Контрагент не найден, создаем нового");
        $create_url = $api_url . 'contragents/?token=' . $api_token;
        
        // Формируем имя без телефона
        $first_name = $order->get_billing_first_name();
        $last_name = $order->get_billing_last_name();
        
        // Функция для проверки, является ли строка телефоном
        $is_phone = function($str) {
            if (empty($str)) return false;
            // Проверяем на наличие телефонных паттернов
            return preg_match('/^[\+]?[0-9\(\)\-\s]{7,}$/', $str) || 
                   strpos($str, '+7') === 0 || 
                   strpos($str, '8(') === 0 ||
                   preg_match('/\d{3}.*\d{3}.*\d{2}.*\d{2}/', $str);
        };
        
        // Очищаем имя и фамилию от телефонов
        $clean_first_name = (!empty($first_name) && !$is_phone($first_name)) ? $first_name : '';
        $clean_last_name = (!empty($last_name) && !$is_phone($last_name)) ? $last_name : '';
        
        $full_name = trim($clean_first_name . ' ' . $clean_last_name);
        
        // Если имя пустое, используем email как fallback
        if (empty($full_name)) {
            $full_name = $order->get_billing_email();
        }
        
        $email = $order->get_billing_email();

        $create_data = array(
            'name' => $full_name,
            'phone' => $search_phone, // Используем нормализованный номер с +7
            'email' => $email,
            'our_cost' => false
        );
        
        $this->log_info("Создаем контрагента: имя='$full_name', телефон='$search_phone', email='$email'");
        
        $create_response = wp_remote_post($create_url, array(
            'method' => 'POST',
            'headers' => array('Content-Type' => 'application/json; charset=utf-8'),
            'body' => json_encode($create_data, JSON_UNESCAPED_UNICODE)
        ));

        if (!is_wp_error($create_response)) {
            $create_response_code = wp_remote_retrieve_response_code($create_response);
            $create_response_body = wp_remote_retrieve_body($create_response);
            $this->log_info("Создание контрагента - Status: {$create_response_code}, Response: " . substr($create_response_body, 0, 200));
            
            $create_result = json_decode($create_response_body, true);

            if ($create_response_code >= 200 && $create_response_code < 300) {
                if (isset($create_result['id'])) {
                    $new_contragent_id = $create_result['id'];
                    $this->log_success("Новый контрагент создан. ID: {$new_contragent_id}");
                    return $new_contragent_id;
                } else if (is_array($create_result) && isset($create_result[0]['id'])) {
                    $new_contragent_id = $create_result[0]['id'];
                    $this->log_success("Новый контрагент создан (массив). ID: {$new_contragent_id}");
                    return $new_contragent_id;
                }
            } else {
                // Если контрагент уже существует, попробуем найти его еще раз
                if ($create_response_code == 400 && strpos($create_response_body, 'Phone number already in use') !== false) {
                    $this->log_info("Контрагент уже существует, повторный поиск...");
                    
                    // Повторный поиск с параметром phone
                    $search_again_url = $api_url . 'contragents/?token=' . $api_token . 
                                       '&limit=100&offset=0&sort=created_at:desc&phone=' . urlencode($normalized_phone);
                    $search_again_response = wp_remote_get($search_again_url);
                    
                    if (!is_wp_error($search_again_response)) {
                        $again_body = wp_remote_retrieve_body($search_again_response);
                        $again_data = json_decode($again_body, true);
                        
                        if (isset($again_data['result']) && !empty($again_data['result'])) {
                            $found_contragent_id = $again_data['result'][0]['id'];
                            $this->log_success("Контрагент найден при повторном поиске. ID: {$found_contragent_id}");
                            return $found_contragent_id;
                        }
                    }
                }
            }
        } else {
            $this->log_error("Ошибка создания контрагента: " . $create_response->get_error_message());
        }

        $this->log_error("Не удалось создать контрагента");
        return null;
    }
    
    // Получение или создание номенклатуры
    private function get_or_create_nomenclature($item_name, $api_url, $api_token) {
        $this->log_info("Поиск/создание номенклатуры для товара: '{$item_name}'");

        // 1. Поиск номенклатуры по названию с правильными параметрами
        $search_url = $api_url . 'nomenclature/?token=' . $api_token . 
                      '&name=' . urlencode($item_name) . 
                      '&limit=100&offset=0&with_prices=false&with_balance=false&with_attributes=false&only_main_from_group=false';
        $search_response = wp_remote_get($search_url);

        if (!is_wp_error($search_response)) {
            $search_response_code = wp_remote_retrieve_response_code($search_response);
            $search_body = wp_remote_retrieve_body($search_response);
            $this->log_info("Поиск номенклатуры - Status: {$search_response_code}, Response: " . substr($search_body, 0, 200));
            
            $search_data = json_decode($search_body, true);
            
            // Проверяем разные форматы ответа
            if (!empty($search_data)) {
                // Если ответ содержит result (правильный формат API)
                if (isset($search_data['result']) && is_array($search_data['result']) && !empty($search_data['result'])) {
                    $nomenclature_id = $search_data['result'][0]['id'];
                    $this->log_success("Номенклатура найдена в result. ID: {$nomenclature_id}");
                    return $nomenclature_id;
                }
                // Если ответ содержит results (альтернативный формат)
                else if (isset($search_data['results']) && is_array($search_data['results']) && !empty($search_data['results'])) {
                    $nomenclature_id = $search_data['results'][0]['id'];
                    $this->log_success("Номенклатура найдена в results. ID: {$nomenclature_id}");
                    return $nomenclature_id;
                }
            }
        } else {
            $this->log_error("Ошибка поиска номенклатуры: " . $search_response->get_error_message());
        }

        // 2. Создание новой номенклатуры
        $this->log_info("Номенклатура не найдена, создаем новую");
        $create_url = $api_url . 'nomenclature/?token=' . $api_token;
        
        $unit_id = get_option('tablecrm_complete_unit_id', 116);
        // API ожидает массив объектов, а не один объект
        $create_data = array(array('name' => $item_name, 'unit' => $unit_id));
        
        $create_response = wp_remote_post($create_url, array(
            'method' => 'POST',
            'headers' => array('Content-Type' => 'application/json; charset=utf-8'),
            'body' => json_encode($create_data, JSON_UNESCAPED_UNICODE)
        ));

        if (!is_wp_error($create_response)) {
            $create_response_code = wp_remote_retrieve_response_code($create_response);
            $create_response_body = wp_remote_retrieve_body($create_response);
            $this->log_info("Создание номенклатуры - Status: {$create_response_code}, Response: " . substr($create_response_body, 0, 200));
            
            $create_result = json_decode($create_response_body, true);

            if ($create_response_code >= 200 && $create_response_code < 300) {
                if (isset($create_result['id'])) {
                    $new_nomenclature_id = $create_result['id'];
                    $this->log_success("Новая номенклатура создана. ID: {$new_nomenclature_id}");
                    return $new_nomenclature_id;
                } else if (is_array($create_result) && isset($create_result[0]['id'])) {
                    $new_nomenclature_id = $create_result[0]['id'];
                    $this->log_success("Новая номенклатура создана (массив). ID: {$new_nomenclature_id}");
                    return $new_nomenclature_id;
                }
            }
        } else {
            $this->log_error("Ошибка создания номенклатуры: " . $create_response->get_error_message());
        }

        $this->log_error("Не удалось создать номенклатуру для товара '{$item_name}'");
        return null;
    }
    
    // Адаптация данных для API
    private function adapt_data_format($order, $warehouse_id, $organization_id, $paybox_id, $contragent_id, $nomenclature_ids_map) {
        $order_id = $order->get_id();
        $items = $order->get_items();
        $goods = array();

        $this->log_info("Формирование данных для заказа #$order_id");
        
        // Получаем и нормализуем основной телефон
        $phone = $this->get_recipient_phone($order);
        $normalized_phone = '';
        if (!empty($phone)) {
            $normalized_phone = preg_replace('/[^0-9]/', '', $phone);
            if (strlen($normalized_phone) == 11 && substr($normalized_phone, 0, 1) == '8') {
                $normalized_phone = '7' . substr($normalized_phone, 1);
            }
            if (strlen($normalized_phone) == 10) {
                $normalized_phone = '7' . $normalized_phone;
            }
            $normalized_phone = '+' . $normalized_phone;
        }

        $this->log_info("Количество товаров в заказе: " . count($items));
        $this->log_info("Количество номенклатур: " . count($nomenclature_ids_map));
        $this->log_info("Основной телефон клиента: " . $normalized_phone);

        $total_sum_for_crm = 0; // Инициализируем переменную для подсчета общей суммы

        foreach ($items as $item_id => $item) {
            $product = $item->get_product();
            $item_name = $item->get_name();

            $nomenclature_id = isset($nomenclature_ids_map[$item_id]) ? $nomenclature_ids_map[$item_id] : null;
            if (!$nomenclature_id) {
                $this->log_error("ID номенклатуры для товара '{$item_name}' не найден");
                continue;
            }
            
            $line_total = (float) $item->get_total();
            $quantity   = $item->get_quantity();
            $unit_price = $quantity > 0 ? $line_total / $quantity : 0;

            $goods[] = array(
                'price'          => (float) round($unit_price, 2), // явно приводим к float
                'quantity'       => (int) $quantity,
                'unit'           => (int) get_option('tablecrm_complete_unit_id', 116),
                'discount'       => 0,
                'sum_discounted' => 0, // скидка отсутствует
                'nomenclature'   => (int) $nomenclature_id,
                'name'           => $item_name,
                'sku'            => $product ? $product->get_sku() : '',
            );
            $total_sum_for_crm += $unit_price * $quantity;
            
            $this->log_info("Добавлен товар: '{$item_name}' (nomenclature: $nomenclature_id, price: $unit_price, qty: " . $quantity . ")");
        }

        // Добавляем стоимость доставки как отдельный товар в том же заказе
        $shipping_total = (float) $order->get_shipping_total();
        if ($shipping_total > 0) {
            $this->log_info("Добавляем стоимость доставки как отдельный товар: {$shipping_total}");
            
            // Создаем или находим номенклатуру для доставки
            $api_url = get_option('tablecrm_complete_api_url', 'https://app.tablecrm.com/api/v1/');
            $api_token = get_option('tablecrm_complete_api_key');
            $api_url = trailingslashit($api_url);
            
            $delivery_nomenclature_id = $this->get_or_create_nomenclature('Доставка', $api_url, $api_token);
            if ($delivery_nomenclature_id) {
                $goods[] = array(
                    'price' => (float) $shipping_total, // явно приводим к float
                    'quantity' => 1,
                    'unit' => (int) get_option('tablecrm_complete_unit_id', 116),
                    'discount' => 0,
                    'sum_discounted' => 0,
                    'nomenclature' => (int) $delivery_nomenclature_id,
                    'name' => 'Доставка',
                    'sku' => '',
                );
                $total_sum_for_crm += $shipping_total;
                $this->log_success("Доставка добавлена как товар с ID номенклатуры: {$delivery_nomenclature_id}");
            } else {
                $this->log_warning("Не удалось создать номенклатуру для доставки");
            }
        }

        if (empty($goods)) {
            $this->log_error("В заказе #{$order_id} нет товаров для отправки");
            return null;
        }
        
        $this->log_info("Итого товаров для отправки: " . count($goods));
        
        // --- Новая логика оплаты и суммы для CRM ---
        $is_paid = $order->is_paid();
        $this->log_info("Статус оплаты для CRM: " . ($is_paid ? 'оплачен' : 'не оплачен') . ", сумма: " . $total_sum_for_crm);

        $comment_text = "Заказ с сайта №{$order_id}";
        if (!empty($normalized_phone)) {
            $comment_text .= ". Телефон клиента: " . $normalized_phone;
        }
        if ($note = $order->get_customer_note()) {
            $comment_text .= ". Комментарий клиента: " . $note;
        }
        
        // Добавляем информацию о способе доставки в комментарий
        if ($shipping_total > 0) {
            $shipping_method = $order->get_shipping_method();
            if ($shipping_method) {
                $comment_text .= ". Способ доставки: " . $shipping_method;
            }
        }
        
        // Получение UTM и аналитических данных из заказа
        $analytics = $this->get_analytics_data($order);
        
        // Формирование аналитической информации для комментария
        $analytics_comment = $this->format_analytics_comment($analytics);
        
        $comment_text .= $analytics_comment;
        
        $final_data = array(array(
            'dated' => $order->get_date_created()->getTimestamp(),
            'operation' => 'Заказ',
            'comment' => $comment_text,
            'tax_included' => true,
            'tax_active' => true,
            'goods' => $goods,
            'settings' => new stdClass(),
            'warehouse' => (int) $warehouse_id,
            'contragent' => (int) $contragent_id,
            'paybox' => (int) $paybox_id,
            'organization' => (int) $organization_id,
            'status' => $is_paid,
            // paid_rubles отправляем только для оплаченных заказов
            'paid_rubles' => $is_paid ? (float) round($total_sum_for_crm, 2) : 0,
            'paid_lt' => 0,
        ));
        
        $this->log_info("Данные для отправки сформированы успешно");
        return $final_data;
    }
    
    // Получение UTM и аналитических данных из заказа
    private function get_analytics_data($order) {
        $analytics = array();
        $meta_data = $order->get_meta_data();
        
        foreach ($meta_data as $meta) {
            $key = $meta->get_data()['key'];
            $value = $meta->get_data()['value'];
            
            // WooCommerce Order Attribution data
            if (strpos($key, '_wc_order_attribution_') === 0) {
                $clean_key = str_replace('_wc_order_attribution_', '', $key);
                $analytics[$clean_key] = $value;
            }
            
            // Standard UTM parameters
            if (strpos($key, 'utm_') === 0) {
                $analytics[$key] = $value;
            }
            
            // Extended UTM parameters from API documentation
            $extended_utm_fields = array(
                'utm_name', 'utm_phone', 'utm_email', 'utm_leadid', 
                'utm_yclientid', 'utm_gaclientid'
            );
            if (in_array($key, $extended_utm_fields)) {
                $analytics[$key] = $value;
            }
            
            // Other tracking data
            if (in_array($key, array('gclid', 'fbclid', 'yclid', 'referrer', 'landing_page', 'source', 'medium', 'campaign'))) {
                $analytics[$key] = $value;
            }
        }
        
        // Также проверяем GET и POST параметры если они сохранены
        if (isset($_GET)) {
            foreach ($_GET as $key => $value) {
                if (strpos($key, 'utm_') === 0 && !isset($analytics[$key])) {
                    $analytics[$key] = sanitize_text_field($value);
                }
            }
        }
        
        // Проверяем куки для UTM-данных
        if (isset($_COOKIE)) {
            foreach ($_COOKIE as $key => $value) {
                if (strpos($key, 'utm_') === 0 && !isset($analytics[$key])) {
                    $analytics[$key] = sanitize_text_field($value);
                }
            }
        }
        
        return $analytics;
    }
    
    // Формирование аналитической информации для комментария
    private function format_analytics_comment($analytics) {
        if (empty($analytics)) {
            return '';
        }
        
        $comment_parts = array();
        
        // Источник трафика
        if (isset($analytics['utm_source']) || isset($analytics['source_type'])) {
            $source = isset($analytics['utm_source']) ? $analytics['utm_source'] : $analytics['source_type'];
            $comment_parts[] = "Источник: $source";
        }
        
        // Канал/медиум
        if (isset($analytics['utm_medium'])) {
            $comment_parts[] = "Канал: " . $analytics['utm_medium'];
        }
        
        // Кампания
        if (isset($analytics['utm_campaign'])) {
            $comment_parts[] = "Кампания: " . $analytics['utm_campaign'];
        }
        
        // Реферер
        if (isset($analytics['referrer'])) {
            $referrer_domain = parse_url($analytics['referrer'], PHP_URL_HOST);
            if ($referrer_domain) {
                $comment_parts[] = "Реферер: $referrer_domain";
            }
        }
        
        // Google Ads
        if (isset($analytics['gclid'])) {
            $comment_parts[] = "Google Ads: " . substr($analytics['gclid'], 0, 20) . "...";
        }
        
        // Facebook Ads
        if (isset($analytics['fbclid'])) {
            $comment_parts[] = "Facebook Ads: " . substr($analytics['fbclid'], 0, 20) . "...";
        }
        
        // Yandex Direct
        if (isset($analytics['yclid'])) {
            $comment_parts[] = "Yandex Direct: " . substr($analytics['yclid'], 0, 20) . "...";
        }
        
        return empty($comment_parts) ? '' : '. Аналитика: ' . implode(', ', $comment_parts);
    }
    
    // Выполнение API запроса
    private function make_api_request($endpoint, $data = array(), $method = 'POST') {
        $api_url = get_option('tablecrm_complete_api_url', 'https://app.tablecrm.com/api/v1/');
        $api_key = get_option('tablecrm_complete_api_key');
        
        if (empty($api_url) || empty($api_key)) {
            return array('success' => false, 'message' => 'Не настроены API параметры');
        }
        
        $url = rtrim($api_url, '/') . '/' . ltrim($endpoint, '/') . '?token=' . $api_key;
        
        $args = array(
            'method' => $method,
            'headers' => array(
                'Content-Type' => 'application/json; charset=utf-8',
                'User-Agent' => 'WordPress-TableCRM-Integration-Complete/1.0.24'
            ),
            'timeout' => 30,
            'sslverify' => true
        );
        
        if (in_array($method, array('POST', 'PUT')) && !empty($data)) {
            $args['body'] = json_encode($data, JSON_UNESCAPED_UNICODE);
        }
        
        $this->log_info("API запрос: $method $url");
        if (!empty($data)) {
            $this->log_info('Request body: ' . json_encode($data, JSON_UNESCAPED_UNICODE));
        }
        
        $response = wp_remote_request($url, $args);
        
        if (is_wp_error($response)) {
            $error_message = $response->get_error_message();
            $this->log_error("Ошибка HTTP запроса: $error_message");
            return array('success' => false, 'message' => $error_message);
        }
        
        $status_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        
        $this->log_info("API ответ: HTTP $status_code");
        
        if ($status_code >= 200 && $status_code < 300) {
            $decoded = json_decode($body, true);
            return array(
                'success' => true, 
                'data' => $decoded, 
                'status_code' => $status_code
            );
        } else {
            // Детальное логирование ошибок
            $this->log_error("API запрос неуспешен - Status: {$status_code}");
            $this->log_error("Полное тело ответа: " . $body);
            
            // Специальная обработка для 500 ошибок
            if ($status_code == 500) {
                $this->log_error("КРИТИЧЕСКАЯ ОШИБКА 500: Внутренняя ошибка сервера TableCRM");
                $this->log_error("URL запроса: " . $url);
                $this->log_error("Данные запроса: " . json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
                
                // Попытаемся декодировать JSON ошибки, если есть
                $error_data = json_decode($body, true);
                if ($error_data && isset($error_data['detail'])) {
                    $this->log_error("Детали ошибки от API: " . $error_data['detail']);
                }
                if ($error_data && isset($error_data['errors'])) {
                    $this->log_error("Список ошибок от API: " . json_encode($error_data['errors'], JSON_UNESCAPED_UNICODE));
                }
            }
            
            // Проверка корректности основных ID в настройках
            if ($status_code == 500 && strpos($endpoint, 'docs_sales') !== false) {
                $warehouse_id = get_option('tablecrm_complete_warehouse_id');
                $organization_id = get_option('tablecrm_complete_organization_id');
                $paybox_id = get_option('tablecrm_complete_cashbox_id');
                
                $this->log_error("ПРОВЕРКА НАСТРОЕК:");
                $this->log_error("Warehouse ID: " . ($warehouse_id ?: 'НЕ УСТАНОВЛЕН'));
                $this->log_error("Organization ID: " . ($organization_id ?: 'НЕ УСТАНОВЛЕН'));
                $this->log_error("Paybox ID: " . ($paybox_id ?: 'НЕ УСТАНОВЛЕН'));
                
                if (empty($warehouse_id) || empty($organization_id) || empty($paybox_id)) {
                    $this->log_error("ВНИМАНИЕ: Один или несколько обязательных ID не установлены!");
                }
            }
            
            $error_message = "HTTP $status_code: $body";
            return array('success' => false, 'message' => $error_message, 'status_code' => $status_code);
        }
    }
    
    // Извлечение Document ID из ответа
    private function extract_document_id($response) {
        // Формат 1: ['document_id' => 123]
        if ( isset( $response['document_id'] ) && is_numeric( $response['document_id'] ) ) {
            return (int) $response['document_id'];
        }

        // Формат 2: ['data' => 123]
        if ( isset( $response['data'] ) && is_numeric( $response['data'] ) ) {
            return (int) $response['data'];
        }

        // Формат 3: ['data' => ['id' => 123]]
        if ( isset( $response['data']['id'] ) && is_numeric( $response['data']['id'] ) ) {
            return (int) $response['data']['id'];
        }

        // Формат 4: ['data' => [ ['id' => 123], … ]]
        if ( isset( $response['data'][0]['id'] ) && is_numeric( $response['data'][0]['id'] ) ) {
            return (int) $response['data'][0]['id'];
        }

        // Формат 5: ['data' => ['result' => [ ['id' => 123] ] ] ]
        if ( isset( $response['data']['result'][0]['id'] ) && is_numeric( $response['data']['result'][0]['id'] ) ) {
            return (int) $response['data']['result'][0]['id'];
        }

        // Формат 6: ['data' => ['results' => [ ['id' => 123] ] ] ]
        if ( isset( $response['data']['results'][0]['id'] ) && is_numeric( $response['data']['results'][0]['id'] ) ) {
            return (int) $response['data']['results'][0]['id'];
        }

        $this->log_warning( 'Не удалось извлечь document_id из ответа: ' . json_encode( $response, JSON_UNESCAPED_UNICODE ) );
        return 'unknown';
    }
    
    // Добавление заметки к заказу
    private function add_order_note($order_id, $note) {
        $order = wc_get_order($order_id);
        if ($order) {
            $order->add_order_note($note);
            $order->save();
        }
    }
    
    // Санитизация методы
    public function sanitize_api_url($value) {
        if (empty($value)) {
            return 'https://app.tablecrm.com/api/v1/';
        }
        
        $value = esc_url_raw($value);
        if (!filter_var($value, FILTER_VALIDATE_URL)) {
            return 'https://app.tablecrm.com/api/v1/';
        }
        
        return rtrim($value, '/') . '/';
    }
    
    public function sanitize_api_key($value) {
        return sanitize_text_field(trim($value));
    }
    
    public function sanitize_checkbox($value) {
        return !empty($value) ? 1 : 0;
    }
    
    public function sanitize_order_statuses($value) {
        if (!is_array($value)) {
            return array('processing', 'completed');
        }
        
        $allowed_statuses = array('pending', 'processing', 'on-hold', 'completed', 'cancelled', 'refunded', 'failed');
        $sanitized = array();
        
        foreach ($value as $status) {
            $status = sanitize_key($status);
            if (in_array($status, $allowed_statuses)) {
                $sanitized[] = $status;
            }
        }
        
        return empty($sanitized) ? array('processing', 'completed') : $sanitized;
    }
    
    // Логирование
    private function log($message, $level = 'INFO') {
        // Логирование работает только если включен режим отладки в настройках плагина
        if (!get_option('tablecrm_complete_debug_mode', 1)) {
            return;
        }

        $timestamp = current_time('Y-m-d H:i:s');
        $log_entry = "[$timestamp] [$level] $message" . PHP_EOL;
        
        // Защищенная запись в файл
        $log_file = TABLECRM_COMPLETE_PLUGIN_PATH . 'tablecrm_integration_complete.log';
        // Используем @ для подавления ошибок, если директория недоступна для записи.
        // Это предотвратит ошибки на странице оформления заказа.
        @file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
        
        // Также выводим в стандартный лог WordPress, если WP_DEBUG включен
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("TableCRM Integration: " . trim($log_entry));
        }
    }

    private function log_error($message) { $this->log($message, 'ERROR'); }
    
    // Дополнительные уровни логирования для удобства
    private function log_info($message)    { $this->log($message, 'INFO'); }
    private function log_success($message) { $this->log($message, 'SUCCESS'); }
    private function log_warning($message) { $this->log($message, 'WARNING'); }
    
    // Отправка UTM-данных через специальный API эндпоинт
    private function send_utm_data($document_id, $analytics) {
        if (empty($analytics) || empty($document_id)) {
            return true; // Нет данных для отправки
        }
        
        $this->log_info("Отправка UTM-данных для документа $document_id");
        
        // Формируем данные для UTM API согласно официальной документации
        $utm_data = array();
        
        // Основные UTM параметры
        if (isset($analytics['utm_source'])) {
            $utm_data['utm_source'] = $analytics['utm_source'];
        }
        if (isset($analytics['utm_medium'])) {
            $utm_data['utm_medium'] = $analytics['utm_medium'];
        }
        if (isset($analytics['utm_campaign'])) {
            $utm_data['utm_campaign'] = $analytics['utm_campaign'];
        }
        if (isset($analytics['utm_term'])) {
            // utm_term может быть массивом согласно API
            $utm_data['utm_term'] = is_array($analytics['utm_term']) ? $analytics['utm_term'] : array($analytics['utm_term']);
        }
        if (isset($analytics['utm_content'])) {
            $utm_data['utm_content'] = $analytics['utm_content'];
        }
        
        // Дополнительные UTM поля
        if (isset($analytics['utm_name'])) {
            $utm_data['utm_name'] = $analytics['utm_name'];
        }
        if (isset($analytics['utm_phone'])) {
            $utm_data['utm_phone'] = $analytics['utm_phone'];
        }
        if (isset($analytics['utm_email'])) {
            $utm_data['utm_email'] = $analytics['utm_email'];
        }
        if (isset($analytics['utm_leadid'])) {
            $utm_data['utm_leadid'] = $analytics['utm_leadid'];
        }
        if (isset($analytics['utm_yclientid'])) {
            $utm_data['utm_yclientid'] = $analytics['utm_yclientid'];
        }
        if (isset($analytics['utm_gaclientid'])) {
            $utm_data['utm_gaclientid'] = $analytics['utm_gaclientid'];
        }
        
        // Дополнительные параметры (для обратной совместимости)
        if (isset($analytics['referrer'])) {
            $utm_data['referrer'] = $analytics['referrer'];
        }
        if (isset($analytics['source_type'])) {
            $utm_data['source_type'] = $analytics['source_type'];
        }
        if (isset($analytics['device_type'])) {
            $utm_data['device_type'] = $analytics['device_type'];
        }
        if (isset($analytics['user_agent'])) {
            $utm_data['user_agent'] = substr($analytics['user_agent'], 0, 255); // Ограничиваем длину
        }
        
        // Google/Facebook/Yandex click IDs
        if (isset($analytics['gclid'])) {
            $utm_data['gclid'] = $analytics['gclid'];
        }
        if (isset($analytics['fbclid'])) {
            $utm_data['fbclid'] = $analytics['fbclid'];
        }
        if (isset($analytics['yclid'])) {
            $utm_data['yclid'] = $analytics['yclid'];
        }
        
        if (empty($utm_data)) {
            $this->log_info("Нет UTM-данных для отправки");
            return true;
        }
        
        $this->log_info("UTM-данные для отправки: " . json_encode($utm_data, JSON_UNESCAPED_UNICODE));
        
        // Отправляем через специальный эндпоинт
        $response = $this->make_api_request("docs_sales/$document_id/utm/", $utm_data, 'POST');
        
        if ($response['success']) {
            $this->log_success("UTM-данные успешно отправлены для документа $document_id");
            return true;
        } else {
            $this->log_warning("Ошибка отправки UTM-данных: " . $response['message']);
            return false; // Не критичная ошибка, продолжаем работу
        }
    }
    
    // Отправка информации о доставке через специальный API эндпоинт
    private function send_delivery_info($document_id, $order) {
        $this->log_info("Запуск функции send_delivery_info для заказа ID: " . $order->get_id());

        $endpoint = "docs_sales/{$document_id}/delivery_info/";

        // Проверяем и нормализуем телефон
        $raw_phone = $this->get_recipient_phone($order);
        if (empty($raw_phone)) {
            $msg = 'Информация о доставке не отправлена: отсутствует телефон получателя.';
            $this->log_error($msg);
            $this->add_order_note($order->get_id(), $msg);
            return;
        }
        $digits = preg_replace('/\D+/', '', $raw_phone);
        if (strlen($digits) == 11 && $digits[0] == '8') {
            $digits = '7' . substr($digits, 1);
        }
        if (strlen($digits) == 10) {
            $digits = '7' . $digits;
        }
        $phone = '+' . $digits;

        // Получаем дату и время доставки в виде timestamp
        $delivery_timestamp = $this->get_delivery_date($order);

        // Получаем имя получателя из данных доставки ровно так, как оно было введено
        $shipping_first_name = $order->get_shipping_first_name();
        $shipping_last_name = $order->get_shipping_last_name();
        $recipient_name = trim($shipping_first_name . ' ' . $shipping_last_name);
        
        // Логируем полученные данные
        $this->log_info(sprintf(
            'Данные получателя (как есть из WooCommerce): shipping_first_name="%s", shipping_last_name="%s", полное имя="%s"',
            $shipping_first_name,
            $shipping_last_name,
            $recipient_name
        ));
        
        // Если имя получателя пустое, используем имя из платежных данных с предупреждением
        if (empty($recipient_name)) {
            $billing_first_name = $order->get_billing_first_name();
            $billing_last_name = $order->get_billing_last_name();
            $recipient_name = trim($billing_first_name . ' ' . $billing_last_name);
            
            if (!empty($recipient_name)) {
                $this->log_warning(sprintf(
                    'Имя получателя из данных доставки пустое. Используется имя из платежных данных: "%s"',
                    $recipient_name
                ));
            } else {
                // Если и в платежных данных пусто, используем email
                $email = $order->get_billing_email();
                if (!empty($email)) {
                    $recipient_name = $email;
                    $this->log_warning('Имя получателя не найдено. Используется email: ' . $email);
                } else {
                    $recipient_name = 'Получатель';
                    $this->log_warning('Имя получателя не найдено. Используется стандартное значение: "Получатель"');
                }
            }
        }
        
        // Формируем массив данных для отправки
        $delivery_data = [
            'recipient' => [
                'name'  => $recipient_name,  // Используем имя получателя из доставки
                'phone' => $phone,
            ],
            'address' => $this->get_recipient_address($order),
            'note'    => $order->get_customer_note(),
        ];
        
        $this->log_info("Используется имя получателя из данных доставки: " . $recipient_name);

        if ($delivery_timestamp) {
            $delivery_data['delivery_date'] = $delivery_timestamp;
            $this->log_info("Дата доставки (timestamp) для заказа ID " . $order->get_id() . ": " . $delivery_timestamp);
        } else {
            $this->log_warning("Дата доставки не найдена для заказа ID " . $order->get_id() . ". Поле 'delivery_date' не будет отправлено.");
        }

        // Логирование подготовленных данных перед отправкой
        $this->log_info("Подготовленные данные для API доставки (заказ ID: " . $order->get_id() . "): " . json_encode($delivery_data, JSON_UNESCAPED_UNICODE));

        // Отправка данных с использованием POST для обновления информации о доставке
        $response = $this->make_api_request($endpoint, $delivery_data, 'POST');

        // Обработка ответа
        if ($response && isset($response['success']) && $response['success']) {
            $this->log_success("Информация о доставке для заказа ID " . $order->get_id() . " успешно отправлена в TableCRM. Document ID: {$document_id}");
            $this->add_order_note($order->get_id(), 'Информация о доставке успешно отправлена в TableCRM.');
        } else {
            $error_message = 'Не удалось отправить информацию о доставке в TableCRM.';
            if (isset($response['message'])) {
                $error_message .= ' Причина: ' . $response['message'];
            }
            if(isset($response['data']['errors'])) {
                // Более детальное логирование ошибки 422
                $error_details = [];
                foreach($response['data']['errors'] as $error) {
                    $field = implode('.', $error['loc']);
                    $error_details[] = "Поле '{$field}': {$error['msg']}";
                }
                $error_message .= ' Детали ошибки: ' . implode('; ', $error_details);
            }
            if (is_wp_error($response)) {
                $error_message .= ' WP_Error: ' . $response->get_error_message();
            }

            $this->log_error("Ошибка отправки информации о доставке для заказа ID " . $order->get_id() . ". " . $error_message);
            $this->add_order_note($order->get_id(), $error_message);
        }
    }

    /**
     * Получает дату доставки из мета-полей заказа и конвертирует в Unix timestamp.
     * Сначала проверяем плагин CodeRockz, затем стандартные поля.
     *
     * @param WC_Order $order
     * @return int|null Unix timestamp или null, если дата не найдена.
     */
    private function get_delivery_date($order) {
        $this->log_info("Поиск даты доставки для заказа ID: " . $order->get_id());

        // 1. Проверяем мета-поля от плагина "CodeRockz WooCommerce Delivery Date Time Pro"
        $coderockz_date = $this->get_order_meta($order, 'delivery_date'); // e.g., '14/08/2024'
        $coderockz_time = $this->get_order_meta($order, 'delivery_time'); // e.g., '10:00 - 12:00'

        if (!empty($coderockz_date)) {
            $this->log_info("Найдена дата от CodeRockz: '{$coderockz_date}'");
            
            // --- улучшённый парсинг даты и времени ---
            $date_part = str_replace('/', '-', $coderockz_date); // d-m-Y
            $time_part = '00:00:00';

            if (!empty($coderockz_time)) {
                $this->log_info("Найдено время от CodeRockz: '{$coderockz_time}'");
                $start_time = trim(explode(' - ', $coderockz_time)[0]);
                $time_part  = substr_count($start_time, ':') === 1 ? $start_time . ':00' : $start_time;
            }

            $datetime_string = $date_part . ' ' . $time_part; // d-m-Y H:i:s
            $date_obj = DateTime::createFromFormat('d-m-Y H:i:s', $datetime_string);

            if ($date_obj === false) {
                $timestamp = strtotime($datetime_string);
                if ($timestamp === false) {
                    $this->log_error("Не удалось распознать дату '{$datetime_string}'.");
                    return null;
                }
            } else {
                $timestamp = $date_obj->getTimestamp();
            }
            
            $this->log_success("Дата доставки успешно преобразована: {$timestamp}");
            return $timestamp;
        }

        // 2. Проверяем другие возможные мета-поля
        $legacy_delivery_date = $this->get_order_meta($order, '_delivery_date');
        if (!empty($legacy_delivery_date)) {
            $this->log_info("Найдена дата в устаревшем поле _delivery_date: {$legacy_delivery_date}");
            $timestamp = strtotime($legacy_delivery_date);
            if ($timestamp !== false) {
                $this->log_success("Устаревшая дата успешно преобразована в timestamp: {$timestamp}");
                return $timestamp;
            }
        }

        $this->log_warning("Дата доставки не найдена ни в одном из известных мета-полей для заказа ID: " . $order->get_id());
        return null;
    }


    /**
     * Получает время доставки из мета-полей заказа (если необходимо).
     *
     * @param WC_Order $order
     * @return string|null Строка времени или null, если время не найдено.
     */
    private function get_delivery_time($order) {
        // 1. Проверяем CodeRockz WooCommerce Delivery Date Time Pro
        $delivery_time = $this->get_order_meta($order, 'delivery_time');
        if (!empty($delivery_time)) {
            return $delivery_time; // Возвращаем как есть (например "12:30 - 14:00")
        }
        
        // 2. Проверяем Order Delivery Date Pro
        $delivery_time = $this->get_order_meta($order, '_orddd_time_slot');
        if (!empty($delivery_time)) {
            return $delivery_time;
        }
        
        // 3. Проверяем WooCommerce Delivery Time
        $delivery_time = $this->get_order_meta($order, '_delivery_time');
        if (!empty($delivery_time)) {
            return $delivery_time;
        }
        
        return '';
    }
    
    // HPOS-совместимое получение мета-данных заказа
    private function get_order_meta($order, $key) {
        if (method_exists($order, 'get_meta')) {
            return $order->get_meta($key);
        } else {
            return get_post_meta($order->get_id(), $key, true);
        }
    }

    private function get_recipient_phone($order) {
        // Предпочитаем телефон, указанный в полях доставки
        $phone = $order->get_shipping_phone();
        if (empty($phone)) {
            // Популярные кастомные meta-ключи, где магазин может хранить телефон получателя
            $phone = $order->get_meta('shipping_phone');
            if (empty($phone)) {
                $phone = $order->get_meta('_shipping_phone');
            }
        }
        return empty($phone) ? $order->get_billing_phone() : $phone;
    }

    // Формирует адрес получателя с максимальным количеством деталей
    private function get_recipient_address($order) {
        $address_parts = array(
            $order->get_shipping_address_1(),
            $order->get_shipping_address_2(),
            $order->get_shipping_city(),
            $order->get_shipping_state(),
            $order->get_shipping_postcode(),
        );
        // Если адрес доставки отсутствует, используем платежный
        if (implode('', $address_parts) === '') {
            $address_parts = array(
                $order->get_billing_address_1(),
                $order->get_billing_address_2(),
                $order->get_billing_city(),
                $order->get_billing_state(),
                $order->get_billing_postcode(),
            );
        }

        $address = trim(preg_replace('/\s+,/', ',', implode(', ', array_filter($address_parts))));

        // Убираем имя получателя из конца адреса
        $recipient_name = $this->get_recipient_name_for_removal($order);
        if (!empty($recipient_name) && !empty($address)) {
            $original_address = $address;
            // Убираем имя из конца адреса (с запятой или без)
            $address = preg_replace('/,\s*' . preg_quote($recipient_name, '/') . '\s*$/ui', '', $address);
            $address = preg_replace('/\s+' . preg_quote($recipient_name, '/') . '\s*$/ui', '', $address);

            if ($original_address !== $address) {
                $this->log_info("Удалено имя '{$recipient_name}' из адреса. Было: '{$original_address}', стало: '{$address}'");
            }
        }

        return trim($address);
    }

    // Получает имя получателя для удаления из адреса
    private function get_recipient_name_for_removal($order) {
        // Получаем все возможные варианты имени
        $names = array();

        // Имя из доставки
        $shipping_first = trim($order->get_shipping_first_name());
        $shipping_last = trim($order->get_shipping_last_name());
        if (!empty($shipping_first)) $names[] = $shipping_first;
        if (!empty($shipping_last)) $names[] = $shipping_last;
        if (!empty($shipping_first) && !empty($shipping_last)) {
            $names[] = $shipping_first . ' ' . $shipping_last;
        }

        // Имя из платежных данных
        $billing_first = trim($order->get_billing_first_name());
        $billing_last = trim($order->get_billing_last_name());
        if (!empty($billing_first)) $names[] = $billing_first;
        if (!empty($billing_last)) $names[] = $billing_last;
        if (!empty($billing_first) && !empty($billing_last)) {
            $names[] = $billing_first . ' ' . $billing_last;
        }

        // Возвращаем самое длинное имя для более точного удаления
        if (!empty($names)) {
            usort($names, function($a, $b) {
                return strlen($b) - strlen($a);
            });
            return $names[0];
        }

        return '';
    }

    // ... existing code ...
    /**
     * Возвращает имя получателя, очищенное от телефонов и лишних пробелов
     * 1) данные доставки; 2) платёжные данные; 3) email; 4) плейсхолдер «Получатель».
     */
    private function get_recipient_name($order) {
        // Сначала получаем имя из полей доставки
        $shipping_name = trim($order->get_shipping_first_name() . ' ' . $order->get_shipping_last_name());
        if ($shipping_name !== '') {
            $name = $shipping_name;
        } else {
            // Если в доставке нет, берем из платежных данных
            $billing_name = trim($order->get_billing_first_name() . ' ' . $order->get_billing_last_name());
            if ($billing_name !== '') {
                $name = $billing_name;
            } else {
                // Если и там нет, берем email или плейсхолдер
                $email = $order->get_billing_email();
                $name = $email !== '' ? $email : __('Получатель', 'tablecrm-integration-complete');
            }
        }
        
        // Очищаем имя от телефонов и лишних пробелов
        $name = preg_replace('/\s*\+?[78]?[\s\-\(\)\+]*\d[\s\-\(\)\d]*\d\s*/', ' ', $name);
        $name = trim(preg_replace('/\s+/', ' ', $name));
        
        return $name;
    }
    // ... existing code ...
}

// Инициализация плагина
function initialize_tablecrm_integration_complete() {
    global $tablecrm_integration_complete;
    $tablecrm_integration_complete = new TableCRM_Integration_Complete();
}
add_action('plugins_loaded', 'initialize_tablecrm_integration_complete'); 