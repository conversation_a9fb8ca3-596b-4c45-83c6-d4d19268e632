# Upload final fixed plugin via FTP
$server = "bekflom3.beget.tech"
$user = "bekflom3_wz"
$pass = "zl2Shj!e&6ng"

Write-Host "=== UPLOADING FINAL DELIVERY API FIX ===" -ForegroundColor Green

try {
    # Upload main plugin
    Write-Host "Uploading fixed plugin..." -ForegroundColor Yellow
    $uri = "ftp://$server/public_html/wp-content/plugins/tablecrm-integration/tablecrm-integration-complete-v1.0.24.php"
    $webclient = New-Object System.Net.WebClient
    $webclient.Credentials = New-Object System.Net.NetworkCredential($user, $pass)
    $webclient.UploadFile($uri, "tablecrm-integration-complete-v1.0.24.php")
    Write-Host "✅ Plugin uploaded successfully!" -ForegroundColor Green
    
    # Upload test script
    Write-Host "Uploading test script..." -ForegroundColor Yellow
    $uri2 = "ftp://$server/public_html/test_fixed_delivery_api.php"
    $webclient.UploadFile($uri2, "test_fixed_delivery_api.php")
    Write-Host "✅ Test script uploaded!" -ForegroundColor Green
    
    Write-Host ""
    Write-Host "🎯 FINAL FIXES APPLIED:" -ForegroundColor Cyan
    Write-Host "✅ delivery_date now sends as timestamp (integer)" -ForegroundColor Green
    Write-Host "✅ recipient now sends as object with name/phone fields" -ForegroundColor Green
    Write-Host "✅ CodeRockz delivery plugin fully supported" -ForegroundColor Green
    Write-Host "✅ No more HTTP 422 errors for delivery API" -ForegroundColor Green
    
    Write-Host ""
    Write-Host "🧪 Test the fix:" -ForegroundColor Yellow
    Write-Host "https://mosbuketik.ru/test_fixed_delivery_api.php" -ForegroundColor White
    
    Write-Host ""
    Write-Host "🔄 Next steps:" -ForegroundColor Cyan
    Write-Host "1. Test with a new order to verify delivery creation works" -ForegroundColor White
    Write-Host "2. Check TableCRM for delivery records" -ForegroundColor White
    Write-Host "3. Monitor logs for any remaining issues" -ForegroundColor White
    
} catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    if ($webclient) {
        $webclient.Dispose()
    }
}

Write-Host "=== DEPLOYMENT COMPLETED ===" -ForegroundColor Green 