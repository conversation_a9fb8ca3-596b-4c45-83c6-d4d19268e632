<?php
// Test automatic hooks for Woo<PERSON>ommer<PERSON> orders
echo "=== ТЕСТ АВТОМАТИЧЕСКИХ ХУКОВ WOOCOMMERCE ===\n";

$wp_path = '/home/<USER>/bekflom3/mosbuketik.ru/public_html';
require_once($wp_path . '/wp-config.php');
require_once($wp_path . '/wp-load.php');

echo "WordPress загружен успешно\n\n";

// Check if WooCommerce is active
if (!class_exists('WooCommerce')) {
    echo "❌ WooCommerce не активен\n";
    exit;
}

echo "✅ WooCommerce активен\n";

// Check if our plugin is loaded
global $tablecrm_integration_complete;
if (isset($tablecrm_integration_complete)) {
    echo "✅ Плагин TableCRM загружен\n";
} else {
    echo "❌ Плагин TableCRM не загружен\n";
    exit;
}

// Check hooks
echo "\n=== ПРОВЕРКА ХУКОВ ===\n";

// Check if our hook is registered
$hooks = $GLOBALS['wp_filter']['woocommerce_order_status_changed'] ?? null;
if ($hooks) {
    echo "✅ Хук woocommerce_order_status_changed зарегистрирован\n";
    echo "Количество функций на хуке: " . count($hooks->callbacks) . "\n";
    
    // Check if our function is in the hooks
    $found_our_hook = false;
    foreach ($hooks->callbacks as $priority => $callbacks) {
        foreach ($callbacks as $callback) {
            if (is_array($callback['function']) && 
                isset($callback['function'][0]) && 
                is_object($callback['function'][0]) &&
                get_class($callback['function'][0]) === 'TableCRM_Integration_Complete') {
                echo "✅ Наш хук найден с приоритетом: $priority\n";
                $found_our_hook = true;
                break 2;
            }
        }
    }
    
    if (!$found_our_hook) {
        echo "❌ Наш хук НЕ найден среди зарегистрированных\n";
    }
} else {
    echo "❌ Хук woocommerce_order_status_changed НЕ зарегистрирован\n";
}

// Test manual hook trigger
echo "\n=== ТЕСТ РУЧНОГО ВЫЗОВА ХУКА ===\n";
$test_order_id = 21773;
$order = wc_get_order($test_order_id);

if ($order) {
    echo "Тестируем заказ #$test_order_id\n";
    echo "Текущий статус: " . $order->get_status() . "\n";
    
    // Check if already sent
    $existing_doc_id = get_post_meta($test_order_id, '_tablecrm_complete_document_id', true);
    if ($existing_doc_id) {
        echo "Заказ уже отправлен, Document ID: $existing_doc_id\n";
        
        // Clear the meta to test again
        delete_post_meta($test_order_id, '_tablecrm_complete_document_id');
        delete_post_meta($test_order_id, '_tablecrm_complete_sent_at');
        echo "Метаданные очищены для повторного теста\n";
    }
    
    echo "Вызываем хук вручную...\n";
    do_action('woocommerce_order_status_changed', $test_order_id);
    
    // Check if it was sent
    $new_doc_id = get_post_meta($test_order_id, '_tablecrm_complete_document_id', true);
    if ($new_doc_id) {
        echo "✅ УСПЕХ! Хук сработал, Document ID: $new_doc_id\n";
    } else {
        echo "❌ Хук не сработал или заказ не отправлен\n";
    }
} else {
    echo "❌ Заказ #$test_order_id не найден\n";
}

// Test status change simulation
echo "\n=== ТЕСТ СИМУЛЯЦИИ ИЗМЕНЕНИЯ СТАТУСА ===\n";
$test_order_id_2 = 21772;
$order2 = wc_get_order($test_order_id_2);

if ($order2) {
    echo "Тестируем заказ #$test_order_id_2\n";
    $current_status = $order2->get_status();
    echo "Текущий статус: $current_status\n";
    
    // Clear existing data
    delete_post_meta($test_order_id_2, '_tablecrm_complete_document_id');
    delete_post_meta($test_order_id_2, '_tablecrm_complete_sent_at');
    
    // Simulate status change
    echo "Симулируем изменение статуса с '$current_status' на 'processing'...\n";
    $order2->set_status('processing', 'Тест автоматической отправки');
    $order2->save();
    
    // Check if it was sent
    sleep(2); // Wait a bit for hooks to process
    $new_doc_id_2 = get_post_meta($test_order_id_2, '_tablecrm_complete_document_id', true);
    if ($new_doc_id_2) {
        echo "✅ УСПЕХ! Автоматическая отправка сработала, Document ID: $new_doc_id_2\n";
    } else {
        echo "❌ Автоматическая отправка не сработала\n";
    }
    
    // Restore original status
    $order2->set_status($current_status, 'Восстановление исходного статуса');
    $order2->save();
    echo "Статус восстановлен на: $current_status\n";
}

echo "\n=== ТЕСТ ЗАВЕРШЕН ===\n";
?> 