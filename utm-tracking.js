/**
 * UTM Tracking Script for TableCRM Integration
 * Отслеживает UTM параметры и сохраняет их в куки
 */

(function($) {
    'use strict';

    // Функция для получения параметров из URL
    function getUrlParameter(name) {
        name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
        var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
        var results = regex.exec(location.search);
        return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
    }

    // Функция для установки куки
    function setCookie(name, value, days) {
        var expires = "";
        if (days) {
            var date = new Date();
            date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
            expires = "; expires=" + date.toUTCString();
        }
        document.cookie = name + "=" + (value || "") + expires + "; path=/";
    }

    // Функция для получения куки
    function getCookie(name) {
        var nameEQ = name + "=";
        var ca = document.cookie.split(';');
        for (var i = 0; i < ca.length; i++) {
            var c = ca[i];
            while (c.charAt(0) == ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length, c.length);
        }
        return null;
    }

    // Список UTM параметров для отслеживания
    var utmParams = ['utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content'];
    
    // Дополнительные параметры для отслеживания
    var additionalParams = ['gclid', 'fbclid', 'yclid'];

    $(document).ready(function() {
        
        // Отслеживаем UTM параметры
        utmParams.forEach(function(param) {
            var value = getUrlParameter(param);
            if (value) {
                setCookie(param, value, 30); // Сохраняем на 30 дней
                console.log('TableCRM: Сохранен UTM параметр ' + param + ' = ' + value);
            }
        });

        // Отслеживаем дополнительные параметры (Google Ads, Facebook, Yandex)
        additionalParams.forEach(function(param) {
            var value = getUrlParameter(param);
            if (value) {
                setCookie(param, value, 30);
                console.log('TableCRM: Сохранен параметр ' + param + ' = ' + value);
            }
        });

        // Сохраняем referrer при первом посещении
        if (!getCookie('referrer') && document.referrer) {
            setCookie('referrer', document.referrer, 30);
            console.log('TableCRM: Сохранен referrer = ' + document.referrer);
        }

        // Сохраняем landing page при первом посещении
        if (!getCookie('landing_page')) {
            setCookie('landing_page', window.location.href, 30);
            console.log('TableCRM: Сохранена landing page = ' + window.location.href);
        }

        // Отслеживаем клики по внешним ссылкам
        $('a[href*="://"]').not('[href*="' + location.hostname + '"]').on('click', function() {
            var href = $(this).attr('href');
            console.log('TableCRM: Клик по внешней ссылке: ' + href);
            
            // Можно отправить данные о клике в Google Analytics или другую систему аналитики
            if (typeof gtag !== 'undefined') {
                gtag('event', 'click', {
                    'event_category': 'outbound',
                    'event_label': href,
                    'transport_type': 'beacon'
                });
            }
        });

        // Отслеживаем время на странице для аналитики
        var startTime = new Date().getTime();
        
        $(window).on('beforeunload', function() {
            var timeSpent = Math.round((new Date().getTime() - startTime) / 1000);
            setCookie('time_on_site', timeSpent, 1); // Сохраняем на 1 день
        });

        // Отслеживаем скролл для определения вовлеченности
        var maxScroll = 0;
        $(window).on('scroll', function() {
            var scrollPercent = Math.round(($(window).scrollTop() / ($(document).height() - $(window).height())) * 100);
            if (scrollPercent > maxScroll) {
                maxScroll = scrollPercent;
                setCookie('max_scroll', maxScroll, 1);
            }
        });

        // Функция для отправки события в TableCRM (если нужно)
        window.tableCrmTrack = function(event, data) {
            data = data || {};
            data.event = event;
            data.timestamp = new Date().getTime();
            data.url = window.location.href;
            
            // Добавляем UTM данные
            utmParams.forEach(function(param) {
                var value = getCookie(param);
                if (value) {
                    data[param] = value;
                }
            });
            
            console.log('TableCRM Track Event:', event, data);
            
            // Здесь можно отправить данные через AJAX в WordPress
            // $.post(tablecrm_ajax.ajax_url, {
            //     action: 'tablecrm_track_event',
            //     data: data
            // });
        };

        // Логируем все сохраненные UTM данные для отладки
        if (console && console.group) {
            console.group('TableCRM UTM Tracking');
            utmParams.forEach(function(param) {
                var value = getCookie(param);
                if (value) {
                    console.log(param + ': ' + value);
                }
            });
            console.log('referrer: ' + getCookie('referrer'));
            console.log('landing_page: ' + getCookie('landing_page'));
            console.groupEnd();
        }
    });

})(jQuery); 