<?php
/**
 * Диагностика проблем с checkout в WooCommerce
 * Скрипт для поиска причин ошибки "Произошла ошибка при обработке вашего заказа"
 */

echo "=== Диагностика проблем с checkout ===\n\n";

// 1. Проверка синтаксиса файла плагина
echo "1. Проверка синтаксиса плагина TableCRM...\n";
$plugin_file = 'tablecrm-integration-complete-v1.0.24.php';

if (file_exists($plugin_file)) {
    $syntax_check = shell_exec("php -l $plugin_file 2>&1");
    if (strpos($syntax_check, 'No syntax errors') !== false) {
        echo "✅ Синтаксис плагина корректен\n";
    } else {
        echo "❌ СИНТАКСИЧЕСКАЯ ОШИБКА в плагине:\n";
        echo $syntax_check . "\n";
    }
} else {
    echo "❌ Файл плагина не найден: $plugin_file\n";
}

// 2. Проверка активных плагинов WordPress
echo "\n2. Проверка активных плагинов...\n";
echo "Предполагаемые активные плагины:\n";
echo "- WooCommerce (обязательно)\n";
echo "- TableCRM Integration Complete\n";
echo "- CodeRockz WooCommerce Delivery Date Time Pro\n";
echo "- Yandex Metrika\n";
echo "- Yoast SEO\n";
echo "- Health Check\n";

// 3. Анализ последних ошибок из debug.log
echo "\n3. Анализ последних ошибок из debug.log...\n";
if (file_exists('debug.log')) {
    $log_content = file_get_contents('debug.log');
    
    // Поиск фатальных ошибок за последние 2 часа
    $current_time = time();
    $two_hours_ago = $current_time - 7200;
    
    $lines = explode("\n", $log_content);
    $recent_errors = [];
    
    foreach (array_reverse($lines) as $line) {
        if (preg_match('/\[(\d{2}-\w{3}-\d{4} \d{2}:\d{2}:\d{2})\]/', $line, $matches)) {
            $log_time = strtotime($matches[1]);
            if ($log_time > $two_hours_ago) {
                if (stripos($line, 'fatal') !== false || 
                    stripos($line, 'parse error') !== false ||
                    stripos($line, 'syntax error') !== false) {
                    $recent_errors[] = $line;
                }
            } else {
                break; // Выходим, так как записи становятся старше
            }
        }
    }
    
    if (!empty($recent_errors)) {
        echo "❌ Найдены критические ошибки за последние 2 часа:\n";
        foreach (array_slice($recent_errors, 0, 5) as $error) {
            echo "   " . substr($error, 0, 150) . "...\n";
        }
    } else {
        echo "✅ Критических ошибок за последние 2 часа не найдено\n";
    }
    
    // Поиск ошибок TableCRM
    echo "\n4. Проверка ошибок TableCRM за последний час...\n";
    $tablecrm_errors = [];
    
    foreach (array_reverse($lines) as $line) {
        if (stripos($line, 'tablecrm') !== false && 
            (stripos($line, 'error') !== false || stripos($line, 'fatal') !== false)) {
            $tablecrm_errors[] = $line;
            if (count($tablecrm_errors) >= 3) break;
        }
    }
    
    if (!empty($tablecrm_errors)) {
        echo "⚠️ Найдены ошибки TableCRM:\n";
        foreach ($tablecrm_errors as $error) {
            echo "   " . substr($error, 0, 150) . "...\n";
        }
    } else {
        echo "✅ Ошибок TableCRM не найдено\n";
    }
    
} else {
    echo "❌ Файл debug.log не найден\n";
}

// 5. Рекомендации по устранению
echo "\n=== РЕКОМЕНДАЦИИ ПО УСТРАНЕНИЮ ===\n";

echo "\n1. Если есть синтаксические ошибки в плагине:\n";
echo "   - Проверьте правильность всех кавычек и скобок\n";
echo "   - Убедитесь, что все строки правильно завершены\n";
echo "   - Временно деактивируйте плагин TableCRM\n";

echo "\n2. Если ошибки в других плагинах:\n";
echo "   - Деактивируйте проблемные плагины поочередно\n";
echo "   - Проверьте совместимость версий\n";
echo "   - Обновите плагины до последних версий\n";

echo "\n3. Если ошибок не найдено:\n";
echo "   - Очистите все кэши (плагины кэширования, опкэш)\n";
echo "   - Проверьте лимиты PHP (memory_limit, max_execution_time)\n";
echo "   - Проверьте соединение с внешними API\n";

echo "\n4. Общие рекомендации:\n";
echo "   - Создайте резервную копию сайта\n";
echo "   - Включите режим отладки WP_DEBUG\n";
echo "   - Проверьте логи сервера (error.log)\n";
echo "   - Тестируйте checkout в режиме инкогнито\n";

echo "\n=== ЗАВЕРШЕНИЕ ДИАГНОСТИКИ ===\n";
?> 