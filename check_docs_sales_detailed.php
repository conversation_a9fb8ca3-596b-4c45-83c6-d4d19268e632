<?php
/**
 * Детальная проверка документов продаж в TableCRM
 * Пробует различные способы получения и сортировки данных
 */

echo "📄 ДЕТАЛЬНАЯ ПРОВЕРКА ДОКУМЕНТОВ ПРОДАЖ\n";
echo "=======================================\n\n";

$api_token = 'af1874616430e04cfd4bce30035789907e899fc7c3a1a4bb27254828ff304a77';
$api_url = 'https://app.tablecrm.com/api/v1/';

function make_api_request($endpoint, $params = []) {
    global $api_url, $api_token;
    
    $url = rtrim($api_url, '/') . '/' . ltrim($endpoint, '/');
    $params['token'] = $api_token;
    $url .= '?' . http_build_query($params);
    
    echo "🔗 " . str_replace($api_token, '***', $url) . "\n";
    
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => [
                'User-Agent: TableCRM-Sales-Check/1.0',
                'Accept: application/json'
            ],
            'timeout' => 30
        ]
    ]);
    
    $response = file_get_contents($url, false, $context);
    
    if ($response === false) {
        echo "❌ Ошибка запроса\n\n";
        return false;
    }
    
    // Получаем HTTP статус
    $http_status = 200;
    if (isset($http_response_header[0])) {
        preg_match('/HTTP\/\d\.\d\s+(\d+)/', $http_response_header[0], $matches);
        if (isset($matches[1])) {
            $http_status = (int)$matches[1];
        }
    }
    
    echo "📊 HTTP: $http_status";
    
    if ($http_status !== 200) {
        echo " ❌\n";
        echo "Ответ: " . substr($response, 0, 300) . "...\n\n";
        return false;
    }
    
    echo " ✅\n";
    
    $decoded = json_decode($response, true);
    if ($decoded === null) {
        echo "❌ JSON ошибка\n\n";
        return false;
    }
    
    return $decoded;
}

// Различные варианты запроса документов продаж
$request_variants = [
    'По умолчанию' => ['limit' => 10],
    'С сортировкой по ID (убывание)' => ['limit' => 10, 'ordering' => '-id'],
    'С сортировкой по дате (убывание)' => ['limit' => 10, 'ordering' => '-dated'],
    'Последние 5' => ['limit' => 5, 'offset' => 0],
    'Следующие 5' => ['limit' => 5, 'offset' => 5],
    'Первые 20' => ['limit' => 20],
    'Без лимита' => [], // Попробуем без ограничения
];

foreach ($request_variants as $variant_name => $params) {
    echo "🔍 Вариант: $variant_name\n";
    echo str_repeat("=", 50) . "\n";
    
    $result = make_api_request('docs_sales/', $params);
    
    if ($result) {
        $total_count = $result['count'] ?? 0;
        $results = $result['result'] ?? $result['results'] ?? [];
        $returned_count = count($results);
        
        echo "📊 Всего в системе: $total_count\n";
        echo "📊 Получено: $returned_count\n";
        
        if ($returned_count > 0) {
            echo "\n📋 Первые документы:\n";
            
            foreach (array_slice($results, 0, 3) as $index => $doc) {
                echo "\n📄 Документ #" . ($index + 1) . ":\n";
                echo "   ID: " . ($doc['id'] ?? 'не указан') . "\n";
                echo "   Номер: " . ($doc['number'] ?? 'не указан') . "\n";
                echo "   Дата: " . ($doc['dated'] ?? 'не указана') . "\n";
                echo "   Создан: " . ($doc['created_at'] ?? 'не указано') . "\n";
                echo "   Обновлен: " . ($doc['updated_at'] ?? 'не указано') . "\n";
                echo "   Операция: " . ($doc['operation'] ?? 'не указана') . "\n";
                echo "   Сумма: " . ($doc['paid_rubles'] ?? $doc['total_rubles'] ?? 'не указана') . " руб.\n";
                echo "   Статус: " . (($doc['is_paid'] ?? false) ? 'Оплачен' : 'Не оплачен') . "\n";
                echo "   Контрагент: " . ($doc['contragent'] ?? 'не указан') . "\n";
                echo "   Организация: " . ($doc['organization'] ?? 'не указана') . "\n";
                
                if (!empty($doc['comment'])) {
                    $comment = strlen($doc['comment']) > 100 ? 
                              substr($doc['comment'], 0, 100) . '...' : 
                              $doc['comment'];
                    echo "   Комментарий: $comment\n";
                }
                
                if (!empty($doc['external_id'])) {
                    echo "   External ID: {$doc['external_id']}\n";
                }
                
                // Проверяем на тестовые данные
                $test_fields = [];
                foreach ($doc as $field => $value) {
                    if (is_string($value) && 
                        (stripos($value, 'тест') !== false || 
                         stripos($value, 'test') !== false ||
                         stripos($value, 'demo') !== false ||
                         stripos($value, 'Айгуль') !== false)) {
                        $test_fields[] = "$field: $value";
                    }
                }
                
                if (!empty($test_fields)) {
                    echo "   🧪 ТЕСТОВЫЕ МАРКЕРЫ:\n";
                    foreach ($test_fields as $field) {
                        echo "     - $field\n";
                    }
                }
                
                // Анализируем время создания
                if (!empty($doc['created_at'])) {
                    $created_time = strtotime($doc['created_at']);
                    $now = time();
                    $diff = $now - $created_time;
                    
                    if ($diff < 3600) {
                        echo "   🕐 Создан: " . round($diff/60) . " минут назад (НЕДАВНО!)\n";
                    } elseif ($diff < 86400) {
                        echo "   🕐 Создан: " . round($diff/3600) . " часов назад\n";
                    } elseif ($diff < 604800) {
                        echo "   🕐 Создан: " . round($diff/86400) . " дней назад\n";
                    } else {
                        echo "   🕐 Создан: " . date('d.m.Y', $created_time) . "\n";
                    }
                }
            }
            
            // Анализ последнего документа
            if ($returned_count > 0) {
                $last_doc = $results[0];
                $last_id = $last_doc['id'] ?? null;
                
                if ($last_id) {
                    echo "\n🔍 Получение полной информации о документе ID: $last_id\n";
                    echo str_repeat("-", 50) . "\n";
                    
                    $full_doc = make_api_request("docs_sales/$last_id/");
                    
                    if ($full_doc) {
                        echo "✅ Полная информация получена:\n";
                        
                        foreach ($full_doc as $key => $value) {
                            if (is_scalar($value)) {
                                $display_value = is_string($value) && strlen($value) > 100 ? 
                                               substr($value, 0, 100) . '...' : 
                                               $value;
                                echo "   $key: $display_value\n";
                            } elseif (is_array($value)) {
                                echo "   $key: [массив из " . count($value) . " элементов]\n";
                                
                                // Показываем товары, если есть
                                if ($key === 'goods' && !empty($value)) {
                                    echo "     Товары:\n";
                                    foreach ($value as $good_index => $good) {
                                        echo "       - Товар #" . ($good_index + 1) . ":\n";
                                        foreach ($good as $good_key => $good_value) {
                                            if (is_scalar($good_value)) {
                                                echo "         $good_key: $good_value\n";
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            
        } else {
            echo "❌ Документы не найдены\n";
        }
        
        echo "\n" . str_repeat("=", 60) . "\n\n";
        
        // Если нашли данные в первом варианте, можно остановиться
        if ($returned_count > 0) {
            echo "✅ Данные найдены! Останавливаем дальнейшую проверку вариантов.\n\n";
            break;
        }
    }
    
    // Небольшая пауза между запросами
    sleep(1);
}

echo "✅ ПРОВЕРКА ЗАВЕРШЕНА\n";
echo "====================\n";
echo "⏱️ Время выполнения: " . round((microtime(true) - $_SERVER['REQUEST_TIME_FLOAT']) * 1000, 2) . " мс\n"; 