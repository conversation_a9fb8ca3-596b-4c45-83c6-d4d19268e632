<?php
// Check TableCRM Complete plugin settings
echo "=== ПРОВЕРКА НАСТРОЕК TABLECRM COMPLETE ===\n";

// WordPress path - adjust if needed
$wp_path = '/home/<USER>/bekflom3/mosbuketik.ru/public_html';
require_once($wp_path . '/wp-config.php');
require_once($wp_path . '/wp-includes/wp-db.php');
require_once($wp_path . '/wp-includes/functions.php');

// Connect to database
$wpdb = new wpdb(DB_USER, DB_PASSWORD, DB_NAME, DB_HOST);

echo "Подключение к базе данных: " . (is_null($wpdb->last_error) ? "УСПЕШНО" : "ОШИБКА") . "\n";
echo "Префикс таблиц: " . $wpdb->prefix . "\n\n";

// Check all TableCRM Complete options
$options = array(
    'tablecrm_complete_api_url',
    'tablecrm_complete_api_key',
    'tablecrm_complete_organization_id',
    'tablecrm_complete_cashbox_id',
    'tablecrm_complete_warehouse_id',
    'tablecrm_complete_unit_id',
    'tablecrm_complete_enable_orders',
    'tablecrm_complete_debug_mode',
    'tablecrm_complete_order_statuses'
);

foreach ($options as $option) {
    $query = "SELECT option_value FROM {$wpdb->prefix}options WHERE option_name = %s";
    $value = $wpdb->get_var($wpdb->prepare($query, $option));
    
    echo sprintf("%-35s: %s\n", $option, $value !== null ? $value : 'НЕ НАЙДЕНО');
}

echo "\n=== ПРОВЕРКА АКТИВНЫХ ПЛАГИНОВ ===\n";
$active_plugins = $wpdb->get_var("SELECT option_value FROM {$wpdb->prefix}options WHERE option_name = 'active_plugins'");
$plugins = unserialize($active_plugins);

$tablecrm_found = false;
foreach ($plugins as $plugin) {
    if (strpos($plugin, 'tablecrm') !== false) {
        echo "Найден TableCRM плагин: $plugin\n";
        $tablecrm_found = true;
    }
}

if (!$tablecrm_found) {
    echo "TableCRM плагины не найдены в активных!\n";
}

echo "\n=== ДОПОЛНИТЕЛЬНАЯ ДИАГНОСТИКА ===\n";

// Check if the current settings exist with different names
$all_tablecrm_options = $wpdb->get_results("SELECT option_name, option_value FROM {$wpdb->prefix}options WHERE option_name LIKE '%tablecrm%'");

if ($all_tablecrm_options) {
    echo "Все опции, содержащие 'tablecrm':\n";
    foreach ($all_tablecrm_options as $option) {
        echo sprintf("%-40s: %s\n", $option->option_name, substr($option->option_value, 0, 100));
    }
} else {
    echo "Опции с 'tablecrm' в названии не найдены!\n";
}

echo "\nГОТОВО!\n";
?> 