# Upload and run shipping test
$server = "bekflom3.beget.tech"
$user = "bekflom3_wz"
$pass = "zl2Shj!e&6ng"

Write-Host "Uploading shipping test script..." -ForegroundColor Green

try {
    $uri = "ftp://$server/public_html/test_shipping_cost.php"
    $webclient = New-Object System.Net.WebClient
    $webclient.Credentials = New-Object System.Net.NetworkCredential($user, $pass)
    $webclient.UploadFile($uri, "test_shipping_cost.php")
    Write-Host "Shipping test script uploaded!" -ForegroundColor Green
    
    # Run the test
    Write-Host "Running shipping cost test..." -ForegroundColor Yellow
    $result = Invoke-WebRequest -Uri "https://mosbuketik.ru/test_shipping_cost.php" -UseBasicParsing
    Write-Host $result.Content -ForegroundColor White
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

$webclient.Dispose()
Write-Host "Shipping test completed!" -ForegroundColor Green 