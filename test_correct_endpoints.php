<?php
// Тестирование правильных эндпоинтов TableCRM API

$api_url = 'https://app.tablecrm.com/api/v1/';
$api_key = 'af1874616430e04cfd4bce30035789907e899fc7c3a1a4bb27254828ff304a77';

echo "🎯 ТЕСТИРОВАНИЕ ПРАВИЛЬНЫХ ЭНДПОИНТОВ TABLECRM\n";
echo "============================================\n\n";

// Правильные эндпоинты для ERP системы
$endpoints_to_test = [
    'docs_sales/' => 'POST', // Создание документа продаж
    'contragents/' => 'POST', // Создание контрагента
    'docs_sales/' => 'GET',   // Получение списка документов
    'contragents/' => 'GET',  // Получение списка контрагентов
];

// Тестируем разные варианты авторизации
$auth_variants = [
    [
        'name' => 'Bearer Token в заголовке',
        'headers' => [
            'Authorization: Bearer ' . $api_key,
            'Content-Type: application/json'
        ]
    ],
    [
        'name' => 'X-API-Key в заголовке',
        'headers' => [
            'X-API-Key: ' . $api_key,
            'Content-Type: application/json'
        ]
    ],
    [
        'name' => 'Token в query параметре',
        'headers' => [
            'Content-Type: application/json'
        ],
        'query' => 'token=' . $api_key
    ],
    [
        'name' => 'Authorization без Bearer',
        'headers' => [
            'Authorization: ' . $api_key,
            'Content-Type: application/json'
        ]
    ]
];

foreach ($endpoints_to_test as $endpoint => $method) {
    echo "🔍 Тестируем: $method $endpoint\n";
    echo str_repeat("-", 50) . "\n";
    
    foreach ($auth_variants as $auth_idx => $auth) {
        $url = $api_url . $endpoint;
        if (isset($auth['query'])) {
            $url .= '?' . $auth['query'];
        }
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $auth['headers']);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        if ($method === 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
            // Минимальные тестовые данные
            if ($endpoint === 'docs_sales/') {
                $test_data = [
                    'name' => 'Тестовый заказ',
                    'amount' => 1000
                ];
            } else if ($endpoint === 'contragents/') {
                $test_data = [
                    'name' => 'Тестовый покупатель',
                    'email' => '<EMAIL>'
                ];
            }
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($test_data));
        }
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo sprintf(
            "Авторизация: %-25s | HTTP: %d",
            $auth['name'],
            $http_code
        );
        
        if ($http_code == 200 || $http_code == 201) {
            echo " ✅ УСПЕХ!";
        } else if ($http_code == 401) {
            echo " 🔑 Ошибка авторизации";
        } else if ($http_code == 422) {
            echo " 📝 Ошибка валидации данных";
        } else if ($http_code == 404) {
            echo " ❌ Не найден";
        } else {
            echo " ⚠️  Другая ошибка";
        }
        
        if ($response) {
            $decoded = json_decode($response, true);
            if ($decoded) {
                echo " | Ответ: " . substr(json_encode($decoded), 0, 100) . "...";
            } else {
                echo " | Ответ: " . substr($response, 0, 50) . "...";
            }
        }
        echo "\n";
    }
    echo "\n";
}

echo "🏥 Тестируем health эндпоинт (известно что работает):\n";
echo str_repeat("-", 50) . "\n";

$health_response = file_get_contents($api_url . 'health');
echo "Health эндпоинт: " . ($health_response ? "✅ OK - $health_response" : "❌ Ошибка") . "\n";

echo "\n📋 ВЫВОДЫ:\n";
echo "==========\n";
echo "Если видите HTTP 401 - проблема с авторизацией\n";
echo "Если видите HTTP 422 - проблема с форматом данных\n";
echo "Если видите HTTP 200/201 - эндпоинт работает!\n";
?> 