# 📋 КРАТКОЕ РЕЗЮМЕ РЕПОЗИТОРИЯ

**Проект**: TableCRM Integration для WordPress/WooCommerce  
**Текущая версия**: 1.0.3 (Fixed v2)  
**Статус**: ✅ ГОТОВ К ИСПОЛЬЗОВАНИЮ  
**Дата обновления**: 5 июня 2025

---

## 🎯 ГЛАВНЫЙ ФАЙЛ ПРОЕКТА

### 📦 Рекомендуемая версия:
**`tablecrm-integration-fixed-v2.php`** (версия 1.0.3)

**Почему именно эта версия:**
- ✅ Валидация данных - отправляются только реальные заказы
- ✅ Оптимизированные API запросы - без лишних попыток
- ✅ Улучшенная обработка ошибок и логирование
- ✅ Настраиваемый фильтр данных

### 📥 Готовый архив:
**`tablecrm-integration-fixed-v2.zip`** - готов для установки

---

## 🚀 БЫСТРЫЙ СТАРТ

### 1️⃣ Установка
```bash
# Скачать готовый архив
wget tablecrm-integration-fixed-v2.zip

# Установить в WordPress
# Плагины → Добавить новый → Загрузить плагин
```

### 2️⃣ Настройка
1. Активировать плагин
2. Перейти: **Настройки → TableCRM Integration (Fixed v2)**
3. Заполнить API данные TableCRM
4. ✅ Включить "Отправлять только реальные данные"
5. Протестировать соединение

### 3️⃣ Проверка
- Создать тестовый заказ в WooCommerce
- Проверить логи: `wp-content/debug.log`
- Убедиться что заказ появился в TableCRM

---

## 📊 ОСНОВНЫЕ ВОЗМОЖНОСТИ

### 🛒 WooCommerce
- Автоматическая отправка заказов в TableCRM
- Создание документов продаж
- Синхронизация статусов заказов
- Отправка информации о доставке

### 📝 Contact Form 7
- Отправка форм как лидов в CRM
- Обработка различных типов полей
- Проверка дубликатов

### 🏷️ UTM Отслеживание
- Сохранение UTM меток в заказах
- Отправка источников трафика
- Отслеживание конверсий

---

## 🗂️ СТРУКТУРА РЕПОЗИТОРИЯ

### 📁 Основные файлы:
```
├── tablecrm-integration-fixed-v2.php    [ОСНОВНОЙ ПЛАГИН v1.0.3]
├── tablecrm-integration-fixed-v2.zip    [ГОТОВЫЙ АРХИВ]
├── utm-tracking.js                      [UTM СКРИПТ]
├── README.md                           [ГЛАВНАЯ ДОКУМЕНТАЦИЯ]
└── CHANGELOG.md                        [ИСТОРИЯ ИЗМЕНЕНИЙ]
```

### 📚 Документация:
```
├── КРАТКАЯ_ИНСТРУКЦИЯ_ПОЛЬЗОВАТЕЛЯ.md  [ПОШАГОВАЯ НАСТРОЙКА]
├── ДИАГНОСТИКА_ПРОБЛЕМ.md             [РЕШЕНИЕ ПРОБЛЕМ]
├── ИНСТРУКЦИЯ_API.md                  [НАСТРОЙКА API]
├── ФИНАЛЬНАЯ_ИНСТРУКЦИЯ_ЗАГРУЗКИ.md   [УСТАНОВКА]
└── ИТОГОВОЕ_РЕЗЮМЕ_ПРОЕКТА.md         [ПОЛНОЕ РЕЗЮМЕ]
```

### 🛠️ Утилиты:
```
├── check_logs.sh                      [ПРОСМОТР ЛОГОВ]
├── create_test_order.sh              [ТЕСТОВЫЙ ЗАКАЗ]
├── update_fixed_plugin.sh            [ОБНОВЛЕНИЕ ПЛАГИНА]
└── check_plugin_status.php           [ПРОВЕРКА СТАТУСА]
```

---

## 📈 ИСТОРИЯ ВЕРСИЙ

| Версия | Дата | Статус | Описание |
|--------|------|--------|----------|
| **1.0.3** | 05.06.25 | ✅ **АКТУАЛЬНАЯ** | Валидация данных, оптимизация API |
| 1.0.2 | 04.06.25 | ❌ Устарела | Шифрование API ключей |
| 1.0.1 | 04.06.25 | ❌ Устарела | Первая рабочая версия |
| 1.0.0 | 03.06.25 | ❌ Устарела | Начальная версия |

---

## ⚙️ СИСТЕМНЫЕ ТРЕБОВАНИЯ

### WordPress/WooCommerce:
- WordPress 5.0+
- WooCommerce 3.0+
- PHP 7.4+ (рекомендуется 8.0+)
- PHP расширение `sodium` (для шифрования)

### TableCRM:
- Активный аккаунт TableCRM
- API ключ доступа
- Настроенные организация, касса, склад

---

## 🔧 НАСТРОЙКИ ПО УМОЛЧАНИЮ

### 📋 Предустановленные значения:
- **API URL**: `https://app.tablecrm.com/api/v1/`
- **Organization ID**: 38
- **Cashbox ID**: 218  
- **Warehouse ID**: 39
- **Unit ID**: 116

### ✅ Рекомендуемые опции:
- **Отправлять только реальные данные**: ВКЛЮЧЕНО
- **Режим отладки**: ВКЛЮЧЕНО
- **Проверять дубликаты**: ВКЛЮЧЕНО

---

## 🔍 ДИАГНОСТИКА

### 📊 Проверка работы:
1. **Тестирование API** - кнопка в настройках
2. **Просмотр логов** - `wp-content/debug.log`
3. **Тестовый заказ** - создание через WooCommerce
4. **Проверка CRM** - документы в TableCRM

### 🛠️ Инструменты:
```bash
# Просмотр логов
./check_logs.sh

# Создание тестового заказа  
./create_test_order.sh

# Проверка статуса плагина
php check_plugin_status.php
```

---

## 🚨 ВАЖНЫЕ ЗАМЕЧАНИЯ

### ✅ ОБЯЗАТЕЛЬНО:
- Используйте только версию 1.0.3
- Включите валидацию данных
- Создайте резервную копию перед установкой
- Протестируйте на тестовом сайте

### ❌ НЕ РЕКОМЕНДУЕТСЯ:
- Использовать устаревшие версии плагина
- Отключать валидацию в продакшене
- Игнорировать ошибки в логах

---

## 📞 ПОДДЕРЖКА

### 📚 Документация:
- [`README.md`](README.md) - основная документация
- [`ДИАГНОСТИКА_ПРОБЛЕМ.md`](ДИАГНОСТИКА_ПРОБЛЕМ.md) - решение проблем
- [`КРАТКАЯ_ИНСТРУКЦИЯ_ПОЛЬЗОВАТЕЛЯ.md`](КРАТКАЯ_ИНСТРУКЦИЯ_ПОЛЬЗОВАТЕЛЯ.md) - пошаговая настройка

### 🔧 Диагностика:
1. Включите режим отладки
2. Проверьте логи WordPress
3. Используйте встроенные инструменты тестирования
4. Обратитесь к документации

---

## 🎊 РЕЗЮМЕ

**TableCRM Integration v1.0.3** - полностью готовое решение для интеграции WordPress/WooCommerce с TableCRM. Плагин протестирован, документирован и готов к использованию в продакшене.

**🎯 Рекомендация: Используйте файл `tablecrm-integration-fixed-v2.php` для максимальной стабильности!** 