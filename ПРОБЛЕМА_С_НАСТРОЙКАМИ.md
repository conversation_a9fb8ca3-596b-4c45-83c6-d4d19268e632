# 🚨 ПРОБЛЕМА: Не заполнены настройки плагина

## 🔍 **ДИАГНОЗ**

Анализ логов показывает, что **настройки плагина не сохранились** или **заполнены не полностью**.

### **Ошибка в логах:**
```
[2025-06-07 08:22:17] [ERROR] Не настроены обязательные параметры API
```

### **Код проверки в плагине (строки 346-351):**
```php
$api_url = get_option('tablecrm_complete_api_url');
$api_token = get_option('tablecrm_complete_api_key');
$warehouse_id = get_option('tablecrm_complete_warehouse_id');
$organization_id = get_option('tablecrm_complete_organization_id');
$paybox_id = get_option('tablecrm_complete_cashbox_id');

if (empty($api_url) || empty($api_token) || empty($warehouse_id) || empty($organization_id) || empty($paybox_id)) {
    $this->log_error("Не настроены обязательные параметры API");
    return false;
}
```

---

## 🛠️ **РЕШЕНИЕ**

### **Шаг 1: Проверить настройки в админке WordPress**

Зайдите в: **WordPress Админка → Настройки → TableCRM Complete**

### **Шаг 2: Заполнить ВСЕ обязательные поля:**

```
✅ API URL: https://app.tablecrm.com/api/v1/
✅ API Key: af1874616430e04cfd4bce30035789907e899fc7c3a1a4bb27254828ff304a77
✅ Organization ID: 38
✅ Warehouse ID: 39
✅ Cashbox ID: 218
✅ Unit ID: 116
```

### **Шаг 3: Важные моменты при заполнении**

1. **Поля НЕ должны быть пустыми**
2. **Убрать пробелы** в начале и конце полей
3. **API Key скопировать полностью** (64 символа)
4. **Числовые поля** должны содержать только цифры
5. **API URL** должен заканчиваться на `/`

### **Шаг 4: Сохранить и протестировать**

1. Нажать **"Сохранить настройки"**
2. Нажать **"Тестировать соединение"** 
3. Должно появиться: ✅ **"Соединение успешно!"**

---

## 🔧 **ВОЗМОЖНЫЕ ПРИЧИНЫ ПРОБЛЕМЫ**

### 1. **Настройки не сохранились**
- Ошибка при сохранении формы
- Проблемы с правами доступа
- Конфликт с другими плагинами

### 2. **Поля заполнены неправильно**
- Лишние пробелы
- Неполный API ключ
- Неправильные числовые значения

### 3. **Кэширование**
- WordPress кэширует настройки
- Нужно очистить кэш

---

## 📋 **ЧЕКЛИСТ ПРОВЕРКИ**

- [ ] Зашел в админку WordPress
- [ ] Открыл Настройки → TableCRM Complete  
- [ ] Проверил что все 6 полей заполнены
- [ ] API URL: `https://app.tablecrm.com/api/v1/`
- [ ] API Key: `af1874616430e04cfd4bce30035789907e899fc7c3a1a4bb27254828ff304a77`
- [ ] Organization ID: `38`
- [ ] Warehouse ID: `39`
- [ ] Cashbox ID: `218`
- [ ] Unit ID: `116`
- [ ] Нажал "Сохранить настройки"
- [ ] Нажал "Тестировать соединение"
- [ ] Получил сообщение об успехе
- [ ] Создал тестовый заказ
- [ ] Проверил логи

---

## 🎯 **ОЖИДАЕМЫЙ РЕЗУЛЬТАТ**

После правильного заполнения настроек:

### ✅ **В логах должно появиться:**
```
[TableCRM Complete] [INFO] === НАЧАЛО ОТПРАВКИ ЗАКАЗА XXX В TABLECRM ===
[TableCRM Complete] [INFO] Поиск/создание контрагента для телефона: +7...
[TableCRM Complete] [SUCCESS] Контрагент найден в results. ID: 330985
[TableCRM Complete] [SUCCESS] Номенклатура найдена в results. ID: 39697
[TableCRM Complete] [SUCCESS] Заказ XXX успешно отправлен. Document ID: 456789
```

### ❌ **Вместо ошибки:**
```
[TableCRM Complete] [ERROR] Не настроены обязательные параметры API
```

---

**Дата:** 06.01.2025  
**Статус:** 🔴 ТРЕБУЕТ ИСПРАВЛЕНИЯ  
**Действие:** Заполнить настройки в админке WordPress 