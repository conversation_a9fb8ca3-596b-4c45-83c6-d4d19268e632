<!-- Generated by SpecStory -->

# Debugging recipient name issue in TableCRM (2025-06-30 07:39Z)

_**User**_

@tablecrm-integration-complete-v1.0.24.php имя отличается

---

_**Assistant**_

Вы абсолютно правы, на скриншоте видно, что в поле "Имя" получателя в TableCRM попадает строка вида:  
**"Артемест +7(987)878-78-78"**  
То есть имя и телефон объединены в одно поле, хотя телефон должен быть отдельным полем (и он там есть ниже).

В коде плагина (`tablecrm-integration-complete-v1.0.24.php`) функция, отвечающая за передачу имени получателя в TableCRM, выглядит так:

```php
private function get_recipient_name($order) {
    $shipping_name = trim($order->get_shipping_first_name() . ' ' . $order->get_shipping_last_name());
    if ($shipping_name !== '') {
        return $shipping_name;
    }
    $billing_name = trim($order->get_billing_first_name() . ' ' . $order->get_billing_last_name());
    if ($billing_name !== '') {
        return $billing_name;
    }
    $email = $order->get_billing_email();
    return $email !== '' ? $email : __('Получатель', 'tablecrm-integration-complete');
}
```

**В этом коде нет объединения имени и телефона!**  
Значит, проблема, скорее всего, не в этом методе, а в том, как формируются данные в WooCommerce (например, если в поле "Имя" доставки или оплаты уже записано "

---

