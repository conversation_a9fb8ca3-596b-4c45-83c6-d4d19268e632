# 🚀 Краткое руководство TableCRM Integration v1.0.4

## ⚡ Главные улучшения версии 1.0.4

### 🎯 Что нового:
- ✅ **Action Scheduler** - надежная очередь задач вместо WP-Cron
- ✅ **Защита от дублирования** - заказы никогда не отправятся дважды  
- ✅ **Автоповторы** - неудачные отправки повторяются автоматически
- ✅ **Мониторинг** - статус очереди прямо в админке
- ✅ **Производительность** - заказы обрабатываются в фоне

### 🛡️ Решенные проблемы:
- 🐛 Дублирование заказов в CRM
- 🐛 "Размножение" сделок
- 🐛 Коллизии платежей  
- 🐛 Потеря задач при перезагрузке сервера

---

## 🚀 Быстрая установка

### 1. Требования
- **WooCommerce ≥ 8.5** (обязательно!)
- WordPress ≥ 5.0
- PHP ≥ 7.4

### 2. Установка
1. **Скачать**: `tablecrm-integration-fixed-v3.zip`
2. **Админка** → **Плагины** → **Добавить новый** → **Загрузить**
3. **Активировать плагин**

### 3. Настройка
1. **Настройки** → **TableCRM Integration (Fixed v3)**
2. **API URL**: `https://app.tablecrm.com/api/v1/`
3. **API Key**: ваш ключ из TableCRM
4. **Сохранить** → **Тестировать соединение**

---

## ⚙️ Основные настройки

### Обязательные поля:
| Поле | Значение | Где взять |
|------|----------|-----------|
| **API URL** | `https://app.tablecrm.com/api/v1/` | Стандартный адрес |
| **API Key** | `your_api_key_here` | TableCRM → Настройки → API |
| **Organization ID** | `38` | TableCRM → Организации |
| **Cashbox ID** | `218` | TableCRM → Кассы |

### Рекомендуемые настройки:
- ✅ **Отправлять только реальные данные** ← ВАЖНО!
- ✅ **Повторять неудачные попытки** ← НОВОЕ!
- ✅ **Режим отладки** (для диагностики)

---

## 📊 Мониторинг работы

### В админ-панели видно:
- **Задач в очереди**: 0 (хорошо)
- **Неудачных задач**: 0 (отлично)

### В случае проблем:
1. **Проверить логи**: `tail -f /wp-content/debug.log | grep "TableCRM v3"`
2. **WP-CLI команды**:
   ```bash
   wp action-scheduler list --hook=tablecrm_process_order
   wp action-scheduler status
   ```

---

## 🔧 Устранение проблем

### ❌ "Action Scheduler недоступен"
**Причина**: WooCommerce < 8.5  
**Решение**: Обновить WooCommerce

### ❌ Заказы не отправляются
**Проверить**:
1. Включена ли опция "Отправлять заказы WooCommerce"
2. Правильность API ключа (кнопка "Тестировать соединение")
3. Логи WordPress на предмет ошибок

### ❌ Дублирование заказов
**Решение**: Версия 1.0.4 решает эту проблему автоматически!

---

## 🎯 Как работает Action Scheduler

### Процесс отправки заказа:
1. **Создается заказ** в WooCommerce
2. **Задача ставится в очередь** с задержкой 30 сек
3. **Action Scheduler обрабатывает** задачу в фоне
4. **Заказ отправляется** в TableCRM
5. **Сохраняется маркер** успешной отправки

### Защита от дублирования:
- Проверка существующего маркера отправки
- Проверка очереди на наличие задачи для заказа
- Временный маркер обработки
- Финальная проверка перед API запросом

---

## 📈 Преимущества версии 1.0.4

| Проблема | Старые версии | Версия 1.0.4 |
|----------|---------------|---------------|
| **Дублирование заказов** | ❌ Возможно | ✅ Исключено |
| **Потеря задач** | ❌ При перезагрузке | ✅ Хранятся в БД |
| **Блокировка сайта** | ❌ Во время отправки | ✅ Фоновая обработка |
| **Повторы ошибок** | ❌ Ручные | ✅ Автоматические |
| **Мониторинг** | ❌ Только логи | ✅ Панель в админке |

---

## 🔄 Миграция с предыдущих версий

### Безопасная миграция:
1. **Деактивировать** старую версию
2. **Установить** v1.0.4  
3. **Проверить** настройки API
4. **Создать** тестовый заказ
5. **Убедиться** в отправке в CRM

### Совместимость:
- ✅ Старые маркеры остаются валидными
- ✅ Настройки импортируются автоматически
- ✅ Нет конфликтов между версиями

---

## ✅ Чек-лист успешной установки

- [ ] WooCommerce ≥ 8.5 установлен
- [ ] Плагин v1.0.4 активирован
- [ ] API настройки заполнены
- [ ] Тест соединения прошел успешно
- [ ] Создан тестовый заказ
- [ ] Заказ появился в TableCRM
- [ ] В панели мониторинга "0 задач в очереди"

---

## 🆘 Техническая поддержка

### При возникновении проблем:
1. **Включить режим отладки** в настройках
2. **Собрать логи**: `grep "TableCRM v3" /wp-content/debug.log`
3. **Создать issue** в репозитории проекта
4. **Приложить**:
   - Версию WordPress/WooCommerce
   - Сообщения об ошибках из логов
   - Скриншот настроек плагина

### Полезные команды:
```bash
# Статус Action Scheduler
wp action-scheduler status

# Список задач TableCRM
wp action-scheduler list --hook=tablecrm_process_order --status=pending

# Последние логи плагина  
tail -20 /wp-content/debug.log | grep "TableCRM v3"
```

---

> **💡 Совет**: Версия 1.0.4 с Action Scheduler — это единственная рекомендуемая версия для продакшена. Она решает все критические проблемы предыдущих версий. 