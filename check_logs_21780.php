<?php
// Check logs for UTM and delivery data
echo "=== ПРОВЕРКА ЛОГОВ UTM И ДОСТАВКИ ===\n";

$wp_path = '/home/<USER>/bekflom3/mosbuketik.ru/public_html';
require_once($wp_path . '/wp-config.php');
require_once($wp_path . '/wp-load.php');

echo "WordPress загружен успешно\n\n";

$log_file = $wp_path . '/wp-content/uploads/tablecrm_integration_complete.log';

if (file_exists($log_file)) {
    echo "Файл логов найден\n";
    
    $log_content = file_get_contents($log_file);
    $lines = explode("\n", $log_content);
    
    echo "Поиск записей о UTM и доставке...\n\n";
    
    $found_lines = array();
    foreach ($lines as $line_num => $line) {
        if (strpos($line, 'UTM') !== false || 
            strpos($line, 'доставк') !== false ||
            strpos($line, 'delivery') !== false ||
            strpos($line, 'utm') !== false ||
            strpos($line, '138285') !== false) {
            $found_lines[] = ($line_num + 1) . ": " . $line;
        }
    }
    
    if (!empty($found_lines)) {
        echo "НАЙДЕННЫЕ ЗАПИСИ:\n";
        foreach (array_slice($found_lines, -20) as $found_line) { // Последние 20 записей
            echo $found_line . "\n";
        }
    } else {
        echo "Записи о UTM и доставке не найдены\n";
    }
    
    // Показать последние 25 строк лога
    echo "\n=== ПОСЛЕДНИЕ 25 СТРОК ЛОГА ===\n";
    $last_lines = array_slice($lines, -25);
    foreach ($last_lines as $i => $line) {
        $line_num = count($lines) - 25 + $i + 1;
        echo "$line_num: $line\n";
    }
    
} else {
    echo "❌ Файл логов не найден: $log_file\n";
}

echo "\n=== ПРОВЕРКА ЗАВЕРШЕНА ===\n";
?> 