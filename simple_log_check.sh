#!/bin/bash

echo "🔍 Простая проверка логов TableCRM"
echo "=================================="

# Используем lftp для более надежного соединения
lftp -c "
set sftp:auto-confirm yes
set ssl:verify-certificate no
open sftp://bekflom3_wz:zl2Shj!<EMAIL>
cd public_html

echo '📋 Проверяем debug.log...'
ls -la wp-content/debug.log 2>/dev/null && {
    echo '📄 Последние 20 строк debug.log:'
    tail -20 wp-content/debug.log | grep -i tablecrm || echo 'Нет записей TableCRM'
} || echo 'debug.log не найден'

echo ''
echo '📋 Проверяем error.log...'
ls -la wp-content/error.log 2>/dev/null && {
    echo '📄 Последние 10 строк error.log:'
    tail -10 wp-content/error.log
} || echo 'error.log не найден'

echo ''
echo '📋 Проверяем статус плагина...'
ls -la wp-content/plugins/tablecrm-integration/ 2>/dev/null && {
    echo '✅ Плагин установлен'
} || echo '❌ Плагин не найден'

echo ''
echo '📋 Проверяем настройки отладки...'
grep -i 'WP_DEBUG' wp-config.php 2>/dev/null || echo 'Настройки WP_DEBUG не найдены'

quit
"

echo ""
echo "✅ Проверка завершена!" 