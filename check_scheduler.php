<?php
/**
 * Action Scheduler Diagnoser for TableCRM
 * Проверяет очередь задач и выявляет застрявшие или проваленные отправки.
 */

@ini_set('error_reporting', E_ALL);
@ini_set('display_errors', 1);
@set_time_limit(300);

header('Content-Type: text/plain; charset=utf-8');

// --- Функция для поиска wp-load.php ---
function find_wp_load_path_sched() {
    $dir = __DIR__;
    do {
        if (file_exists($dir . '/wp-load.php')) {
            return $dir . '/wp-load.php';
        }
    } while ($dir = realpath("$dir/.."));
    return null;
}

// --- Загрузка окружения WordPress ---
echo "===============================================\n";
echo "    ДИАГНОСТИКА ПЛАНИРОВЩИКА (ACTION SCHEDULER)\n";
echo "===============================================\n\n";

$wp_load_path = find_wp_load_path_sched();
if ($wp_load_path) {
    require_once($wp_load_path);
    echo "[OK] Окружение WordPress успешно загружено.\n\n";
} else {
    echo "[CRITICAL] Не удалось найти wp-load.php. Диагностика невозможна.\n";
    exit;
}

// --- Проверка статуса Action Scheduler ---
echo "1. ПРОВЕРКА ACTION SCHEDULER\n";
echo "-------------------------------\n";
if (!class_exists('ActionScheduler_Store')) {
    echo "[CRITICAL] Класс ActionScheduler_Store не найден. Планировщик не работает или поврежден!\n";
    echo "Возможно, WooCommerce или другой плагин, использующий Action Scheduler, установлен некорректно.\n\n";
    exit;
}

echo "[OK] Action Scheduler активен.\n\n";

// --- Проверка ПРОВАЛЕННЫХ задач ---
echo "2. АНАЛИЗ ПРОВАЛЕННЫХ ЗАДАЧ (FAILED ACTIONS)\n";
echo "-------------------------------------------\n";
$failed_actions = as_get_scheduled_actions([
    'hook' => 'tablecrm_complete_async_send',
    'status' => 'failed',
    'per_page' => 20, // Проверяем последние 20 проваленных
    'orderby' => 'date',
    'order' => 'DESC'
]);

if (!empty($failed_actions)) {
    echo "[CRITICAL] ОБНАРУЖЕНЫ ПРОВАЛЕННЫЕ ЗАДАЧИ ОТПРАВКИ ЗАКАЗОВ!\n";
    echo "Это основная причина, почему заказы не доходят до CRM.\n\n";
    foreach ($failed_actions as $action) {
        $order_id = $action->get_args()['order_id'] ?? 'N/A';
        $log_entries = ActionScheduler_Logger::instance()->get_logs($action->get_id());
        echo "  - Заказ ID: {$order_id}\n";
        echo "    Дата попытки: " . $action->get_schedule()->get_date('Y-m-d H:i:s') . "\n";
        if (!empty($log_entries)) {
            $last_log = end($log_entries);
            echo "    Причина сбоя: " . $last_log->get_message() . "\n\n";
        } else {
            echo "    Причина сбоя не залогирована.\n\n";
        }
    }
} else {
    echo "[OK] Проваленных задач для отправки заказов в TableCRM не найдено.\n\n";
}

// --- Проверка ОЖИДАЮЩИХ задач ---
echo "3. АНАЛИЗ ОЖИДАЮЩИХ ЗАДАЧ (PENDING ACTIONS)\n";
echo "-------------------------------------------\n";
$pending_actions = as_get_scheduled_actions([
    'hook' => 'tablecrm_complete_async_send',
    'status' => 'pending',
    'per_page' => 50, // Проверяем 50 ожидающих
    'orderby' => 'date',
    'order' => 'ASC' // Сначала самые старые
]);

if (!empty($pending_actions)) {
    echo "[WARN] ОБНАРУЖЕНЫ ЗАДАЧИ, ОЖИДАЮЩИЕ В ОЧЕРЕДИ!\n";
    echo "Если они находятся в этом статусе слишком долго, это указывает на проблему с WP-Cron.\n\n";
    $count = count($pending_actions);
    echo "Всего в очереди: {$count}\n\n";
    $actions_to_show = array_slice($pending_actions, 0, 10); // Показываем первые 10
    foreach ($actions_to_show as $action) {
        $order_id = $action->get_args()['order_id'] ?? 'N/A';
        echo "  - Заказ ID: {$order_id}\n";
        echo "    Должен был выполниться: " . $action->get_schedule()->get_date('Y-m-d H:i:s') . "\n\n";
    }
    if ($count > 10) {
        echo "... и еще " . ($count - 10) . " задач в очереди.\n\n";
    }
} else {
    echo "[INFO] Активных задач в очереди на отправку нет.\n\n";
}

// --- Проверка WP-Cron ---
echo "4. ПРОВЕРКА СТАТУСА WP-CRON\n";
echo "--------------------------------\n";
if (defined('DISABLE_WP_CRON') && DISABLE_WP_CRON) {
    echo "[CRITICAL] WP-Cron отключен в wp-config.php (define('DISABLE_WP_CRON', true);).\n";
    echo "Action Scheduler не может работать. Вам нужно либо убрать эту строку, либо настроить системный cron на сервере.\n\n";
} else {
    echo "[OK] WP-Cron не отключен через константу.\n";
}

// Проверка через loopback
$response = wp_remote_post(site_url('wp-cron.php?doing_wp_cron'), [
    'timeout'   => 10,
    'blocking'  => true,
    'sslverify' => apply_filters('https_local_ssl_verify', false),
]);

if (is_wp_error($response)) {
    echo "[CRITICAL] Тестовый запрос к wp-cron.php провалился (loopback-запрос)!\n";
    echo "   Ошибка: " . $response->get_error_message() . "\n";
    echo "   Это частая причина неработающих фоновых задач. Обратитесь к хостинг-провайдеру.\n\n";
} else {
    echo "[OK] Тестовый запрос к wp-cron.php выполнен успешно. Loopback работает.\n\n";
}


echo "===============================================\n";
echo "            ДИАГНОСТИКА ЗАВЕРШЕНА\n";
echo "===============================================\n";
echo "РЕЗУЛЬТАТ:\n";
echo "1. Если есть ПРОВАЛЕННЫЕ задачи - это и есть корень проблемы. Ошибка будет указана.\n";
echo "2. Если есть много ОЖИДАЮЩИХ задач (особенно просроченных) - проблема с WP-Cron.\n";
echo "3. Проверьте раздел WP-CRON на наличие критических ошибок.\n";

?> 