#!/bin/bash

echo "=========================================="
echo "Установка плагина TableCRM Integration"
echo "=========================================="

# Проверка наличия файла плагина
if [ ! -f "tablecrm-integration.php" ]; then
    echo "❌ Ошибка: файл tablecrm-integration.php не найден"
    exit 1
fi

echo "✅ Файл плагина найден"

# Создание папки плагина и загрузка
echo "📁 Создание папки плагина на сервере..."

expect << 'EOF'
spawn lftp sftp://<EMAIL>
expect "Пароль:"
send "zl2Shj!e&6ng\r"
expect "lftp"

# Переход в папку плагинов
send "cd public_html/wp-content/plugins\r"
expect "lftp"

# Создание папки плагина
send "mkdir tablecrm-integration\r"
expect "lftp"

# Переход в папку плагина
send "cd tablecrm-integration\r"
expect "lftp"

# Загрузка основного файла плагина
send "put tablecrm-integration.php\r"
expect "lftp"

# Установка прав на файл
send "chmod 644 tablecrm-integration.php\r"
expect "lftp"

# Проверка загрузки
send "ls -la\r"
expect "lftp"

send "quit\r"
expect eof
EOF

echo ""
echo "✅ Плагин успешно загружен на сервер!"
echo ""
echo "📋 Следующие шаги:"
echo "1. Войдите в админ-панель WordPress"
echo "2. Перейдите в 'Плагины → Установленные'"
echo "3. Найдите 'TableCRM Integration' и нажмите 'Активировать'"
echo "4. Перейдите в 'Настройки → TableCRM Integration'"
echo "5. Заполните настройки API и протестируйте соединение"
echo ""
echo "📖 Подробная инструкция в файле README_plugin.md"
echo ""
echo "=========================================="
echo "Установка завершена!"
echo "==========================================" 