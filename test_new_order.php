<?php
// Test the newest order with full functionality
echo "=== ТЕСТ САМОГО НОВОГО ЗАКАЗА ===\n";

$wp_path = '/home/<USER>/bekflom3/mosbuketik.ru/public_html';
require_once($wp_path . '/wp-config.php');
require_once($wp_path . '/wp-load.php');

echo "WordPress загружен успешно\n\n";

// Get the newest order
$orders = wc_get_orders(array(
    'limit' => 1,
    'orderby' => 'date',
    'order' => 'DESC',
    'status' => array('processing', 'completed', 'pending', 'on-hold')
));

if (empty($orders)) {
    echo "❌ Заказы не найдены\n";
    exit;
}

$order = $orders[0];
$order_id = $order->get_id();

echo "✅ Найден самый новый заказ #$order_id\n";
echo "Дата: " . $order->get_date_created()->format('Y-m-d H:i:s') . "\n";
echo "Статус: " . $order->get_status() . "\n";
echo "Клиент: " . $order->get_formatted_billing_full_name() . "\n";
echo "Сумма: " . $order->get_total() . " руб.\n";

// Check if already sent
$existing_doc_id = get_post_meta($order_id, '_tablecrm_complete_document_id', true);
if (!empty($existing_doc_id)) {
    echo "Заказ уже отправлен. Document ID: $existing_doc_id\n";
    
    // Clear to test re-sending
    delete_post_meta($order_id, '_tablecrm_complete_document_id');
    delete_post_meta($order_id, '_tablecrm_complete_sent_at');
    echo "Очищены метки для повторной отправки\n";
    }
    
// Show order items
echo "\n=== ТОВАРЫ В ЗАКАЗЕ ===\n";
$items = $order->get_items();
foreach ($items as $item) {
    echo "- " . $item->get_name() . " x" . $item->get_quantity() . " = " . $order->get_item_total($item) . " руб.\n";
}

$shipping_total = $order->get_shipping_total();
if ($shipping_total > 0) {
    echo "- Доставка: " . $shipping_total . " руб. (" . $order->get_shipping_method() . ")\n";
}

// Show UTM data if available
echo "\n=== UTM-ДАННЫЕ ===\n";
$meta_data = $order->get_meta_data();
$utm_found = false;

foreach ($meta_data as $meta) {
    $key = $meta->get_data()['key'];
    $value = $meta->get_data()['value'];
    
    if (strpos($key, '_wc_order_attribution_') === 0) {
        $clean_key = str_replace('_wc_order_attribution_', '', $key);
        echo "$clean_key: $value\n";
        $utm_found = true;
    }
}

if (!$utm_found) {
    echo "UTM-данные не найдены\n";
}

// Test sending
echo "\n=== ОТПРАВКА В TABLECRM ===\n";
global $tablecrm_integration_complete;
if (isset($tablecrm_integration_complete)) {
    echo "Плагин найден, начинаем отправку...\n";
    
    $result = $tablecrm_integration_complete->send_order_to_tablecrm($order_id);
    
    if ($result) {
        echo "✅ ЗАКАЗ ОТПРАВЛЕН УСПЕШНО!\n";
        
        $doc_id = get_post_meta($order_id, '_tablecrm_complete_document_id', true);
        if ($doc_id) {
            echo "Document ID: $doc_id\n";
        }
    } else {
        echo "❌ ОШИБКА ОТПРАВКИ ЗАКАЗА\n";
    }
} else {
    echo "❌ Плагин не найден\n";
}

echo "\n=== ТЕСТ ЗАВЕРШЕН ===\n";
?> 