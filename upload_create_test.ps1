# Upload and run create test order
$server = "bekflom3.beget.tech"
$user = "bekflom3_wz"
$pass = "zl2Shj!e&6ng"

Write-Host "Uploading create test order script..." -ForegroundColor Green

try {
    $uri = "ftp://$server/public_html/create_test_order.php"
    $webclient = New-Object System.Net.WebClient
    $webclient.Credentials = New-Object System.Net.NetworkCredential($user, $pass)
    $webclient.UploadFile($uri, "create_test_order.php")
    Write-Host "Create test order script uploaded!" -ForegroundColor Green
    
    # Run the test
    Write-Host "Running create test order..." -ForegroundColor Yellow
    $result = Invoke-WebRequest -Uri "https://mosbuketik.ru/create_test_order.php" -UseBasicParsing
    Write-Host $result.Content -ForegroundColor White
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

$webclient.Dispose()
Write-Host "Create test order completed!" -ForegroundColor Green 