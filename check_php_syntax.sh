#!/bin/bash

echo "=== ПРОВЕРКА СИНТАКСИСА PHP ==="
echo "Проверяем файл: tablecrm-integration-fixed-v2.php"

if [ ! -f "tablecrm-integration-fixed-v2.php" ]; then
    echo "❌ ОШИБКА: Файл не найден!"
    exit 1
fi

# Проверяем синтаксис PHP
echo "🔍 Проверяем синтаксис..."
php -l tablecrm-integration-fixed-v2.php

if [ $? -eq 0 ]; then
    echo "✅ СИНТАКСИС КОРРЕКТЕН! Файл готов к загрузке."
    echo "📁 Размер файла: $(ls -lh tablecrm-integration-fixed-v2.php | awk '{print $5}')"
else
    echo "❌ НАЙДЕНЫ ОШИБКИ СИНТАКСИСА! Файл нужно исправить."
    exit 1
fi 