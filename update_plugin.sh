#!/bin/bash

echo "=========================================="
echo "Обновление плагина TableCRM Integration"
echo "=========================================="

# Проверка наличия файлов
if [ ! -f "tablecrm-integration.php" ]; then
    echo "❌ Ошибка: файл tablecrm-integration.php не найден"
    exit 1
fi

if [ ! -f "utm-tracking.js" ]; then
    echo "❌ Ошибка: файл utm-tracking.js не найден"
    exit 1
fi

echo "✅ Файлы плагина найдены"

# Загрузка обновленных файлов
echo "📁 Загрузка обновленных файлов..."

expect << 'EOF'
spawn lftp sftp://<EMAIL>
expect "Пароль:"
send "zl2Shj!e&6ng\r"
expect "lftp"

# Переход в папку плагина
send "cd public_html/wp-content/plugins/tablecrm-integration\r"
expect "lftp"

# Загрузка основного файла плагина
send "put tablecrm-integration.php\r"
expect "lftp"

# Загрузка JavaScript файла
send "put utm-tracking.js\r"
expect "lftp"

# Установка прав
send "chmod 644 tablecrm-integration.php\r"
expect "lftp"
send "chmod 644 utm-tracking.js\r"
expect "lftp"

# Проверка загрузки
send "ls -la\r"
expect "lftp"

send "quit\r"
expect eof
EOF

echo ""
echo "✅ Плагин успешно обновлен!"
echo ""
echo "🔄 Новые возможности:"
echo "• Проверка дубликатов товаров в заказах"
echo "• Отправка данных о способе оплаты и статусе"
echo "• Сбор UTM-меток и аналитических данных"
echo "• Отслеживание даты и времени доставки"
echo "• Информация о домене и источнике заказа"
echo ""
echo "📋 Рекомендации:"
echo "1. Обновите плагин в админ-панели WordPress"
echo "2. Проверьте логи для отладки новых функций"
echo "3. Протестируйте создание тестового заказа"
echo ""
echo "=========================================="
echo "Обновление завершено!"
echo "==========================================" 