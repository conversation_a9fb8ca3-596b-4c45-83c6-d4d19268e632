# 🔍 Диагностика: Заказ не поступил в TableCRM

## 🚨 Основные причины, почему заказ не отправляется:

### 1. **Плагин отключен или не активен**
- ✅ **Проверить:** WordPress Admin → Плагины → TableCRM Integration
- ✅ **Статус:** Должен быть "Активен"

### 2. **Отключена отправка заказов в настройках**
- ✅ **Проверить:** WordPress Admin → Настройки → TableCRM Integration
- ✅ **Настройка:** "Отправлять заказы WooCommerce" должна быть включена

### 3. **Статус заказа не выбран для отправки**
- ✅ **Проверить:** WordPress Admin → Настройки → TableCRM Integration
- ✅ **Раздел:** "Статусы заказов для отправки"
- ✅ **Проблема:** Если статус заказа (pending/processing/completed) не отмечен галочкой

### 4. **Проблемы с API ключом или URL**
- ✅ **Проверить:** WordPress Admin → Настройки → TableCRM Integration
- ✅ **API URL:** `https://app.tablecrm.com/api/v1/`
- ✅ **API Key:** `af1874616430e04cfd4bce30035789907e899fc7c3a1a4bb27254828ff304a77`
- ✅ **Тест:** Нажать кнопку "Тестировать соединение"

### 5. **Заказ был определен как дубликат**
- ✅ **Проверить:** Настройка "Проверять дубликаты"
- ✅ **Проблема:** Если email или телефон уже есть в системе

### 6. **Ошибки в логах WordPress**
- ✅ **Включить:** "Режим отладки" в настройках плагина
- ✅ **Проверить:** Логи в wp-content/debug.log

## 🔧 **Быстрая диагностика (5 минут):**

### Шаг 1: Проверить активность плагина
```
WordPress Admin → Плагины → Найти "TableCRM Integration"
Статус: Активен ✅ / Неактивен ❌
```

### Шаг 2: Проверить основные настройки
```
WordPress Admin → Настройки → TableCRM Integration

✅ Отправлять заказы WooCommerce: [x] Включено
✅ API URL: https://app.tablecrm.com/api/v1/
✅ API Key: af1874616430e04cfd4bce30035789907e899fc7c3a1a4bb27254828ff304a77
```

### Шаг 3: Проверить статусы заказов
```
В разделе "Статусы заказов для отправки" должны быть отмечены:
✅ [x] Ожидает оплаты (pending)
✅ [x] В обработке (processing)  
✅ [x] Выполнен (completed)
```

### Шаг 4: Тест соединения
```
Нажать кнопку "Тестировать соединение"
Результат: ✅ "Соединение установлено успешно!" / ❌ Ошибка
```

### Шаг 5: Включить отладку
```
✅ [x] Режим отладки: Включен
Нажать "Сохранить настройки"
```

## 📊 **Детальная диагностика заказа:**

### Проверить конкретный заказ:
1. **Найти заказ:** WordPress Admin → WooCommerce → Заказы
2. **Открыть заказ** и запомнить ID (например, #12345)
3. **Статус заказа:** pending/processing/completed/cancelled/etc.
4. **Метаданные заказа:** Поискать поле `_tablecrm_lead_id`

### Если `_tablecrm_lead_id` есть:
✅ **Заказ БЫЛ отправлен** в TableCRM  
➡️ **Проблема:** TableCRM API не принял данные или ошибка в эндпоинте

### Если `_tablecrm_lead_id` нет:
❌ **Заказ НЕ БЫЛ отправлен** из WordPress  
➡️ **Проблема:** В настройках плагина, статусах или произошла ошибка

## 🔥 **Возможные ошибки в логах:**

### Типичные сообщения в debug.log:
```
[TableCRM Integration] Заказ 12345 не отправлен - статус wc-cancelled не выбран для отправки
→ РЕШЕНИЕ: Включить нужные статусы в настройках

[TableCRM Integration] Дубликат найден для заказа 12345: <EMAIL>
→ РЕШЕНИЕ: Отключить проверку дубликатов или использовать другой email

[TableCRM Integration] Ошибка отправки заказа 12345 в TableCRM: HTTP 404
→ РЕШЕНИЕ: Проблема с эндпоинтом TableCRM API

[TableCRM Integration] Не настроены обязательные параметры API (URL и Key)
→ РЕШЕНИЕ: Заполнить API URL и API Key в настройках
```

## ⚡ **Экстренные действия:**

### Если заказ КРИТИЧНО важен:
1. **Записать данные заказа вручную**
2. **Отправить в TableCRM через их веб-интерфейс**
3. **Зафиксировать проблему для разработчика**

### Если проблема массовая:
1. **Отключить проверку дубликатов** временно
2. **Включить ВСЕ статусы заказов** для отправки
3. **Включить режим отладки**
4. **Создать тестовый заказ** для проверки

## 📞 **Кому обратиться:**

### Техническая поддержка TableCRM:
- **Вопрос:** "API ключ работает, но эндпоинт возвращает 404"
- **Данные:** API Key `af1874616430e04cfd4bce30035789907e899fc7c3a1a4bb27254828ff304a77`

### Разработчик плагина:
- **Если:** Настройки правильные, но заказы не отправляются
- **Предоставить:** Логи WordPress, скриншоты настроек

### Администратор WordPress:
- **Если:** Проблемы с активацией плагина или доступом к настройкам

## 🎯 **Следующие шаги:**

1. **Выполнить быструю диагностику** (5 пунктов выше)
2. **Создать тестовый заказ** с включенной отладкой
3. **Проверить логи** после создания тестового заказа
4. **Сообщить результаты** разработчику для исправления 