# Upload and run order #21771 check
$server = "bekflom3.beget.tech"
$user = "bekflom3_wz"
$pass = "zl2Shj!e&6ng"

Write-Host "Uploading order check script..." -ForegroundColor Green

try {
    $uri = "ftp://$server/public_html/check_order_21771.php"
    $webclient = New-Object System.Net.WebClient
    $webclient.Credentials = New-Object System.Net.NetworkCredential($user, $pass)
    $webclient.UploadFile($uri, "check_order_21771.php")
    Write-Host "Check script uploaded!" -ForegroundColor Green
    
    # Run the check
    Write-Host "Running order #21771 diagnostics..." -ForegroundColor Yellow
    $result = Invoke-WebRequest -Uri "https://mosbuketik.ru/check_order_21771.php" -UseBasicParsing
    Write-Host $result.Content -ForegroundColor White
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

$webclient.Dispose()
Write-Host "Order check completed!" -ForegroundColor Green 