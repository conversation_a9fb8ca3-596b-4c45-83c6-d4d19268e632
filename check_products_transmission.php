<?php
/**
 * ДЕТАЛЬНАЯ ПРОВЕРКА ПЕРЕДАЧИ ТОВАРОВ В TableCRM
 * Анализирует, почему товары могут не передаваться корректно
 */

echo "🔍 ДЕТАЛЬНАЯ ПРОВЕРКА ПЕРЕДАЧИ ТОВАРОВ В TableCRM\n";
echo "===============================================\n\n";

// Анализ того, как плагин обрабатывает товары
function analyze_product_processing() {
    echo "📦 АНАЛИЗ ОБРАБОТКИ ТОВАРОВ\n";
    echo str_repeat("=", 40) . "\n";
    
    // Имитируем данные заказа WooCommerce
    $wc_order_items = [
        [
            'name' => 'Тестовый товар 1',
            'quantity' => 2,
            'total' => 3000.00,
            'sku' => 'TEST-001',
            'product_id' => 1001
        ],
        [
            'name' => 'Тестовый товар 2', 
            'quantity' => 1,
            'total' => 2500.00,
            'sku' => 'TEST-002',
            'product_id' => 1002
        ]
    ];
    
    echo "✅ Исходные товары WooCommerce:\n";
    foreach ($wc_order_items as $index => $item) {
        $unit_price = $item['quantity'] > 0 ? ($item['total'] / $item['quantity']) : 0;
        echo "   Товар " . ($index + 1) . ":\n";
        echo "     Название: {$item['name']}\n";
        echo "     SKU: {$item['sku']}\n";
        echo "     Количество: {$item['quantity']}\n";
        echo "     Цена за единицу: $unit_price руб.\n";
        echo "     Общая сумма: {$item['total']} руб.\n\n";
    }
    
    return $wc_order_items;
}

// Как плагин формирует данные для API
function simulate_plugin_processing($items) {
    echo "🔄 ОБРАБОТКА В ПЛАГИНЕ (строки 647-661)\n";
    echo str_repeat("=", 40) . "\n";
    
    $data = ['items' => []];
    
    // Имитируем код из строк 647-661 плагина
    foreach ($items as $item) {
        $unit_price = $item['quantity'] > 0 ? ($item['total'] / $item['quantity']) : 0;
        
        $data['items'][] = array(
            'name' => $item['name'],
            'quantity' => $item['quantity'],
            'price' => $item['total'],
            'unit_price' => $unit_price,
            'sku' => $item['sku'],
            'product_id' => $item['product_id']
        );
    }
    
    echo "✅ Данные после обработки плагином:\n";
    foreach ($data['items'] as $index => $item) {
        echo "   Товар " . ($index + 1) . ":\n";
        echo "     name: {$item['name']}\n";
        echo "     quantity: {$item['quantity']}\n";
        echo "     price: {$item['price']}\n";
        echo "     unit_price: {$item['unit_price']}\n";
        echo "     sku: {$item['sku']}\n\n";
    }
    
    return $data;
}

// Адаптация для TableCRM API (строки 814-825)
function simulate_tablecrm_adaptation($data) {
    echo "📡 АДАПТАЦИЯ ДЛЯ TableCRM (строки 814-825)\n";
    echo str_repeat("=", 40) . "\n";
    
    $unit_id = 116;
    $goods = [];
    
    if (!empty($data['items'])) {
        echo "✅ Формируем товары для TableCRM:\n";
        foreach ($data['items'] as $index => $item) {
            $good = array(
                'price' => floatval($item['unit_price']),
                'quantity' => intval($item['quantity']),
                'unit' => intval($unit_id),
                'discount' => 0,
                'sum_discounted' => 0,
                'nomenclature' => 39697
            );
            
            $goods[] = $good;
            
            echo "   Товар " . ($index + 1) . " для TableCRM:\n";
            echo "     price: {$good['price']}\n";
            echo "     quantity: {$good['quantity']}\n";
            echo "     unit: {$good['unit']}\n";
            echo "     nomenclature: {$good['nomenclature']}\n\n";
        }
    } else {
        echo "❌ ПРОБЛЕМА: data['items'] пуст!\n\n";
    }
    
    return $goods;
}

// Проверка финального JSON
function check_final_json($goods) {
    echo "📄 ФИНАЛЬНЫЙ JSON ДЛЯ ОТПРАВКИ\n";
    echo str_repeat("=", 40) . "\n";
    
    $document = [
        'dated' => time(),
        'operation' => 'Заказ',
        'comment' => 'Тестовый заказ',
        'tax_included' => true,
        'tax_active' => true,
        'goods' => $goods,
        'warehouse' => 39,
        'contragent' => 330985,
        'paybox' => 218,
        'organization' => 38,
        'status' => false,
        'paid_rubles' => '5500.00'
    ];
    
    $json = json_encode([$document], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    
    echo "JSON размер: " . strlen($json) . " байт\n";
    echo "Товаров в goods: " . count($goods) . "\n\n";
    
    echo "Фрагмент JSON (товары):\n";
    echo json_encode($goods, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";
    
    return $json;
}

// Основной анализ
echo "🚀 НАЧИНАЕМ АНАЛИЗ\n";
echo "==================\n\n";

// Шаг 1: Анализ исходных данных
$original_items = analyze_product_processing();

// Шаг 2: Обработка плагином  
$processed_data = simulate_plugin_processing($original_items);

// Шаг 3: Адаптация для TableCRM
$tablecrm_goods = simulate_tablecrm_adaptation($processed_data);

// Шаг 4: Проверка финального JSON
$final_json = check_final_json($tablecrm_goods);

// Выводы
echo "🎯 ЗАКЛЮЧЕНИЕ\n";
echo "=============\n";

if (count($tablecrm_goods) > 0) {
    echo "✅ Товары ДОЛЖНЫ передаваться в TableCRM!\n";
    echo "✅ Плагин корректно обрабатывает товары\n";
    echo "✅ JSON формируется правильно\n\n";
    
    echo "🔍 ЕСЛИ ТОВАРЫ НЕ ВИДНЫ В TableCRM:\n";
    echo "1. Проверьте раздел 'Продажи' → 'Продажи' (НЕ 'Платежи')\n";
    echo "2. Откройте документ и найдите вкладку 'Товары'\n";
    echo "3. Проверьте ID номенклатуры (39697) в TableCRM\n";
    echo "4. Убедитесь что unit ID (116) существует\n\n";
    
} else {
    echo "❌ ПРОБЛЕМА: Товары не формируются!\n";
    echo "Проверьте данные заказа в WooCommerce\n\n";
}

echo "💡 ВОЗМОЖНЫЕ ПРИЧИНЫ ОТСУТСТВИЯ ТОВАРОВ:\n";
echo "1. Номенклатура 39697 не существует в TableCRM\n";
echo "2. Единица измерения 116 не найдена\n";
echo "3. Нет прав доступа к товарам в API\n";
echo "4. Товары скрыты в интерфейсе TableCRM\n\n";

echo "✅ АНАЛИЗ ЗАВЕРШЕН\n";
?>