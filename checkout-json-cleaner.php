<?php
/*
 * Checkout JSON Sanitizer
 * Обрезает весь «шум» (Notice, echo, пробелы) до и после JSON-ответа WooCommerce checkout.
 * Автор: AI Assistant
 */
defined( 'ABSPATH' ) || exit;

add_action(
	'muplugins_loaded',
	static function () {

		// Работаем ТОЛЬКО в ajax-запросе оформления заказа.
		$is_wc_checkout_ajax =
			defined( 'DOING_AJAX' ) && DOING_AJAX &&
			(
				( isset( $_REQUEST['wc-ajax'] ) && $_REQUEST['wc-ajax'] === 'checkout' ) ||
				( isset( $_REQUEST['action'] )   && $_REQUEST['action'] === 'woocommerce_checkout' )
			);

		if ( ! $is_wc_checkout_ajax ) {
			return;
		}

		// Логируем запуск (можно убрать позже).
		error_log( '[Checkout JSON Sanitizer] started' );

		// 1. Стартуем буферизацию как можно раньше.
		ob_start();

		// 2. В shutdown обрабатываем уже сформированный вывод (даже если wp_send_json вызвал ob_end_clean).
		register_shutdown_function(
			static function () {

				$buffer = ob_get_clean();    // всё, что вывели скрипты
				if ( $buffer === false ) {
					// Буфер мог быть закрыт wp_send_json; просто ничего не делаем.
					return;
				}

				$start = strpos( $buffer, '{' );
				$end   = strrpos( $buffer, '}' );

				if ( $start > 0 ) {
					error_log( '[Checkout JSON Sanitizer] noise removed: ' . substr( $buffer, 0, $start ) );
				}

				if ( $start !== false && $end !== false && $end > $start ) {
					echo substr( $buffer, $start, $end - $start + 1 );
				} else {
					// На случай, если JSON так и не нашли.
					echo '{}';
				}
			}
		);
	},
	0 // САМЫЙ ранний приоритет
);