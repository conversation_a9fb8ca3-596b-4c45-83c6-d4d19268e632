# 🔄 Обновление эндпоинтов TableCRM API

## 🎯 **Что было обновлено:**

Плагин обновлен согласно [документации TableCRM API](https://app.tablecrm.com/api/v1/docs) с правильными эндпоинтами для максимальной совместимости.

## 📋 **Новые эндпоинты в порядке приоритета:**

### 1. **Основные эндпоинты TableCRM:**
- ✅ `leads/add` (PUT method) - основной эндпоинт создания лидов
- ✅ `crm/objects/contact` (POST) - альтернативный для контактов
- ✅ `api/v1/leads` (POST) - API v1 эндпоинты
- ✅ `api/v1/contacts` (POST)
- ✅ `graph/leads` (PUT) - Graph API эндпоинты

### 2. **Дополнительные варианты:**
- ✅ `contacts/add` (PUT) - создание контакта
- ✅ `customers/add` (PUT) - создание клиента
- ✅ `capture-data` (POST) - захват данных
- ✅ `submissions` (POST) - заявки

### 3. **Legacy эндпоинты:**
- ✅ `leads`, `contacts`, `customers` (POST)
- ✅ `api/leads`, `api/contacts` (POST)

## 🔧 **HTTP методы:**

### GET запросы:
- `status`, `health`, `info`, `ping`, `test`

### PUT запросы:
- `leads/add`, `contacts/add`, `customers/add`, `graph/leads`

### POST запросы:
- Все остальные эндпоинты

## 📊 **Адаптация формата данных:**

### Формат для `graph/leads` и `leads/add`:
```json
{
  "title": "Иван Петров",
  "value": 2500.00,
  "contactId": "<EMAIL>",
  "source": "WordPress API"
}
```

### Формат для `crm/objects/contact`:
```json
{
  "fields": {
    "name": "Иван Петров",
    "email": "<EMAIL>",
    "phone": "+7(900)123-45-67",
    "city": "Москва",
    "country": "RU"
  }
}
```

### Формат для `capture-data`:
```json
{
  "Name": "Иван Петров",
  "Email": "<EMAIL>",
  "Phone": "+7(900)123-45-67",
  "Source": "WordPress"
}
```

### Стандартный формат TableCRM:
```json
{
  "name": "Иван Петров",
  "email": "<EMAIL>",
  "phone": "+7(900)123-45-67",
  "order_id": "12345",
  "order_total": "2500.00",
  "source": "WooCommerce Order"
}
```

## 🚀 **Что изменилось:**

### 1. **Расширен список эндпоинтов:**
- Добавлено 15+ новых эндпоинтов
- Приоритет отдан официальным эндпоинтам TableCRM

### 2. **Поддержка PUT запросов:**
- Автоматическое определение HTTP метода
- Правильные PUT запросы для создания лидов

### 3. **Адаптивный формат данных:**
- Автоматическая адаптация под разные API форматы
- Поддержка 3Dolphins, Acquire, Simbla стилей

### 4. **Улучшенное логирование:**
- Подробные логи для каждого эндпоинта
- Информация о методе HTTP и формате данных

## 📈 **Увеличенные шансы успеха:**

### До обновления:
- 18 эндпоинтов
- Только POST/GET методы
- 1 формат данных

### После обновления:
- 25+ эндпоинтов  
- POST/GET/PUT методы
- 4 формата данных
- Адаптация под разные CRM системы

## 🎯 **Следующие шаги:**

### 1. **Тестирование соединения:**
Нажмите "Тестировать соединение" в настройках плагина

### 2. **Создание тестового заказа:**
Оформите тестовый заказ на сайте для проверки

### 3. **Анализ логов:**
После тестового заказа проверьте логи WordPress

### 4. **Результат тестирования:**
Сообщите какой эндпоинт сработал или какие ошибки возникли

## 🔍 **Диагностика:**

В логах WordPress будут записи вида:
```
[TableCRM Integration] Попытка соединения с эндпоинтом: https://app.tablecrm.com/api/v1/leads/add
[TableCRM Integration] Эндпоинт: leads/add, Вариант 1 - Status: 200 - Response: {"success": true}
```

Или в случае ошибки:
```
[TableCRM Integration] Эндпоинт: leads/add, Вариант 1 - Status: 404 - Response: {"error": "Not Found"}
```

## 🎉 **Результат:**

**Плагин теперь пробует 25+ различных комбинаций эндпоинтов, HTTP методов и форматов данных для максимальной совместимости с TableCRM API!**

После тестирования мы точно узнаем, какой эндпоинт работает для вашего TableCRM аккаунта. 🔧 