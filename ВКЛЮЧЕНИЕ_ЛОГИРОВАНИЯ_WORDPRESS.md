# 🔧 ВКЛЮЧЕНИЕ ЛОГИРОВАНИЯ В WORDPRESS

## 📋 **Проблема: Логи не создаются**

Если файл `debug.log` не появляется в папке `wp-content/`, значит логирование отключено в WordPress.

---

## 🚀 **РУЧНОЕ ВКЛЮЧЕНИЕ ЛОГИРОВАНИЯ (РЕКОМЕНДУЕТСЯ)**

### 1️⃣ **Подключение к серверу:**

```bash
sftp <EMAIL>
# Пароль: zl2Shj!e&6ng
```

### 2️⃣ **Скачивание wp-config.php:**

```bash
cd public_html
get wp-config.php
quit
```

### 3️⃣ **Редактирование wp-config.php:**

Откройте скачанный файл `wp-config.php` в текстовом редакторе и найдите строку:
```php
/* That's all, stop editing! Happy publishing. */
```

**ПЕРЕД** этой строкой добавьте следующий код:

```php
// === НАСТРОЙКИ ОТЛАДКИ TABLECRM ===
// Включение отладки WordPress
define('WP_DEBUG', true);

// Включение записи в файл логов
define('WP_DEBUG_LOG', true);

// Отключение показа ошибок на сайте (безопасность)
define('WP_DEBUG_DISPLAY', false);

// Скрытие ошибок от посетителей
@ini_set('display_errors', 0);

// Логирование ошибок базы данных
define('WP_DEBUG_LOG_DB', true);

// Логирование скриптов
define('SCRIPT_DEBUG', true);

// === КОНЕЦ НАСТРОЕК ОТЛАДКИ ===
```

### 4️⃣ **Загрузка обратно на сервер:**

```bash
sftp <EMAIL>
cd public_html
put wp-config.php
quit
```

---

## ✅ **ПРОВЕРКА РЕЗУЛЬТАТА**

### **1. Создайте тестовый заказ на сайте**
- Перейдите на mosbuketik.ru
- Добавьте товар в корзину
- Оформите заказ с тестовыми данными

### **2. Проверьте появление debug.log:**
```bash
sftp <EMAIL>
cd public_html/wp-content
ls -la debug.log
```

### **3. Скачайте и проверьте логи:**
```bash
get debug.log
quit

# Локально:
grep -i "tablecrm" debug.log | tail -20
```

---

## 📊 **ЧТО ДОЛЖНО ПОЯВИТЬСЯ В ЛОГАХ**

### ✅ **При активации плагина:**
```
[TableCRM Integration] [INFO] Плагин активирован
[TableCRM Integration] [INFO] Установлен API URL по умолчанию
[TableCRM Integration] [INFO] Включена отправка заказов WooCommerce
[TableCRM Integration] [SUCCESS] Плагин успешно активирован и настроен
```

### ✅ **При создании заказа:**
```
[TableCRM Integration] [INFO] === НАЧАЛО ОТПРАВКИ ЗАКАЗА 12345 В TABLECRM ===
[TableCRM Integration] [INFO] Заказ 12345 найден. Статус: pending
[TableCRM Integration] [SUCCESS] Статус заказа wc-pending разрешен для отправки
[TableCRM Integration] [INFO] Подготовлены данные заказа 12345
[TableCRM Integration] [INFO] Отправляем заказ 12345 в TableCRM API...
[TableCRM Integration] [SUCCESS] Заказ 12345 успешно отправлен в TableCRM. Lead ID: abc123
```

### ❌ **При ошибках:**
```
[TableCRM Integration] [ERROR] Не настроены обязательные параметры API (URL и Key)
[TableCRM Integration] [WARNING] Заказ 12345 не отправлен - статус wc-cancelled не выбран
[TableCRM Integration] [ERROR] Ошибка отправки заказа 12345 в TableCRM: HTTP 404
```

---

## 🔧 **НАСТРОЙКИ ПЛАГИНА TABLECRM**

### **Обязательно проверьте в админ-панели WordPress:**

**Путь:** `WordPress Admin → Настройки → TableCRM Integration`

1. ✅ **API URL:** `https://app.tablecrm.com/api/v1/`
2. ✅ **API Key:** `af1874616430e04cfd4bce30035789907e899fc7c3a1a4bb27254828ff304a77`
3. ✅ **Отправлять заказы WooCommerce:** Включено
4. ✅ **Режим отладки:** Включено
5. ✅ **Статусы заказов:** Выбрать нужные (pending, processing, completed)

---

## 🚨 **ТИПИЧНЫЕ ПРОБЛЕМЫ**

### **1. debug.log не появляется после настройки**
- ✅ **Решение:** Перезагрузите сайт, создайте тестовый заказ
- ✅ **Проверка:** Убедитесь что wp-config.php сохранен правильно

### **2. Логи есть, но нет записей TableCRM**
- ❌ **Причина:** Плагин неактивен
- ✅ **Решение:** Активируйте плагин в разделе "Плагины"

### **3. Ошибка "headers already sent"**
- ❌ **Причина:** Лишние пробелы в wp-config.php
- ✅ **Решение:** Проверьте отсутствие пробелов до `<?php` и после `?>`

### **4. Плагин активен, но не отправляет заказы**
- ❌ **Причина:** Неверные настройки API или статусы заказов
- ✅ **Решение:** Проверьте настройки, нажмите "Тестировать соединение"

---

## 📞 **АЛЬТЕРНАТИВНЫЕ СПОСОБЫ**

### **1. Через файловый менеджер хостинга:**
- Войдите в панель управления bekflom3.beget.tech
- Откройте файловый менеджер
- Отредактируйте `/public_html/wp-config.php`

### **2. Через WordPress плагин:**
- Установите плагин "WP Debugging" или "Query Monitor"
- Включите отладку через админ-панель

### **3. Через .htaccess (не рекомендуется):**
```apache
php_flag display_errors Off
php_flag log_errors On
php_value error_log /home/<USER>/to/public_html/wp-content/debug.log
```

---

## 🎯 **БЫСТРАЯ ПРОВЕРКА (3 МИНУТЫ)**

1. **Скачать wp-config.php** → Добавить настройки отладки → Загрузить обратно
2. **Создать тестовый заказ** на сайте
3. **Проверить debug.log** в wp-content/
4. **Найти записи TableCRM** в логах

---

## ✅ **АВТОМАТИЧЕСКИЙ СКРИПТ**

Создан скрипт: `enable_wp_debug.sh`
- Автоматически скачивает wp-config.php
- Добавляет настройки отладки
- Загружает обновленный файл

**Использование:**
```bash
chmod +x enable_wp_debug.sh
./enable_wp_debug.sh
```

**⚠️ Примечание:** Скрипт может не работать из-за проблем с SFTP соединением. В этом случае используйте ручную настройку.

---

## 🎉 **РЕЗУЛЬТАТ**

После включения логирования вы получите:
- ✅ Подробные логи работы плагина TableCRM
- ✅ Информацию о каждом отправленном заказе
- ✅ Диагностику ошибок API
- ✅ Отслеживание активности плагина

**🎯 Логирование - ключ к диагностике проблем с интеграцией!** 