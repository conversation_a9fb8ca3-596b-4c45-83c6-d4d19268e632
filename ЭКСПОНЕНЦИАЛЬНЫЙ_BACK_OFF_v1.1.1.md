# 🚀 TableCRM Integration v1.1.1 - Экспоненциальный Back-off

**Дата релиза:** 6 января 2025  
**Версия:** 1.1.1  
**Критическое обновление:** Да

## 📝 Описание проблемы

При неудаче `make_api_request()` делался мгновенный повтор, что приводило к:
- Action Scheduler получал Exception и "заваливал" очередь
- Сетевые ошибки не обрабатывались корректно
- Временные сбои API приводили к отказу без попыток восстановления

## ✅ Решение

### 1. **Экспоненциальный Back-off**
```php
// Конфигурация повторов с экспоненциальным back-off
$max_attempts = 3;
$backoff_delays = array(0, 30, 120, 300); // задержки для 1-й, 2-й, 3-й попыток
```

**Схема повторов:**
- 🔄 **1-я попытка**: мгновенно
- ⏰ **2-я попытка**: через 30 секунд
- ⏰ **3-я попытка**: через 120 секунд (2 минуты)
- ❌ **Финальная неудача**: через 300 секунд (5 минут)

### 2. **Умная обработка ошибок**

#### Повторяемые ошибки (retry):
- ❌ `500, 502, 503, 504` - серверные ошибки
- ❌ `429` - превышение лимита запросов
- ❌ `http_request_failed, resolve_host, connect_error, timeout` - сетевые ошибки

#### Неповторяемые ошибки (fail fast):
- ❌ `401, 403` - проблемы авторизации
- ❌ `404` - неверный эндпоинт
- ❌ `400` - некорректные данные

### 3. **Защита Action Scheduler**

#### ❌ Раньше:
```php
// При неудаче генерировалось исключение
throw new Exception("API failed");
// Action Scheduler ставил задачу обратно в очередь
```

#### ✅ Теперь:
```php
// Все повторы обрабатываются внутри make_api_request()
// Action Scheduler получает финальный результат
public function handle_async_order($order_id) {
    try {
        $result = $this->send_order_to_tablecrm($order_id);
        // Не генерируем исключения для Action Scheduler
    } catch (Exception $e) {
        $this->log_error("Критическая ошибка: " . $e->getMessage());
        // Не пробрасываем исключение дальше
    }
}
```

## 🔧 Технические детали

### Изменения в `make_api_request()`

```php
private function make_api_request($initial_endpoint, $data = array(), 
    $api_url = null, $api_key = null, $project_id = null, $attempt = 1) {
    
    // Если это повторная попытка, применяем задержку
    if ($attempt > 1 && isset($backoff_delays[$attempt - 1])) {
        $delay = $backoff_delays[$attempt - 1];
        $this->log_warning("Повторная попытка $attempt после задержки $delay секунд");
        sleep($delay);
    }
    
    // ... выполняем запрос ...
    
    // Если сетевая ошибка и не достигли лимита попыток
    if ($is_network_error && $attempt < $max_attempts) {
        return $this->make_api_request($initial_endpoint, $data, 
            $api_url, $api_key, $project_id, $attempt + 1);
    }
}
```

### Изменения в обработчиках

#### `handle_async_order()`:
- ✅ Обернут в try-catch для предотвращения исключений
- ✅ Возвращает статус вместо генерации ошибок
- ✅ Логирует результат без воздействия на Action Scheduler

#### `send_order_to_tablecrm()`:
- ✅ Возвращает `true/false` вместо `void`
- ✅ Дифференцирует ошибки API от логических пропусков

## 📊 Логирование

### Новые типы логов:

```log
[INFO] API запрос к TableCRM (попытка 1/3)
[WARNING] Повторная попытка 2 после задержки 30 секунд
[ERROR] WP Error для эндпоинта docs_sales/: timeout
[WARNING] Сетевая ошибка или временная ошибка сервера. Планируется повтор (2/3)
[SUCCESS] ✅ Успешная авторизация и запрос! Эндпоинт: docs_sales/ (попытка 3)
[ERROR] Все варианты API запросов не сработали после 3 попыток
```

## 🎯 Преимущества

### 1. **Надежность**
- 🔄 Автоматическое восстановление при временных сбоях
- ⏰ Разумные задержки предотвращают перегрузку API
- 🛡️ Защита от rate limiting

### 2. **Производительность Action Scheduler**
- ❌ Нет "завала" очереди ложными ошибками
- ✅ Финальный статус задачи корректен
- 🚫 Исключения не пробрасываются наверх

### 3. **Мониторинг**
- 📊 Детальное логирование каждой попытки
- 🔍 Различение типов ошибок
- 📈 Отслеживание эффективности повторов

## 🔄 Обратная совместимость

- ✅ Все существующие вызовы `make_api_request()` работают без изменений
- ✅ Параметр `$attempt` имеет значение по умолчанию `1`
- ✅ Поведение для успешных запросов не изменилось

## 🚀 Установка

### Ручная установка:
1. Загрузите `tablecrm-integration-hpos-v1.1.1-backoff.zip`
2. Деактивируйте старую версию плагина
3. Удалите старые файлы плагина
4. Загрузите новую версию через WordPress Admin

### Автоматическое обновление:
```bash
# Через WP-CLI
wp plugin update tablecrm-integration --version=1.1.1
```

## 📋 Тестирование

### Проверка back-off:
1. Временно заблокируйте доступ к TableCRM API
2. Создайте тестовый заказ
3. Проверьте логи на наличие повторных попыток с задержками

### Проверка Action Scheduler:
1. Откройте WooCommerce → Status → Scheduled Actions
2. Убедитесь, что задачи не накапливаются в статусе "pending"
3. Проверьте, что неудачные задачи помечаются как "failed" (не "pending")

## ⚠️ Важные замечания

### Время выполнения:
- Максимальное время одного запроса: ~8 минут (30+120+300 сек + сетевые таймауты)
- Обычное время: 30-60 секунд для успешных запросов

### Конфигурация сервера:
```php
// Убедитесь, что PHP не прерывает долгие скрипты
ini_set('max_execution_time', 600); // 10 минут
ini_set('memory_limit', '256M');
```

### WordPress настройки:
```php
// wp-config.php
define('WP_CRON_LOCK_TIMEOUT', 300); // 5 минут для Action Scheduler
```

## 🎯 Результат

### До обновления:
- ❌ Мгновенные повторы при сбоях API
- ❌ Action Scheduler "заваливался" исключениями
- ❌ Временные сбои приводили к полному отказу

### После обновления:
- ✅ Умные повторы с экспоненциальным back-off
- ✅ Action Scheduler работает стабильно
- ✅ Временные сбои автоматически восстанавливаются
- ✅ Детальное логирование для диагностики

---

**💡 Рекомендация:** Версия 1.1.1 критически важна для стабильной работы в продакшене, особенно при высокой нагрузке или нестабильном соединении с TableCRM API. 