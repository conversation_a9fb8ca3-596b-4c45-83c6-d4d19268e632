<!-- Generated by SpecStory -->

# Заказы не доходят до црм (2025-06-20 08:59:25)

_**User**_

@tablecrm-integration-complete-v1.0.24.php  заказы перестали доходит до црм 

---

_**Assistant**_



---

Ниже приведён алгоритм быстрой диагностики, который обычно позволяет найти причину, по которой заказы перестают доходить до TableCRM. Все шаги выполняются прямо на прод-сайте; ничего «ломать» не будем.

1. Проверьте логи плагина  
   • Логи пишутся в `wp-content/uploads/tablecrm-logs/tablecrm_integration_complete.log`.  
   • Откройте последние 20-30 строк и посмотрите, есть ли там:  
     – `[ERROR]` или `[WARNING]` с указанием причины;  
     – сообщения «Circuit Breaker открыт…», «Превышен лимит частоты запросов», «Ошибка отправки заказа … HTTP 4xx / 5xx».  
   • Если лог пустой — значит код до отправки не доходит (чаще всего отключена отправка заказов или Action Scheduler не выполняет задачу).

2. Убедитесь, что отправка заказов включена  
   Админка → Настройки → «TableCRM Complete»  
   • «Отправлять заказы» должен быть отмечен.  
   • В «Статусы заказов для отправки» выделены те статусы, которые реально получает заказ после оформления (чаще всего `processing`, `completed`).  
   • Если statuses совпадают, но при оформлении заказ остаётся, например, в `pending` → он и не будет отправлен.

3. Проверьте Action Scheduler  
   WooCommerce → Статус → Запланированные действия  
   • В поле «Поиск по-хуку» введите `tablecrm_complete_async_send` и смотрите, нет ли действий со статусом «Failed»/«Запланировано, но не выполнено».  
   • Если задач много и они «failed» → нажмите «Запустить» повторно и следите за логом.  
   • Если задачи вообще не создаются — значит сработал Circuit Breaker или отправка отключена пунктами 1-2.

4. Сбросьте Circuit Breaker (если открылся)  
   • В БД `wp_options` удалите транзиенты `tablecrm_cb_state`, `tablecrm_cb_failures`, `tablecrm_cb_open_timestamp` (или выполните `wp transient delete --all | grep tablecrm_cb_`).  
   • Либо включите WP-CLI и пропишите:  
     `wp option delete _transient_tablecrm_cb_state` и т. д.  
   После удаления сделайте тест-запрос (кнопка «Тестировать соединение») — если API ответит 200, плагин автоматически закроет «предохранитель».

5. Проверьте соединение с CRM  
   На странице настроек нажмите «Тестировать соединение».  
   • Если сообщение «✅ Соединение успешно!» — API-ключ и URL верны.  
   • Если ошибка 401/403 → неверный токен.  
   • Если 5xx или timeout → проблема на стороне CRM, сработает Circuit Breaker, см. шаг 4.

6. Посмотрите, нет ли лимита частоты  
   В логе ищите «API запрос к … заблокирован лимитом частоты». Если есть — за минуту было > 30 запросов. Лечение: увеличить время «retry_delay» или снизить нагрузку.

7. Типовые причины, не требующие кода  
   • Изменён API-токен в TableCRM → поменять в `wp-config.php` или в настройках плагина.  
   • Перевели сайт на HTTPS без обновления `tablecrm_complete_api_url`.  
   • Новый плагин кеширования/файрвол заблокировал внешние POST-запросы.

Что делать дальше  
– Если в логе чётко видно ошибку API (например, 422 с деталями) — пришлите её, подскажем, что поправить в данных.  
– Если задач Action Scheduler «failed» → нажмите «Run» и сразу посмотрите лог — появится точная причина.  
– После исправления причин запустите ручную переотправку (Страница «Инструменты → TableCRM Resend Order») или из списка заказов нажмите «Отправить в TableCRM».

Следуя этим шагам, в 90 % случаев удаётся восстановить отправку без правки кода. Если после проверки всех пунктов лог продолжает молчать или выдаёт непонятную ошибку — пришлите последние строки лога, разберём детально.

---

