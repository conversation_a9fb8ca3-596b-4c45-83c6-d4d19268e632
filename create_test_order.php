<?php
// Create a new test order with unique phone
echo "=== СОЗДАНИЕ ТЕСТОВОГО ЗАКАЗА С УНИКАЛЬНЫМ ТЕЛЕФОНОМ ===\n";

$wp_path = '/home/<USER>/bekflom3/mosbuketik.ru/public_html';
require_once($wp_path . '/wp-config.php');
require_once($wp_path . '/wp-load.php');

echo "WordPress загружен успешно\n\n";

// Create a new order
$order = wc_create_order();

// Set customer data with unique phone
$unique_phone = '+7916' . rand(1000000, 9999999); // Generate unique phone
$order->set_billing_first_name('Иван');
$order->set_billing_last_name('Петров');
$order->set_billing_phone($unique_phone);
$order->set_billing_email('<EMAIL>');

echo "Клиент: Ива<PERSON> Петров\n";
echo "Телефон: $unique_phone\n";
echo "Email: <EMAIL>\n\n";

// Add a product (find any product)
$products = wc_get_products(array('limit' => 1));
if (!empty($products)) {
    $product = $products[0];
    $order->add_product($product, 1);
    echo "Добавлен товар: " . $product->get_name() . " (ID: " . $product->get_id() . ")\n";
} else {
    echo "❌ Товары не найдены\n";
    exit;
}

// Add shipping
$shipping_item = new WC_Order_Item_Shipping();
$shipping_item->set_method_title('Тестовая доставка');
$shipping_item->set_method_id('flat_rate');
$shipping_item->set_total(500);
$order->add_item($shipping_item);

// Set order status and save
$order->set_status('processing');
$order->calculate_totals();
$order->save();

$order_id = $order->get_id();
echo "Создан заказ #$order_id\n";
echo "Общая сумма: " . $order->get_total() . " руб.\n";
echo "Доставка: " . $order->get_shipping_total() . " руб.\n\n";

// Send to TableCRM
global $tablecrm_integration_complete;
if (isset($tablecrm_integration_complete)) {
    echo "Отправляем заказ в TableCRM...\n";
    
    $result = $tablecrm_integration_complete->send_order_to_tablecrm($order_id);
    
    if ($result) {
        echo "✅ ЗАКАЗ ОТПРАВЛЕН УСПЕШНО!\n";
        
        $doc_id = get_post_meta($order_id, '_tablecrm_complete_document_id', true);
        if ($doc_id) {
            echo "Document ID: $doc_id\n";
        }
    } else {
        echo "❌ ОШИБКА ОТПРАВКИ ЗАКАЗА\n";
    }
} else {
    echo "❌ Плагин не найден\n";
}

echo "\n=== ТЕСТ ЗАВЕРШЕН ===\n";
?> 