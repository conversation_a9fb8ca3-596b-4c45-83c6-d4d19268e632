# 🔄 ОБНОВЛЕНИЕ API TABLECRM - ПРАВИЛЬНЫЕ ЭНДПОИНТЫ

## ✅ ПОЛУЧЕНА АКТУАЛЬНАЯ ИНФОРМАЦИЯ ОТ РУШАНА

### 📋 ПРАВИЛЬНЫЕ ЭНДПОИНТЫ TABLECRM API

#### 1. СОЗДАНИЕ ЗАКАЗА (ДОКУМЕНТА ПРОДАЖИ)
```
POST https://app.tablecrm.com/api/v1/docs_sales/?token={API_TOKEN}
```

**Пример данных для отправки:**
```json
[{
    "dated": 1749020764,
    "operation": "Заказ",
    "comment": "текст",
    "tax_included": true,
    "tax_active": true,
    "goods": [{
        "price": 500,
        "quantity": 1,
        "unit": 116,
        "discount": 0,
        "sum_discounted": 0,
        "nomenclature": 39697
    }],
    "settings": {},
    "warehouse": 39,
    "contragent": 330985,
    "paybox": 218,
    "organization": 38,
    "status": false,
    "paid_rubles": "500.00",
    "paid_lt": 0
}]
```

#### 2. ПОИСК НОМЕНКЛАТУРЫ (ТОВАРОВ)
```
GET https://app.tablecrm.com/api/v1/nomenclature/?token={API_TOKEN}&name={НАЗВАНИЕ}&limit=100&offset=0&with_prices=false&with_balance=false&with_attributes=false&only_main_from_group=false
```

**Пример поиска товара "хлеб":**
```
https://app.tablecrm.com/api/v1/nomenclature/?token=af1874616430e04cfd4bce30035789907e899fc7c3a1a4bb27254828ff304a77&name=хлеб&limit=100&offset=0&with_prices=false&with_balance=false&with_attributes=false&only_main_from_group=false
```

#### 3. СОЗДАНИЕ НОМЕНКЛАТУРЫ (ЕСЛИ НЕ НАЙДЕНА)
```
POST https://app.tablecrm.com/api/v1/nomenclatures/
```

#### 4. ПОИСК КОНТРАГЕНТА (КЛИЕНТА)
```
GET https://app.tablecrm.com/api/v1/contragents/?token={API_TOKEN}&limit=100&offset=0&sort=created_at:desc&phone={ТЕЛЕФОН}
```

**Пример поиска клиента по телефону:**
```
https://app.tablecrm.com/api/v1/contragents/?token=af1874616430e04cfd4bce30035789907e899fc7c3a1a4bb27254828ff304a77&limit=100&offset=0&sort=created_at%3Adesc&phone=79377799906
```

#### 5. ДОКУМЕНТАЦИЯ API
```
https://app.tablecrm.com/api/v1/docs
```

## 🔧 ИЗМЕНЕНИЯ В ПЛАГИНЕ

### 1. ОБНОВИТЬ БАЗОВЫЙ URL
**Старый (неработающий):**
```php
$api_url = 'https://app.tablecrm.com/api/v1/';
```

**Новый (правильный):**
```php
$api_url = 'https://app.tablecrm.com/api/v1/';
```
*URL остается тот же, но эндпоинты нужно изменить*

### 2. ОБНОВИТЬ ЭНДПОИНТЫ

**Вместо старых эндпоинтов:**
- `leads/add` ❌
- `leads/check-duplicate` ❌  
- `crm/objects/contact` ❌

**Использовать новые:**
- `docs_sales/` ✅ (для создания заказов)
- `contragents/` ✅ (для поиска/создания клиентов)
- `nomenclature/` ✅ (для поиска товаров)
- `nomenclatures/` ✅ (для создания товаров)

### 3. ИЗМЕНИТЬ МЕТОД АУТЕНТИФИКАЦИИ

**Старый способ (в заголовках):**
```php
'Authorization: Bearer ' . $api_key
```

**Новый способ (в URL):**
```php
$url = $base_url . $endpoint . '?token=' . $api_key;
```

## 🛠️ ПЛАН ИСПРАВЛЕНИЯ ПЛАГИНА

### Шаг 1: Обновить функцию отправки заказа
```php
private function send_order_to_tablecrm($order_data) {
    $api_url = 'https://app.tablecrm.com/api/v1/docs_sales/?token=' . $this->api_key;
    
    // Подготовить данные в формате TableCRM
    $tablecrm_data = $this->prepare_tablecrm_order_data($order_data);
    
    // Отправить POST запрос
    return $this->make_api_request($api_url, $tablecrm_data, 'POST');
}
```

### Шаг 2: Обновить поиск контрагентов
```php
private function find_or_create_contragent($phone, $email, $name) {
    $search_url = 'https://app.tablecrm.com/api/v1/contragents/?token=' . $this->api_key . 
                  '&phone=' . urlencode($phone) . '&limit=100&offset=0';
    
    $response = $this->make_api_request($search_url, [], 'GET');
    
    // Если не найден - создать нового
    if (empty($response['results'])) {
        return $this->create_contragent($phone, $email, $name);
    }
    
    return $response['results'][0]['id'];
}
```

### Шаг 3: Обновить поиск номенклатуры
```php
private function find_or_create_nomenclature($product_name) {
    $search_url = 'https://app.tablecrm.com/api/v1/nomenclature/?token=' . $this->api_key . 
                  '&name=' . urlencode($product_name) . '&limit=100&offset=0';
    
    $response = $this->make_api_request($search_url, [], 'GET');
    
    // Если не найден - создать новый
    if (empty($response['results'])) {
        return $this->create_nomenclature($product_name);
    }
    
    return $response['results'][0]['id'];
}
```

## 🚀 СЛЕДУЮЩИЕ ДЕЙСТВИЯ

1. **Обновить плагин** с новыми эндпоинтами
2. **Изменить метод аутентификации** (token в URL вместо заголовков)
3. **Адаптировать формат данных** под требования TableCRM
4. **Протестировать** отправку заказа
5. **Проверить логи** на наличие ошибок

## ⚠️ ВАЖНЫЕ ПАРАМЕТРЫ

- **API Token:** `af1874616430e04cfd4bce30035789907e899fc7c3a1a4bb27254828ff304a77`
- **Organization ID:** `38`
- **Warehouse ID:** `39`
- **Paybox ID:** `218`
- **Unit ID:** `116`

## 🎯 РЕЗУЛЬТАТ

После обновления плагин должен:
1. ✅ Успешно находить/создавать контрагентов
2. ✅ Успешно находить/создавать номенклатуру
3. ✅ Успешно отправлять заказы в TableCRM
4. ✅ Получать корректные ответы от API (не 404) 