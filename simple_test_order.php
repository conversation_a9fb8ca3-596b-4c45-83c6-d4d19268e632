<?php
// Простой скрипт для создания тестового заказа WooCommerce

// Подключение к WordPress
require_once('/home/<USER>/bekflom3/mosbuketik.ru/public_html/wp-config.php');
require_once('/home/<USER>/bekflom3/mosbuketik.ru/public_html/wp-load.php');

echo "🛒 СОЗДАНИЕ ПРОСТОГО ТЕСТОВОГО ЗАКАЗА\n";
echo "=====================================\n\n";

// Проверяем WooCommerce
if (!class_exists('WooCommerce')) {
    echo "❌ WooCommerce не найден\n";
    exit(1);
}

echo "✅ WooCommerce найден\n";

// Генерируем случайные данные
$random_id = mt_rand(1000, 9999);
$test_email = "test{$random_id}@example.com";
$test_phone = "+7 (999) 123-{$random_id}";

echo "📝 Тестовые данные:\n";
echo "   Email: $test_email\n";
echo "   Телефон: $test_phone\n\n";

// Создаем заказ
$order = wc_create_order();

if (!$order) {
    echo "❌ Не удалось создать заказ\n";
    exit(1);
}

$order_id = $order->get_id();
echo "✅ Заказ создан с ID: $order_id\n";

// Устанавливаем минимальные данные
$order->set_billing_first_name('Тестовый');
$order->set_billing_last_name("Покупатель $random_id");
$order->set_billing_email($test_email);
$order->set_billing_phone($test_phone);

// Получаем первый товар
$products = wc_get_products(array('limit' => 1, 'status' => 'publish'));
if (!empty($products)) {
    $product = $products[0];
    $order->add_product($product, 1);
    echo "✅ Добавлен товар: " . $product->get_name() . "\n";
}

// Устанавливаем способ оплаты
$order->set_payment_method('cod');
$order->set_payment_method_title('Наложенный платеж');

// Сохраняем заказ
$order->calculate_totals();
$order->save();

echo "✅ Заказ сохранен\n";

// Устанавливаем статус pending (это должно триггернуть отправку в TableCRM)
$order->update_status('pending', 'Тестовый заказ для проверки логирования');

echo "✅ Статус установлен: pending\n";
echo "\n🎯 ЗАКАЗ ГОТОВ!\n";
echo "ID: $order_id\n";
echo "Email: $test_email\n";
echo "Телефон: $test_phone\n";

echo "\n⏳ Ждем 3 секунды для обработки хуков...\n";
sleep(3);

echo "\n📋 ПРОВЕРЬТЕ:\n";
echo "1. Админ-панель: https://mosbuketik.ru/wp-admin/edit.php?post_type=shop_order\n";
echo "2. Логи: wp-content/debug.log\n";
echo "3. TableCRM: https://app.tablecrm.com\n";

exit(0);
?> 