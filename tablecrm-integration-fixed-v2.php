<?php
/**
 * Plugin Name: TableCRM Integration (Fixed v2)
 * Description: Интеграция WordPress/WooCommerce с TableCRM для отправки заявок через API - полностью исправленная версия с валидацией данных
 * Version: 1.0.3
 * Author: Your Name
 * Text Domain: tablecrm-integration-fixed
 */

// Предотвращение прямого доступа
if (!defined('ABSPATH')) {
    exit;
}

// Определение констант плагина
define('TABLECRM_FIXED_PLUGIN_URL', plugin_dir_url(__FILE__));
define('TABLECRM_FIXED_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('TABLECRM_FIXED_VERSION', '1.0.3');

class TableCRM_Integration_Fixed {
    
    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'register_settings'));
        
        // Хуки для WooCommerce - ИСПРАВЛЕННЫЕ
        add_action('woocommerce_new_order', array($this, 'handle_new_order'), 20); // Приоритет 20 для получения полных данных
        add_action('woocommerce_order_status_completed', array($this, 'update_order_in_tablecrm'));
        add_action('woocommerce_order_status_processing', array($this, 'update_order_in_tablecrm'));
        add_action('woocommerce_order_status_cancelled', array($this, 'update_order_in_tablecrm'));
        add_action('woocommerce_order_status_refunded', array($this, 'update_order_in_tablecrm'));
        
        // Хуки для Contact Form 7 (если установлен)
        add_action('wpcf7_mail_sent', array($this, 'send_cf7_to_tablecrm'));
        
        // AJAX хуки
        add_action('wp_ajax_test_tablecrm_connection_fixed', array($this, 'test_connection'));
        add_action('wp_ajax_nopriv_test_tablecrm_connection_fixed', array($this, 'test_connection'));
        
        // Хук для сохранения UTM данных
        add_action('woocommerce_checkout_create_order', array($this, 'save_utm_data_to_order'), 10, 2);
    }
    
    public function init() {
        load_plugin_textdomain('tablecrm-integration-fixed', false, dirname(plugin_basename(__FILE__)) . '/languages');
        
        // Добавляем скрипт для отслеживания UTM на фронтенде
        add_action('wp_enqueue_scripts', array($this, 'enqueue_utm_tracking_script'));
    }
    
    // Подключение скрипта отслеживания UTM
    public function enqueue_utm_tracking_script() {
        wp_enqueue_script('tablecrm-utm-tracking-fixed', TABLECRM_FIXED_PLUGIN_URL . 'utm-tracking.js', array('jquery'), TABLECRM_FIXED_VERSION, true);
    }
    
    // Добавление меню в админку
    public function add_admin_menu() {
        add_options_page(
            'TableCRM Integration Settings (Fixed v2)',
            'TableCRM Integration (Fixed v2)',
            'manage_options',
            'tablecrm-fixed-v2-settings',
            array($this, 'admin_page')
        );
    }
    
    // Регистрация настроек
    public function register_settings() {
        register_setting('tablecrm_fixed_settings', 'tablecrm_fixed_api_url');
        register_setting('tablecrm_fixed_settings', 'tablecrm_fixed_api_key');
        register_setting('tablecrm_fixed_settings', 'tablecrm_fixed_project_id');
        register_setting('tablecrm_fixed_settings', 'tablecrm_fixed_organization_id');
        register_setting('tablecrm_fixed_settings', 'tablecrm_fixed_cashbox_id');
        register_setting('tablecrm_fixed_settings', 'tablecrm_fixed_warehouse_id');
        register_setting('tablecrm_fixed_settings', 'tablecrm_fixed_unit_id');
        register_setting('tablecrm_fixed_settings', 'tablecrm_fixed_enable_orders');
        register_setting('tablecrm_fixed_settings', 'tablecrm_fixed_enable_cf7');
        register_setting('tablecrm_fixed_settings', 'tablecrm_fixed_check_duplicates');
        register_setting('tablecrm_fixed_settings', 'tablecrm_fixed_debug_mode');
        register_setting('tablecrm_fixed_settings', 'tablecrm_fixed_order_statuses');
        register_setting('tablecrm_fixed_settings', 'tablecrm_fixed_send_real_data_only');
    }
    
    // Страница настроек
    public function admin_page() {
        ?>
        <div class="wrap">
            <h1>TableCRM Integration Settings (Fixed v2 - v1.0.3)</h1>
            <div class="notice notice-info">
                <p><strong>🎯 Исправленная версия плагина с валидацией данных!</strong></p>
                <p>✅ Отправляются только реальные заказы с полными данными (имя, email, сумма > 0)</p>
                <p>✅ Нет лишних попыток подключения к несуществующим эндпоинтам</p>
                <p>✅ Прямая отправка в правильный эндпоинт docs_sales/</p>
            </div>
            <form method="post" action="options.php">
                <?php
                settings_fields('tablecrm_fixed_settings');
                do_settings_sections('tablecrm_fixed_settings');
                ?>
                <table class="form-table">
                    <tr>
                        <th scope="row">API URL</th>
                        <td>
                            <input type="url" name="tablecrm_fixed_api_url" value="<?php echo esc_attr(get_option('tablecrm_fixed_api_url', 'https://app.tablecrm.com/api/v1/')); ?>" class="regular-text" />
                            <p class="description">URL API TableCRM</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">API Key</th>
                        <td>
                            <input type="password" name="tablecrm_fixed_api_key" value="<?php echo esc_attr(get_option('tablecrm_fixed_api_key')); ?>" class="regular-text" />
                            <p class="description">Ваш API ключ TableCRM</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Organization ID</th>
                        <td>
                            <input type="number" name="tablecrm_fixed_organization_id" value="<?php echo esc_attr(get_option('tablecrm_fixed_organization_id', 38)); ?>" class="regular-text" />
                            <p class="description">ID организации в TableCRM</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Cashbox ID</th>
                        <td>
                            <input type="number" name="tablecrm_fixed_cashbox_id" value="<?php echo esc_attr(get_option('tablecrm_fixed_cashbox_id', 218)); ?>" class="regular-text" />
                            <p class="description">ID кассы в TableCRM</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Warehouse ID</th>
                        <td>
                            <input type="number" name="tablecrm_fixed_warehouse_id" value="<?php echo esc_attr(get_option('tablecrm_fixed_warehouse_id', 39)); ?>" class="regular-text" />
                            <p class="description">ID склада в TableCRM</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Unit ID</th>
                        <td>
                            <input type="number" name="tablecrm_fixed_unit_id" value="<?php echo esc_attr(get_option('tablecrm_fixed_unit_id', 116)); ?>" class="regular-text" />
                            <p class="description">ID единицы измерения в TableCRM</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row" style="background-color: #ffffcc;">Отправлять только реальные данные</th>
                        <td style="background-color: #ffffcc;">
                            <input type="checkbox" name="tablecrm_fixed_send_real_data_only" value="1" <?php checked(1, get_option('tablecrm_fixed_send_real_data_only', 1)); ?> />
                            <label><strong>ВКЛЮЧЕНО: Отправлять только заказы с полными данными (имя, email, сумма > 0)</strong></label>
                            <p class="description" style="color: red;">⚠️ Это предотвращает отправку пустых/тестовых заказов в CRM</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Отправлять заказы WooCommerce</th>
                        <td>
                            <input type="checkbox" name="tablecrm_fixed_enable_orders" value="1" <?php checked(1, get_option('tablecrm_fixed_enable_orders', 1)); ?> />
                            <label>Включить отправку заказов</label>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Режим отладки</th>
                        <td>
                            <input type="checkbox" name="tablecrm_fixed_debug_mode" value="1" <?php checked(1, get_option('tablecrm_fixed_debug_mode', 1)); ?> />
                            <label>Включить подробное логирование</label>
                        </td>
                    </tr>
                </table>
                
                <p class="submit">
                    <input type="submit" name="submit" id="submit" class="button button-primary" value="Сохранить настройки" />
                    <button type="button" id="test-connection-fixed" class="button">Тестировать соединение (Fixed)</button>
                </p>
            </form>
            
            <div id="test-result-fixed" style="margin-top: 20px;"></div>
            
            <script>
            jQuery(document).ready(function($) {
                $('#test-connection-fixed').click(function() {
                    var button = $(this);
                    var result = $('#test-result-fixed');
                    
                    button.prop('disabled', true).text('Тестируем...');
                    result.html('<p>Проверяем соединение с TableCRM API...</p>');
                    
                    $.ajax({
                        url: ajaxurl,
                        method: 'POST',
                        data: {
                            action: 'test_tablecrm_connection_fixed',
                            nonce: '<?php echo wp_create_nonce("test_tablecrm_fixed"); ?>'
                        },
                        success: function(response) {
                            if(response.success) {
                                result.html('<div class="notice notice-success"><p><strong>✅ Соединение успешно!</strong><br>' + response.data.message + '</p></div>');
                            } else {
                                result.html('<div class="notice notice-error"><p><strong>❌ Ошибка соединения:</strong><br>' + response.data.message + '</p></div>');
                            }
                        },
                        error: function() {
                            result.html('<div class="notice notice-error"><p><strong>❌ Ошибка AJAX запроса</strong></p></div>');
                        },
                        complete: function() {
                            button.prop('disabled', false).text('Тестировать соединение (Fixed)');
                        }
                    });
                });
            });
            </script>
        </div>
        <?php
    }
    
    // Тестирование соединения
    public function test_connection() {
        if (!wp_verify_nonce($_POST['nonce'], 'test_tablecrm_fixed')) {
            wp_die('Неверный nonce');
        }
        
        $api_url = get_option('tablecrm_fixed_api_url', 'https://app.tablecrm.com/api/v1/');
        $api_key = get_option('tablecrm_fixed_api_key');
        
        if (empty($api_key)) {
            wp_send_json_error(array('message' => 'API ключ не настроен'));
            return;
        }
        
        // Тестируем эндпоинт health
        $test_url = rtrim($api_url, '/') . '/health/?token=' . $api_key;
        
        $response = wp_remote_get($test_url, array(
            'timeout' => 15,
            'sslverify' => true
        ));
        
        if (is_wp_error($response)) {
            wp_send_json_error(array('message' => 'Ошибка подключения: ' . $response->get_error_message()));
            return;
        }
        
        $status_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        
        if ($status_code === 200) {
            wp_send_json_success(array('message' => 'API доступно! Статус: ' . $status_code . ', Ответ: ' . $body));
        } else {
            wp_send_json_error(array('message' => 'API недоступно. HTTP статус: ' . $status_code . ', Ответ: ' . substr($body, 0, 200)));
        }
    }
    
    // ИСПРАВЛЕННАЯ функция обработки нового заказа
    public function handle_new_order($order_id) {
        // Небольшая задержка для получения полных данных заказа
        wp_schedule_single_event(time() + 5, 'tablecrm_fixed_process_order', array($order_id));
        
        // Также попробуем обработать сразу
        $this->send_order_to_tablecrm($order_id);
    }
    
    // Отправка заказа WooCommerce в TableCRM (ПОЛНОСТЬЮ ИСПРАВЛЕННАЯ ВЕРСИЯ)
    public function send_order_to_tablecrm($order_id) {
        $this->log_info("=== НАЧАЛО ОТПРАВКИ ЗАКАЗА $order_id В TABLECRM (FIXED v2) ===");
        
        if (!get_option('tablecrm_fixed_enable_orders', 1)) {
            $this->log_warning("Отправка заказов отключена в настройках плагина");
            return;
        }
        
        $order = wc_get_order($order_id);
        if (!$order) {
            $this->log_error("Заказ $order_id не найден в WooCommerce");
            return;
        }

        $this->log_info("Заказ $order_id найден. Статус: " . $order->get_status());

        // Проверяем, был ли заказ уже отправлен ранее
        $existing_doc_id = get_post_meta($order_id, '_tablecrm_fixed_document_id', true);
        if (!empty($existing_doc_id)) {
            $this->log_info("Заказ $order_id уже обработан. Document ID: $existing_doc_id");
            return;
        }
        
        // КРИТИЧЕСКАЯ ПРОВЕРКА: Пропускаем черновики и незавершенные заказы
        if (in_array($order->get_status(), array('auto-draft', 'draft', 'trash'))) {
            $this->log_warning("Заказ $order_id пропущен - статус '" . $order->get_status() . "' (черновик)");
            return;
        }
        
        // Собираем данные заказа
        $billing_first_name = $order->get_billing_first_name();
        $billing_last_name = $order->get_billing_last_name();
        $billing_email = $order->get_billing_email();
        $billing_phone = $order->get_billing_phone();
        $order_total = $order->get_total();
        
        // Проверяем наличие телефона
        if (empty($billing_phone)) {
            $this->log_error("Заказ $order_id: отсутствует номер телефона клиента (billing_phone)");
            return;
        }
        
        // КРИТИЧЕСКАЯ ВАЛИДАЦИЯ ДАННЫХ
        $validation_errors = array();
        
        if (empty($billing_first_name) && empty($billing_last_name)) {
            $validation_errors[] = "Отсутствует имя клиента";
        }
        
        if (empty($billing_email)) {
            $validation_errors[] = "Отсутствует email клиента";
        }
        
        if ($order_total <= 0) {
            $validation_errors[] = "Сумма заказа равна нулю или отрицательная ($order_total)";
        }
        
        // Проверка настройки "отправлять только реальные данные"
        if (get_option('tablecrm_fixed_send_real_data_only', 1) && !empty($validation_errors)) {
            $this->log_error("Заказ $order_id НЕ ОТПРАВЛЕН - обнаружены ошибки валидации:");
            foreach ($validation_errors as $error) {
                $this->log_error("  - $error");
            }
            $this->log_info("=== ЗАВЕРШЕНИЕ ОТПРАВКИ ЗАКАЗА $order_id (ОШИБКА ВАЛИДАЦИИ) ===");
            return;
        }
        
        // Формируем полные данные заказа
        $data = array(
            // Основные данные
            'name' => trim($billing_first_name . ' ' . $billing_last_name),
            'email' => $billing_email,
            'phone' => $billing_phone,
            'shipping_phone' => $order->get_shipping_phone(),
            'billing_phone' => $order->get_billing_phone(),
            'order_id' => $order_id,
            'order_total' => $order_total,
            'order_status' => $order->get_status(),
            'order_date' => $order->get_date_created() ? $order->get_date_created()->format('Y-m-d H:i:s') : current_time('mysql'),
            'source' => 'WooCommerce Order (Fixed Plugin v2)',
            'payment_method' => $order->get_payment_method_title(),
            'payment_status' => $order->is_paid() ? 'paid' : 'unpaid',
            'domain' => isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : '',
            
            // SHIPPING ДАННЫЕ для информации о доставке
            'shipping_first_name' => $order->get_shipping_first_name(),
            'shipping_last_name' => $order->get_shipping_last_name(),
            'shipping_company' => $order->get_shipping_company(),
            'shipping_address_1' => $order->get_shipping_address_1(),
            'shipping_address_2' => $order->get_shipping_address_2(),
            'shipping_city' => $order->get_shipping_city(),
            'shipping_state' => $order->get_shipping_state(),
            'shipping_postcode' => $order->get_shipping_postcode(),
            'shipping_country' => $order->get_shipping_country(),
            
            // Billing данные (fallback)
            'billing_address_1' => $order->get_billing_address_1(),
            'billing_address_2' => $order->get_billing_address_2(),
            'billing_city' => $order->get_billing_city(),
            'billing_state' => $order->get_billing_state(),
            'billing_postcode' => $order->get_billing_postcode(),
            'billing_country' => $order->get_billing_country(),
            
            // Дата доставки из мета-полей заказа
            'delivery_date_source' => $order->get_meta('_delivery_date') ?: '',
            'delivery_time_source' => $order->get_meta('_delivery_time') ?: '',
            
            'items' => array()
        );
        
        // Добавляем товары
        foreach ($order->get_items() as $item_id => $item) {
            $product = $item->get_product();
            if ($product) {
                $data['items'][] = array(
                    'name' => $item->get_name(),
                    'quantity' => $item->get_quantity(),
                    'price' => $item->get_total(),
                    'unit_price' => ($item->get_quantity() > 0) ? ($item->get_total() / $item->get_quantity()) : 0,
                    'sku' => $product->get_sku() ?: '',
                    'product_id' => $product->get_id()
                );
            }
        }
        
        // ЛОГИРОВАНИЕ ПОДГОТОВЛЕННЫХ ДАННЫХ
        $this->log_info("Подготовлены ВАЛИДНЫЕ данные заказа $order_id:");
        $this->log_info("  - Имя: '" . $data['name'] . "'");
        $this->log_info("  - Email: '" . $data['email'] . "'");
        $this->log_info("  - Телефон: '" . $data['phone'] . "'");
        $this->log_info("  - Сумма заказа: " . $data['order_total']);
        $this->log_info("  - Количество товаров: " . count($data['items']));
        
        $this->log_info("Отправляем ВАЛИДНЫЙ заказ $order_id в TableCRM API (docs_sales/)...");
        $response = $this->make_api_request('docs_sales/', $data);
        
        if ($response['success']) {
            $document_id = $this->extract_document_id($response);
            
            if ($document_id !== 'unknown' && !empty($document_id)) {
                update_post_meta($order_id, '_tablecrm_fixed_document_id', $document_id);
                $this->log_success("Заказ $order_id успешно отправлен в TableCRM. Document ID: $document_id");
                
                // ИСПРАВЛЕННАЯ отправка информации о доставке
                $this->send_delivery_info($document_id, $data);
                
                $this->log_info("=== ЗАВЕРШЕНИЕ ОТПРАВКИ ЗАКАЗА $order_id (УСПЕШНО) ===");
            } else {
                $this->log_error("Заказ $order_id отправлен, но не удалось получить Document ID");
                $this->log_info("=== ЗАВЕРШЕНИЕ ОТПРАВКИ ЗАКАЗА $order_id (ОШИБКА ID) ===");
            }
        } else {
            $this->log_error("Ошибка отправки заказа $order_id в TableCRM: " . $response['message']);
            $this->log_info("=== ЗАВЕРШЕНИЕ ОТПРАВКИ ЗАКАЗА $order_id (ОШИБКА API) ===");
        }
    }
    
    // Извлечение Document ID из ответа API
    private function extract_document_id($response) {
        if (isset($response['document_id'])) {
            return $response['document_id'];
        } elseif (isset($response['lead_id'])) {
            return $response['lead_id'];
        } elseif (isset($response['data']) && is_array($response['data']) && !empty($response['data'][0]['id'])) {
            return $response['data'][0]['id'];
        }
        return 'unknown';
    }
    
    // ИСПРАВЛЕННАЯ функция отправки информации о доставке
    private function send_delivery_info($document_id, $data) {
        if (empty($document_id) || $document_id === 'unknown') {
            $this->log_debug("Отправка информации о доставке отменена: неверный document_id ($document_id).");
            return;
        }
        
        $delivery_payload = array();
        
        // 1. Формируем адрес доставки из shipping полей
        $address_parts = array();
        if (!empty($data['shipping_address_1'])) $address_parts[] = $data['shipping_address_1'];
        if (!empty($data['shipping_address_2'])) $address_parts[] = $data['shipping_address_2'];
        if (!empty($data['shipping_city'])) $address_parts[] = $data['shipping_city'];
        if (!empty($data['shipping_state'])) $address_parts[] = $data['shipping_state'];
        if (!empty($data['shipping_postcode'])) $address_parts[] = $data['shipping_postcode'];
        
        if (!empty($address_parts)) {
            $delivery_payload['address'] = trim(implode(', ', $address_parts));
        } else {
            // Fallback к billing адресу если shipping пустой
            $billing_parts = array();
            if (!empty($data['billing_address_1'])) $billing_parts[] = $data['billing_address_1'];
            if (!empty($data['billing_address_2'])) $billing_parts[] = $data['billing_address_2'];
            if (!empty($data['billing_city'])) $billing_parts[] = $data['billing_city'];
            
            if (!empty($billing_parts)) {
                $delivery_payload['address'] = trim(implode(', ', $billing_parts));
            }
        }
        
        // 2. Формируем получателя как JSON объект
        $recipient_info = array();
        
        // Используем только billing_phone как основной контактный номер
        $recipient_phone = $data['billing_phone'];
        
        // Используем shipping имя, если есть, иначе billing
        $recipient_name = !empty($data['shipping_first_name']) ? $data['shipping_first_name'] : '';
        $recipient_surname = !empty($data['shipping_last_name']) ? $data['shipping_last_name'] : '';
        
        if (empty($recipient_name) && !empty($data['name'])) {
            $name_parts = explode(' ', $data['name'], 2);
            $recipient_name = $name_parts[0] ?? '';
            $recipient_surname = $name_parts[1] ?? '';
        }
        
        if (!empty($recipient_name)) $recipient_info['name'] = $recipient_name;
        if (!empty($recipient_surname)) $recipient_info['surname'] = $recipient_surname;
        if (!empty($recipient_phone)) $recipient_info['phone'] = $recipient_phone;
        
        if (!empty($recipient_info)) {
            $delivery_payload['recipient'] = $recipient_info;
        }
        
        if (empty($delivery_payload)) {
            $this->log_debug("Нет данных для отправки информации о доставке документа $document_id");
            return;
        }
        
        $this->log_info("Отправляем информацию о доставке для документа $document_id: " . json_encode($delivery_payload, JSON_UNESCAPED_UNICODE));
        
        $api_url = get_option('tablecrm_fixed_api_url', 'https://app.tablecrm.com/api/v1/');
        $api_key = get_option('tablecrm_fixed_api_key');
        
        if (empty($api_key)) {
            $this->log_error("Не настроен API ключ для отправки информации о доставке");
            return;
        }
        
        $delivery_url = rtrim($api_url, '/') . '/docs_sales/' . $document_id . '/delivery_info/?token=' . $api_key;
        
        $response = wp_remote_post($delivery_url, array(
            'method' => 'POST',
            'headers' => array(
                'Content-Type' => 'application/json; charset=utf-8',
                'User-Agent' => 'WordPress-TableCRM-Integration-Fixed/1.0'
            ),
            'body' => json_encode($delivery_payload, JSON_UNESCAPED_UNICODE),
            'timeout' => 30,
            'sslverify' => true
        ));
        
        if (is_wp_error($response)) {
            $this->handle_wp_error("Ошибка отправки информации о доставке для документа $document_id", $response);
            return;
        }
        
        $status_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);
        
        if ($status_code >= 200 && $status_code < 300) {
            $this->log_success("Информация о доставке успешно отправлена для документа $document_id. Статус: $status_code");
        } else {
            $this->log_error("Ошибка отправки информации о доставке (HTTP $status_code) для документа $document_id: " . substr($response_body, 0, 500));
        }
    }
    
    // Базовая функция API запроса (ИСПРАВЛЕННАЯ - БЕЗ ЛИШНИХ ПОПЫТОК)
    private function make_api_request($endpoint, $data = array()) {
        $api_url = get_option('tablecrm_fixed_api_url', 'https://app.tablecrm.com/api/v1/');
        $api_key = get_option('tablecrm_fixed_api_key');
        
        if (empty($api_key)) {
            return array('success' => false, 'message' => 'API ключ не настроен');
        }
        
        // Адаптируем данные для эндпоинта
        $adapted_data = $this->adapt_data_format($endpoint, $data);
        
        $url = rtrim($api_url, '/') . '/' . $endpoint . '?token=' . $api_key;
        
        $this->log_debug("Отправка запроса к: $url");
        $this->log_debug("Данные: " . json_encode($adapted_data, JSON_UNESCAPED_UNICODE));
        
        $response = wp_remote_post($url, array(
            'method' => 'POST',
            'headers' => array(
                'Content-Type' => 'application/json; charset=utf-8',
                'User-Agent' => 'WordPress-TableCRM-Integration-Fixed/1.0'
            ),
            'body' => json_encode($adapted_data, JSON_UNESCAPED_UNICODE),
            'timeout' => 30,
            'sslverify' => true
        ));
        
        if (is_wp_error($response)) {
            $this->handle_wp_error('Ошибка WP_Error', $response);
            return array('success' => false, 'message' => $response->get_error_message());
        }
        
        $status_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        
        $this->log_debug("Ответ API: статус $status_code, тело: " . substr($body, 0, 500));
        
        if ($status_code >= 200 && $status_code < 300) {
            $decoded = json_decode($body, true);
            $this->log_success("✅ Успешный запрос к API! Эндпоинт: $endpoint");
            
            // Определяем ID документа из ответа
            $document_id = 'unknown';
            if (is_array($decoded) && !empty($decoded[0]['id'])) {
                $document_id = $decoded[0]['id'];
                $this->log_success("Создан документ продаж ID: $document_id");
            }
            
            return array(
                'success' => true, 
                'data' => $decoded,
                'document_id' => $document_id,
                'lead_id' => $document_id
            );
        } else {
            $this->log_error("Ошибка API (HTTP $status_code): " . substr($body, 0, 500));
            return array('success' => false, 'message' => "HTTP $status_code: " . $body);
        }
    }
    
    // Адаптация данных под формат TableCRM (ИСПРАВЛЕННАЯ ВЕРСИЯ)
    private function adapt_data_format($endpoint, $data) {
        if ($endpoint === 'docs_sales/') {
            $organization_id = get_option('tablecrm_fixed_organization_id', 38);
            $cashbox_id = get_option('tablecrm_fixed_cashbox_id', 218);
            $warehouse_id = get_option('tablecrm_fixed_warehouse_id', 39);
            $unit_id = get_option('tablecrm_fixed_unit_id', 116);
            
            // ИСПРАВЛЕНИЕ: Проверяем что есть товары, если нет - создаем общий товар
            $goods = array();
            if (isset($data['items']) && !empty($data['items'])) {
                foreach ($data['items'] as $item) {
                    $goods[] = array(
                        'price' => floatval($item['unit_price'] ?? 0),
                        'quantity' => intval($item['quantity'] ?? 1),
                        'unit' => intval($unit_id),
                        'discount' => 0,
                        'sum_discounted' => 0,
                        'nomenclature' => 39697 // Фиксированная номенклатура
                    );
                }
            } else {
                // Если нет товаров - создаем общий товар на всю сумму заказа
                $goods[] = array(
                    'price' => floatval($data['order_total'] ?? 500),
                    'quantity' => 1,
                    'unit' => intval($unit_id),
                    'discount' => 0,
                    'sum_discounted' => 0,
                    'nomenclature' => 39697
                );
            }
            
            $document_data = array(
                'dated' => time(),
                'operation' => 'Заказ с сайта (Fixed Plugin v2)',
                'comment' => $this->format_order_comment($data),
                'tax_included' => true,
                'tax_active' => true,
                'goods' => $goods,
                'settings' => (object)array(),
                'warehouse' => intval($warehouse_id),
                'contragent' => 330985, // Фиксированный контрагент
                'paybox' => intval($cashbox_id),
                'organization' => intval($organization_id),
                'status' => false,
                'paid_rubles' => number_format(floatval($data['order_total'] ?? 500), 2, '.', ''),
                'paid_lt' => 0
            );
            
            return array($document_data);
        }
        
        return array();
    }
    
    // Форматирование комментария (ИСПРАВЛЕННАЯ ВЕРСИЯ)
    private function format_order_comment($data) {
        $comment = "Заказ с сайта WordPress (Fixed Plugin v2 - v1.0.3)\n";
        if (isset($data['name']) && !empty($data['name'])) $comment .= "Клиент: " . $data['name'] . "\n";
        if (isset($data['email']) && !empty($data['email'])) $comment .= "Email: " . $data['email'] . "\n";
        if (isset($data['phone']) && !empty($data['phone'])) $comment .= "Телефон: " . $data['phone'] . "\n";
        if (isset($data['order_date'])) $comment .= "Дата заказа: " . $data['order_date'] . "\n";
        if (isset($data['payment_method']) && !empty($data['payment_method'])) $comment .= "Способ оплаты: " . $data['payment_method'] . "\n";
        
        // Добавляем адрес доставки
        if (!empty($data['shipping_address_1'])) {
            $comment .= "Адрес доставки: " . $data['shipping_address_1'];
            if (!empty($data['shipping_city'])) $comment .= ", " . $data['shipping_city'];
            $comment .= "\n";
        }
        
        // Добавляем информацию о товарах
        if (isset($data['items']) && !empty($data['items'])) {
            $comment .= "\nТовары:\n";
            foreach ($data['items'] as $item) {
                $comment .= "- " . $item['name'] . " (количество: " . $item['quantity'] . ")\n";
            }
        }
        
        return $comment;
    }
    
    /**
     * Log WP_Errors with extra notice for SSL certificate problems.
     */
    private function handle_wp_error($context, $error) {
        $message = $error->get_error_message();
        if (stripos($message, 'certificate') !== false || stripos($message, 'ssl') !== false) {
            $this->log_error("{$context}: SSL certificate error - {$message}");
        } else {
            $this->log_error("{$context}: {$message}");
        }
    }

    // Функции логирования
    private function log($message, $level = 'INFO') {
        if (get_option('tablecrm_fixed_debug_mode', 1)) {
            $timestamp = date('Y-m-d H:i:s');
            $log_message = "[{$timestamp}] [TableCRM Integration FIXED v2] [{$level}] {$message}";
            error_log($log_message);
        }
    }
    
    private function log_info($message) {
        $this->log($message, 'INFO');
    }
    
    private function log_warning($message) {
        $this->log($message, 'WARNING');
    }
    
    private function log_error($message) {
        $this->log($message, 'ERROR');
    }
    
    private function log_success($message) {
        $this->log($message, 'SUCCESS');
    }
    
    private function log_debug($message) {
        $this->log($message, 'DEBUG');
    }
    
    // Заглушки для совместимости
    public function update_order_in_tablecrm($order_id) {
        $this->log_info("Обновление статуса заказа $order_id в TableCRM (пока не реализовано)");
    }
    
    public function send_cf7_to_tablecrm($contact_form) {
        $this->log_info("Отправка формы CF7 в TableCRM (пока не реализовано)");
    }
    
    public function save_utm_data_to_order($order, $data) {
        // Список стандартных UTM параметров
        $utm_params = array('utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content');

        foreach ($utm_params as $param) {
            if (isset($_COOKIE[$param])) {
                $order->update_meta_data('_' . $param, sanitize_text_field($_COOKIE[$param]));
            } elseif (isset($_SESSION[$param])) {
                $order->update_meta_data('_' . $param, sanitize_text_field($_SESSION[$param]));
            }
        }

        // Дополнительная информация о источнике и посадочной странице
        if (isset($_COOKIE['referrer'])) {
            $order->update_meta_data('_referrer', sanitize_text_field($_COOKIE['referrer']));
        } elseif (isset($_SERVER['HTTP_REFERER'])) {
            $order->update_meta_data('_referrer', sanitize_text_field($_SERVER['HTTP_REFERER']));
        }

        if (isset($_COOKIE['landing_page'])) {
            $order->update_meta_data('_landing_page', sanitize_text_field($_COOKIE['landing_page']));
        }
    }
}

// Инициализация плагина через хук plugins_loaded
function initialize_tablecrm_integration_fixed() {
    if (class_exists('WooCommerce')) {
        // Создаем глобальный экземпляр, если нужен доступ из других мест (редко)
        // $GLOBALS['tablecrm_integration_fixed_instance'] = new TableCRM_Integration_Fixed();
        // Обычно достаточно просто создать экземпляр:
        $tablecrm_plugin_instance = new TableCRM_Integration_Fixed();
        
        // ВАЖНО: Добавляем хук для отложенной обработки заказов
        add_action('tablecrm_fixed_process_order', array($tablecrm_plugin_instance, 'send_order_to_tablecrm'));
        
        // Логируем успешную инициализацию
        if (method_exists($tablecrm_plugin_instance, 'log_info')) {
             $tablecrm_plugin_instance->log_info('Плагин TableCRM Integration FIXED v2 инициализирован УСПЕШНО (через plugins_loaded). WooCommerce найден.');
        } else {
            error_log('[TableCRM Integration FIXED v2] [INFO] Плагин TableCRM Integration FIXED v2 инициализирован УСПЕШНО (через plugins_loaded). WooCommerce найден. (log_info не доступен)');
        }

    } else {
        // Эта ситуация теперь менее вероятна, если WooCommerce действительно активен
        error_log('[TableCRM Integration FIXED v2] [WARNING] WooCommerce не найден при инициализации через plugins_loaded - плагин не будет полнофункциональным.');
        // Можно добавить admin_notice, чтобы пользователь видел проблему
        add_action( 'admin_notices', function() {
            echo '<div class="notice notice-error is-dismissible"><p><strong>TableCRM Integration (Fixed v2):</strong> WooCommerce не активен или не найден! Плагин не сможет работать корректно.</p></div>';
        });
    }
}
add_action('plugins_loaded', 'initialize_tablecrm_integration_fixed');

// Хуки активации/деактивации
register_activation_hook(__FILE__, 'tablecrm_fixed_v2_activation');
register_deactivation_hook(__FILE__, 'tablecrm_fixed_v2_deactivation');

function tablecrm_fixed_v2_activation() {
    // Включаем отладку по умолчанию при активации
    update_option('tablecrm_fixed_debug_mode', 1);
    update_option('tablecrm_fixed_send_real_data_only', 1); // Включаем валидацию по умолчанию
    update_option('tablecrm_fixed_enable_orders', 1);
    error_log('[TableCRM Integration FIXED v2] [INFO] Плагин активирован с валидацией данных');
}

function tablecrm_fixed_v2_deactivation() {
    error_log('[TableCRM Integration FIXED v2] [INFO] Плагин деактивирован');
}
