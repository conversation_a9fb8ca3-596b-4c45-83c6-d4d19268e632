# ⚠️ КРИТИЧЕСКАЯ ПРОБЛЕМА: Заказы WooCommerce не отправляются в TableCRM

## 🔍 ДИАГНОЗ
**TableCRM API полностью недоступен** - все эндпоинты возвращают **404 Not Found**

## 📊 ФАКТЫ ИЗ ЛОГОВ
- ❌ **100% эндпоинтов** возвращают 404
- ❌ **Пустые данные заказа** (имя, email, телефон = пустые)  
- ❌ **Сумма заказа = 0, товаров = 0**
- ⚠️ **API URL:** `https://app.tablecrm.com/api/v1/` (возможно устарел)

## 🛠️ ЧТО ДЕЛАТЬ НЕМЕДЛЕННО

### 1. СВЯЗАТЬСЯ С TABLECRM ☎️
- Получить **актуальную документацию API 2024-2025**
- Уточнить **правильные эндпоинты**
- Проверить **действительность API ключа**

### 2. ПРОВЕРИТЬ ДАННЫЕ ЗАКАЗОВ 📦
- Убедиться что WooCommerce заказы содержат данные
- Проверить настройки плагина

### 3. ПРОТЕСТИРОВАТЬ API 🧪
- Запустить `test_tablecrm_api.php` (предварительно вставив реальный API ключ)
- Проверить доступность различных URL

## 🚨 СТАТУС: КРИТИЧЕСКИЙ
**Интеграция полностью не работает** - требуется срочное исправление API эндпоинтов

## 📋 ФАЙЛЫ ДЛЯ АНАЛИЗА
- `ДИАГНОСТИКА_ПРОБЛЕМЫ_TABLECRM.md` - подробная диагностика
- `test_tablecrm_api.php` - скрипт тестирования API
- `server_logs_latest/debug_latest.log` - логи с ошибками 