# Upload and run contragent search test
$server = "bekflom3.beget.tech"
$user = "bekflom3_wz"
$pass = "zl2Shj!e&6ng"

Write-Host "Uploading contragent search test..." -ForegroundColor Green

try {
    $uri = "ftp://$server/public_html/test_contragent_search.php"
    $webclient = New-Object System.Net.WebClient
    $webclient.Credentials = New-Object System.Net.NetworkCredential($user, $pass)
    $webclient.UploadFile($uri, "test_contragent_search.php")
    Write-Host "Test uploaded!" -ForegroundColor Green
    
    # Run the test
    Write-Host "Running contragent search test..." -ForegroundColor Yellow
    $result = Invoke-WebRequest -Uri "https://mosbuketik.ru/test_contragent_search.php" -UseBasicParsing
    Write-Host $result.Content -ForegroundColor White
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

$webclient.Dispose()
Write-Host "Contragent test completed!" -ForegroundColor Green 