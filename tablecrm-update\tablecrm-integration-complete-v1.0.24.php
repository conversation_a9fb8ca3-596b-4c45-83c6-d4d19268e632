<?php
/**
 * Plugin Name: TableCRM Integration Complete v1.0.24
 * Description: Полная версия интеграции с TableCRM с динамическим созданием номенклатуры и контрагентов
 * Version: 1.0.24
 * Author: AI Assistant
 */

// 👉 Auto-invalidate OPcache for this file (helps pickup hot-fixes without server reload)
if (function_exists('opcache_invalidate')) {
    @opcache_invalidate(__FILE__, true);
}

if (!defined('ABSPATH')) {
    exit;
}

// Определение констант плагина
define('TABLECRM_COMPLETE_PLUGIN_URL', plugin_dir_url(__FILE__));
define('TABLECRM_COMPLETE_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('TABLECRM_COMPLETE_VERSION', '1.0.24');

class TableCRM_Integration_Complete {
    
    // Кэш для номенклатур (избегаем повторных API запросов)
    private $nomenclature_cache = array();
    
    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'register_settings'));
        
        // Хуки для WooCommerce
        add_action('woocommerce_order_status_changed', array($this, 'handle_order_status_change'), 10, 3);
        // Отслеживаем создание нового заказа сразу после checkout (без тяжёлых операций в синхроне)
        add_action('woocommerce_checkout_order_processed', array($this, 'handle_new_order'), 10, 1);
        
        // AJAX хуки
        add_action('wp_ajax_test_tablecrm_connection_complete', array($this, 'test_connection'));
        
        // Хук для фоновой отправки заказа в TableCRM
        add_action('tablecrm_complete_schedule_send', array($this, 'send_order_to_tablecrm'), 10, 1);
        
        // Хук активации
        register_activation_hook(__FILE__, array($this, 'activate_plugin'));
    }
    
    public function init() {
        load_plugin_textdomain('tablecrm-integration-complete', false, dirname(plugin_basename(__FILE__)) . '/languages');
    }
    
    public function activate_plugin() {
        // Устанавливаем настройки по умолчанию
        if (!get_option('tablecrm_complete_api_url')) {
            update_option('tablecrm_complete_api_url', 'https://app.tablecrm.com/api/v1/');
        }
        if (!get_option('tablecrm_complete_organization_id')) {
            update_option('tablecrm_complete_organization_id', 38);
        }
        if (!get_option('tablecrm_complete_cashbox_id')) {
            update_option('tablecrm_complete_cashbox_id', 218);
        }
        if (!get_option('tablecrm_complete_warehouse_id')) {
            update_option('tablecrm_complete_warehouse_id', 39);
        }
        if (!get_option('tablecrm_complete_unit_id')) {
            update_option('tablecrm_complete_unit_id', 116);
        }
        if (!get_option('tablecrm_complete_enable_orders')) {
            update_option('tablecrm_complete_enable_orders', 1);
        }
        if (!get_option('tablecrm_complete_debug_mode')) {
            update_option('tablecrm_complete_debug_mode', 1);
        }
        if (!get_option('tablecrm_complete_order_statuses')) {
            update_option('tablecrm_complete_order_statuses', array('processing', 'completed'));
        }
        
        $this->log_success('Плагин TableCRM Integration Complete v1.0.24 активирован');
    }
    
    // Добавление меню в админку
    public function add_admin_menu() {
        add_options_page(
            'TableCRM Integration Complete Settings',
            'TableCRM Complete',
            'manage_options',
            'tablecrm-complete-settings',
            array($this, 'admin_page')
        );
        
        // Добавляем страницу для тестирования
        add_submenu_page(
            'options-general.php',
            'Тест TableCRM Доставка',
            'Тест TableCRM Доставка',
            'manage_options',
            'test-tablecrm-delivery',
            array($this, 'test_delivery_page')
        );
    }
    
    // Страница тестирования доставки
    public function test_delivery_page() {
        // Проверяем права доступа
        if (!current_user_can('manage_options')) {
            wp_die('Доступ запрещен');
        }
        
        // Создаем nonce для безопасности
        $nonce_url = wp_nonce_url(admin_url('admin.php?page=test-tablecrm-delivery'), 'test_tablecrm_delivery');
        
        // Выводим ссылку на тест
        echo '<div class="wrap">';
        echo '<h1>Тест интеграции TableCRM - Доставка</h1>';
        echo '<p>Нажмите кнопку ниже, чтобы протестировать отправку информации о доставке для последнего заказа:</p>';
        echo '<a href="' . esc_url($nonce_url) . '" class="button button-primary">Запустить тест</a>';
        echo '</div>';
        
        // Если есть nonce, запускаем тест
        if (isset($_GET['_wpnonce']) && wp_verify_nonce($_GET['_wpnonce'], 'test_tablecrm_delivery')) {
            include_once(plugin_dir_path(__FILE__) . 'test-tablecrm-delivery.php');
        }
    }
    
    // Регистрация настроек
    public function register_settings() {
        register_setting('tablecrm_complete_settings', 'tablecrm_complete_api_url', array(
            'type' => 'string',
            'sanitize_callback' => array($this, 'sanitize_api_url'),
            'default' => 'https://app.tablecrm.com/api/v1/'
        ));
        
        register_setting('tablecrm_complete_settings', 'tablecrm_complete_api_key', array(
            'type' => 'string',
            'sanitize_callback' => array($this, 'sanitize_api_key'),
            'default' => ''
        ));
        
        register_setting('tablecrm_complete_settings', 'tablecrm_complete_organization_id', array(
            'type' => 'integer',
            'sanitize_callback' => 'absint',
            'default' => 38
        ));
        
        register_setting('tablecrm_complete_settings', 'tablecrm_complete_cashbox_id', array(
            'type' => 'integer',
            'sanitize_callback' => 'absint',
            'default' => 218
        ));
        
        register_setting('tablecrm_complete_settings', 'tablecrm_complete_warehouse_id', array(
            'type' => 'integer',
            'sanitize_callback' => 'absint',
            'default' => 39
        ));
        
        register_setting('tablecrm_complete_settings', 'tablecrm_complete_unit_id', array(
            'type' => 'integer',
            'sanitize_callback' => 'absint',
            'default' => 116
        ));
        
        register_setting('tablecrm_complete_settings', 'tablecrm_complete_enable_orders', array(
            'type' => 'boolean',
            'sanitize_callback' => array($this, 'sanitize_checkbox'),
            'default' => true
        ));
        
        register_setting('tablecrm_complete_settings', 'tablecrm_complete_debug_mode', array(
            'type' => 'boolean',
            'sanitize_callback' => array($this, 'sanitize_checkbox'),
            'default' => true
        ));
        
        register_setting('tablecrm_complete_settings', 'tablecrm_complete_order_statuses', array(
            'type' => 'array',
            'sanitize_callback' => array($this, 'sanitize_order_statuses'),
            'default' => array('processing', 'completed')
        ));
        
        register_setting('tablecrm_complete_settings', 'tablecrm_complete_paid_statuses', array(
            'type' => 'array',
            'sanitize_callback' => array($this, 'sanitize_order_statuses'),
            'default' => array('completed', 'processing')
        ));
    }
    
    // Страница настроек
    public function admin_page() {
        ?>
        <div class="wrap">
            <h1>TableCRM Integration Complete Settings v1.0.24</h1>
            <div class="notice notice-success">
                <p><strong>🚀 Полная версия с динамическим созданием номенклатуры!</strong></p>
                <p>✅ Автоматическое создание товаров в TableCRM</p>
                <p>✅ Автоматическое создание контрагентов</p>
                <p>✅ Защита от дублирования</p>
                <p>✅ Подробное логирование</p>
            </div>
            <form method="post" action="options.php">
                <?php
                settings_fields('tablecrm_complete_settings');
                do_settings_sections('tablecrm_complete_settings');
                ?>
                <table class="form-table">
                    <tr>
                        <th scope="row">API URL</th>
                        <td>
                            <input type="url" name="tablecrm_complete_api_url" value="<?php echo esc_attr(get_option('tablecrm_complete_api_url', 'https://app.tablecrm.com/api/v1/')); ?>" class="regular-text" />
                            <p class="description">URL API TableCRM</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">API Key</th>
                        <td>
                            <input type="password" name="tablecrm_complete_api_key" value="<?php echo esc_attr(get_option('tablecrm_complete_api_key')); ?>" class="regular-text" />
                            <p class="description">Ваш API токен TableCRM</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Organization ID</th>
                        <td>
                            <input type="number" name="tablecrm_complete_organization_id" value="<?php echo esc_attr(get_option('tablecrm_complete_organization_id', 38)); ?>" class="regular-text" />
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Cashbox ID</th>
                        <td>
                            <input type="number" name="tablecrm_complete_cashbox_id" value="<?php echo esc_attr(get_option('tablecrm_complete_cashbox_id', 218)); ?>" class="regular-text" />
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Warehouse ID</th>
                        <td>
                            <input type="number" name="tablecrm_complete_warehouse_id" value="<?php echo esc_attr(get_option('tablecrm_complete_warehouse_id', 39)); ?>" class="regular-text" />
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Unit ID (по умолчанию)</th>
                        <td>
                            <input type="number" name="tablecrm_complete_unit_id" value="<?php echo esc_attr(get_option('tablecrm_complete_unit_id', 116)); ?>" class="regular-text" />
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Отправлять заказы</th>
                        <td>
                            <input type="checkbox" name="tablecrm_complete_enable_orders" value="1" <?php checked(1, get_option('tablecrm_complete_enable_orders', 1)); ?> />
                            <label>Включить отправку заказов WooCommerce</label>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Режим отладки</th>
                        <td>
                            <input type="checkbox" name="tablecrm_complete_debug_mode" value="1" <?php checked(1, get_option('tablecrm_complete_debug_mode', 1)); ?> />
                            <label>Включить подробное логирование</label>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Статусы заказов</th>
                        <td>
                            <?php 
                            $selected_statuses = get_option('tablecrm_complete_order_statuses', array('processing', 'completed'));
                            $available_statuses = array(
                                'pending' => 'Ожидает оплаты',
                                'processing' => 'В обработке',
                                'on-hold' => 'На удержании',
                                'completed' => 'Выполнен',
                                'cancelled' => 'Отменен',
                                'refunded' => 'Возврат',
                                'failed' => 'Неудачен'
                            );
                            
                            foreach ($available_statuses as $status => $label) {
                                $checked = in_array($status, $selected_statuses) ? 'checked' : '';
                                echo "<label><input type='checkbox' name='tablecrm_complete_order_statuses[]' value='$status' $checked /> $label</label><br>";
                            }
                            ?>
                            <p class="description">Статусы заказов, при которых они отправляются в TableCRM</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Оплаченные статусы</th>
                        <td>
                            <?php 
                            $selected_paid_statuses = get_option('tablecrm_complete_paid_statuses', array('completed', 'processing'));
                            
                            foreach ($available_statuses as $status => $label) {
                                $checked = in_array($status, $selected_paid_statuses) ? 'checked' : '';
                                echo "<label><input type='checkbox' name='tablecrm_complete_paid_statuses[]' value='$status' $checked /> $label</label><br>";
                            }
                            ?>
                            <p class="description">Статусы заказов, которые считаются оплаченными в TableCRM</p>
                        </td>
                    </tr>
                </table>
                
                <p class="submit">
                    <input type="submit" name="submit" id="submit" class="button button-primary" value="Сохранить настройки" />
                    <button type="button" id="test-connection-complete" class="button">Тестировать соединение</button>
                </p>
            </form>
            
            <div id="test-result-complete" style="margin-top: 20px;"></div>
            
            <script>
            jQuery(document).ready(function($) {
                $('#test-connection-complete').click(function() {
                    var button = $(this);
                    var result = $('#test-result-complete');
                    
                    button.prop('disabled', true).text('Тестируем...');
                    result.html('<p>Проверяем соединение с TableCRM API...</p>');
                    
                    $.ajax({
                        url: ajaxurl,
                        method: 'POST',
                        data: {
                            action: 'test_tablecrm_connection_complete',
                            nonce: '<?php echo wp_create_nonce("test_tablecrm_complete"); ?>'
                        },
                        success: function(response) {
                            if(response.success) {
                                result.html('<div class="notice notice-success"><p><strong>✅ Соединение успешно!</strong><br>' + response.data.message + '</p></div>');
                            } else {
                                result.html('<div class="notice notice-error"><p><strong>❌ Ошибка соединения:</strong><br>' + response.data.message + '</p></div>');
                            }
                        },
                        error: function() {
                            result.html('<div class="notice notice-error"><p><strong>❌ Ошибка AJAX запроса</strong></p></div>');
                        },
                        complete: function() {
                            button.prop('disabled', false).text('Тестировать соединение');
                        }
                    });
                });
            });
            </script>
        </div>
        <?php
    }
    
    // Тестирование соединения
    public function test_connection() {
        // Логируем информацию о вызывающем AJAX-запросе
        $this->log_info('AJAX test_connection request', array(
            'remote_addr' => $_SERVER['REMOTE_ADDR'] ?? '',
            'user'        => is_user_logged_in() ? wp_get_current_user()->user_login : 'guest'
        ));
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Недостаточно прав доступа');
            return;
        }
        
        if (!isset($_POST['nonce']) || !wp_verify_nonce(sanitize_text_field($_POST['nonce']), 'test_tablecrm_complete')) {
            wp_send_json_error('Неверный токен безопасности');
            return;
        }
        
        $api_url = get_option('tablecrm_complete_api_url', 'https://app.tablecrm.com/api/v1/');
        $api_key = get_option('tablecrm_complete_api_key');
        
        if (empty($api_url) || empty($api_key)) {
            wp_send_json_error('Пожалуйста, заполните API URL и API Key');
            return;
        }
        
        // ВАЛИДАЦИЯ API KEY: Проверяем длину и формат (минимум 32 символа)
        if (strlen($api_key) < 32) {
            wp_send_json_error('API Key должен содержать минимум 32 символа');
            return;
        }
        
        // ВАЛИДАЦИЯ URL: Проверяем корректность формата URL
        if (!filter_var($api_url, FILTER_VALIDATE_URL)) {
            wp_send_json_error('Некорректный формат API URL');
            return;
        }
        
        $response = $this->make_api_request('health', array(), 'GET');
        
        if ($response['success']) {
            wp_send_json_success('Соединение установлено успешно! Динамическое создание номенклатуры работает.');
        } else {
            wp_send_json_error('Ошибка соединения: ' . $response['message']);
        }
    }
    
    // Обработка изменения статуса заказа
    public function handle_order_status_change($order_id, $old_status = '', $new_status = '') {
        try {
            if (!get_option('tablecrm_complete_enable_orders', 1)) {
                return;
            }

            $order = wc_get_order($order_id);
            if (!$order) {
                $this->log_error("Заказ $order_id не найден");
                return;
            }
            
            $order_status     = $order->get_status();
            $trigger_statuses = get_option('tablecrm_complete_order_statuses', array('processing', 'completed'));

            if (!in_array($order_status, $trigger_statuses)) {
                $this->log_info("Заказ $order_id имеет статус '$order_status', который не настроен для отправки");
                return;
            }

            // Проверяем, не был ли заказ уже отправлен
            $existing_doc_id = get_post_meta($order_id, '_tablecrm_complete_document_id', true);
            if (!empty($existing_doc_id)) {
                $this->log_info("Заказ $order_id уже отправлен. Document ID: $existing_doc_id");
                return;
            }

            // Если отправка уже запланирована, не создаём дубликат
            $scheduled_time = wp_next_scheduled('tablecrm_complete_schedule_send', array($order_id));
            if ($scheduled_time) {
                // Если задача запланирована слишком давно (более 5 минут), отменяем её и создаём новую
                if ((time() - $scheduled_time) > 300) {
                    wp_unschedule_event($scheduled_time, 'tablecrm_complete_schedule_send', array($order_id));
                    $this->log_info("Отменена старая cron задача для заказа $order_id и создана новая");
                } else {
                $this->log_info("Отправка заказа $order_id уже запланирована.");
                return;
                }
            }

            // Планируем отправку через 10 секунд, чтобы не блокировать чек-аут
            wp_schedule_single_event(time() + 10, 'tablecrm_complete_schedule_send', array($order_id));
            $this->log_info("Отправка заказа $order_id запланирована в фоне через cron.");
        } catch (Exception $e) {
            $this->log_error('КРИТИЧЕСКАЯ ОШИБКА в handle_order_status_change: ' . $e->getMessage() . ' в ' . $e->getFile() . ':' . $e->getLine());
        }
    }
    
    // Отправка заказа в TableCRM
    public function send_order_to_tablecrm($order_id) {
        $this->log_info("Начинаем отправку заказа $order_id в TableCRM");
        
        $order = wc_get_order($order_id);
        if (!$order) {
            $this->log_error("Заказ $order_id не найден");
            return false;
        }

        // Проверяем, не был ли заказ уже отправлен
        $existing_doc_id = get_post_meta($order_id, '_tablecrm_complete_document_id', true);
        if (!empty($existing_doc_id)) {
            $this->log_info("Заказ $order_id уже был отправлен (Document ID: $existing_doc_id)");
            return true;
        }

        // Получаем настройки
        $api_url = get_option('tablecrm_complete_api_url', 'https://app.tablecrm.com/api/v1/');
        $api_token = get_option('tablecrm_complete_api_key');
        $warehouse_id = get_option('tablecrm_complete_warehouse_id');
        $organization_id = get_option('tablecrm_complete_organization_id');
        $paybox_id = get_option('tablecrm_complete_cashbox_id');

        if (empty($api_url) || empty($api_token) || empty($warehouse_id) || empty($organization_id) || empty($paybox_id)) {
            $this->log_error("Не все настройки API заполнены");
            return false;
        }
        
        // Логируем, какие ID будут использованы при отправке
        $this->log_info("Используемые ID: warehouse={$warehouse_id}, organization={$organization_id}, paybox={$paybox_id}");
        
        $api_url = trailingslashit($api_url);

        // 1. Получаем или создаем контрагента
        $contragent_id = $this->get_or_create_contragent($order, $api_url, $api_token);
        if (!$contragent_id) {
            $this->log_error("Не удалось получить или создать контрагента для заказа $order_id");
            return false;
        }

        // 2. Получаем или создаем номенклатуры для всех товаров
        $nomenclature_ids = array();
        $items = $order->get_items();
        $failed_items = array();
        
        foreach ($items as $item_id => $item) {
            $product_name = $item->get_name();
            $nomenclature_id = $this->get_or_create_nomenclature($product_name, $api_url, $api_token);
            if (!$nomenclature_id) {
                $this->log_warning("Не удалось получить или создать номенклатуру для товара '$product_name' - пропускаем этот товар");
                $failed_items[] = $product_name;
                continue;
            }
            $nomenclature_ids[$item_id] = $nomenclature_id;
        }

        // Если не удалось создать номенклатуры ни для одного товара
        if (empty($nomenclature_ids)) {
            $this->log_error("Не удалось создать номенклатуры ни для одного товара в заказе $order_id");
            return false;
        }
        
        // Логируем информацию о пропущенных товарах
        if (!empty($failed_items)) {
            $this->log_warning("Пропущены товары: " . implode(', ', $failed_items));
        }

        // 3. Формируем данные заказа
        $adapted_data = $this->adapt_data_format($order, $warehouse_id, $organization_id, $paybox_id, $contragent_id, $nomenclature_ids);

        if (!$adapted_data) {
            $this->log_error("Не удалось адаптировать данные заказа $order_id");
            return false;
        }

        // 4. Отправляем заказ
        $response = $this->make_api_request('docs_sales/', $adapted_data, 'POST');
        
        if ($response['success']) {
            $document_id = $this->extract_document_id($response);
            
            if ($document_id && $document_id !== 'unknown') {
                update_post_meta($order_id, '_tablecrm_complete_document_id', $document_id);
                update_post_meta($order_id, '_tablecrm_complete_sent_at', current_time('mysql'));
                
                $this->log_success("Заказ $order_id успешно отправлен. Document ID: $document_id");
                $this->add_order_note($order_id, "Заказ успешно отправлен в TableCRM. Document ID: $document_id");
                
                // Отправка UTM-данных через специальный API эндпоинт
                $this->send_utm_data($document_id, $this->get_analytics_data($order));
                
                // Отправка информации о доставке через специальный API эндпоинт
                $this->send_delivery_info($document_id, $order);
                
                return true;
            }
        }
        
        $this->log_error("Ошибка отправки заказа $order_id: " . $response['message']);
        $this->add_order_note($order_id, "Ошибка отправки в TableCRM: " . $response['message']);
        return false;
    }
    
    // Получение или создание контрагента
    private function get_or_create_contragent($order, $api_url, $api_token) {
        $phone = $order->get_billing_phone(); // Используем только billing_phone
        if (empty($phone)) {
            $this->log_warning("Телефон клиента в заказе #{$order->get_id()} не найден");
            return null;
        }

        // Нормализуем номер телефона - убираем все кроме цифр и добавляем +7
        $normalized_phone = preg_replace('/[^0-9]/', '', $phone);
        if (strlen($normalized_phone) == 11 && substr($normalized_phone, 0, 1) == '8') {
            $normalized_phone = '7' . substr($normalized_phone, 1);
        }
        if (strlen($normalized_phone) == 10) {
            $normalized_phone = '7' . $normalized_phone;
        }
        $search_phone = '+' . $normalized_phone;

        $this->log_info("Поиск/создание контрагента для телефона: {$phone} (нормализованный: {$search_phone})");

        // 1. Поиск контрагента по телефону с правильными параметрами
        // Используем параметр phone для прямого поиска
        $search_url = $api_url . 'contragents/?token=' . $api_token . 
                      '&limit=100&offset=0&sort=created_at:desc&phone=' . urlencode($normalized_phone);
        $search_response = wp_remote_get($search_url);

        if (!is_wp_error($search_response)) {
            $search_response_code = wp_remote_retrieve_response_code($search_response);
            $search_body = wp_remote_retrieve_body($search_response);
            $this->log_info("Поиск контрагента - Status: {$search_response_code}, Response: " . substr($search_body, 0, 200));
            
            $search_data = json_decode($search_body, true);
            
            // Проверяем разные форматы ответа
            if (!empty($search_data)) {
                // Если ответ содержит result (правильный формат API)
                if (isset($search_data['result']) && is_array($search_data['result']) && !empty($search_data['result'])) {
                    // Берем первого найденного контрагента (API уже отфильтровал по номеру)
                    $contragent_id = $search_data['result'][0]['id'];
                    $this->log_success("Контрагент найден в result. ID: {$contragent_id}");
                    return $contragent_id;
                }
                // Если ответ содержит results (альтернативный формат)
                else if (isset($search_data['results']) && is_array($search_data['results']) && !empty($search_data['results'])) {
                    $contragent_id = $search_data['results'][0]['id'];
                    $this->log_success("Контрагент найден в results. ID: {$contragent_id}");
                    return $contragent_id;
                }
                // Если ответ - прямой массив (только если это действительно массив контрагентов)
                else if (is_array($search_data) && !isset($search_data['result']) && !isset($search_data['results']) && !empty($search_data) && isset($search_data[0]['id'])) {
                    $contragent_id = $search_data[0]['id'];
                    $this->log_success("Контрагент найден в массиве. ID: {$contragent_id}");
                    return $contragent_id;
                }
            }
        } else {
            $this->log_error("Ошибка поиска контрагента: " . $search_response->get_error_message());
        }

        // 2. Создание нового контрагента
        $this->log_info("Контрагент не найден, создаем нового");
        $create_url = $api_url . 'contragents/?token=' . $api_token;
        
        // Формируем имя без телефона
        $first_name = $order->get_billing_first_name();
        $last_name = $order->get_billing_last_name();
        
        // Функция для проверки, является ли строка телефоном
        $is_phone = function($str) {
            if (empty($str)) return false;
            // Проверяем на наличие телефонных паттернов
            return preg_match('/^[\+]?[0-9\(\)\-\s]{7,}$/', $str) || 
                   strpos($str, '+7') === 0 || 
                   strpos($str, '8(') === 0 ||
                   preg_match('/\d{3}.*\d{3}.*\d{2}.*\d{2}/', $str);
        };
        
        // Очищаем имя и фамилию от телефонов
        $clean_first_name = (!empty($first_name) && !$is_phone($first_name)) ? $first_name : '';
        $clean_last_name = (!empty($last_name) && !$is_phone($last_name)) ? $last_name : '';
        
        $full_name = trim($clean_first_name . ' ' . $clean_last_name);
        
        // Если имя пустое, используем email как fallback
        if (empty($full_name)) {
            $full_name = $order->get_billing_email();
        }
        
        $email = $order->get_billing_email();

        $create_data = array(
            'name' => $full_name,
            'phone' => $search_phone, // Используем нормализованный номер с +7
            'email' => $email,
            'our_cost' => false
        );
        
        $this->log_info("Создаем контрагента: имя='$full_name', телефон='$search_phone', email='$email'");
        
        $create_response = wp_remote_post($create_url, array(
            'method' => 'POST',
            'headers' => array('Content-Type' => 'application/json; charset=utf-8'),
            'body' => json_encode($create_data, JSON_UNESCAPED_UNICODE)
        ));

        if (!is_wp_error($create_response)) {
            $create_response_code = wp_remote_retrieve_response_code($create_response);
            $create_response_body = wp_remote_retrieve_body($create_response);
            $this->log_info("Создание контрагента - Status: {$create_response_code}, Response: " . substr($create_response_body, 0, 200));
            
            $create_result = json_decode($create_response_body, true);

            if ($create_response_code >= 200 && $create_response_code < 300) {
                if (isset($create_result['id'])) {
                    $new_contragent_id = $create_result['id'];
                    $this->log_success("Новый контрагент создан. ID: {$new_contragent_id}");
                    return $new_contragent_id;
                } else if (is_array($create_result) && isset($create_result[0]['id'])) {
                    $new_contragent_id = $create_result[0]['id'];
                    $this->log_success("Новый контрагент создан (массив). ID: {$new_contragent_id}");
                    return $new_contragent_id;
                }
            } else {
                // Если контрагент уже существует, попробуем найти его еще раз
                if ($create_response_code == 400 && strpos($create_response_body, 'Phone number already in use') !== false) {
                    $this->log_info("Контрагент уже существует, повторный поиск...");
                    
                    // Повторный поиск с параметром phone
                    $search_again_url = $api_url . 'contragents/?token=' . $api_token . 
                                       '&limit=100&offset=0&sort=created_at:desc&phone=' . urlencode($normalized_phone);
                    $search_again_response = wp_remote_get($search_again_url);
                    
                    if (!is_wp_error($search_again_response)) {
                        $again_body = wp_remote_retrieve_body($search_again_response);
                        $again_data = json_decode($again_body, true);
                        
                        if (isset($again_data['result']) && !empty($again_data['result'])) {
                            $found_contragent_id = $again_data['result'][0]['id'];
                            $this->log_success("Контрагент найден при повторном поиске. ID: {$found_contragent_id}");
                            return $found_contragent_id;
                        }
                    }
                }
            }
        } else {
            $this->log_error("Ошибка создания контрагента: " . $create_response->get_error_message());
        }

        $this->log_error("Не удалось создать контрагента");
        return null;
    }
    
    // Получение или создание номенклатуры
    private function get_or_create_nomenclature($item_name, $api_url, $api_token) {
        // Проверяем кэш
        $cache_key = md5($item_name);
        if (isset($this->nomenclature_cache[$cache_key])) {
            $this->log_info("Номенклатура для товара '{$item_name}' найдена в кэше. ID: " . $this->nomenclature_cache[$cache_key]);
            return $this->nomenclature_cache[$cache_key];
        }
        
        $this->log_info("Поиск/создание номенклатуры для товара: '{$item_name}'");

        // 1. Поиск номенклатуры по названию с правильными параметрами
        $search_url = $api_url . 'nomenclature/?token=' . $api_token . 
                      '&name=' . urlencode($item_name) . 
                      '&limit=100&offset=0&with_prices=false&with_balance=false&with_attributes=false&only_main_from_group=false';
        $search_response = wp_remote_get($search_url);

        if (!is_wp_error($search_response)) {
            $search_response_code = wp_remote_retrieve_response_code($search_response);
            $search_body = wp_remote_retrieve_body($search_response);
            $this->log_info("Поиск номенклатуры - Status: {$search_response_code}, Response: " . substr($search_body, 0, 200));
            
            $search_data = json_decode($search_body, true);
            
            // Проверяем разные форматы ответа
            if (!empty($search_data)) {
                // Если ответ содержит result (правильный формат API)
                if (isset($search_data['result']) && is_array($search_data['result']) && !empty($search_data['result'])) {
                    $nomenclature_id = $search_data['result'][0]['id'];
                    // Сохраняем в кэш
                    $this->nomenclature_cache[$cache_key] = $nomenclature_id;
                    $this->log_success("Номенклатура найдена в result. ID: {$nomenclature_id}");
                    return $nomenclature_id;
                }
                // Если ответ содержит results (альтернативный формат)
                else if (isset($search_data['results']) && is_array($search_data['results']) && !empty($search_data['results'])) {
                    $nomenclature_id = $search_data['results'][0]['id'];
                    // Сохраняем в кэш
                    $this->nomenclature_cache[$cache_key] = $nomenclature_id;
                    $this->log_success("Номенклатура найдена в results. ID: {$nomenclature_id}");
                    return $nomenclature_id;
                }
            }
        } else {
            $this->log_error("Ошибка поиска номенклатуры: " . $search_response->get_error_message());
        }

        // 2. Создание новой номенклатуры
        $this->log_info("Номенклатура не найдена, создаем новую");
        $create_url = $api_url . 'nomenclature/?token=' . $api_token;
        
        $unit_id = get_option('tablecrm_complete_unit_id', 116);
        // API ожидает массив объектов, а не один объект
        $create_data = array(array('name' => $item_name, 'unit' => $unit_id));
        
        $create_response = wp_remote_post($create_url, array(
            'method' => 'POST',
            'headers' => array('Content-Type' => 'application/json; charset=utf-8'),
            'body' => json_encode($create_data, JSON_UNESCAPED_UNICODE)
        ));

        if (!is_wp_error($create_response)) {
            $create_response_code = wp_remote_retrieve_response_code($create_response);
            $create_response_body = wp_remote_retrieve_body($create_response);
            $this->log_info("Создание номенклатуры - Status: {$create_response_code}, Response: " . substr($create_response_body, 0, 200));
            
            $create_result = json_decode($create_response_body, true);

            if ($create_response_code >= 200 && $create_response_code < 300) {
                if (isset($create_result['id'])) {
                    $new_nomenclature_id = $create_result['id'];
                    // Сохраняем в кэш
                    $this->nomenclature_cache[$cache_key] = $new_nomenclature_id;
                    $this->log_success("Новая номенклатура создана. ID: {$new_nomenclature_id}");
                    return $new_nomenclature_id;
                } else if (is_array($create_result) && isset($create_result[0]['id'])) {
                    $new_nomenclature_id = $create_result[0]['id'];
                    // Сохраняем в кэш
                    $this->nomenclature_cache[$cache_key] = $new_nomenclature_id;
                    $this->log_success("Новая номенклатура создана (массив). ID: {$new_nomenclature_id}");
                    return $new_nomenclature_id;
                }
            }
        } else {
            $this->log_error("Ошибка создания номенклатуры: " . $create_response->get_error_message());
        }

        $this->log_error("Не удалось создать номенклатуру для товара '{$item_name}'");
        return null;
    }
    
    // Адаптация данных для API
    private function adapt_data_format($order, $warehouse_id, $organization_id, $paybox_id, $contragent_id, $nomenclature_ids_map) {
        $order_id = $order->get_id();
        $items = $order->get_items();
        $goods = array();

        $this->log_info("Формирование данных для заказа #$order_id");
        
        // Получаем и нормализуем основной телефон
        $phone = $order->get_billing_phone();
        $normalized_phone = '';
        if (!empty($phone)) {
            $normalized_phone = preg_replace('/[^0-9]/', '', $phone);
            if (strlen($normalized_phone) == 11 && substr($normalized_phone, 0, 1) == '8') {
                $normalized_phone = '7' . substr($normalized_phone, 1);
            }
            if (strlen($normalized_phone) == 10) {
                $normalized_phone = '7' . $normalized_phone;
            }
            $normalized_phone = '+' . $normalized_phone;
        }

        $this->log_info("Количество товаров в заказе: " . count($items));
        $this->log_info("Количество номенклатур: " . count($nomenclature_ids_map));
        $this->log_info("Основной телефон клиента: " . $normalized_phone);

        foreach ($items as $item_id => $item) {
            $product = $item->get_product();
            $item_name = $item->get_name();

            $nomenclature_id = isset($nomenclature_ids_map[$item_id]) ? $nomenclature_ids_map[$item_id] : null;
            if (!$nomenclature_id) {
                $this->log_error("ID номенклатуры для товара '{$item_name}' не найден");
                continue;
            }
            
            // Получаем цену без учета скидок (базовую цену)
            $base_price = (float) $order->get_item_total($item, false, true);
            // Получаем цену с учетом скидок
            $discounted_price = (float) $order->get_item_total($item, false, false);
            
            $quantity = $item->get_quantity();
            $discount_amount = ($base_price - $discounted_price) * $quantity;
            $total_base = $base_price * $quantity;
            $total_discounted = $discounted_price * $quantity;
            
            $this->log_info("Товар '{$item_name}': Базовая цена: {$base_price}, Цена со скидкой: {$discounted_price}, Кол-во: {$quantity}, Скидка: {$discount_amount}");

            $goods[] = array(
                'price'          => number_format($base_price, 2, '.', ''),
                'quantity'       => $quantity,
                'unit'           => (int) get_option('tablecrm_complete_unit_id', 116),
                'discount'       => number_format($discount_amount, 2, '.', ''),
                'sum_discounted' => number_format($total_discounted, 2, '.', ''),
                'nomenclature'   => (int) $nomenclature_id,
                'name'           => $item_name,
                'sku'            => $product ? $product->get_sku() : '',
            );
            
            $this->log_info("Добавлен товар: '{$item_name}' (nomenclature: $nomenclature_id, price: $price, qty: " . $item->get_quantity() . ")");
        }

        // Добавляем стоимость доставки как отдельный товар в том же заказе
        $shipping_total = $order->get_shipping_total();
        if ($shipping_total > 0) {
            $this->log_info("Добавляем стоимость доставки как отдельный товар: {$shipping_total}");
            
            // Создаем или находим номенклатуру для доставки
            $api_url = get_option('tablecrm_complete_api_url', 'https://app.tablecrm.com/api/v1/');
            $api_token = get_option('tablecrm_complete_api_key');
            $api_url = trailingslashit($api_url);
            
            $delivery_nomenclature_id = $this->get_or_create_nomenclature('Доставка', $api_url, $api_token);
            if ($delivery_nomenclature_id) {
                $goods[] = array(
                    'price'          => number_format((float) $shipping_total, 2, '.', ''),
                    'quantity'       => 1,
                    'unit'           => (int) get_option('tablecrm_complete_unit_id', 116),
                    'discount'       => 0,
                    'sum_discounted' => number_format((float) $shipping_total, 2, '.', ''),
                    'nomenclature'   => (int) $delivery_nomenclature_id,
                    'name'           => 'Доставка',
                    'sku'            => '',
                );
                $this->log_success("Доставка добавлена как товар с ID номенклатуры: {$delivery_nomenclature_id}");
            } else {
                $this->log_warning("Не удалось создать номенклатуру для доставки");
            }
        }

        if (empty($goods)) {
            $this->log_error("В заказе #{$order_id} нет товаров для отправки");
            return null;
        }
        
        $this->log_info("Итого товаров для отправки: " . count($goods));
        
        $comment_text = "Заказ с сайта №{$order_id}";
        if (!empty($normalized_phone)) {
            $comment_text .= ". Телефон клиента: " . $normalized_phone;
        }
        if ($note = $order->get_customer_note()) {
            $comment_text .= ". Комментарий клиента: " . $note;
        }
        
        // Добавляем информацию о способе доставки в комментарий
        if ($shipping_total > 0) {
            $shipping_method = $order->get_shipping_method();
            if ($shipping_method) {
                $comment_text .= ". Способ доставки: " . $shipping_method;
            }
        }
        
        // Получение UTM и аналитических данных из заказа
        $analytics = $this->get_analytics_data($order);
        
        // Формирование аналитической информации для комментария
        $analytics_comment = $this->format_analytics_comment($analytics);
        
        $comment_text .= $analytics_comment;
        
        // Определяем статус оплаты на основе настроек и статуса заказа WooCommerce
        $is_paid = false;
        $paid_amount = 0;
        
        $order_status = $order->get_status();
        $paid_statuses = get_option('tablecrm_complete_paid_statuses', array('completed', 'processing'));
        
        if (in_array($order_status, $paid_statuses)) {
            $is_paid = true;
            $paid_amount = (float) $order->get_total();
        }
        
        $this->log_info("Статус заказа WooCommerce: '{$order_status}', Настроенные оплаченные статусы: " . implode(', ', $paid_statuses) . ", Считается оплаченным: " . ($is_paid ? 'Да' : 'Нет') . ", Сумма к оплате: {$paid_amount}");
        
        $final_data = array(array(
            'dated' => time(),
            'operation' => 'Заказ',
            'comment' => $comment_text,
            'tax_included' => true,
            'tax_active' => true,
            'goods' => $goods,
            'settings' => new stdClass(),
            'warehouse' => (int) $warehouse_id,
            'contragent' => (int) $contragent_id,
            'paybox' => (int) $paybox_id,
            'organization' => (int) $organization_id,
            'status' => $is_paid,
            'paid_rubles' => $is_paid ? number_format($paid_amount, 2, '.', '') : '0.00',
            'paid_lt' => 0,
        ));
        
        // КРИТИЧЕСКИ ВАЖНОЕ ЛОГИРОВАНИЕ - показываем точно что отправляется
        $this->log_error("🔍 ДЕТАЛЬНЫЙ АНАЛИЗ ОТПРАВЛЯЕМЫХ ДАННЫХ:");
        $this->log_error("Order ID: {$order_id}");
        $this->log_error("WooCommerce статус: '{$order_status}'");
        $this->log_error("Настроенные оплаченные статусы: [" . implode(', ', $paid_statuses) . "]");
        $this->log_error("Заказ считается оплаченным: " . ($is_paid ? 'TRUE' : 'FALSE'));
        $this->log_error("Отправляемый статус в TableCRM: " . ($is_paid ? 'TRUE (ОПЛАЧЕНО)' : 'FALSE (НЕ ОПЛАЧЕНО)'));
        $this->log_error("Сумма заказа WooCommerce: " . $order->get_total());
        $this->log_error("Отправляемая сумма paid_rubles: " . ($is_paid ? number_format($paid_amount, 2, '.', '') : '0.00'));
        
        // Детально анализируем товары
        $this->log_error("Анализ товаров:");
        foreach ($goods as $index => $good) {
            $this->log_error("  Товар #" . ($index + 1) . ":");
            $this->log_error("    Название: " . $good['name']);
            $this->log_error("    Базовая цена: " . $good['price']);
            $this->log_error("    Количество: " . $good['quantity']);
            $this->log_error("    Скидка: " . $good['discount']);
            $this->log_error("    Сумма со скидкой: " . $good['sum_discounted']);
        }
        
        $this->log_error("JSON для отправки в TableCRM: " . json_encode($final_data, JSON_UNESCAPED_UNICODE));
        
        $this->log_info("Данные для отправки сформированы успешно");
        return $final_data;
    }
    
    // Получение UTM и аналитических данных из заказа
    private function get_analytics_data($order) {
        $analytics = array();
        $meta_data = $order->get_meta_data();
        
        foreach ($meta_data as $meta) {
            $key = $meta->get_data()['key'];
            $value = $meta->get_data()['value'];
            
            // WooCommerce Order Attribution data
            if (strpos($key, '_wc_order_attribution_') === 0) {
                $clean_key = str_replace('_wc_order_attribution_', '', $key);
                $analytics[$clean_key] = $value;
            }
            
            // Standard UTM parameters
            if (strpos($key, 'utm_') === 0) {
                $analytics[$key] = $value;
            }
            
            // Extended UTM parameters from API documentation
            $extended_utm_fields = array(
                'utm_name', 'utm_phone', 'utm_email', 'utm_leadid', 
                'utm_yclientid', 'utm_gaclientid'
            );
            if (in_array($key, $extended_utm_fields)) {
                $analytics[$key] = $value;
            }
            
            // Other tracking data
            if (in_array($key, array('gclid', 'fbclid', 'yclid', 'referrer', 'landing_page', 'source', 'medium', 'campaign'))) {
                $analytics[$key] = $value;
            }
        }
        
        // Также проверяем GET и POST параметры если они сохранены
        if (isset($_GET)) {
            foreach ($_GET as $key => $value) {
                if (strpos($key, 'utm_') === 0 && !isset($analytics[$key])) {
                    $analytics[$key] = sanitize_text_field($value);
                }
            }
        }
        
        // Проверяем куки для UTM-данных
        if (isset($_COOKIE)) {
            foreach ($_COOKIE as $key => $value) {
                if (strpos($key, 'utm_') === 0 && !isset($analytics[$key])) {
                    $analytics[$key] = sanitize_text_field($value);
                }
            }
        }
        
        return $analytics;
    }
    
    // Формирование аналитической информации для комментария
    private function format_analytics_comment($analytics) {
        if (empty($analytics)) {
            return '';
        }
        
        $comment_parts = array();
        
        // Источник трафика
        if (isset($analytics['utm_source']) || isset($analytics['source_type'])) {
            $source = isset($analytics['utm_source']) ? $analytics['utm_source'] : $analytics['source_type'];
            $comment_parts[] = "Источник: $source";
        }
        
        // Канал/медиум
        if (isset($analytics['utm_medium'])) {
            $comment_parts[] = "Канал: " . $analytics['utm_medium'];
        }
        
        // Кампания
        if (isset($analytics['utm_campaign'])) {
            $comment_parts[] = "Кампания: " . $analytics['utm_campaign'];
        }
        
        // Реферер
        if (isset($analytics['referrer'])) {
            $referrer_domain = parse_url($analytics['referrer'], PHP_URL_HOST);
            if ($referrer_domain) {
                $comment_parts[] = "Реферер: $referrer_domain";
            }
        }
        
        // Google Ads
        if (isset($analytics['gclid'])) {
            $comment_parts[] = "Google Ads: " . substr($analytics['gclid'], 0, 20) . "...";
        }
        
        // Facebook Ads
        if (isset($analytics['fbclid'])) {
            $comment_parts[] = "Facebook Ads: " . substr($analytics['fbclid'], 0, 20) . "...";
        }
        
        // Yandex Direct
        if (isset($analytics['yclid'])) {
            $comment_parts[] = "Yandex Direct: " . substr($analytics['yclid'], 0, 20) . "...";
        }
        
        return empty($comment_parts) ? '' : '. Аналитика: ' . implode(', ', $comment_parts);
    }
    
    // Выполнение API запроса
    private function make_api_request($endpoint, $data = array(), $method = 'POST') {
        $api_url = get_option('tablecrm_complete_api_url', 'https://app.tablecrm.com/api/v1/');
        $api_key = get_option('tablecrm_complete_api_key');
        
        if (empty($api_url) || empty($api_key)) {
            return array('success' => false, 'message' => 'Не настроены API параметры');
        }
        
        $url = rtrim($api_url, '/') . '/' . ltrim($endpoint, '/') . '?token=' . $api_key;
        
        $args = array(
            'method' => $method,
            'headers' => array(
                'Content-Type' => 'application/json; charset=utf-8',
                'User-Agent' => 'WordPress-TableCRM-Integration-Complete/1.0.24'
            ),
            'timeout' => 30,
            'sslverify' => true
        );
        
        if (in_array($method, array('POST', 'PUT')) && !empty($data)) {
            $args['body'] = json_encode($data, JSON_UNESCAPED_UNICODE);
        }
        
        $this->log_info("API запрос: $method $url");
        
        // Логируем тело запроса (POST / PUT) полностью, чтобы диагностировать ошибки 500
        if (!empty($data)) {
            $this->log_info('Request body: ' . substr(json_encode($data, JSON_UNESCAPED_UNICODE), 0, 5000));
        }
        
        $response = wp_remote_request($url, $args);
        
        if (is_wp_error($response)) {
            $error_message = $response->get_error_message();
            $this->log_error("Ошибка HTTP запроса: $error_message");
            return array('success' => false, 'message' => $error_message);
        }
        
        $status_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        
        $this->log_info("API ответ: HTTP $status_code");
        
        // Логируем тело ответа, если код не успешный (>=300)
        if ($status_code >= 300) {
            $this->log_error('Response body: ' . substr($body, 0, 5000));
        }
        
        if ($status_code >= 200 && $status_code < 300) {
            $decoded = json_decode($body, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->log_error("Ошибка парсинга JSON: " . json_last_error_msg());
                return array('success' => false, 'message' => 'Некорректный ответ API (не JSON)', 'status_code' => $status_code);
            }
            return array(
                'success' => true, 
                'data' => $decoded, 
                'status_code' => $status_code
            );
        } else {
            $error_message = "HTTP $status_code: " . substr($body, 0, 200);
            $this->log_error("API ошибка: $error_message");
            return array('success' => false, 'message' => $error_message, 'status_code' => $status_code);
        }
    }
    
    // Извлечение Document ID из ответа
    private function extract_document_id($response) {
        if (isset($response['document_id'])) {
            return $response['document_id'];
        } elseif (isset($response['data']) && is_array($response['data']) && !empty($response['data'][0]['id'])) {
            return $response['data'][0]['id'];
        }
        return 'unknown';
    }
    
    // Добавление заметки к заказу
    private function add_order_note($order_id, $note) {
        $order = wc_get_order($order_id);
        if ($order) {
            $order->add_order_note($note);
            $order->save();
        }
    }
    
    // Санитизация методы
    public function sanitize_api_url($value) {
        if (empty($value)) {
            return 'https://app.tablecrm.com/api/v1/';
        }
        
        $value = esc_url_raw($value);
        if (!filter_var($value, FILTER_VALIDATE_URL)) {
            return 'https://app.tablecrm.com/api/v1/';
        }
        
        return rtrim($value, '/') . '/';
    }
    
    public function sanitize_api_key($value) {
        if (empty($value)) {
            return '';
        }
        
        // Убираем все HTML теги и пробелы
        $value = wp_strip_all_tags(trim($value));
        
        // ДОПОЛНИТЕЛЬНАЯ ВАЛИДАЦИЯ: Проверяем длину токена (минимум 32 символа)
        if (strlen($value) < 32) {
            $this->log_warning("API Key слишком короткий (требуется минимум 32 символа). Используется предыдущее значение.");
            return get_option('tablecrm_complete_api_key', ''); // Возвращаем предыдущее значение
        }
        
        // ЗАЩИТА ОТ INJECTION: Санитизируем как текстовое поле с alphanumeric символами
        $value = sanitize_text_field($value);
        
        // ДОПОЛНИТЕЛЬНАЯ ФИЛЬТРАЦИЯ: Убираем всё кроме букв, цифр и базовых символов
        $value = preg_replace('/[^a-zA-Z0-9\-_]/', '', $value);
        
        return $value;
    }
    
    public function sanitize_checkbox($value) {
        return !empty($value) ? 1 : 0;
    }
    
    public function sanitize_order_statuses($value) {
        if (!is_array($value)) {
            return array('processing', 'completed');
        }
        
        $allowed_statuses = array('pending', 'processing', 'on-hold', 'completed', 'cancelled', 'refunded', 'failed');
        $sanitized = array();
        
        foreach ($value as $status) {
            $status = sanitize_key($status);
            if (in_array($status, $allowed_statuses)) {
                $sanitized[] = $status;
            }
        }
        
        return empty($sanitized) ? array('processing', 'completed') : $sanitized;
    }
    
    // Логирование
    private function log($message, $level = 'INFO', $context = null) {
        $timestamp  = current_time('Y-m-d H:i:s');
        $log_entry  = "[$timestamp] [$level] $message";

        // Если передан контекст, добавляем его в лог в формате JSON для наглядности
        if ($context !== null) {
            $json_context = json_encode($context, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
            if ($json_context !== false) {
                $log_entry .= PHP_EOL . $json_context;
            }
        }

        $log_entry .= PHP_EOL; // завершаем запись переносом строки

        // Всегда записываем в файл для диагностики
        $log_file = WP_CONTENT_DIR . '/uploads/tablecrm_integration_complete.log';
        file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);

        // Также выводим в лог WordPress, если включена отладка
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('TableCRM Integration: ' . $log_entry);
        }
    }

    // Обёртки
    private function log_info($message, $context = null)    { $this->log($message, 'INFO',    $context); }
    private function log_success($message, $context = null) { $this->log($message, 'SUCCESS', $context); }
    private function log_warning($message, $context = null) { $this->log($message, 'WARNING', $context); }
    private function log_error($message, $context = null)   { $this->log($message, 'ERROR',   $context); }
    
    // Отправка UTM-данных через специальный API эндпоинт
    private function send_utm_data($document_id, $analytics) {
        if (empty($analytics) || empty($document_id)) {
            return true; // Нет данных для отправки
        }
        
        $this->log_info("Отправка UTM-данных для документа $document_id");
        
        // Формируем данные для UTM API согласно официальной документации
        $utm_data = array();
        
        // Основные UTM параметры
        if (isset($analytics['utm_source'])) {
            $utm_data['utm_source'] = $analytics['utm_source'];
        }
        if (isset($analytics['utm_medium'])) {
            $utm_data['utm_medium'] = $analytics['utm_medium'];
        }
        if (isset($analytics['utm_campaign'])) {
            $utm_data['utm_campaign'] = $analytics['utm_campaign'];
        }
        if (isset($analytics['utm_term'])) {
            // utm_term может быть массивом согласно API
            $utm_data['utm_term'] = is_array($analytics['utm_term']) ? $analytics['utm_term'] : array($analytics['utm_term']);
        }
        if (isset($analytics['utm_content'])) {
            $utm_data['utm_content'] = $analytics['utm_content'];
        }
        
        // Дополнительные UTM поля
        if (isset($analytics['utm_name'])) {
            $utm_data['utm_name'] = $analytics['utm_name'];
        }
        if (isset($analytics['utm_phone'])) {
            $utm_data['utm_phone'] = $analytics['utm_phone'];
        }
        if (isset($analytics['utm_email'])) {
            $utm_data['utm_email'] = $analytics['utm_email'];
        }
        if (isset($analytics['utm_leadid'])) {
            $utm_data['utm_leadid'] = $analytics['utm_leadid'];
        }
        if (isset($analytics['utm_yclientid'])) {
            $utm_data['utm_yclientid'] = $analytics['utm_yclientid'];
        }
        if (isset($analytics['utm_gaclientid'])) {
            $utm_data['utm_gaclientid'] = $analytics['utm_gaclientid'];
        }
        
        // Дополнительные параметры (для обратной совместимости)
        if (isset($analytics['referrer'])) {
            $utm_data['referrer'] = $analytics['referrer'];
        }
        if (isset($analytics['source_type'])) {
            $utm_data['source_type'] = $analytics['source_type'];
        }
        if (isset($analytics['device_type'])) {
            $utm_data['device_type'] = $analytics['device_type'];
        }
        if (isset($analytics['user_agent'])) {
            $utm_data['user_agent'] = substr($analytics['user_agent'], 0, 255); // Ограничиваем длину
        }
        
        // Google/Facebook/Yandex click IDs
        if (isset($analytics['gclid'])) {
            $utm_data['gclid'] = $analytics['gclid'];
        }
        if (isset($analytics['fbclid'])) {
            $utm_data['fbclid'] = $analytics['fbclid'];
        }
        if (isset($analytics['yclid'])) {
            $utm_data['yclid'] = $analytics['yclid'];
        }
        
        if (empty($utm_data)) {
            $this->log_info("Нет UTM-данных для отправки");
            return true;
        }
        
        $this->log_info("UTM-данные для отправки: " . json_encode($utm_data, JSON_UNESCAPED_UNICODE));
        
        // Отправляем через специальный эндпоинт
        $response = $this->make_api_request("docs_sales/$document_id/utm/", $utm_data, 'POST');
        
        if ($response['success']) {
            $this->log_success("UTM-данные успешно отправлены для документа $document_id");
            return true;
        } else {
            $this->log_warning("Ошибка отправки UTM-данных: " . $response['message']);
            return false; // Не критичная ошибка, продолжаем работу
        }
    }
    
    // Отправка информации о доставке через специальный API эндпоинт
    private function send_delivery_info($document_id, $order) {
        // --- Расширенный лог при старте ---
        $this->log_info("Запуск send_delivery_info; Order ID: " . ($order ? $order->get_id() : 'unknown'));

        // 1. Базовая валидация входных параметров ---------------------------
        if (empty($document_id) || !$order) {
            $this->log_error("Некорректные входные параметры: document_id или order пустые", compact('document_id'));
            return false;
        }

        // Если доставка не требуется – завершаем
        if ($order->get_shipping_total() <= 0) {
            $this->log_info("Стоимость доставки 0 – отправка информации о доставке не требуется");
            return true;
        }

        // 2. Проверяем настройки API ----------------------------------------
        $api_url   = get_option('tablecrm_complete_api_url', 'https://app.tablecrm.com/api/v1/');
        $api_token = get_option('tablecrm_complete_api_key');
        if (empty($api_url) || empty($api_token)) {
            $this->log_error("Не настроены API параметры (URL или токен)");
            return false;
        }

        // 3. Формируем данные доставки --------------------------------------
        $delivery_data = [];

        // Адрес доставки
        $address = $this->get_validated_address($order);
        if (!empty($address)) {
            $delivery_data['address'] = $address;
        }

        // Получатель
        $recipient = $this->get_validated_recipient_data($order);
        if (!empty($recipient)) {
            $delivery_data['recipient'] = $recipient;
        }

        // Дата доставки (timestamp)
        $delivery_date_string = $this->get_delivery_date($order);
        if (!empty($delivery_date_string)) {
            $timestamp = strtotime($delivery_date_string);
            if ($this->validate_timestamp($timestamp)) {
                $delivery_data['delivery_date'] = $timestamp;
            } else {
                $this->log_warning("Получена невалидная дата доставки: " . $delivery_date_string);
            }
        }

        // Примечание (shipping method, стоимость, время и т.д.)
        $note_parts = [];
        if ($method = $order->get_shipping_method()) {
            $note_parts[] = "Способ доставки: " . $method;
        }
        if (($total = $order->get_shipping_total()) > 0) {
            $note_parts[] = "Стоимость доставки: " . $total . " руб.";
        }
        if ($time = $this->get_delivery_time($order)) {
            $note_parts[] = "Время доставки: " . $time;
        }
        if ($type = $this->get_order_meta($order, 'delivery_type')) {
            $note_parts[] = "Тип доставки: " . $type;
        }
        if ($customer_note = $order->get_customer_note()) {
            $note_parts[] = "Комментарий клиента: " . $customer_note;
        }
        if (!empty($note_parts)) {
            $note_full = implode('. ', $note_parts);
            // Санитизируем и ограничиваем до 500 символов
            $delivery_data['note'] = substr(sanitize_text_field($note_full), 0, 500);
        }

        // 4. Финальная валидация сформированных данных ----------------------
        $validation = $this->validate_delivery_data($delivery_data);
        if (!$validation['valid']) {
            $this->log_error("Валидация delivery_data неуспешна: " . $validation['message'], $delivery_data);
            return false;
        }

        $this->log_info("Итоговые данные доставки: " . json_encode($delivery_data, JSON_UNESCAPED_UNICODE));

        // 5. Отправка --------------------------------------------------------
        $response = $this->make_api_request("docs_sales/{$document_id}/delivery_info/", $delivery_data, 'POST');
        if ($response['success']) {
            $this->log_success("Информация о доставке успешно отправлена; Document ID: {$document_id}");
            return true;
        }

        // 6. Ошибка при отправке -------------------------------------------
        $this->log_warning("Ошибка при отправке информации о доставке: " . $response['message']);
        return false;
    }

    // ----------- Вспомогательные методы для send_delivery_info ------------

    /**
     * Нормализация телефонного номера до формата +7XXXXXXXXXX
     */
    private function normalize_phone($phone) {
        $digits = preg_replace('/[^0-9]/', '', $phone);
        if (strlen($digits) == 11 && substr($digits, 0, 1) == '8') {
            $digits = '7' . substr($digits, 1);
        }
        if (strlen($digits) == 10) {
            $digits = '7' . $digits;
        }
        return !empty($digits) ? '+' . $digits : '';
    }

    /**
     * Формирует и валидирует данные получателя
     */
    private function get_validated_recipient_data($order) {
        $recipient = [];

        // Имя и фамилия
        $first_name = sanitize_text_field($order->get_shipping_first_name());
        $last_name  = sanitize_text_field($order->get_shipping_last_name());

        if (!empty($first_name)) {
            $recipient['name'] = $first_name;
        }
        if (!empty($last_name)) {
            $recipient['surname'] = $last_name;
        }

        // Телефон – приоритет: shipping -> meta _shipping_phone -> billing
        $phone = '';
        if (method_exists($order, 'get_shipping_phone')) {
            $phone = $order->get_shipping_phone();
        }
        if (empty($phone)) {
            $phone = $this->get_order_meta($order, '_shipping_phone');
        }
        if (empty($phone)) {
            $phone = $order->get_billing_phone();
        }
        if (!empty($phone)) {
            $normalized = $this->normalize_phone($phone);
            if (!empty($normalized) && strlen($normalized) >= 5) {
                $recipient['phone'] = $normalized;
            }
        }

        return $recipient;
    }

    /**
     * Составляет адрес доставки (shipping, затем billing) и приводит его к читабельному виду
     */
    private function get_validated_address($order) {
        $compose_address = function($prefix) use ($order) {
            $parts = [];
            foreach (['_address_1', '_address_2', '_city', '_state', '_postcode'] as $field) {
                // Формируем корректное имя метода, например get_shipping_address_1
                $method = 'get_' . $prefix . $field;
                if (method_exists($order, $method) && ($value = $order->{$method}())) {
                    $parts[] = trim($value);
                }
            }
            return implode(', ', array_filter($parts));
        };

        $address = $compose_address('shipping');
        if (empty($address)) {
            $address = $compose_address('billing');
        }

        // Удаляем лишние пробелы и потенциальные имена в конце
        $address = sanitize_text_field(preg_replace('/\s+/', ' ', $address));
        $address = preg_replace('/,\s*[А-ЯЁ][а-яё]+\s*[А-ЯЁа-яё]*$/u', '', $address);

        return $address;
    }

    /**
     * Проверка валидности timestamp (не 0, не слишком старый/будущий)
     */
    private function validate_timestamp($timestamp) {
        return is_int($timestamp) && $timestamp > 1000000000 && $timestamp < (time() + YEAR_IN_SECONDS * 2);
    }

    /**
     * Финальная валидация сформированных данных перед отправкой
     */
    private function validate_delivery_data($data) {
        if (empty($data['address'])) {
            return ['valid' => false, 'message' => 'Поле address обязательно'];
        }
        if (isset($data['recipient']['phone']) && strlen($data['recipient']['phone']) < 6) {
            return ['valid' => false, 'message' => 'Телефон получателя некорректен'];
        }
        return ['valid' => true, 'message' => 'OK'];
    }

    // Получение даты доставки (HPOS-совместимо)
    private function get_delivery_date($order) {
        // 1. Проверяем CodeRockz WooCommerce Delivery Date Time Pro
        $delivery_date = $this->get_order_meta($order, 'delivery_date');
        if (!empty($delivery_date) && $delivery_date !== '0' && strtotime($delivery_date) !== false) {
            // Если есть время доставки, добавляем его
            $delivery_time = $this->get_delivery_time($order);
            if (!empty($delivery_time)) {
                // Парсим время (например "12:30 - 14:00" берем начальное время)
                $time_parts = explode(' - ', $delivery_time);
                if (!empty($time_parts[0])) {
                    return $delivery_date . ' ' . trim($time_parts[0]) . ':00';
                }
            }
            // Если времени нет, возвращаем дату с временем по умолчанию
            return $delivery_date . ' 12:00:00';
        }
        
        // 2. Проверяем Order Delivery Date Pro
        $delivery_date = $this->get_order_meta($order, '_orddd_timestamp');
        if (!empty($delivery_date) && is_numeric($delivery_date) && $delivery_date > 1000000000) {
            return date('Y-m-d H:i:s', $delivery_date);
        }
        
        // 3. Проверяем WooCommerce Delivery Date
        $delivery_date = $this->get_order_meta($order, '_delivery_date');
        if (!empty($delivery_date) && $delivery_date !== '0' && strtotime($delivery_date) !== false) {
            return date('Y-m-d H:i:s', strtotime($delivery_date));
        }
        
        // 4. Проверяем expected delivery date
        $delivery_date = $this->get_order_meta($order, '_expected_delivery_date');
        if (!empty($delivery_date) && $delivery_date !== '0' && strtotime($delivery_date) !== false) {
            return date('Y-m-d H:i:s', strtotime($delivery_date));
        }
        
        // 5. Проверяем delivery_details_timestamp как fallback
        $delivery_timestamp = $this->get_order_meta($order, 'delivery_details_timestamp');
        if (!empty($delivery_timestamp) && is_numeric($delivery_timestamp) && $delivery_timestamp > 1000000000) {
            return date('Y-m-d H:i:s', $delivery_timestamp);
        }
        
        return ''; // Возвращаем пустую строку если ничего не найдено
    }
    
    // Получение времени доставки (HPOS-совместимо)
    private function get_delivery_time($order) {
        // 1. Проверяем CodeRockz WooCommerce Delivery Date Time Pro
        $delivery_time = $this->get_order_meta($order, 'delivery_time');
        if (!empty($delivery_time)) {
            return $delivery_time; // Возвращаем как есть (например "12:30 - 14:00")
        }
        
        // 2. Проверяем Order Delivery Date Pro
        $delivery_time = $this->get_order_meta($order, '_orddd_time_slot');
        if (!empty($delivery_time)) {
            return $delivery_time;
        }
        
        // 3. Проверяем WooCommerce Delivery Time
        $delivery_time = $this->get_order_meta($order, '_delivery_time');
        if (!empty($delivery_time)) {
            return $delivery_time;
        }
        
        return '';
    }
    
    // HPOS-совместимое получение мета-данных заказа
    private function get_order_meta($order, $key) {
        if (method_exists($order, 'get_meta')) {
            return $order->get_meta($key);
        } else {
            return get_post_meta($order->get_id(), $key, true);
        }
    }

    // Обработка только что созданного заказа (checkout)
    public function handle_new_order($order_id) {
        try {
            if (!get_option('tablecrm_complete_enable_orders', 1)) {
                return;
            }

            $order = wc_get_order($order_id);
            if (!$order) { return; }

            $trigger_statuses = get_option('tablecrm_complete_order_statuses', array('processing', 'completed'));
            if (!in_array($order->get_status(), $trigger_statuses)) {
                return; // статус не триггерный
            }

            // Если уже отправлен или запланирован — выходим
            if (get_post_meta($order_id, '_tablecrm_complete_document_id', true) || wp_next_scheduled('tablecrm_complete_schedule_send', array($order_id))) {
                return;
            }

            // Планируем отправку через cron
            wp_schedule_single_event(time() + 10, 'tablecrm_complete_schedule_send', array($order_id));
            $this->log_info("Отправка нового заказа {$order_id} запланирована в фоне через cron.");

        } catch (Exception $e) {
            $this->log_error('КРИТИЧЕСКАЯ ОШИБКА в handle_new_order: ' . $e->getMessage() . ' в ' . $e->getFile() . ':' . $e->getLine());
        }
    }
}

// Инициализация плагина
function initialize_tablecrm_integration_complete() {
    global $tablecrm_integration_complete;
    $tablecrm_integration_complete = new TableCRM_Integration_Complete();
}
add_action('plugins_loaded', 'initialize_tablecrm_integration_complete'); 
</rewritten_file>