#!/bin/bash

# ========================================
# СКРИПТ ОБНОВЛЕНИЯ TableCRM Integration
# До исправленной версии с валидацией данных
# ========================================

echo "🔧 ОБНОВЛЕНИЕ ПЛАГИНА TableCRM Integration до исправленной версии"
echo "================================================================"

# Определяем рабочую директорию
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
echo "📁 Рабочая директория: $SCRIPT_DIR"

# Проверяем наличие исправленного файла плагина
FIXED_PLUGIN="$SCRIPT_DIR/tablecrm-integration-fixed-v2.php"
if [ ! -f "$FIXED_PLUGIN" ]; then
    echo "❌ ОШИБКА: Файл tablecrm-integration-fixed-v2.php не найден!"
    echo "   Убедитесь что файл находится в той же папке что и этот скрипт"
    exit 1
fi

echo "✅ Исправленный плагин найден: $FIXED_PLUGIN"

# Ищем WordPress в стандартных местах
WP_PATHS=(
    "/var/www/html"
    "/home/<USER>/public_html"
    "/var/www/wordpress"
    "/usr/share/wordpress"
    "$(find /var/www -name "wp-config.php" -type f -exec dirname {} \; 2>/dev/null | head -1)"
)

WP_ROOT=""
for path in "${WP_PATHS[@]}"; do
    if [ -f "$path/wp-config.php" ]; then
        WP_ROOT="$path"
        break
    fi
done

if [ -z "$WP_ROOT" ]; then
    echo "❌ ОШИБКА: WordPress не найден в стандартных местах"
    echo "   Укажите путь к WordPress вручную:"
    read -p "   Введите полный путь к корню WordPress: " WP_ROOT
    
    if [ ! -f "$WP_ROOT/wp-config.php" ]; then
        echo "❌ ОШИБКА: wp-config.php не найден в указанной директории"
        exit 1
    fi
fi

echo "✅ WordPress найден: $WP_ROOT"

# Определяем директорию плагинов
PLUGINS_DIR="$WP_ROOT/wp-content/plugins"
if [ ! -d "$PLUGINS_DIR" ]; then
    echo "❌ ОШИБКА: Директория плагинов не найдена: $PLUGINS_DIR"
    exit 1
fi

echo "✅ Директория плагинов: $PLUGINS_DIR"

# Копируем исправленный плагин
echo ""
echo "📋 КОПИРОВАНИЕ ИСПРАВЛЕННОГО ПЛАГИНА..."
echo "========================================="

cp "$FIXED_PLUGIN" "$PLUGINS_DIR/" 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✅ Исправленный плагин скопирован в: $PLUGINS_DIR/tablecrm-integration-fixed-v2.php"
else
    echo "❌ ОШИБКА: Не удалось скопировать плагин. Возможно нужны права администратора"
    echo "   Попробуйте выполнить команду с sudo:"
    echo "   sudo cp \"$FIXED_PLUGIN\" \"$PLUGINS_DIR/\""
    exit 1
fi

# Проверяем текущие плагины TableCRM
echo ""
echo "📋 АНАЛИЗ ТЕКУЩИХ ПЛАГИНОВ TableCRM..."
echo "======================================"

OLD_PLUGINS=$(find "$PLUGINS_DIR" -name "*tablecrm*" -type f | grep -v "fixed-v2")
if [ ! -z "$OLD_PLUGINS" ]; then
    echo "🔍 Найдены старые плагины TableCRM:"
    echo "$OLD_PLUGINS"
    echo ""
    echo "⚠️  ВНИМАНИЕ: Старые плагины не удалены автоматически"
    echo "   Рекомендуется деактивировать их через админ-панель WordPress"
else
    echo "✅ Старые плагины TableCRM не найдены"
fi

# Проверяем права доступа
echo ""
echo "📋 ПРОВЕРКА ПРАВ ДОСТУПА..."
echo "=========================="

if [ -r "$PLUGINS_DIR/tablecrm-integration-fixed-v2.php" ]; then
    echo "✅ Файл плагина доступен для чтения"
else
    echo "❌ ПРЕДУПРЕЖДЕНИЕ: Файл плагина недоступен для чтения"
fi

# Показываем следующие шаги
echo ""
echo "🎯 СЛЕДУЮЩИЕ ШАГИ:"
echo "=================="
echo "1. 🌐 Зайдите в админ-панель WordPress"
echo "2. 🔧 Перейдите в: Плагины → Установленные плагины"
echo "3. ❌ Деактивируйте старый плагин 'TableCRM Integration' (если активен)"
echo "4. ✅ Активируйте новый плагин 'TableCRM Integration (Fixed v2)'"
echo "5. ⚙️  Перейдите в: Настройки → TableCRM Integration (Fixed v2)"
echo "6. 🔑 Введите настройки API:"
echo "   - API URL: https://app.tablecrm.com/api/v1/"
echo "   - API Key: [ваш ключ]"
echo "   - Organization ID: 38"
echo "   - ✅ ВКЛЮЧИТЕ: 'Отправлять только реальные данные'"
echo "7. 🧪 Нажмите 'Тестировать соединение (Fixed)'"
echo "8. 📝 Проверьте логи в debug.log на наличие маркировки 'FIXED v2'"

echo ""
echo "📄 ПОДРОБНАЯ ИНСТРУКЦИЯ:"
echo "======================="
echo "Смотрите файл: ИНСТРУКЦИЯ_ОБНОВЛЕНИЯ_ПЛАГИНА.md"

echo ""
echo "✅ ОБНОВЛЕНИЕ ЗАВЕРШЕНО!"
echo "======================="
echo "Исправленный плагин установлен: $PLUGINS_DIR/tablecrm-integration-fixed-v2.php"
echo ""
echo "🚨 ВАЖНО: Активируйте плагин через админ-панель WordPress!" 