# Check logs for delivery issues
$localFile = "check_logs.php"
$remoteFile = "/home/<USER>/bekflom3/mosbuketik.ru/public_html/check_logs.php"
$server = "bekflom3.beget.tech"
$username = "bekflom3"

Write-Host "=== CHECKING LOGS FOR DELIVERY ISSUES ===" -ForegroundColor Green
Write-Host "Password: zl2Shj!e&6ng" -ForegroundColor Yellow

# Upload log checker
Write-Host "Uploading log checker script..." -ForegroundColor Cyan
scp $localFile "${username}@${server}:${remoteFile}"

if ($LASTEXITCODE -eq 0) {
    Write-Host "Log checker uploaded successfully" -ForegroundColor Green
    Write-Host ""
    Write-Host "Now running log check on server..." -ForegroundColor Yellow
    Write-Host "Password: zl2Shj!e&6ng" -ForegroundColor Yellow
    
    # Run the script on server
    ssh "${username}@${server}" "cd /home/<USER>/bekflom3/mosbuketik.ru/public_html && php check_logs.php"
    
} else {
    Write-Host "Error uploading log checker" -ForegroundColor Red
}

Write-Host "=== COMPLETED ===" -ForegroundColor Green 