<?php
/**
 * Диагностика проблемы со статусом оплаты и скидками в TableCRM
 * Анализ последних заказов и отправляемых данных
 */

// Подключение WordPress
if (!defined('ABSPATH')) {
    require_once(__DIR__ . '/../../../../wp-config.php');
}

if (!function_exists('wc_get_orders')) {
    echo "❌ WooCommerce не активен или не установлен\n";
    exit;
}

echo "🔍 ДИАГНОСТИКА ПРОБЛЕМЫ ОПЛАТЫ И СКИДОК\n";
echo "=====================================\n\n";

// 1. Проверяем настройки плагина
echo "1. НАСТРОЙКИ ПЛАГИНА:\n";
echo "---------------------\n";
$paid_statuses = get_option('tablecrm_complete_paid_statuses', array('completed', 'processing'));
$order_statuses = get_option('tablecrm_complete_order_statuses', array('processing', 'completed'));

echo "Оплаченные статусы: " . implode(', ', $paid_statuses) . "\n";
echo "Статусы для отправки: " . implode(', ', $order_statuses) . "\n\n";

// 2. Анализируем последние заказы
echo "2. АНАЛИЗ ПОСЛЕДНИХ ЗАКАЗОВ:\n";
echo "----------------------------\n";

$recent_orders = wc_get_orders(array(
    'limit' => 5,
    'orderby' => 'date',
    'order' => 'DESC',
    'return' => 'objects'
));

foreach ($recent_orders as $order) {
    $order_id = $order->get_id();
    $order_status = $order->get_status();
    $order_total = $order->get_total();
    
    echo "Заказ #$order_id:\n";
    echo "  Статус: $order_status\n";
    echo "  Сумма: $order_total\n";
    
    // Проверяем логику оплаты
    $is_paid = in_array($order_status, $paid_statuses);
    $paid_amount = $is_paid ? (float) $order_total : 0;
    
    echo "  Считается оплаченным: " . ($is_paid ? 'ДА' : 'НЕТ') . "\n";
    echo "  Сумма к оплате в TableCRM: $paid_amount\n";
    
    // Анализируем товары и скидки
    $items = $order->get_items();
    echo "  Товары:\n";
    
    foreach ($items as $item_id => $item) {
        $item_name = $item->get_name();
        $quantity = $item->get_quantity();
        
        // Базовая цена (без скидок)
        $base_price = (float) $order->get_item_total($item, false, true);
        // Цена со скидкой
        $discounted_price = (float) $order->get_item_total($item, false, false);
        
        $discount_per_item = $base_price - $discounted_price;
        $total_discount = $discount_per_item * $quantity;
        
        echo "    - $item_name (x$quantity):\n";
        echo "      Базовая цена: $base_price\n";
        echo "      Цена со скидкой: $discounted_price\n";
        echo "      Скидка на единицу: $discount_per_item\n";
        echo "      Общая скидка: $total_discount\n";
    }
    
    echo "\n";
}

// 3. Проверяем что отправляется в TableCRM
echo "3. СИМУЛЯЦИЯ ДАННЫХ ДЛЯ TABLECRM:\n";
echo "---------------------------------\n";

if (!empty($recent_orders)) {
    $test_order = $recent_orders[0];
    $order_id = $test_order->get_id();
    
    echo "Тестируем заказ #$order_id:\n\n";
    
    // Воссоздаем логику из плагина
    $order_status = $test_order->get_status();
    $is_paid = in_array($order_status, $paid_statuses);
    $paid_amount = $is_paid ? (float) $test_order->get_total() : 0;
    
    $goods = array();
    $items = $test_order->get_items();
    
    foreach ($items as $item_id => $item) {
        $product = $item->get_product();
        $item_name = $item->get_name();
        $quantity = $item->get_quantity();
        
        // Расчет как в плагине
        $base_price = (float) $test_order->get_item_total($item, false, true);
        $discounted_price = (float) $test_order->get_item_total($item, false, false);
        $discount_amount = ($base_price - $discounted_price) * $quantity;
        $total_discounted = $discounted_price * $quantity;
        
        $goods[] = array(
            'name' => $item_name,
            'price' => number_format($base_price, 2, '.', ''),
            'quantity' => $quantity,
            'discount' => number_format($discount_amount, 2, '.', ''),
            'sum_discounted' => number_format($total_discounted, 2, '.', ''),
            'sku' => $product ? $product->get_sku() : '',
        );
    }
    
    $final_data = array(
        'status' => $is_paid,
        'paid_rubles' => $is_paid ? number_format($paid_amount, 2, '.', '') : '0.00',
        'goods' => $goods
    );
    
    echo "JSON для отправки в TableCRM:\n";
    echo json_encode($final_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";
}

// 4. Проверяем логи плагина
echo "4. ПОСЛЕДНИЕ ЛОГИ ПЛАГИНА:\n";
echo "--------------------------\n";

$log_file = WP_CONTENT_DIR . '/uploads/tablecrm-complete.log';
if (file_exists($log_file)) {
    $log_content = file_get_contents($log_file);
    $log_lines = explode("\n", $log_content);
    
    // Показываем последние 20 строк
    $recent_logs = array_slice($log_lines, -20);
    foreach ($recent_logs as $line) {
        if (trim($line)) {
            echo $line . "\n";
        }
    }
} else {
    echo "Файл логов не найден: $log_file\n";
}

echo "\n5. РЕКОМЕНДАЦИИ:\n";
echo "----------------\n";
echo "1. Проверьте настройки 'Оплаченные статусы' в админ-панели\n";
echo "2. Убедитесь что плагин использует исправленную версию\n";
echo "3. Создайте тестовый заказ со статусом 'pending' и проверьте результат\n";
echo "4. Проверьте логи выше на наличие ошибок\n";

echo "\n✅ Диагностика завершена\n";
?> 