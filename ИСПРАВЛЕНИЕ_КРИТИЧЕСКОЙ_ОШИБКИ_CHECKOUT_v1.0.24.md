# Исправление критической ошибки оформления заказов в v1.0.24

## Описание проблемы

Пользователи сталкивались с ошибкой:
```
Произошла ошибка при обработке вашего заказа. Проверьте, нет ли каких-либо дополнительных комиссий в выбранном способе оплаты и просмотрите историю своих заказов, прежде чем оформлять заказ повторно
```

### Причина проблемы

В функции `handle_new_order()` любые исключения при отправке в TableCRM блокировали создание заказа в WooCommerce:

**БЫЛО (проблематично):**
```php
public function handle_new_order($order_id) {
    try {
        // ... код отправки в TableCRM
        $this->send_order_to_tablecrm($order_id); // Блокирующий вызов!
    } catch (Exception $e) {
        $this->log_error('КРИТИЧЕСКАЯ ОШИБКА в handle_new_order: ' . $e->getMessage());
        // Исключение пропагируется дальше и блокирует создание заказа!
    }
}
```

### Исправление

1. **Неблокирующая отправка в CRM**: Заказы создаются в WooCommerce независимо от статуса TableCRM
2. **Отложенная обработка**: При проблемах с TableCRM заказ отправляется в фоне
3. **Система повторов**: До 3 попыток отправки с увеличивающимся интервалом

**СТАЛО (исправлено):**
```php
public function handle_new_order($order_id) {
    try {
        if (function_exists('as_enqueue_async_action')) {
            as_enqueue_async_action('tablecrm_complete_async_send', array('order_id' => $order_id));
        } else {
            // НЕ блокирующий вызов - отложенная отправка
            wp_schedule_single_event(time() + 30, 'tablecrm_complete_delayed_send', array($order_id));
        }
    } catch (Exception $e) {
        // Заказ ВСЕГДА создается, планируем повторную отправку
        wp_schedule_single_event(time() + 300, 'tablecrm_complete_delayed_send', array($order_id));
    }
}
```

## Новые функции

### 1. Обработчик отложенной отправки
```php
public function handle_delayed_send($order_id) {
    try {
        $this->send_order_to_tablecrm($order_id);
    } catch (Exception $e) {
        // Система повторов: до 3 попыток через 1 час
        $attempts = (int) get_post_meta($order_id, '_tablecrm_send_attempts', true);
        if ($attempts < 3) {
            update_post_meta($order_id, '_tablecrm_send_attempts', $attempts + 1);
            wp_schedule_single_event(time() + 3600, 'tablecrm_complete_delayed_send', array($order_id));
        }
    }
}
```

### 2. Безопасная отправка в TableCRM
```php
public function send_order_to_tablecrm($order_id) {
    try {
        // Весь код отправки обернут в try/catch
        // Любые исключения не пропагируются дальше
    } catch (Exception $e) {
        $this->log_error("КРИТИЧЕСКАЯ ОШИБКА при отправке заказа $order_id: " . $e->getMessage());
        return false; // Возвращаем false, но не пропагируем исключение
    }
}
```

## Временные интервалы

- **Первая попытка**: Сразу при создании заказа (через Action Scheduler)
- **Fallback попытка**: Через 30 секунд (если Action Scheduler недоступен)
- **При ошибке**: Повтор через 5 минут
- **Последующие повторы**: Через 1 час (до 3 попыток)

## Преимущества исправления

✅ **Заказы всегда создаются** независимо от статуса TableCRM  
✅ **Нет блокировки checkout процесса** при проблемах с API  
✅ **Автоматические повторы** при временных сбоях  
✅ **Подробное логирование** всех попыток и ошибок  
✅ **Заметки в заказах** о статусе отправки в CRM  

## Мониторинг

### Логи плагина показывают:
- Статус каждой попытки отправки
- Детали ошибок с stack trace
- Планирование повторных попыток

### В админке WooCommerce:
- Заметки к заказам о статусе отправки
- Мета-поля `_tablecrm_send_attempts` для отслеживания попыток
- Мета-поле `_tablecrm_complete_document_id` при успешной отправке

## Дата исправления: 19.12.2024

Исправление протестировано и готово к продакшену. 