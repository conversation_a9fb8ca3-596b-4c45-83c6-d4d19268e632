<?php
// Проверка безопасности
if (!defined('ABSPATH')) {
    exit;
}

// Проверяем права доступа
if (!current_user_can('manage_options')) {
    wp_die('Доступ запрещен');
}

// Получаем последний заказ
$orders = wc_get_orders(array(
    'limit' => 1,
    'orderby' => 'date',
    'order' => 'DESC',
));

if (empty($orders)) {
    echo '<div class="error"><p>Заказы не найдены</p></div>';
    return;
}

$order = $orders[0];
$order_id = $order->get_id();

echo '<div class="wrap">';
echo '<h2>Тест интеграции TableCRM - Информация о доставке</h2>';
echo '<div class="notice notice-info">';

// Тестовый вывод
echo "<h3>Тестирование заказа #{$order_id}</h3>";

// Данные заказчика
echo "<h4>Данные заказчика:</h4>";
echo "<ul>";
echo "<li>Имя заказчика: " . esc_html($order->get_billing_first_name() . ' ' . $order->get_billing_last_name()) . "</li>";
echo "<li>Телефон заказчика: " . esc_html($order->get_billing_phone()) . "</li>";
echo "</ul>";

// Данные получателя
echo "<h4>Данные получателя:</h4>";
echo "<ul>";
echo "<li>Имя получателя: " . esc_html($order->get_shipping_first_name() . ' ' . $order->get_shipping_last_name()) . "</li>";
echo "<li>Телефон получателя (get_shipping_phone): " . esc_html($order->get_shipping_phone()) . "</li>";
echo "<li>Телефон получателя (мета shipping_phone): " . esc_html($order->get_meta('shipping_phone')) . "</li>";
echo "<li>Телефон получателя (мета _shipping_phone): " . esc_html($order->get_meta('_shipping_phone')) . "</li>";
echo "</ul>";

// Адрес доставки
echo "<h4>Адрес доставки:</h4>";
echo "<ul>";

// Стандартные поля адреса доставки
echo "<li><strong>Стандартные поля доставки WooCommerce:</strong></li>";
$shipping_fields = array(
    'first_name' => 'Имя',
    'last_name' => 'Фамилия',
    'company' => 'Компания',
    'address_1' => 'Адрес 1',
    'address_2' => 'Адрес 2',
    'city' => 'Город',
    'state' => 'Область',
    'postcode' => 'Индекс',
    'country' => 'Страна'
);

foreach ($shipping_fields as $field => $label) {
    $method = 'get_shipping_' . $field;
    $value = $order->$method();
    echo "<li>$label: " . esc_html($value) . "</li>";
}

// Биллинг адрес как запасной вариант
echo "<li><strong>Адрес оплаты (биллинг):</strong></li>";
$billing_fields = array(
    'first_name' => 'Имя',
    'last_name' => 'Фамилия',
    'company' => 'Компания',
    'address_1' => 'Адрес 1',
    'address_2' => 'Адрес 2',
    'city' => 'Город',
    'state' => 'Область',
    'postcode' => 'Индекс',
    'country' => 'Страна'
);

foreach ($billing_fields as $field => $label) {
    $method = 'get_billing_' . $field;
    $value = $order->$method();
    echo "<li>$label: " . esc_html($value) . "</li>";
}

// Форматированные адреса
echo "<li><strong>Форматированный адрес доставки:</strong> " . esc_html($order->get_formatted_shipping_address()) . "</li>";
echo "<li><strong>Форматированный адрес оплаты:</strong> " . esc_html($order->get_formatted_billing_address()) . "</li>";

// Проверяем все мета-поля с адресом
echo "<li><strong>Поиск адреса в мета-полях:</strong></li>";
$meta_data = $order->get_meta_data();
foreach ($meta_data as $meta) {
    $data = $meta->get_data();
    $key = strtolower($data['key']);
    if (strpos($key, 'address') !== false || 
        strpos($key, 'город') !== false || 
        strpos($key, 'улица') !== false || 
        strpos($key, 'дом') !== false) {
        echo "<li>" . esc_html($data['key']) . ": " . esc_html(print_r($data['value'], true)) . "</li>";
    }
}

// Адрес из API
echo "<li><strong style='color: blue;'>Адрес в API:</strong> г Казань, ул Зайцева, д 10</li>";

echo "</ul>";

// Информация о доставке
echo "<h4>Информация о доставке:</h4>";
echo "<ul>";
echo "<li><strong>Способ доставки:</strong> " . esc_html($order->get_shipping_method()) . "</li>";
echo "<li><strong>Стоимость доставки:</strong> " . esc_html($order->get_shipping_total()) . " руб.</li>";
echo "<li><strong>Дата доставки:</strong> " . esc_html($order->get_meta('delivery_date')) . "</li>";
echo "<li><strong>Время доставки:</strong> " . esc_html($order->get_meta('delivery_time')) . "</li>";
echo "<li><strong>Тип доставки:</strong> " . esc_html($order->get_meta('delivery_type')) . "</li>";
echo "<li><strong>Timestamp доставки:</strong> " . esc_html($order->get_meta('delivery_details_timestamp')) . "</li>";
echo "</ul>";

// Все мета-данные заказа
echo "<h4>Все мета-данные заказа:</h4>";
echo "<div style='max-height: 200px; overflow-y: auto; background: #f5f5f5; padding: 10px; margin: 10px 0;'>";
echo "<pre>";
foreach ($meta_data as $meta) {
    $data = $meta->get_data();
    echo esc_html($data['key']) . ": " . esc_html(print_r($data['value'], true)) . "\n";
}
echo "</pre>";
echo "</div>";

// Дополнительная информация
echo "<h4>Дополнительная информация:</h4>";
echo "<ul>";
echo "<li>Способ доставки: " . esc_html($order->get_shipping_method()) . "</li>";
echo "<li>Стоимость доставки: " . esc_html($order->get_shipping_total()) . " руб.</li>";

// Проверяем все возможные поля времени доставки
$delivery_time = '';
$time_meta_fields = array(
    'delivery_time',
    '_delivery_time',
    'shipping_time',
    '_shipping_time'
);

foreach ($time_meta_fields as $meta_key) {
    $time_value = $order->get_meta($meta_key);
    if (!empty($time_value)) {
        $delivery_time = $time_value;
        break;
    }
}

echo "<li>Время доставки: " . esc_html($delivery_time) . "</li>";

// Проверяем все возможные поля типа доставки
$delivery_type = '';
$type_meta_fields = array(
    'delivery_type',
    '_delivery_type',
    'shipping_type',
    '_shipping_type'
);

foreach ($type_meta_fields as $meta_key) {
    $type_value = $order->get_meta($meta_key);
    if (!empty($type_value)) {
        $delivery_type = $type_value;
        break;
    }
}

echo "<li>Тип доставки: " . esc_html($delivery_type) . "</li>";
echo "<li>Комментарий клиента: " . esc_html($order->get_customer_note()) . "</li>";
echo "</ul>";

echo "<h4>Отправка данных в TableCRM...</h4>";

// Получаем последние 15 записей лога
$log_file = WP_CONTENT_DIR . '/uploads/tablecrm_integration_complete.log';
if (file_exists($log_file)) {
    $logs = file($log_file);
    $logs = array_slice($logs, -15);
    echo "<h4>Последние записи лога:</h4>";
    echo "<pre>";
    foreach ($logs as $log) {
        echo esc_html($log);
    }
    echo "</pre>";
}

echo '</div></div>';

// Отправляем данные
global $tablecrm_integration_complete;
if ($tablecrm_integration_complete) {
    $document_id = get_post_meta($order_id, '_tablecrm_complete_document_id', true);
    if ($document_id) {
        $result = $tablecrm_integration_complete->send_delivery_info($document_id, $order);
        if ($result) {
            echo '<div class="notice notice-success"><p>Отправка успешна!</p></div>';
        } else {
            echo '<div class="notice notice-error"><p>Ошибка при отправке данных.</p></div>';
        }
    } else {
        echo '<div class="notice notice-error"><p>Document ID не найден для заказа.</p></div>';
    }
} 