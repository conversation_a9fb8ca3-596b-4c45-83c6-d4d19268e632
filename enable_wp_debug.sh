#!/bin/bash

echo "🔧 Включение логирования WordPress"
echo "=================================="

echo "📡 Подключение к серверу для настройки логирования..."

# Создаем временный файл с настройками отладки
cat > /tmp/wp_debug_settings.txt << 'EOF'
// === НАСТРОЙКИ ОТЛАДКИ TABLECRM ===
// Включение отладки WordPress
define('WP_DEBUG', true);

// Включение записи в файл логов
define('WP_DEBUG_LOG', true);

// Отключение показа ошибок на сайте (безопасность)
define('WP_DEBUG_DISPLAY', false);

// Скрытие ошибок от посетителей
@ini_set('display_errors', 0);

// Логирование ошибок базы данных
define('WP_DEBUG_LOG_DB', true);

// Логирование скриптов
define('SCRIPT_DEBUG', true);

// === КОНЕЦ НАСТРОЕК ОТЛАДКИ ===
EOF

echo "🔄 Скачиваем текущий wp-config.php..."

# Скачиваем wp-config.php
lftp -u bekflom3_wz,zl2Shj!e%266ng -e "
set ssl:verify-certificate no;
cd public_html;
get wp-config.php /tmp/wp-config-original.php;
quit
" bekflom3.beget.tech

if [ -f /tmp/wp-config-original.php ]; then
    echo "✅ wp-config.php скачан успешно"
    
    # Проверяем, есть ли уже настройки отладки
    if grep -q "WP_DEBUG" /tmp/wp-config-original.php; then
        echo "⚠️ Настройки WP_DEBUG уже существуют"
        echo "📋 Текущие настройки:"
        grep -i "debug\|log" /tmp/wp-config-original.php
        
        echo ""
        echo "🔄 Обновляем существующие настройки..."
        
        # Создаем новый wp-config.php с обновленными настройками
        sed '/WP_DEBUG/d' /tmp/wp-config-original.php > /tmp/wp-config-new.php
        sed '/WP_DEBUG_LOG/d' /tmp/wp-config-new.php > /tmp/wp-config-temp.php
        mv /tmp/wp-config-temp.php /tmp/wp-config-new.php
        
        # Добавляем новые настройки перед "That's all"
        sed '/That.s all, stop editing/i\
// === НАСТРОЙКИ ОТЛАДКИ TABLECRM ===\
define('\''WP_DEBUG'\'', true);\
define('\''WP_DEBUG_LOG'\'', true);\
define('\''WP_DEBUG_DISPLAY'\'', false);\
@ini_set('\''display_errors'\'', 0);\
define('\''WP_DEBUG_LOG_DB'\'', true);\
define('\''SCRIPT_DEBUG'\'', true);\
// === КОНЕЦ НАСТРОЕК ОТЛАДКИ ===\
' /tmp/wp-config-new.php > /tmp/wp-config-final.php
        
    else
        echo "📝 Добавляем новые настройки отладки..."
        
        # Добавляем настройки отладки перед "That's all"
        sed '/That.s all, stop editing/i\
// === НАСТРОЙКИ ОТЛАДКИ TABLECRM ===\
define('\''WP_DEBUG'\'', true);\
define('\''WP_DEBUG_LOG'\'', true);\
define('\''WP_DEBUG_DISPLAY'\'', false);\
@ini_set('\''display_errors'\'', 0);\
define('\''WP_DEBUG_LOG_DB'\'', true);\
define('\''SCRIPT_DEBUG'\'', true);\
// === КОНЕЦ НАСТРОЕК ОТЛАДКИ ===\
' /tmp/wp-config-original.php > /tmp/wp-config-final.php
    fi
    
    echo "🚀 Загружаем обновленный wp-config.php на сервер..."
    
    # Загружаем обновленный файл
    lftp -u bekflom3_wz,zl2Shj!e%266ng -e "
    set ssl:verify-certificate no;
    cd public_html;
    put /tmp/wp-config-final.php wp-config.php;
    quit
    " bekflom3.beget.tech
    
    echo "✅ wp-config.php обновлен!"
    
    echo ""
    echo "📋 Проверяем результат..."
    
    # Проверяем результат
    lftp -u bekflom3_wz,zl2Shj!e%266ng -e "
    set ssl:verify-certificate no;
    cd public_html;
    get wp-config.php /tmp/wp-config-check.php;
    quit
    " bekflom3.beget.tech
    
    if [ -f /tmp/wp-config-check.php ]; then
        echo "🔍 Новые настройки отладки:"
        grep -A 10 -B 2 "НАСТРОЙКИ ОТЛАДКИ TABLECRM" /tmp/wp-config-check.php || grep -i "debug" /tmp/wp-config-check.php
    fi
    
else
    echo "❌ Не удалось скачать wp-config.php"
    echo "📞 Выполните настройку вручную:"
    echo ""
    echo "1. Подключитесь: sftp <EMAIL>"
    echo "2. Скачайте: get public_html/wp-config.php"
    echo "3. Добавьте перед строкой '/* That's all, stop editing! */':"
    echo ""
    cat /tmp/wp_debug_settings.txt
    echo ""
    echo "4. Загрузите обратно: put wp-config.php public_html/wp-config.php"
fi

# Очищаем временные файлы
rm -f /tmp/wp_debug_settings.txt /tmp/wp-config-*.php

echo ""
echo "✅ Настройка логирования завершена!"
echo ""
echo "📋 Следующие шаги:"
echo "1. Создайте тестовый заказ на сайте"
echo "2. Проверьте появление debug.log: ls wp-content/debug.log"
echo "3. Запустите: ./logs_manual_check.sh для проверки логов" 