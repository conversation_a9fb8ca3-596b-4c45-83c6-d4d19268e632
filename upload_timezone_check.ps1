# Upload and run timezone check
$server = "bekflom3.beget.tech"
$user = "bekflom3_wz"
$pass = "zl2Shj!e&6ng"

Write-Host "Uploading timezone check script..." -ForegroundColor Green

try {
    $uri = "ftp://$server/public_html/check_timezone.php"
    $webclient = New-Object System.Net.WebClient
    $webclient.Credentials = New-Object System.Net.NetworkCredential($user, $pass)
    $webclient.UploadFile($uri, "check_timezone.php")
    Write-Host "Timezone check script uploaded!" -ForegroundColor Green
    
    # Run the check
    Write-Host "Running timezone check..." -ForegroundColor Yellow
    $result = Invoke-WebRequest -Uri "https://mosbuketik.ru/check_timezone.php" -UseBasicParsing
    Write-Host $result.Content -ForegroundColor White
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

$webclient.Dispose()
Write-Host "Timezone check completed!" -ForegroundColor Green 