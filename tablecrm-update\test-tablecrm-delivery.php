<?php
// Проверка безопасности
if (!defined('ABSPATH')) {
    require_once(dirname(dirname(dirname(__FILE__))) . '/wp-load.php');
}

// Проверка прав доступа
if (!current_user_can('manage_options')) {
    wp_die('Доступ запрещен');
}

// Проверка nonce для безопасности
if (!isset($_GET['_wpnonce']) || !wp_verify_nonce($_GET['_wpnonce'], 'test_tablecrm_delivery')) {
    wp_die('Проверка безопасности не пройдена');
}

// Получаем последний заказ
$orders = wc_get_orders(array(
    'limit' => 1,
    'orderby' => 'date',
    'order' => 'DESC',
));

if (empty($orders)) {
    wp_die("Заказы не найдены.");
}

$order = $orders[0];
$order_id = $order->get_id();

// Заголовок страницы
echo '<div class="wrap">';
echo '<h1>Тест интеграции TableCRM - Информация о доставке</h1>';

// Данные заказа
echo '<div style="background: #fff; padding: 20px; margin: 20px 0; border: 1px solid #ccc;">';
echo "<h2>Тестирование заказа #{$order_id}</h2>";

// Данные заказчика
echo '<h3>Данные заказчика:</h3>';
echo '<ul>';
echo '<li><strong>Имя заказчика:</strong> ' . esc_html($order->get_billing_first_name() . ' ' . $order->get_billing_last_name()) . '</li>';
echo '<li><strong>Телефон заказчика:</strong> ' . esc_html($order->get_billing_phone()) . '</li>';
echo '</ul>';

// Данные получателя
echo '<h3>Данные получателя:</h3>';
echo '<ul>';
echo '<li><strong>Имя получателя:</strong> ' . esc_html($order->get_shipping_first_name() . ' ' . $order->get_shipping_last_name()) . '</li>';
echo '<li><strong>Телефон получателя (get_shipping_phone):</strong> ' . esc_html($order->get_shipping_phone()) . '</li>';
echo '<li><strong>Телефон получателя (мета shipping_phone):</strong> ' . esc_html($order->get_meta('shipping_phone')) . '</li>';
echo '<li><strong>Телефон получателя (мета _shipping_phone):</strong> ' . esc_html($order->get_meta('_shipping_phone')) . '</li>';
echo '</ul>';

// Адрес доставки
echo '<h3>Адрес доставки:</h3>';
$shipping_address = array();
if ($order->get_shipping_address_1()) $shipping_address[] = $order->get_shipping_address_1();
if ($order->get_shipping_address_2()) $shipping_address[] = $order->get_shipping_address_2();
if ($order->get_shipping_city()) $shipping_address[] = $order->get_shipping_city();
if ($order->get_shipping_state()) $shipping_address[] = $order->get_shipping_state();
if ($order->get_shipping_postcode()) $shipping_address[] = $order->get_shipping_postcode();

echo '<p><strong>Полный адрес:</strong> ' . esc_html(implode(", ", $shipping_address)) . '</p>';
echo '</div>';

// Инициализируем интеграцию
$integration = new TableCRM_Integration_Complete();

// Получаем Document ID
$document_id = get_post_meta($order_id, '_tablecrm_complete_document_id', true);
if (empty($document_id)) {
    echo '<div class="notice notice-error"><p>Document ID не найден для заказа. Сначала создайте заказ в TableCRM.</p></div>';
    echo '</div>';
    return;
}

echo '<div class="notice notice-info"><p>Document ID: ' . esc_html($document_id) . '</p></div>';

// Тестируем отправку
echo '<h3>Отправка данных в TableCRM...</h3>';
$result = $integration->send_delivery_info($document_id, $order);

if ($result) {
    echo '<div class="notice notice-success"><p>Отправка успешна!</p></div>';
} else {
    echo '<div class="notice notice-error"><p>Ошибка при отправке</p></div>';
}

// Проверяем лог
$log_file = WP_CONTENT_DIR . '/uploads/tablecrm_integration_complete.log';
if (file_exists($log_file)) {
    echo '<h3>Последние записи лога:</h3>';
    echo '<div style="background: #f8f9fa; padding: 15px; border: 1px solid #ddd; max-height: 400px; overflow-y: auto;">';
    $log_content = file_get_contents($log_file);
    $lines = array_slice(explode("\n", $log_content), -20); // последние 20 строк
    foreach ($lines as $line) {
        echo esc_html($line) . '<br>';
    }
    echo '</div>';
}

echo '</div>'; // Закрываем .wrap 