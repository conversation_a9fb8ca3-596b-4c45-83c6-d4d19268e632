# ДИАГНОСТИКА ОШИБКИ 500 В TABLECRM

## Описание проблемы

При отправке заказов в TableCRM может возникать ошибка 500 (Internal Server Error), которая указывает на внутреннюю ошибку сервера CRM. Это означает, что запрос дошёл до сервера, но что-то в данных или настройках вызвало сбой на стороне TableCRM.

## Симптомы

```
[2025-06-27 09:20:28] [ERROR] API запрос неуспешен - Status: 500
[2025-06-27 09:20:28] [ERROR] Полное тело ответа: Internal Server Error
```

## Возможные причины

### 1. Некорректные ID в настройках
- **Warehouse ID** - неверный ID склада
- **Organization ID** - неверный ID организации  
- **Paybox ID** - неверный ID кассы

### 2. Проблемы с данными заказа
- Некорректные данные контрагента
- Проблемы с номенклатурой товаров
- Неправильный формат данных в payload

### 3. Проблемы с данными доставки
- Отсутствие обязательных полей получателя
- Некорректный формат телефона
- Проблемы с датой доставки

## Диагностика

### Шаг 1: Включение расширенного логирования

В версии v1.0.24 добавлена автоматическая диагностика ошибок 500. При возникновении ошибки в лог записывается:

```
[ERROR] КРИТИЧЕСКАЯ ОШИБКА 500: Внутренняя ошибка сервера TableCRM
[ERROR] URL запроса: https://app.tablecrm.com/api/v1/docs_sales/?token=...
[ERROR] Данные запроса: {детализированный JSON payload}
[ERROR] ПРОВЕРКА НАСТРОЕК:
[ERROR] Warehouse ID: 143
[ERROR] Organization ID: 144  
[ERROR] Paybox ID: 457
```

### Шаг 2: Проверка настроек

Убедитесь, что все ID корректны:

1. **Warehouse ID** - проверьте в TableCRM → Настройки → Склады
2. **Organization ID** - проверьте в TableCRM → Настройки → Организации
3. **Paybox ID** - проверьте в TableCRM → Настройки → Кассы

### Шаг 3: Анализ данных запроса

Из лога скопируйте JSON payload и проверьте:

```json
{
  "contragent": 338049,
  "organization": 144,
  "warehouse": 143,
  "paybox": 457,
  "goods": [
    {
      "nomenclature": 45838,
      "price": 7999,
      "qty": 1
    }
  ]
}
```

Проверьте:
- Существуют ли указанные ID в CRM
- Корректны ли типы данных (числа должны быть числами)
- Нет ли отрицательных значений в ценах/количестве

### Шаг 4: Тестирование по частям

Если проблема не очевидна, проведите пошаговое тестирование:

1. **Тест создания контрагента**:
   ```php
   // Создайте простой тестовый скрипт
   $test_data = array(
       'name' => 'Тест',
       'phone' => '+79999999999'
   );
   ```

2. **Тест создания номенклатуры**:
   ```php
   $test_nomenclature = array(
       'name' => 'Тестовый товар'
   );
   ```

3. **Тест минимального заказа**:
   ```php
   $minimal_order = array(
       'contragent' => 338049,
       'organization' => 144,
       'warehouse' => 143,
       'paybox' => 457,
       'goods' => array(
           array(
               'nomenclature' => 45838,
               'price' => 100,
               'qty' => 1
           )
       )
   );
   ```

## Решения

### Проблема с ID настроек

Если один из ID некорректен:

1. Войдите в админку TableCRM
2. Проверьте актуальные ID в соответствующих разделах
3. Обновите настройки плагина

### Проблема с данными заказа

1. **Контрагент не найден** - проверьте корректность телефона и имени
2. **Номенклатура не найдена** - убедитесь, что товары существуют в CRM
3. **Некорректные цены** - проверьте, что цены положительные и в правильном формате

### Проблема с доставкой

С версии v1.0.24 добавлены улучшения для данных доставки:

1. **Автоматический поиск имени получателя**:
   - Имя из адреса доставки
   - Имя из платёжного адреса  
   - Email как резерв
   - Плейсхолдер "Получатель"

2. **Нормализация телефона**:
   - Удаление всех символов кроме цифр
   - Приведение к формату +7XXXXXXXXXX

3. **Улучшенный парсинг даты доставки**

## Логи для анализа

Основной лог: `tablecrm_integration_complete.log` в папке плагина

Ключевые записи для поиска:
- `[ERROR] КРИТИЧЕСКАЯ ОШИБКА 500`
- `[ERROR] API запрос неуспешен - Status: 500`
- `[ERROR] ПРОВЕРКА НАСТРОЕК:`

### Ошибка 422 «value is not a valid list»

**Причина**  – в эндпоинт `docs_sales/` был отправлен ОДИН объект `{...}`, а API ожидает **список документов** `[ {...} ]`.

**Решение**  – верните массив:
```php
// должно быть array($document_data)
$final_data = array($document_data);
```
В плагине v1.0.24 это уже реализовано в методе `adapt_data_format()`, поэтому при повторной ошибке 422 проверьте кэш (**OPcache**).

### Ошибка 500 из-за типов данных

После исправления структуры возможна ошибка 500, если значения полей имеют неверный тип:

| Поле            | Было           | Стало (правильно) |
|-----------------|----------------|-------------------|
| `price`         | "7999.00"     | `7999.00` (float) |
| `shipping price`| "490.00"      | `490.00` (float)  |
| `status`        | `true/false`   | только если `$order->is_paid()` |
| `paid_rubles`   | всегда сумма   | передаётся **только** для оплаченных заказов |

Проверьте, что **все цены – числа**, а не строки, и поле `paid_rubles` отсутствует/=0 для неоплаченных заказов.

### Сброс OPcache (важно)

Любые изменения PHP-файлов применяются ТОЛЬКО после очистки кеша:
```bash
sudo systemctl restart php-fpm       # если есть root-доступ
# или временный скрипт в корне сайта
<?php opcache_reset(); echo 'OPcache flushed';
```
Удалите скрипт сразу после запуска.

## Контакты для поддержки

Если проблема не решается:

1. Соберите логи за последние попытки отправки
2. Укажите ID проблемного заказа
3. Приложите скриншот настроек плагина
4. Обратитесь в техподдержку TableCRM с собранной информацией

## История изменений

- **v1.0.24** - Добавлена автоматическая диагностика ошибок 500
- **v1.0.24** - Улучшена обработка данных доставки и получателя
- **v1.0.24** - Добавлена нормализация телефонов и резервные источники имён 