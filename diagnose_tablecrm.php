<?php
/**
 * diagnose_tablecrm.php
 *
 * Универсальный скрипт диагностики интеграции TableCRM ↔ WooCommerce.
 * Запускается как из CLI (wp eval-file diagnose_tablecrm.php), так и из браузера
 * (https://SITE/diagnose_tablecrm.php?secret_key=XXX).
 *
 * Скрипт выполняет:
 * 1. Проверку актуальности файла плагина (поиск строк и дата mtime).
 * 2. Сброс OPcache (если доступно) и проверку опций OPcache.
 * 3. Пинг API TableCRM /health.
 * 4. Поиск последнего «Request body» в логах плагина и повторную отправку.
 * 5. Вывод сводного отчёта.
 *
 * © 2025 AI Assistant
 */

// Засекаем время выполнения
define('DIAG_START', microtime(true));

// ---------- НАСТРОЙКИ ---------- //
$secret_key = 'CHANGE_ME'; // при запуске из браузера добавьте ?secret_key=CHANGE_ME
$expected_log_path = WP_CONTENT_DIR . '/uploads/tablecrm_integration_complete.log';
$expected_plugin_slug = 'tablecrm-integration-complete'; // часть пути плагина
$crm_api_url = 'https://app.tablecrm.com/api/v1/';

// ---------- БЕЗОПАСНОСТЬ ---------- //
$running_cli = (php_sapi_name() === 'cli');
if (!$running_cli) {
    // Защита от произвольного запуска
    if (!isset($_GET['secret_key']) || $_GET['secret_key'] !== $secret_key) {
        header('HTTP/1.1 403 Forbidden');
        echo 'Forbidden';
        exit;
    }
    // Для наглядности выводим plain-text
    header('Content-Type: text/plain; charset=utf-8');
}

// ---------- ПОДГРУЖАЕМ WORDPRESS ---------- //
$wp_load = __DIR__ . '/wp-load.php';
if (file_exists($wp_load)) {
    require_once $wp_load;
} else {
    report('FAIL', 'Не удалось найти wp-load.php, скрипт надо положить в корень WordPress.');
    exit(1);
}

// ---------- ВСПОМОГАТЕЛЬНЫЕ ФУНКЦИИ ---------- //
function colorize($text, $color) {
    global $running_cli;
    if (!$running_cli) return $text;
    $colors = [
        'green' => "\033[32m",
        'red'   => "\033[31m",
        'yellow'=> "\033[33m",
        'cyan'  => "\033[36m",
        'reset' => "\033[0m"
    ];
    return ($colors[$color] ?? '') . $text . $colors['reset'];
}

function report($status, $message) {
    $color = [
        'OK'   => 'green',
        'FAIL' => 'red',
        'WARN' => 'yellow',
        'INFO' => 'cyan'
    ][$status] ?? 'reset';
    echo '[' . colorize(str_pad($status, 5), $color) . '] ' . $message . PHP_EOL;
}

report('INFO', '===== TableCRM Diagnostic started ' . date('Y-m-d H:i:s') . ' =====');

// ---------- ШАГ 1. Поиск и проверка плагина ---------- //
$plugin_path = null;
$active_plugins = get_option('active_plugins', []);
foreach ($active_plugins as $plugin_rel) {
    if (strpos($plugin_rel, $expected_plugin_slug) !== false) {
        $plugin_path = ABSPATH . $plugin_rel;
        break;
    }
}
if (!$plugin_path || !file_exists($plugin_path)) {
    report('FAIL', 'Не удалось найти активный плагин ' . $expected_plugin_slug . '. Путь: ' . ($plugin_path ?: 'N/A'));
} else {
    report('INFO', 'Плагин найден: ' . $plugin_path);
    $content = file_get_contents($plugin_path);
    $mtime   = filemtime($plugin_path);
    report('INFO', 'Дата изменения: ' . date('Y-m-d H:i:s', $mtime));
    if (strpos($content, 'Request body:') !== false && strpos($content, 'Auto-invalidate OPcache') !== false) {
        report('OK', 'Файл содержит последние патчи (Request body / OPcache).');
    } else {
        report('FAIL', 'Файл не содержит нужных патчей – возможно, загружена старая версия.');
    }
}

// ---------- ШАГ 2. Проверка / сброс OPcache ---------- //
if (function_exists('opcache_reset')) {
    $reset = opcache_reset();
    report($reset ? 'OK' : 'FAIL', 'OPcache reset ' . ($reset ? 'успешен' : 'не удался'));
    if (function_exists('opcache_get_status')) {
        $st = opcache_get_status(false);
        $enabled = $st['opcache_enabled'] ?? null;
        report('INFO', 'OPcache enabled: ' . var_export($enabled, true));
    }
} else {
    report('WARN', 'Функция opcache_reset() недоступна.');
}

// ---------- ШАГ 3. Пинг API /health ---------- //
$api_token = get_option('tablecrm_complete_api_key');
if (empty($api_token)) {
    report('FAIL', 'API-токен не найден в настройках WordPress (tablecrm_complete_api_key).');
} else {
    $health_url = rtrim($crm_api_url, '/') . '/health?token=' . $api_token;
    $ch = curl_init($health_url);
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_CONNECTTIMEOUT => 10,
        CURLOPT_TIMEOUT        => 20,
        CURLOPT_SSL_VERIFYPEER => true,
    ]);
    curl_exec($ch);
    $code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    if ($code == 200) {
        report('OK', 'API /health вернул 200 – CRM доступна, токен валиден.');
    } else {
        report('FAIL', 'API /health вернул код ' . $code . ' – проверьте URL/токен.');
    }
}

// ---------- ШАГ 4. Последний Request body ---------- //
$log_path = file_exists($expected_log_path) ? $expected_log_path : null;
if (!$log_path) {
    report('FAIL', 'Лог-файл не найден: ' . $expected_log_path);
} else {
    report('INFO', 'Чтение лог-файла ' . $log_path);
    $lines = @file($log_path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    $last_body = null;
    if ($lines) {
        foreach (array_reverse($lines) as $l) {
            if (strpos($l, 'Request body:') !== false) {
                $last_body = trim(substr($l, strpos($l, 'Request body:') + 13));
                break;
            }
        }
    }
    if ($last_body) {
        report('OK', 'Найден последний Request body (' . strlen($last_body) . ' байт).');
        // отправляем повторно
        $send_url = rtrim($crm_api_url, '/') . '/docs_sales/?token=' . $api_token;
        $ch = curl_init($send_url);
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST           => true,
            CURLOPT_POSTFIELDS     => $last_body,
            CURLOPT_HTTPHEADER     => ['Content-Type: application/json'],
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_CONNECTTIMEOUT => 10,
            CURLOPT_TIMEOUT        => 30,
        ]);
        $resp = curl_exec($ch);
        $http  = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        report('INFO', 'Повторная отправка завершена. HTTP код: ' . $http);
        if ($http >= 200 && $http < 300) {
            report('OK', 'CRM приняла запрос – проблема, вероятно, в WordPress-хуках.');
        } elseif ($http >= 500) {
            report('FAIL', 'CRM вернула 5xx – проблема подтверждена на их стороне.');
        } else {
            report('WARN', 'CRM вернула код ' . $http . '. Ответ: ' . substr($resp, 0, 300));
        }
    } else {
        report('FAIL', 'Строка "Request body:" не найдена в логе.');
    }
}

report('INFO', '===== Диагностика завершена за ' . round(microtime(true) - DIAG_START, 2) . 'с ====='); 