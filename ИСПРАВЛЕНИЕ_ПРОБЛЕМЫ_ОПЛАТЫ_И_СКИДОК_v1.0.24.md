# Исправление проблемы оплаты и скидок в TableCRM Integration v1.0.24

## 🐛 Обнаруженная проблема

Заказы приходили в TableCRM с неправильными данными:
- **Статус "Оплачено"** для всех заказов независимо от реального статуса
- **Скидка 100%** из-за неправильного расчета
- **Сумма оплаты** равна полной стоимости заказа

## 🔧 Исправления

### 1. Исправлена логика статуса оплаты

**Было:**
```php
'status' => false,
'paid_rubles' => number_format((float) $order->get_total(), 2, '.', ''),
```

**Стало:**
```php
$order_status = $order->get_status();
$paid_statuses = get_option('tablecrm_complete_paid_statuses', array('completed', 'processing'));

if (in_array($order_status, $paid_statuses)) {
    $is_paid = true;
    $paid_amount = (float) $order->get_total();
}

'status' => $is_paid,
'paid_rubles' => $is_paid ? number_format($paid_amount, 2, '.', '') : '0.00',
```

### 2. Исправлен расчет скидок товаров

**Было:**
```php
$price = (float) $order->get_item_total($item, false, false);
'discount' => 0,
'sum_discounted' => number_format($price * $item->get_quantity(), 2, '.', ''),
```

**Стало:**
```php
// Получаем цену без учета скидок (базовую цену)
$base_price = (float) $order->get_item_total($item, false, true);
// Получаем цену с учетом скидок
$discounted_price = (float) $order->get_item_total($item, false, false);

$discount_amount = ($base_price - $discounted_price) * $quantity;

'price' => number_format($base_price, 2, '.', ''),
'discount' => number_format($discount_amount, 2, '.', ''),
'sum_discounted' => number_format($total_discounted, 2, '.', ''),
```

### 3. Добавлены новые настройки

Добавлено поле **"Оплаченные статусы"** в настройки плагина:
- Позволяет настроить какие статусы WooCommerce считаются оплаченными
- По умолчанию: `completed`, `processing`
- Влияет на поля `status` и `paid_rubles` в TableCRM

## ⚙️ Настройка плагина

1. Перейдите в **WordPress Admin → TableCRM Settings**
2. Найдите секцию **"Оплаченные статусы"**
3. Выберите статусы, которые должны считаться оплаченными:
   - ✅ **Выполнен** (completed) - рекомендуется
   - ✅ **В обработке** (processing) - опционально
   - ❌ **Ожидает оплаты** (pending) - не рекомендуется

## 📊 Результат исправлений

### До исправления:
```
Статус: Оплачено
Сумма: 27289
Скидка: 27289 (100%)
Оплачено рублями: 27289
```

### После исправления:
```
Статус: Не оплачено (для pending/on-hold)
Статус: Оплачено (только для completed/processing)
Сумма: 27289
Скидка: 0-5000 (реальная скидка)
Оплачено рублями: 0 (для неоплаченных) / 27289 (для оплаченных)
```

## 🔍 Логирование

Добавлено подробное логирование для диагностики:

```
[INFO] Статус заказа WooCommerce: 'pending', 
       Настроенные оплаченные статусы: completed, processing, 
       Считается оплаченным: Нет, 
       Сумма к оплате: 0

[INFO] Товар 'Название товара': 
       Базовая цена: 1000, 
       Цена со скидкой: 900, 
       Кол-во: 2, 
       Скидка: 200
```

## ✅ Проверка исправлений

1. Создайте тестовый заказ со статусом "Ожидает оплаты"
2. Проверьте в TableCRM:
   - Статус должен быть "Не оплачено"
   - Сумма оплаты должна быть 0
   - Скидка должна соответствовать реальной скидке товаров

3. Измените статус заказа на "Выполнен"
4. Проверьте обновление в TableCRM:
   - Статус должен стать "Оплачено"
   - Сумма оплаты должна равняться итоговой стоимости

## 🚀 Обновление плагина

1. Загрузите исправленный файл `tablecrm-integration-complete-v1.0.24.php`
2. Замените старую версию плагина
3. Проверьте настройки в админ-панели
4. Настройте "Оплаченные статусы" согласно вашим потребностям

## 📝 Примечания

- Исправления сохраняют полную совместимость с существующими настройками
- Добавлено детальное логирование для отладки
- Реализована гибкая настройка статусов оплаты
- Исправлен корректный расчет скидок товаров

**Версия исправлений:** v1.0.24-payment-fix
**Дата:** 15 января 2025 