# ИСПРАВЛЕННАЯ СХЕМА API TABLECRM

## Проблема
TableCRM может фильтровать или не отображать новые заявки, если не хватает обязательных полей или нарушена схема.

## Решение
Все поля в POST запросах должны строго соответствовать схеме, которую ожидает TableCRM (из Swagger).

## Правильная схема документа продаж

### Эталонная схема от Рушана:
```json
[{
    "dated": 1749020764,
    "operation": "Заказ",
    "comment": "текст",
    "tax_included": true,
    "tax_active": true,
    "goods": [{
        "price": 500,
        "quantity": 1,
        "unit": 116,
        "discount": 0,
        "sum_discounted": 0,
        "nomenclature": 39697
    }],
    "settings": {},
    "warehouse": 39,
    "contragent": 330985,
    "paybox": 218,
    "organization": 38,
    "status": false,
    "paid_rubles": "500.00",
    "paid_lt": 0
}]
```

### Обязательные поля:
- `dated` (integer) - Unix timestamp
- `operation` (string) - "Заказ"
- `comment` (string) - Описание заказа
- `tax_included` (boolean) - true
- `tax_active` (boolean) - true
- `goods` (array) - Массив товаров
- `settings` (object) - Пустой объект {}
- `warehouse` (integer) - ID склада
- `contragent` (integer) - ID контрагента
- `paybox` (integer) - ID кассы
- `organization` (integer) - ID организации
- `status` (boolean) - Статус оплаты (false = не оплачен)
- `paid_rubles` (string) - Сумма в рублях "0.00"
- `paid_lt` (integer) - 0

### Структура товара в goods:
- `price` (number) - Цена за единицу
- `quantity` (integer) - Количество
- `unit` (integer) - ID единицы измерения
- `discount` (number) - Скидка (0)
- `sum_discounted` (number) - Сумма скидки (0)
- `nomenclature` (integer) - ID номенклатуры

## Эндпоинты TableCRM API

### 1. Создание документа продаж
**URL:** `POST /docs_sales/`
**Пример:** `https://app.tablecrm.com/api/v1/docs_sales/?token=API_KEY`

### 2. Поиск контрагента по телефону
**URL:** `GET /contragents/`
**Пример:** `https://app.tablecrm.com/api/v1/contragents/?token=API_KEY&phone=79377799906`

### 3. Создание контрагента
**URL:** `POST /contragents/`
**Схема:**
```json
[{
    "name": "Иван Петров",
    "email": "<EMAIL>",
    "phone": "79377799906",
    "external_id": "order_123",
    "type": "individual",
    "comment": "Клиент с сайта"
}]
```

### 4. Поиск номенклатуры
**URL:** `GET /nomenclature/`
**Пример:** `https://app.tablecrm.com/api/v1/nomenclature/?token=API_KEY&name=хлеб`

### 5. Создание номенклатуры
**URL:** `POST /nomenclatures/`
**Схема:**
```json
[{
    "name": "Хлеб белый",
    "code": "BREAD-001",
    "external_id": "product_101",
    "is_active": true,
    "unit": 116
}]
```

### 6. Информация о доставке
**URL:** `POST /docs_sales/{id}/delivery_info/`
**Схема:**
```json
{
    "address": "ул. Ленина, 10",
    "city": "Москва",
    "postcode": "123456",
    "recipient_name": "Иван Петров",
    "recipient_phone": "79377799906"
}
```

### 7. UTM метки
**URL:** `POST /docs_sales/{id}/utm/`
**Схема:**
```json
{
    "utm_source": "google",
    "utm_medium": "cpc",
    "utm_campaign": "winter_sale",
    "utm_term": "хлеб",
    "utm_content": "ad1",
    "referrer": "https://google.com",
    "landing_page": "https://example.com"
}
```

## Последовательность отправки

1. **Поиск/создание контрагента**
   - Ищем по телефону: `GET /contragents/?phone=79377799906`
   - Если не найден, создаем: `POST /contragents/`

2. **Поиск/создание номенклатуры** (для каждого товара)
   - Ищем по названию: `GET /nomenclature/?name=хлеб`
   - Если не найдена, создаем: `POST /nomenclatures/`

3. **Создание документа продаж**
   - `POST /docs_sales/` с полной схемой
   - Получаем ID документа

4. **Дополнительная информация**
   - Отправляем доставку: `POST /docs_sales/{id}/delivery_info/`
   - Отправляем UTM: `POST /docs_sales/{id}/utm/`

## Настройки плагина

### Обязательные ID:
- **organization**: 38 (ID организации)
- **paybox**: 218 (ID кассы)
- **warehouse**: 39 (ID склада)
- **unit**: 116 (ID единицы измерения - штуки)

### Авторизация:
- Только через query параметр `?token=API_KEY`
- Заголовки: `Content-Type: application/json`

## Изменения в коде

### 1. Обновлена функция `adapt_data_format()`
- Правильная схема для `docs_sales/`
- Поддержка поиска/создания контрагентов
- Поддержка поиска/создания номенклатуры

### 2. Добавлены новые функции:
- `get_or_create_contragent()`
- `search_contragent_by_phone()`
- `search_contragent_by_email()`
- `create_new_contragent()`
- `get_or_create_nomenclature()`
- `search_nomenclature_by_name()`
- `create_new_nomenclature()`
- `send_delivery_info()`
- `send_utm_tags()`

### 3. Обновлены настройки плагина:
- Добавлен `tablecrm_warehouse_id`
- Добавлен `tablecrm_unit_id`
- Обновлены значения по умолчанию

## Проверка работы

Используйте файл `test_correct_schema.php` для проверки правильности формируемой схемы данных.

## Результат

После этих изменений заявки будут создаваться в TableCRM с правильной структурой данных и не будут фильтроваться системой. 