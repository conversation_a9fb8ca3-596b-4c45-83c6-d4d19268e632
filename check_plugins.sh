#!/bin/bash

echo "Поиск плагинов для интеграции с TableCRM..."

# Подключение по SFTP и проверка плагинов
expect << 'EOF'
spawn lftp sftp://<EMAIL>
expect "Пароль:"
send "zl2Shj!e&6ng\r"
expect "lftp"
send "cd public_html/wp-content/plugins\r"
expect "lftp"
send "ls\r"
expect "lftp"
send "ls | grep -i crm\r"
expect "lftp"
send "ls | grep -i table\r"
expect "lftp"
send "ls | grep -i api\r"
expect "lftp"
send "ls | grep -i integration\r"
expect "lftp"
send "ls | grep -i webhook\r"
expect "lftp"
send "quit\r"
expect eof
EOF

echo "Проверка завершена." 