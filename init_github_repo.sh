#!/bin/bash

# Скрипт инициализации GitHub репозитория для TableCRM WordPress Integration
# Использование: ./init_github_repo.sh YOUR_GITHUB_USERNAME

echo "🚀 Инициализация GitHub репозитория для TableCRM WordPress Integration"
echo "=================================================================="

# Проверка аргументов
if [ -z "$1" ]; then
    echo "❌ Ошибка: Укажите ваше имя пользователя GitHub"
    echo "💡 Использование: $0 YOUR_GITHUB_USERNAME"
    echo "📝 Пример: $0 myusername"
    exit 1
fi

GITHUB_USERNAME="$1"
REPO_NAME="tablecrm-wordpress-integration"
REPO_URL="https://github.com/$GITHUB_USERNAME/$REPO_NAME.git"

echo "👤 GitHub пользователь: $GITHUB_USERNAME"
echo "📦 Название репозитория: $REPO_NAME"
echo "🔗 URL репозитория: $REPO_URL"
echo

# Проверка установки Git
if ! command -v git &> /dev/null; then
    echo "❌ Git не установлен. Установите Git и попробуйте снова."
    exit 1
fi

echo "✅ Git установлен: $(git --version)"

# Проверка, не инициализирован ли уже Git
if [ -d ".git" ]; then
    echo "⚠️  Git репозиторий уже инициализирован"
    echo "🔄 Добавляем remote origin..."
    git remote remove origin 2>/dev/null || true
    git remote add origin "$REPO_URL"
else
    echo "📁 Инициализация Git репозитория..."
    git init
    echo "🔗 Добавление remote origin..."
    git remote add origin "$REPO_URL"
fi

# Проверка наличия основных файлов
echo "🔍 Проверка наличия необходимых файлов..."

required_files=(
    "README.md"
    "LICENSE" 
    "CHANGELOG.md"
    "INSTALL.md"
    ".gitignore"
    "tablecrm-integration.php"
    "check_integration_logs.php"
)

missing_files=()
for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        missing_files+=("$file")
    else
        echo "  ✅ $file"
    fi
done

if [ ${#missing_files[@]} -ne 0 ]; then
    echo "❌ Отсутствуют файлы:"
    for file in "${missing_files[@]}"; do
        echo "  ❌ $file"
    done
    echo "💡 Убедитесь, что все необходимые файлы созданы и попробуйте снова."
    exit 1
fi

# Добавление файлов в Git
echo "📦 Добавление файлов в репозиторий..."

git add README.md
git add LICENSE
git add CHANGELOG.md
git add INSTALL.md
git add .gitignore
git add GITHUB_SETUP.md

# Основные файлы плагина
git add tablecrm-integration.php
git add tablecrm-integration-fixed.php
git add check_integration_logs.php
git add utm-tracking.js

# Тестовые файлы (если есть)
[ -f "simple_test_order_creation.php" ] && git add simple_test_order_creation.php

echo "✅ Файлы добавлены в staging area"

# Проверка статуса
echo "📊 Статус Git репозитория:"
git status --short

# Создание первого коммита
echo "💾 Создание первого коммита..."

git commit -m "🎉 Initial release: TableCRM WordPress Integration Plugin

✨ Features:
- Automatic WooCommerce orders sync to TableCRM
- Correct delivery info formatting with recipient JSON structure  
- Support for shipping and billing addresses
- Detailed logging system
- Admin panel configuration
- Fixed delivery data structure for TableCRM API compatibility

📁 Files included:
- Main plugin files (tablecrm-integration.php, tablecrm-integration-fixed.php)
- Comprehensive documentation (README.md, INSTALL.md, CHANGELOG.md)
- Integration testing tools (check_integration_logs.php)
- UTM tracking support (utm-tracking.js)

🔧 Version: 1.0.1
🏷️ Ready for production use"

if [ $? -eq 0 ]; then
    echo "✅ Первый коммит создан успешно!"
else
    echo "❌ Ошибка при создании коммита"
    exit 1
fi

# Создание основной ветки
echo "🌿 Настройка основной ветки..."
git branch -M main

# Создание тега версии
echo "🏷️  Создание тега версии v1.0.1..."
git tag -a v1.0.1 -m "Release v1.0.1: Fixed delivery info structure

🔧 Fixed:
- Delivery info API structure for TableCRM compatibility
- Recipient JSON formatting (name, surname, phone)
- Address formatting from shipping/billing data
- Date formatting for delivery_date field

✨ Improved:
- Enhanced logging for delivery info debugging
- Better error handling for API requests
- Fallback mechanisms for missing shipping data"

echo "✅ Тег v1.0.1 создан"

# Информация о следующих шагах
echo
echo "🎉 Локальный репозиторий готов!"
echo "=========================================="
echo
echo "📋 Следующие шаги:"
echo "1️⃣  Создайте репозиторий на GitHub:"
echo "   - Перейдите на https://github.com/new"
echo "   - Repository name: $REPO_NAME"
echo "   - Description: WordPress/WooCommerce integration plugin for TableCRM API"
echo "   - НЕ создавайте README, .gitignore или LICENSE (они уже есть)"
echo
echo "2️⃣  Отправьте код на GitHub:"
echo "   git push -u origin main"
echo "   git push origin v1.0.1"
echo
echo "3️⃣  Создайте релиз на GitHub:"
echo "   - Перейдите в Releases → Create a new release"
echo "   - Tag version: v1.0.1"
echo "   - Release title: TableCRM WordPress Integration v1.0.1"
echo
echo "📦 Создать ZIP архив для релиза:"
echo "   ./create_release_archive.sh"
echo
echo "📖 Подробные инструкции: см. GITHUB_SETUP.md"

# Создание скрипта для создания архива
cat > create_release_archive.sh << 'EOF'
#!/bin/bash

VERSION="v1.0.1"
ARCHIVE_NAME="tablecrm-integration-$VERSION.zip"

echo "📦 Создание архива релиза: $ARCHIVE_NAME"

zip -r "$ARCHIVE_NAME" \
  tablecrm-integration.php \
  tablecrm-integration-fixed.php \
  utm-tracking.js \
  check_integration_logs.php \
  simple_test_order_creation.php \
  README.md \
  INSTALL.md \
  CHANGELOG.md \
  LICENSE \
  -x "*.git*" "*.DS_Store*" "*Thumbs.db*"

if [ $? -eq 0 ]; then
    echo "✅ Архив создан: $ARCHIVE_NAME"
    echo "📁 Размер: $(du -h "$ARCHIVE_NAME" | cut -f1)"
    echo "🚀 Готово для загрузки в GitHub Release!"
else
    echo "❌ Ошибка при создании архива"
    exit 1
fi
EOF

chmod +x create_release_archive.sh
echo "✅ Создан скрипт create_release_archive.sh"

echo
echo "🔗 URL для создания репозитория: https://github.com/new"
echo "🏁 Готово! Следуйте инструкциям выше для завершения настройки." 