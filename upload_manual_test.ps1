# Upload and run manual order test
$server = "bekflom3.beget.tech"
$user = "bekflom3_wz"
$pass = "zl2Shj!e&6ng"

Write-Host "Uploading manual test script..." -ForegroundColor Green

try {
    $uri = "ftp://$server/public_html/test_manual_order_send.php"
    $webclient = New-Object System.Net.WebClient
    $webclient.Credentials = New-Object System.Net.NetworkCredential($user, $pass)
    $webclient.UploadFile($uri, "test_manual_order_send.php")
    Write-Host "Manual test script uploaded!" -ForegroundColor Green
    
    # Run the manual test
    Write-Host "Running manual order test..." -ForegroundColor Yellow
    $result = Invoke-WebRequest -Uri "https://mosbuketik.ru/test_manual_order_send.php" -UseBasicParsing
    Write-Host $result.Content -ForegroundColor White
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

$webclient.Dispose()
Write-Host "Manual test completed!" -ForegroundColor Green 