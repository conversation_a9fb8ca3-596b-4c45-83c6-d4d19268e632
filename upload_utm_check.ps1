# Upload and run UTM check
$server = "bekflom3.beget.tech"
$user = "bekflom3_wz"
$pass = "zl2Shj!e&6ng"

Write-Host "Uploading UTM check script..." -ForegroundColor Green

try {
    $uri = "ftp://$server/public_html/check_utm_data.php"
    $webclient = New-Object System.Net.WebClient
    $webclient.Credentials = New-Object System.Net.NetworkCredential($user, $pass)
    $webclient.UploadFile($uri, "check_utm_data.php")
    Write-Host "UTM check script uploaded!" -ForegroundColor Green
    
    # Run the test
    Write-Host "Running UTM check..." -ForegroundColor Yellow
    $result = Invoke-WebRequest -Uri "https://mosbuketik.ru/check_utm_data.php" -UseBasicParsing
    Write-Host $result.Content -ForegroundColor White
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

$webclient.Dispose()
Write-Host "UTM check completed!" -ForegroundColor Green 