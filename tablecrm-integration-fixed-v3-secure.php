<?php
/**
 * Plugin Name: TableCRM Integration (Fixed v3)
 * Description: Интеграция WordPress/WooCommerce с TableCRM для отправки заявок через API - версия с Action Scheduler и защитой от дублирования
 * Version: 1.0.8
 * Author: Your Name
 * Text Domain: tablecrm-integration-fixed-v3
 */

// Предотвращение прямого доступа
if (!defined('ABSPATH')) {
    exit;
}

// Определение констант плагина
define('TABLECRM_FIXED_V3_PLUGIN_URL', plugin_dir_url(__FILE__));
define('TABLECRM_FIXED_V3_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('TABLECRM_FIXED_V3_VERSION', '1.0.8');

class TableCRM_Integration_Fixed_V3 {
    
    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'register_settings'));
        
        // Хук валидации настроек при сохранении
        add_action('update_option_tablecrm_fixed_v3_api_url', array($this, 'validate_and_log_settings'));
        add_action('update_option_tablecrm_fixed_v3_api_key', array($this, 'validate_and_log_settings'));
        
        // Хуки для WooCommerce - С ACTION SCHEDULER
        add_action('woocommerce_new_order', array($this, 'queue_order_sync'), 20);
        add_action('woocommerce_order_status_completed', array($this, 'queue_order_sync'));
        add_action('woocommerce_order_status_processing', array($this, 'queue_order_sync'));
        add_action('woocommerce_order_status_cancelled', array($this, 'queue_order_sync'));
        add_action('woocommerce_order_status_refunded', array($this, 'queue_order_sync'));
        
        // Action Scheduler хуки
        add_action('tablecrm_process_order', array($this, 'process_order_async'));
        
        // Хуки для Contact Form 7
        add_action('wpcf7_mail_sent', array($this, 'send_cf7_to_tablecrm'));
        
        // AJAX хуки
        add_action('wp_ajax_test_tablecrm_connection_fixed_v3', array($this, 'test_connection'));
        add_action('wp_ajax_nopriv_test_tablecrm_connection_fixed_v3', array($this, 'test_connection'));
        
        // Хук для сохранения UTM данных
        add_action('woocommerce_checkout_create_order', array($this, 'save_utm_data_to_order'), 10, 2);
        
        // Хук очистки старых задач
        add_action('wp_loaded', array($this, 'schedule_cleanup'));
    }
    
    public function init() {
        load_plugin_textdomain('tablecrm-integration-fixed-v3', false, dirname(plugin_basename(__FILE__)) . '/languages');
        
        // Проверяем наличие Action Scheduler
        if (!function_exists('as_schedule_single_action')) {
            add_action('admin_notices', array($this, 'missing_action_scheduler_notice'));
            return;
        }
        
        // Добавляем скрипт для отслеживания UTM на фронтенде
        add_action('wp_enqueue_scripts', array($this, 'enqueue_utm_tracking_script'));
    }
    
    // Уведомление об отсутствии Action Scheduler
    public function missing_action_scheduler_notice() {
        ?>
        <div class="notice notice-error">
            <p><strong>TableCRM Integration v3:</strong> Требуется WooCommerce ≥ 8.5 для работы Action Scheduler. Пожалуйста, обновите WooCommerce.</p>
        </div>
        <?php
    }
    
    // Подключение скрипта отслеживания UTM
    public function enqueue_utm_tracking_script() {
        wp_enqueue_script('tablecrm-utm-tracking-fixed-v3', TABLECRM_FIXED_V3_PLUGIN_URL . 'utm-tracking.js', array('jquery'), TABLECRM_FIXED_V3_VERSION, true);
    }
    
    // Добавление меню в админку
    public function add_admin_menu() {
        add_options_page(
            'TableCRM Integration Settings (Fixed v3)',
            'TableCRM Integration (Fixed v3)',
            'manage_options',
            'tablecrm-fixed-v3-settings',
            array($this, 'admin_page')
        );
    }
    
    // Регистрация настроек с санитизацией
    public function register_settings() {
        // API URL - санитизация URL
        register_setting('tablecrm_fixed_v3_settings', 'tablecrm_fixed_v3_api_url', array(
            'type' => 'string',
            'sanitize_callback' => array($this, 'sanitize_api_url'),
            'default' => 'https://app.tablecrm.com/api/v1/'
        ));
        
        // API Key - санитизация текстового поля (ключ)
        register_setting('tablecrm_fixed_v3_settings', 'tablecrm_fixed_v3_api_key', array(
            'type' => 'string',
            'sanitize_callback' => array($this, 'sanitize_api_key'),
            'default' => ''
        ));
        
        // Project ID - санитизация числа
        register_setting('tablecrm_fixed_v3_settings', 'tablecrm_fixed_v3_project_id', array(
            'type' => 'integer',
            'sanitize_callback' => 'absint',
            'default' => 0
        ));
        
        // Organization ID - санитизация числа
        register_setting('tablecrm_fixed_v3_settings', 'tablecrm_fixed_v3_organization_id', array(
            'type' => 'integer',
            'sanitize_callback' => 'absint',
            'default' => 38
        ));
        
        // Cashbox ID - санитизация числа
        register_setting('tablecrm_fixed_v3_settings', 'tablecrm_fixed_v3_cashbox_id', array(
            'type' => 'integer',
            'sanitize_callback' => 'absint',
            'default' => 218
        ));
        
        // Warehouse ID - санитизация числа
        register_setting('tablecrm_fixed_v3_settings', 'tablecrm_fixed_v3_warehouse_id', array(
            'type' => 'integer',
            'sanitize_callback' => 'absint',
            'default' => 39
        ));
        
        // Unit ID - санитизация числа
        register_setting('tablecrm_fixed_v3_settings', 'tablecrm_fixed_v3_unit_id', array(
            'type' => 'integer',
            'sanitize_callback' => 'absint',
            'default' => 116
        ));
        
        // Булевы настройки - санитизация чекбоксов
        register_setting('tablecrm_fixed_v3_settings', 'tablecrm_fixed_v3_enable_orders', array(
            'type' => 'boolean',
            'sanitize_callback' => array($this, 'sanitize_checkbox'),
            'default' => true
        ));
        
        register_setting('tablecrm_fixed_v3_settings', 'tablecrm_fixed_v3_enable_cf7', array(
            'type' => 'boolean',
            'sanitize_callback' => array($this, 'sanitize_checkbox'),
            'default' => false
        ));
        
        register_setting('tablecrm_fixed_v3_settings', 'tablecrm_fixed_v3_check_duplicates', array(
            'type' => 'boolean',
            'sanitize_callback' => array($this, 'sanitize_checkbox'),
            'default' => true
        ));
        
        register_setting('tablecrm_fixed_v3_settings', 'tablecrm_fixed_v3_debug_mode', array(
            'type' => 'boolean',
            'sanitize_callback' => array($this, 'sanitize_checkbox'),
            'default' => true
        ));
        
        register_setting('tablecrm_fixed_v3_settings', 'tablecrm_fixed_v3_send_real_data_only', array(
            'type' => 'boolean',
            'sanitize_callback' => array($this, 'sanitize_checkbox'),
            'default' => true
        ));
        
        register_setting('tablecrm_fixed_v3_settings', 'tablecrm_fixed_v3_retry_failed', array(
            'type' => 'boolean',
            'sanitize_callback' => array($this, 'sanitize_checkbox'),
            'default' => true
        ));
        
        // Массив статусов заказов - санитизация массива
        register_setting('tablecrm_fixed_v3_settings', 'tablecrm_fixed_v3_order_statuses', array(
            'type' => 'array',
            'sanitize_callback' => array($this, 'sanitize_order_statuses'),
            'default' => array('completed', 'processing')
        ));
    }
    
    // Страница настроек
    public function admin_page() {
        ?>
        <div class="wrap">
            <h1>TableCRM Integration Settings (Fixed v3 - v1.0.5)</h1>
            <div class="notice notice-success">
                <p><strong>🚀 Версия с Action Scheduler!</strong></p>
                <p>✅ Надежная защита от дублирования заказов</p>
                <p>✅ Идемпотентность через уникальные action_id</p>
                <p>✅ Автоматические повторы при ошибках 5xx</p>
                <p>✅ Хранение очереди в базе данных</p>
            </div>
            <form method="post" action="options.php">
                <?php
                settings_fields('tablecrm_fixed_v3_settings');
                do_settings_sections('tablecrm_fixed_v3_settings');
                ?>
                <table class="form-table">
                    <tr>
                        <th scope="row">API URL</th>
                        <td>
                            <input type="url" name="tablecrm_fixed_v3_api_url" value="<?php echo esc_attr(get_option('tablecrm_fixed_v3_api_url', 'https://app.tablecrm.com/api/v1/')); ?>" class="regular-text" />
                            <p class="description">URL API TableCRM</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">API Key</th>
                        <td>
                            <input type="password" name="tablecrm_fixed_v3_api_key" value="<?php echo esc_attr(get_option('tablecrm_fixed_v3_api_key')); ?>" class="regular-text" />
                            <p class="description">Ваш API ключ TableCRM</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Organization ID</th>
                        <td>
                            <input type="number" name="tablecrm_fixed_v3_organization_id" value="<?php echo esc_attr(get_option('tablecrm_fixed_v3_organization_id', 38)); ?>" class="regular-text" />
                            <p class="description">ID организации в TableCRM</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Cashbox ID</th>
                        <td>
                            <input type="number" name="tablecrm_fixed_v3_cashbox_id" value="<?php echo esc_attr(get_option('tablecrm_fixed_v3_cashbox_id', 218)); ?>" class="regular-text" />
                            <p class="description">ID кассы в TableCRM</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Warehouse ID</th>
                        <td>
                            <input type="number" name="tablecrm_fixed_v3_warehouse_id" value="<?php echo esc_attr(get_option('tablecrm_fixed_v3_warehouse_id', 39)); ?>" class="regular-text" />
                            <p class="description">ID склада в TableCRM</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Unit ID</th>
                        <td>
                            <input type="number" name="tablecrm_fixed_v3_unit_id" value="<?php echo esc_attr(get_option('tablecrm_fixed_v3_unit_id', 116)); ?>" class="regular-text" />
                            <p class="description">ID единицы измерения в TableCRM</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row" style="background-color: #ffffcc;">Отправлять только реальные данные</th>
                        <td style="background-color: #ffffcc;">
                            <input type="checkbox" name="tablecrm_fixed_v3_send_real_data_only" value="1" <?php checked(1, get_option('tablecrm_fixed_v3_send_real_data_only', 1)); ?> />
                            <label><strong>ВКЛЮЧЕНО: Отправлять только заказы с полными данными (имя, email, сумма > 0)</strong></label>
                            <p class="description" style="color: red;">⚠️ Это предотвращает отправку пустых/тестовых заказов в CRM</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Отправлять заказы WooCommerce</th>
                        <td>
                            <input type="checkbox" name="tablecrm_fixed_v3_enable_orders" value="1" <?php checked(1, get_option('tablecrm_fixed_v3_enable_orders', 1)); ?> />
                            <label>Включить отправку заказов</label>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row" style="background-color: #e8f5e8;">Повторять неудачные попытки</th>
                        <td style="background-color: #e8f5e8;">
                            <input type="checkbox" name="tablecrm_fixed_v3_retry_failed" value="1" <?php checked(1, get_option('tablecrm_fixed_v3_retry_failed', 1)); ?> />
                            <label><strong>ВКЛЮЧЕНО: Автоматически повторять отправку при ошибках 5xx</strong></label>
                            <p class="description">Action Scheduler будет повторять отправку до 3 раз с интервалами</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Режим отладки</th>
                        <td>
                            <input type="checkbox" name="tablecrm_fixed_v3_debug_mode" value="1" <?php checked(1, get_option('tablecrm_fixed_v3_debug_mode', 1)); ?> />
                            <label>Включить подробное логирование</label>
                        </td>
                    </tr>
                </table>
                
                <p class="submit">
                    <input type="submit" name="submit" id="submit" class="button button-primary" value="Сохранить настройки" />
                    <button type="button" id="test-connection-fixed-v3" class="button">Тестировать соединение (Fixed v3)</button>
                </p>
            </form>
            
            <div id="test-result-fixed-v3" style="margin-top: 20px;"></div>
            
            <?php if (function_exists('as_get_scheduled_actions')): ?>
            <h2>Статус очереди Action Scheduler</h2>
            <?php
            $pending_actions = as_get_scheduled_actions(array(
                'hook' => 'tablecrm_process_order',
                'status' => 'pending',
                'per_page' => 10
            ));
            $failed_actions = as_get_scheduled_actions(array(
                'hook' => 'tablecrm_process_order', 
                'status' => 'failed',
                'per_page' => 5
            ));
            ?>
            <p><strong>Задач в очереди:</strong> <?php echo count($pending_actions); ?></p>
            <p><strong>Неудачных задач:</strong> <?php echo count($failed_actions); ?></p>
            <?php endif; ?>
            
            <script>
            jQuery(document).ready(function($) {
                $('#test-connection-fixed-v3').click(function() {
                    var button = $(this);
                    var result = $('#test-result-fixed-v3');
                    
                    button.prop('disabled', true).text('Тестируем...');
                    result.html('<p>Проверяем соединение с TableCRM API...</p>');
                    
                    $.ajax({
                        url: ajaxurl,
                        method: 'POST',
                        data: {
                            action: 'test_tablecrm_connection_fixed_v3',
                            nonce: '<?php echo wp_create_nonce("test_tablecrm_fixed_v3"); ?>'
                        },
                        success: function(response) {
                            if(response.success) {
                                result.html('<div class="notice notice-success"><p><strong>✅ Соединение успешно!</strong><br>' + response.data.message + '</p></div>');
                            } else {
                                result.html('<div class="notice notice-error"><p><strong>❌ Ошибка соединения:</strong><br>' + response.data.message + '</p></div>');
                            }
                        },
                        error: function() {
                            result.html('<div class="notice notice-error"><p><strong>❌ Ошибка AJAX запроса</strong></p></div>');
                        },
                        complete: function() {
                            button.prop('disabled', false).text('Тестировать соединение (Fixed v3)');
                        }
                    });
                });
            });
            </script>
        </div>
        <?php
    }
    
    // Тестирование соединения
    public function test_connection() {
        // КРИТИЧЕСКАЯ ЗАЩИТА: Проверяем права доступа и nonce
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Недостаточно прав доступа');
            return;
        }
        
        if (!isset($_POST['nonce']) || !wp_verify_nonce(sanitize_text_field($_POST['nonce']), 'test_tablecrm_fixed_v3')) {
            wp_send_json_error('Неверный токен безопасности');
            return;
        }
        
        $api_url = get_option('tablecrm_fixed_v3_api_url', 'https://app.tablecrm.com/api/v1/');
        $api_key = get_option('tablecrm_fixed_v3_api_key');
        
        if (empty($api_url) || empty($api_key)) {
            wp_send_json_error('Пожалуйста, заполните обязательные поля (API URL и API Key)');
        }

        $response = $this->make_api_request('health', array());
        
        if ($response['success']) {
            wp_send_json_success('Соединение установлено успешно! Action Scheduler готов к работе.');
        } else {
            wp_send_json_error('Ошибка соединения: ' . $response['message']);
        }
    }
    
    // ОСНОВНАЯ ФУНКЦИЯ: Постановка заказа в очередь с защитой от дублирования
    public function queue_order_sync($order_id) {
        if (!function_exists('as_schedule_single_action')) {
            $this->log_error("Action Scheduler недоступен. Заказ $order_id не поставлен в очередь.");
            return;
        }
        
        if (!get_option('tablecrm_fixed_v3_enable_orders', 1)) {
            return;
        }
        
        // КРИТИЧЕСКАЯ ЗАЩИТА: Проверяем, был ли заказ уже обработан
        $existing_doc_id = get_post_meta($order_id, '_tablecrm_v3_document_id', true);
        if (!empty($existing_doc_id)) {
            $this->log_info("Заказ $order_id уже обработан (Document ID: $existing_doc_id). Пропускаем.");
            return;
        }
        
        // КРИТИЧЕСКАЯ ЗАЩИТА: Проверяем, есть ли уже задача в очереди для этого заказа
        $existing_actions = as_get_scheduled_actions(array(
            'hook' => 'tablecrm_process_order',
            'args' => array('order_id' => $order_id),
            'status' => array('pending', 'in-progress'),
            'per_page' => 1
        ));
        
        if (!empty($existing_actions)) {
            $this->log_info("Задача для заказа $order_id уже в очереди. Пропускаем дублирование.");
            return;
        }
        
        // КРИТИЧЕСКАЯ ЗАЩИТА: Устанавливаем временный маркер обработки
        $processing_key = "_tablecrm_v3_processing_" . time();
        if (!add_post_meta($order_id, $processing_key, time(), true)) {
            $this->log_warning("Заказ $order_id уже обрабатывается другим процессом. Пропускаем.");
            return;
        }
        
        // Создаем уникальный идентификатор группы для идемпотентности
        $group_id = "tablecrm_order_{$order_id}_" . md5($order_id . time());
        
        // Ставим задачу в очередь Action Scheduler
        $action_id = as_schedule_single_action(
            time() + 30, // Задержка 30 секунд для получения полных данных заказа
            'tablecrm_process_order',
            array('order_id' => $order_id),
            $group_id // Уникальная группа для идемпотентности
        );
        
        if ($action_id) {
            update_post_meta($order_id, '_tablecrm_v3_action_id', $action_id);
            $this->log_success("Заказ $order_id поставлен в очередь Action Scheduler. Action ID: $action_id");
        } else {
            delete_post_meta($order_id, $processing_key);
            $this->log_error("Не удалось поставить заказ $order_id в очередь Action Scheduler");
        }
    }
    
    // ОБРАБОТЧИК АСИНХРОННОЙ ЗАДАЧИ
    public function process_order_async($order_id) {
        $this->log_info("=== НАЧАЛО АСИНХРОННОЙ ОБРАБОТКИ ЗАКАЗА $order_id ===");
        
        // Проверяем, не был ли заказ уже обработан за время ожидания в очереди
        $existing_doc_id = get_post_meta($order_id, '_tablecrm_v3_document_id', true);
        if (!empty($existing_doc_id)) {
            $this->log_info("Заказ $order_id уже обработан (Document ID: $existing_doc_id). Завершаем задачу.");
            $this->cleanup_processing_meta($order_id);
            return;
        }
        
        $order = wc_get_order($order_id);
        if (!$order) {
            $this->log_error("Заказ $order_id не найден в WooCommerce");
            $this->cleanup_processing_meta($order_id);
            return;
        }
        
        try {
            $result = $this->send_order_to_tablecrm($order_id);
            
            if ($result) {
                $this->log_success("Заказ $order_id успешно обработан через Action Scheduler");
            } else {
                // Если включены повторы, генерируем ошибку для Action Scheduler
                if (get_option('tablecrm_fixed_v3_retry_failed', 1)) {
                    throw new Exception("Не удалось отправить заказ $order_id в TableCRM");
                }
            }
        } catch (Exception $e) {
            $this->log_error("Ошибка при обработке заказа $order_id: " . $e->getMessage());
            $this->cleanup_processing_meta($order_id);
            throw $e; // Передаем ошибку Action Scheduler для повтора
        }
        
        $this->cleanup_processing_meta($order_id);
        $this->log_info("=== ЗАВЕРШЕНИЕ АСИНХРОННОЙ ОБРАБОТКИ ЗАКАЗА $order_id ===");
    }
    
    // Очистка временных мета-данных обработки
    private function cleanup_processing_meta($order_id) {
        global $wpdb;
        
        // Удаляем все временные ключи обработки
        $wpdb->query(
            $wpdb->prepare(
                "DELETE FROM {$wpdb->postmeta} 
                 WHERE post_id = %d 
                 AND meta_key LIKE '_tablecrm_v3_processing_%'",
                $order_id
            )
        );
    }
    
    // Отправка заказа WooCommerce в TableCRM (УЛУЧШЕННАЯ ВЕРСИЯ)
    public function send_order_to_tablecrm($order_id) {
        $this->log_info("=== ОТПРАВКА ЗАКАЗА $order_id В TABLECRM (v3) ===");
        
        $order = wc_get_order($order_id);
        if (!$order) {
            $this->log_error("Заказ $order_id не найден");
            return false;
        }
        
        // ФИНАЛЬНАЯ ПРОВЕРКА НА ДУБЛИРОВАНИЕ
        $existing_doc_id = get_post_meta($order_id, '_tablecrm_v3_document_id', true);
        if (!empty($existing_doc_id)) {
            $this->log_info("Заказ $order_id уже обработан. Document ID: $existing_doc_id");
            return true; // Считаем успехом, так как заказ уже отправлен
        }
        
        // Проверка на черновики и мусор
        if (in_array($order->get_status(), array('auto-draft', 'draft', 'trash'))) {
            $this->log_warning("Заказ $order_id пропущен - статус '" . $order->get_status() . "' (системный)");
            return true; // Не считаем ошибкой
        }
        
        // Собираем данные заказа
        $billing_first_name = $order->get_billing_first_name();
        $billing_last_name = $order->get_billing_last_name();
        $billing_email = $order->get_billing_email();
        $billing_phone = $order->get_billing_phone();
        $order_total = $order->get_total();
        
        // ВАЛИДАЦИЯ ДАННЫХ
        $validation_errors = array();
        
        if (empty($billing_first_name) && empty($billing_last_name)) {
            $validation_errors[] = "Отсутствует имя клиента";
        }
        
        if (empty($billing_email)) {
            $validation_errors[] = "Отсутствует email клиента";
        }
        
        if ($order_total <= 0) {
            $validation_errors[] = "Сумма заказа равна нулю или отрицательная ($order_total)";
        }
        
        // Проверка настройки валидации
        if (get_option('tablecrm_fixed_v3_send_real_data_only', 1) && !empty($validation_errors)) {
            $this->log_error("Заказ $order_id НЕ ОТПРАВЛЕН - ошибки валидации:");
            foreach ($validation_errors as $error) {
                $this->log_error("  - $error");
            }
            return true; // Не считаем ошибкой, просто не отправляем
        }
        
        // Формируем данные
        $data = array(
            'name' => trim($billing_first_name . ' ' . $billing_last_name),
            'email' => $billing_email,
            'phone' => $billing_phone,
            'order_id' => $order_id,
            'order_total' => $order_total,
            'order_status' => $order->get_status(),
            'order_date' => $order->get_date_created() ? $order->get_date_created()->format('Y-m-d H:i:s') : current_time('mysql'),
            'source' => 'WooCommerce Order (Fixed Plugin v3 Action Scheduler)',
            'payment_method' => $order->get_payment_method_title(),
            'payment_status' => $order->is_paid() ? 'paid' : 'unpaid',
            'domain' => isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : '',
            'items' => array()
        );
        
        // Добавляем товары
        foreach ($order->get_items() as $item_id => $item) {
            $product = $item->get_product();
            if ($product) {
                $data['items'][] = array(
                    'name' => $item->get_name(),
                    'quantity' => $item->get_quantity(),
                    'price' => $item->get_total(),
                    'unit_price' => ($item->get_quantity() > 0) ? ($item->get_total() / $item->get_quantity()) : 0,
                    'sku' => $product->get_sku() ?: '',
                    'product_id' => $product->get_id()
                );
            }
        }
        
        $this->log_info("Отправляем заказ $order_id в TableCRM API...");
        $response = $this->make_api_request('docs_sales/', $data);
        
        if ($response['success']) {
            $document_id = $this->extract_document_id($response);
            
            if ($document_id !== 'unknown' && !empty($document_id)) {
                // АТОМАРНОЕ СОХРАНЕНИЕ МАРКЕРА УСПЕШНОЙ ОТПРАВКИ
                update_post_meta($order_id, '_tablecrm_v3_document_id', $document_id);
                update_post_meta($order_id, '_tablecrm_v3_sent_at', current_time('mysql'));
                
                $this->log_success("Заказ $order_id успешно отправлен. Document ID: $document_id");

                // После успешного создания документа отправляем информацию о доставке
                $this->send_delivery_info($document_id, $order);

                return true;
            } else {
                $this->log_error("Заказ $order_id отправлен, но не удалось получить Document ID");
                return false;
            }
        } else {
            $this->log_error("Ошибка отправки заказа $order_id: " . $response['message']);
            return false;
        }
    }
    
    // Извлечение Document ID из ответа
    private function extract_document_id($response) {
        if (isset($response['document_id'])) {
            return $response['document_id'];
        } elseif (isset($response['lead_id'])) {
            return $response['lead_id'];
        } elseif (isset($response['data']) && is_array($response['data']) && !empty($response['data'][0]['id'])) {
            return $response['data'][0]['id'];
        }
        return 'unknown';
    }
    
    // API запрос (упрощенная версия)
    private function make_api_request($endpoint, $data = array()) {
        $api_url = get_option('tablecrm_fixed_v3_api_url', 'https://app.tablecrm.com/api/v1/');
        $api_key = get_option('tablecrm_fixed_v3_api_key');
        
        if (empty($api_url) || empty($api_key)) {
            return array('success' => false, 'message' => 'Не настроены API параметры');
        }
        
        $url = rtrim($api_url, '/') . '/' . ltrim($endpoint, '/') . '?token=' . $api_key;
        
        $args = array(
            'method' => $endpoint === 'health' ? 'GET' : 'POST',
            'headers' => array(
                'Content-Type' => 'application/json',
                'User-Agent' => 'WordPress-TableCRM-Integration-v3/1.0.5'
            ),
            'timeout' => 30,
            'sslverify' => true
        );
        
        if ($args['method'] === 'POST') {
            $args['body'] = json_encode($this->adapt_data_format($endpoint, $data));
        }
        
        $response = wp_remote_request($url, $args);
        
        if (is_wp_error($response)) {
            return array('success' => false, 'message' => $response->get_error_message());
        }
        
        $status_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        
        if ($status_code >= 200 && $status_code < 300) {
            $decoded = json_decode($body, true);
            return array('success' => true, 'data' => $decoded, 'document_id' => $this->extract_document_id(array('data' => $decoded)));
        } else {
            return array('success' => false, 'message' => "HTTP $status_code: " . substr($body, 0, 200));
        }
    }
    
    // Адаптация данных (упрощенная версия для docs_sales)
    private function adapt_data_format($endpoint, $data) {
        // Если это основной эндпоинт создания документа – адаптируем под docs_sales
        if ($endpoint === 'docs_sales/') {
            $organization_id = get_option('tablecrm_fixed_v3_organization_id', 38);
            $cashbox_id      = get_option('tablecrm_fixed_v3_cashbox_id', 218);
            $warehouse_id    = get_option('tablecrm_fixed_v3_warehouse_id', 39);
            $unit_id         = get_option('tablecrm_fixed_v3_unit_id', 116);

            // Формируем товары
            $goods = array();
            if (!empty($data['items'])) {
                foreach ($data['items'] as $item) {
                    $goods[] = array(
                        'price'          => floatval($item['unit_price']),
                        'quantity'       => intval($item['quantity']),
                        'unit'           => intval($unit_id),
                        'discount'       => 0,
                        'sum_discounted' => 0,
                        'nomenclature'   => 39697 // Стандартная номенклатура
                    );
                }
            } else {
                $goods[] = array(
                    'price'          => floatval($data['order_total']),
                    'quantity'       => 1,
                    'unit'           => intval($unit_id),
                    'discount'       => 0,
                    'sum_discounted' => 0,
                    'nomenclature'   => 39697
                );
            }

            return array(array(
                'dated'        => time(),
                'operation'    => 'Заказ',
                'comment'      => "Заказ #{$data['order_id']} с сайта (Action Scheduler)\nКлиент: {$data['name']}\nEmail: {$data['email']}\nТелефон: {$data['phone']}",
                'tax_included' => true,
                'tax_active'   => true,
                'goods'        => $goods,
                'settings'     => (object)array(),
                'warehouse'    => intval($warehouse_id),
                'contragent'   => 330985, // Стандартный контрагент
                'paybox'       => intval($cashbox_id),
                'organization' => intval($organization_id),
                'status'       => false,
                'paid_rubles'  => number_format(floatval($data['order_total']), 2, '.', ''),
                'paid_lt'      => 0
            ));
        }

        // Для всех остальных эндпоинтов возвращаем данные без изменений
        return $data;
    }
    
    // Планирование очистки старых задач
    public function schedule_cleanup() {
        if (!wp_next_scheduled('tablecrm_cleanup_old_actions')) {
            wp_schedule_event(time(), 'daily', 'tablecrm_cleanup_old_actions');
        }
    }
    
    // Логирование
    private function log($message, $level = 'INFO') {
        if (get_option('tablecrm_fixed_v3_debug_mode', 1)) {
            $timestamp = current_time('Y-m-d H:i:s');
            error_log("[$timestamp] [TableCRM v3] [$level] $message");
        }
    }
    
    private function log_info($message) { $this->log($message, 'INFO'); }
    private function log_success($message) { $this->log($message, 'SUCCESS'); }
    private function log_warning($message) { $this->log($message, 'WARNING'); }
    private function log_error($message) { $this->log($message, 'ERROR'); }
    
    // ========================================
    // МЕТОДЫ САНИТИЗАЦИИ НАСТРОЕК
    // ========================================
    
    /**
     * Санитизация API URL
     * Проверяет корректность URL и защищает от XSS
     */
    public function sanitize_api_url($value) {
        if (empty($value)) {
            return 'https://app.tablecrm.com/api/v1/';
        }
        
        // Убираем все HTML теги и санитизируем как URL
        $value = wp_strip_all_tags($value);
        $value = esc_url_raw($value);
        
        // Проверяем что URL начинается с https://
        if (!filter_var($value, FILTER_VALIDATE_URL) || 
            (substr($value, 0, 8) !== 'https://' && substr($value, 0, 7) !== 'http://')) {
            
            $this->log_warning("Некорректный API URL: $value. Используется значение по умолчанию.");
            return 'https://app.tablecrm.com/api/v1/';
        }
        
        // Добавляем / в конец если его нет
        return rtrim($value, '/') . '/';
    }
    
    /**
     * Санитизация API ключа
     * Защищает от SQL инъекций и XSS
     */
    public function sanitize_api_key($value) {
        if (empty($value)) {
            return '';
        }
        
        // Убираем все HTML теги и пробелы
        $value = wp_strip_all_tags(trim($value));
        
        // Санитизируем как текстовое поле
        $value = sanitize_text_field($value);
        
        // API ключи обычно содержат только буквы, цифры и некоторые символы
        // Оставляем только безопасные символы
        $value = preg_replace('/[^a-zA-Z0-9\-_\.]/', '', $value);
        
        // Проверяем минимальную длину
        if (strlen($value) < 10) {
            $this->log_warning("API ключ слишком короткий (менее 10 символов)");
            return '';
        }
        
        return $value;
    }
    
    /**
     * Санитизация чекбоксов
     * Гарантирует только boolean значения
     */
    public function sanitize_checkbox($value) {
        return !empty($value) ? 1 : 0;
    }
    
    /**
     * Санитизация массива статусов заказов
     * Защищает от инъекций в массиве
     */
    public function sanitize_order_statuses($value) {
        if (!is_array($value)) {
            return array('completed', 'processing');
        }
        
        $sanitized = array();
        $allowed_statuses = array(
            'pending', 'processing', 'on-hold', 'completed', 
            'cancelled', 'refunded', 'failed', 'checkout-draft'
        );
        
        foreach ($value as $status) {
            $status = sanitize_key($status);
            if (in_array($status, $allowed_statuses)) {
                $sanitized[] = $status;
            }
        }
        
        return empty($sanitized) ? array('completed', 'processing') : $sanitized;
    }
    
    /**
     * Дополнительная валидация при сохранении
     * Запускается при каждом сохранении настроек
     */
    public function validate_and_log_settings() {
        $api_url = get_option('tablecrm_fixed_v3_api_url');
        $api_key = get_option('tablecrm_fixed_v3_api_key');
        
        // Логируем важные изменения для безопасности
        if (!empty($api_key) && strlen($api_key) > 10) {
            $this->log_info("Настройки обновлены. API URL: $api_url");
            
            // Маскируем API ключ в логах
            $masked_key = substr($api_key, 0, 4) . '***' . substr($api_key, -4);
            $this->log_info("API ключ обновлен: $masked_key");
        }
        
        // Проверяем критические ID на корректность
        $organization_id = get_option('tablecrm_fixed_v3_organization_id');
        $cashbox_id = get_option('tablecrm_fixed_v3_cashbox_id');
        
        if ($organization_id <= 0) {
            $this->log_warning("Некорректный Organization ID: $organization_id");
        }
        if ($cashbox_id <= 0) {
            $this->log_warning("Некорректный Cashbox ID: $cashbox_id");
        }
    }
    
    // Заглушки для совместимости
    public function send_cf7_to_tablecrm($contact_form) {}
    public function save_utm_data_to_order($order, $data) {}
    public function update_order_in_tablecrm($order_id) {
        // Для обновлений статуса также используем очередь
        $this->queue_order_sync($order_id);
    }

    // ---------- НОВЫЕ МЕТОДЫ ДЛЯ ОТПРАВКИ ИНФОРМАЦИИ О ДОСТАВКЕ ----------
    private function send_delivery_info($document_id, $order) {
        $this->log_info("Запуск send_delivery_info; Order ID: " . ($order ? $order->get_id() : 'unknown'));

        if (!$order || empty($document_id)) {
            $this->log_warning('send_delivery_info: отсутствует order или document_id');
            return false;
        }

        // Получаем дату доставки (timestamp) – не критично, если не найдено
        $delivery_timestamp = $this->get_delivery_date($order);

        // Формируем данные получателя и адреса
        $delivery_data = array(
            'recipient' => array(
                'name'  => trim($order->get_shipping_first_name() . ' ' . $order->get_shipping_last_name()) ?: trim($order->get_billing_first_name() . ' ' . $order->get_billing_last_name()),
                'phone' => $this->get_recipient_phone($order),
            ),
            'address'   => $this->get_recipient_address($order),
            'note'      => $order->get_customer_note(),
        );

        if ($delivery_timestamp) {
            $delivery_data['delivery_date'] = $delivery_timestamp;
        }

        $this->log_info('Подготовленные данные для delivery_info: ' . json_encode($delivery_data, JSON_UNESCAPED_UNICODE));

        // Отправляем запрос
        $response = $this->make_api_request("docs_sales/{$document_id}/delivery_info/", $delivery_data);

        if ($response['success']) {
            $this->log_success("Информация о доставке успешно отправлена для документа {$document_id}");
            $order->add_order_note(__('Информация о доставке успешно отправлена в TableCRM.', 'tablecrm'));
            return true;
        } else {
            $this->log_warning('Ошибка отправки информации о доставке: ' . $response['message']);
            $order->add_order_note(__('Не удалось отправить информацию о доставке в TableCRM.', 'tablecrm'));
            return false;
        }
    }

    private function get_delivery_date($order) {
        // Пытаемся получить дату доставки из различных мета-полей (CodeRockz и пр.)
        $raw_date = $this->get_order_meta($order, 'delivery_date');
        $raw_time = $this->get_order_meta($order, 'delivery_time');

        if (!empty($raw_date)) {
            $date_string = str_replace('/', '-', $raw_date);
            if (!empty($raw_time)) {
                $time_parts   = explode(' - ', $raw_time);
                $start_time   = trim($time_parts[0]);
                $date_string .= ' ' . $start_time;
            }

            $dt = DateTime::createFromFormat('d-m-Y H:i', $date_string);
            if (!$dt) {
                $dt = DateTime::createFromFormat('d-m-Y', $date_string);
                if ($dt) {
                    $dt->setTime(0, 0, 0);
                }
            }
            if ($dt) {
                return $dt->getTimestamp();
            }
        }
        return null;
    }

    private function get_recipient_phone($order) {
        $phone = $order->get_shipping_phone();
        if (!$phone) {
            $phone = $order->get_billing_phone();
        }
        // Небольшая нормализация: оставляем только цифры и +
        $phone = preg_replace('/[^0-9+]/', '', $phone);
        return $phone;
    }

    private function get_recipient_address($order) {
        $address_parts = array(
            $order->get_shipping_address_1(),
            $order->get_shipping_address_2(),
            $order->get_shipping_city(),
            $order->get_shipping_state(),
            $order->get_shipping_postcode(),
        );
        if (implode('', $address_parts) === '') {
            $address_parts = array(
                $order->get_billing_address_1(),
                $order->get_billing_address_2(),
                $order->get_billing_city(),
                $order->get_billing_state(),
                $order->get_billing_postcode(),
            );
        }
        return trim(preg_replace('/\s+,/', ',', implode(', ', array_filter($address_parts))));
    }

    // Универсальный метод получения мета (HPOS совместимость)
    private function get_order_meta($order, $key) {
        return method_exists($order, 'get_meta') ? $order->get_meta($key, true) : get_post_meta($order->get_id(), $key, true);
    }
}

// Очистка старых задач
add_action('tablecrm_cleanup_old_actions', function() {
    if (function_exists('as_unschedule_all_actions')) {
        // Удаляем завершенные задачи старше 7 дней
        $old_actions = as_get_scheduled_actions(array(
            'hook' => 'tablecrm_process_order',
            'status' => 'complete',
            'date_query' => array(
                'before' => date('Y-m-d H:i:s', strtotime('-7 days'))
            ),
            'per_page' => 100
        ));
        
        foreach ($old_actions as $action) {
            as_unschedule_action('tablecrm_process_order', $action->get_args(), $action->get_group());
        }
    }
});

// Инициализация плагина
function initialize_tablecrm_integration_fixed_v3() {
    global $tablecrm_integration_fixed_v3;
    $tablecrm_integration_fixed_v3 = new TableCRM_Integration_Fixed_V3();
}
add_action('plugins_loaded', 'initialize_tablecrm_integration_fixed_v3');

// Хуки активации/деактивации
register_activation_hook(__FILE__, 'tablecrm_fixed_v3_activation');
register_deactivation_hook(__FILE__, 'tablecrm_fixed_v3_deactivation');

function tablecrm_fixed_v3_activation() {
    // Устанавливаем настройки по умолчанию
    if (!get_option('tablecrm_fixed_v3_api_url')) {
        update_option('tablecrm_fixed_v3_api_url', 'https://app.tablecrm.com/api/v1/');
    }
    if (!get_option('tablecrm_fixed_v3_enable_orders')) {
        update_option('tablecrm_fixed_v3_enable_orders', 1);
    }
    if (!get_option('tablecrm_fixed_v3_send_real_data_only')) {
        update_option('tablecrm_fixed_v3_send_real_data_only', 1);
    }
    if (!get_option('tablecrm_fixed_v3_retry_failed')) {
        update_option('tablecrm_fixed_v3_retry_failed', 1);
    }
    if (!get_option('tablecrm_fixed_v3_debug_mode')) {
        update_option('tablecrm_fixed_v3_debug_mode', 1);
    }
    
    error_log('[TableCRM Integration v3] [SUCCESS] Плагин активирован с Action Scheduler');
}

function tablecrm_fixed_v3_deactivation() {
    // Очищаем запланированные задачи
    wp_clear_scheduled_hook('tablecrm_cleanup_old_actions');
    
    error_log('[TableCRM Integration v3] [INFO] Плагин деактивирован');
}