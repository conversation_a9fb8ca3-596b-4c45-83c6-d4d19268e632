#!/bin/bash

echo "📥 СКАЧИВАНИЕ ЛОГОВ С СЕРВЕРА..."

# Создаем папку для логов
mkdir -p server_logs
cd server_logs

echo "🔗 Подключение к FTP серверу bekflom3.beget.tech..."

ftp -n bekflom3.beget.tech << EOF
user bekflom3_wz zl2Shj!e&6ng
binary
cd public_html/wp-content
get debug.log debug_$(date +%Y%m%d_%H%M).log
get yookassa-debug.log yookassa_debug_$(date +%Y%m%d_%H%M).log
cd plugins
get tablecrm-integration-fixed-v3.php
quit
EOF

cd ..

echo "✅ Логи скачаны в папку server_logs/"
echo "📋 Список файлов:"
ls -la server_logs/

echo "📊 Размеры файлов:"
du -h server_logs/*

echo "🔍 Последние записи из debug.log:"
tail -20 server_logs/debug_*.log 