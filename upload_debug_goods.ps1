# Upload and run API data debug
$server = "bekflom3.beget.tech"
$user = "bekflom3_wz"
$pass = "zl2Shj!e&6ng"

Write-Host "Uploading API data debug script..." -ForegroundColor Green

try {
    $uri = "ftp://$server/public_html/debug_api_data.php"
    $webclient = New-Object System.Net.WebClient
    $webclient.Credentials = New-Object System.Net.NetworkCredential($user, $pass)
    $webclient.UploadFile($uri, "debug_api_data.php")
    Write-Host "API data debug script uploaded!" -ForegroundColor Green
    
    # Run the test
    Write-Host "Running API data debug..." -ForegroundColor Yellow
    $result = Invoke-WebRequest -Uri "https://mosbuketik.ru/debug_api_data.php" -UseBasicParsing
    Write-Host $result.Content -ForegroundColor White
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

$webclient.Dispose()
Write-Host "API data debug completed!" -ForegroundColor Green 