# 📝 Подробное описание текущей проблемы отправки заказа в TableCRM

## TL;DR
Файл **tablecrm-integration-complete-v1.0.24.php** на прод-сервере *не обновился*, поэтому
WordPress продолжает выполнять **старый байт-код** плагина. В результате:

1. В логах **нет** строк «`Request body:` …» и полного текста ответа CRM.
2. Телефон и адрес получателя остаются пустыми (старый код не содержит fallback-логики).
3. API TableCRM отвечает **500 Internal Server Error**, но тело ошибки не видно.

## ⛑️ Симптомы в логах
```
[INFO] === ОТПРАВКА ЗАКАЗА #21807 ===
[INFO] Shipping phone (get_shipping_phone):
[INFO] Shipping phone (meta shipping_phone):
[INFO] Shipping phone (meta _shipping_phone):
[INFO] Delivery address (formatted):
# …дальше сразу попытка вызова API…
# НЕТ строки «Request body: { … }»
# НЕТ строки «HTTP 500: { … }»
```

## 🤔 Почему так происходит
| Причина | Детали |
|---------|--------|
| 1. Файл не загружен | Скрипты **upload_*.ps1** не смогли докачать файл: 553/550 «Invalid file name» или разрыв строки PowerShell/PSReadLine. |
| 2. OPcache | Даже если файл загрузили, OPcache сохраняет старый байт-код. |
| 3. Не тот путь | Плагин может лежать прямо в `wp-content/plugins/`, а скрипт пытался `…/plugins/tablecrm-integration-complete/…`. |

## 🛠️ План решения
1. **Уточнить фактический путь**
   • В админке WP → Плагины → Редактировать → вверху увидите полный путь до подключаемого файла.
2. **Загрузить файл вручную**
   • Открыть проводник Windows и ввести:
     `*********************************/public_html/wp-content/plugins/`
   • Перетащить новый `tablecrm-integration-complete-v1.0.24.php` (или .25.php) поверх старого.
3. **(если нужно) Переименовать файл**
   • Переименовать локально в `tablecrm-integration-complete-v1.0.25.php`.
   • Загрузить.
   • В index-файле плагина изменить require_once на новое имя.
4. **Сбросить OPcache**
   • Самый простой способ — `touch` файла (изменить время) или временно деактивировать/активировать плагин в админке.
5. **Проверить логи**
   • После загрузки и сброса выполнить тест-заказ. Должны появиться строки:
     – `[INFO] Request body: { … }`
     – `[ERROR] HTTP 500: { … }` (если ошибка повторится).

## ✅ Что должно измениться после обновления
• Поле *Телефон получателя* заполняется из `shipping_phone`, `_shipping_phone` или дублируется из billing-телефона.
• Поле *Адрес доставки* формируется из мета-полей и не остаётся пустым.
• Все числа `price`, `sum_discounted`, `paid_rubles`, `delivery_price` передаются строкой в формате `#.##`.
• В логах фиксируется полный JSON запроса и ответ сервера.

## 📌 Контрольный чек-лист после фикса
- [ ] Файл плагина на сервере имеет свежую дату/размер.
- [ ] В логах заказа присутствует «Request body: …».
- [ ] Внутри JSON есть `"price": "8075.00"` (строка, не число).
- [ ] Нет пустых `shipping_phone` и `shipping_address`.
- [ ] API возвращает 201/200, документ создаётся в TableCRM.

## ⚠️ Конфликт на странице оформления заказа
*Симптом:* при попытке оплатить/оформить заказ WooCommerce выводил баннер «Произошла ошибка при обработке вашего заказа…».

*Причина:* плагин вызывал функцию `send_order_to_tablecrm()` из хука `woocommerce_checkout_order_processed`.  Если TableCRM отвечал медленно или 500-й ошибкой, процесс оформления прерывался до того, как WooCommerce успевал создать запись заказа.

*Исправление (v1.0.24-patch2)*  
Строка подключения хука закомментирована:
```php
// add_action('woocommerce_checkout_order_processed', [...]);
```
Теперь отправка выполняется только из `woocommerce_order_status_changed` после успешного создания заказа (обычно статусы *processing* или *completed*).

*Проверка:* оформляем заказ – сообщение об ошибке не появляется; в лог файл плагина запись об отправке появляется уже после перехода статуса.

## 🔬 Тест, подтверждающий, что ошибка на стороне CRM

> Цель — показать, что наш JSON корректен, токен рабочий, а 500-я ошибка воспроизводится при прямом POST без WordPress.

### Шаг 0. Проверяем сам API и токен
```bash
curl -s -o /dev/null -w "%{http_code}\n" \
     "https://app.tablecrm.com/api/v1/health?token=<API_TOKEN>"  # должен вернуть 200
```

### Шаг 1. Получаем «сырое» тело запроса
1. Обновляем файл плагина (см. выше) и сбрасываем OPcache (де-/активация плагина).
2. Создаём тест-заказ или повторно отправляем любой order → в логе `tablecrm_integration_complete.log` появится строка
   ```
   [INFO] Request body: { … }
   ```
3. Копируем JSON полностью.

*Быстрый способ* — открыть `debug_send_last_request.php`: он вытащит последнюю строку `Request body:` автоматически.

### Шаг 2. Ручной POST в Postman / curl
```bash
curl -X POST \
     "https://app.tablecrm.com/api/v1/docs_sales/?token=<API_TOKEN>" \
     -H "Content-Type: application/json" \
     -d '@raw.json'   # файл с сохранённым JSON
```
Ожидаемые варианты:
* **200 / 201** — JSON принят; ошибка была до фикса или связана с окружением PHP.
* **4xx** — проблемный формат (подсказка в теле ответа); исправляем данные.
* **500** — CRM падает на валидном JSON → отправляем в поддержку TableCRM:
  * timestamp запроса,
  * `organization_id` (38),
  * полный JSON.

### Альтернатива с PHP-скриптом
Откройте `https://<SITE>/debug_send_last_request.php` — скрипт:
1. берёт последний `Request body` из лога;
2. отправляет его тем же токеном;
3. выводит HTTP-код и первые 1000 симв. ответа.

Повторная 500-я ошибка доказывает, что баг на стороне TableCRM.

## 🔎 Дополнительные проверки (быстрый чек-лист)

1. **Проверить, что файл действительно заменён**  
   • Скачайте `tablecrm-integration-complete-v1.0.24.php` обратно c сервера и убедитесь, что в нём есть строки «Request body:», «Используемые ID: warehouse=…».  
   • Посмотрите дату изменения и размер: должна быть сегодняшняя дата и ≈ 105–110 KB.

2. **Убедиться, что исполняется новая версия, а не кеш OPcache**  
   2.1 Поменяйте `Version: 1.0.24` → `1.0.99` в шапке файла и перезагрузите страницу «Плагины». Если всё ещё показывается 1.0.24 — активен старый кеш.  
   2.2 Добавьте в начало файла строку `error_log('*** TableCRM plugin LOADED at '.date('H:i:s').' from '.__FILE__);` — перезагрузите страницу и найдите запись в `error_log`.  
   2.3 Создайте временный REST-эндпоинт:
   ```php
   add_action('rest_api_init', function () {
       register_rest_route('tablecrm/v1', '/ping', array(
           'methods'  => 'GET',
           'permission_callback' => '__return_true',
           'callback' => function () {
               return array(
                   'version' => TABLECRM_COMPLETE_VERSION,
                   'file'    => __FILE__,
                   'mtime'   => filemtime(__FILE__)
               );
           },
       ));
   });
   ```
   Затем откройте `https://SITE/wp-json/tablecrm/v1/ping` и проверьте, какой файл и версия реально загружены.

3. **Проверить, что API TableCRM отвечает**  
   • `curl -s -o /dev/null -w "%{http_code}\n" "https://app.tablecrm.com/api/v1/health?token=<API_TOKEN>"` должно вернуть 200.  
   • Отправьте минимальный тестовый JSON напрямую через `curl`/Postman. 2xx — API живое, 500 — проблема на стороне CRM.

4. **Убедиться, что хук `woocommerce_order_status_changed` отрабатывает**  
   • Смените статус тестового заказа на «Обработка»/«Выполнено» и посмотрите, появляется ли в логе строка `[INFO] Начинаем отправку заказа …`.  
   • Проверьте, что в настройках плагина отмечены именно эти статусы.

5. **Проверить правильность пути плагина**  
   Если есть WP-CLI: `wp option get active_plugins | grep tablecrm` — увидите точный путь, откуда WordPress подгружает файл.

---

**Дата:** 12.06.2025  
**Автор:** AutoDoc generator 