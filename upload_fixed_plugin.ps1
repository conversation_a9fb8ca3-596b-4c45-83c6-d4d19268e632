# Upload fixed plugin with delivery API fix
$localFile = "tablecrm-integration-complete-v1.0.24.php"
$remoteFile = "/home/<USER>/bekflom3/mosbuketik.ru/public_html/wp-content/plugins/tablecrm-integration/tablecrm-integration.php"
$server = "bekflom3.beget.tech"
$username = "bekflom3"

Write-Host "=== UPLOADING FIXED DELIVERY API ===" -ForegroundColor Green

# Upload file
Write-Host "Uploading fixed plugin..." -ForegroundColor Yellow
scp $localFile "${username}@${server}:${remoteFile}"

if ($LASTEXITCODE -eq 0) {
    Write-Host "Plugin uploaded successfully" -ForegroundColor Green
    Write-Host "Fixed issues:" -ForegroundColor Cyan
    Write-Host "- Recipient field now sends as string instead of JSON object" -ForegroundColor White
    Write-Host "- Phone number moved to note field" -ForegroundColor White
    Write-Host "- Improved phone number filtering" -ForegroundColor White
    Write-Host "- Added delivery date support" -ForegroundColor White
} else {
    Write-Host "Error uploading plugin" -ForegroundColor Red
}

Write-Host "=== COMPLETED ===" -ForegroundColor Green 