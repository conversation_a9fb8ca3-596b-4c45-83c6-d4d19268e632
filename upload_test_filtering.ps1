# Upload and run phone filtering test
$server = "bekflom3.beget.tech"
$user = "bekflom3_wz"
$pass = "zl2Shj!e&6ng"

Write-Host "Uploading phone filtering test..." -ForegroundColor Green

try {
    $uri = "ftp://$server/public_html/test_phone_filtering.php"
    $webclient = New-Object System.Net.WebClient
    $webclient.Credentials = New-Object System.Net.NetworkCredential($user, $pass)
    $webclient.UploadFile($uri, "test_phone_filtering.php")
    Write-Host "Phone filtering test uploaded!" -ForegroundColor Green
    
    # Run the test
    Write-Host "Running phone filtering test..." -ForegroundColor Yellow
    $result = Invoke-WebRequest -Uri "https://mosbuketik.ru/test_phone_filtering.php" -UseBasicParsing
    Write-Host $result.Content -ForegroundColor White
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

$webclient.Dispose()
Write-Host "Phone filtering test completed!" -ForegroundColor Green 