# 📋 ИНСТРУКЦИЯ: Диагностика активного плагина TableCRM

Если названия товаров все еще не передаются, значит, на сервере активна **старая версия плагина**. Этот скрипт поможет нам точно узнать, какой плагин работает.

### ШАГ 1: Загрузите скрипт на сервер

1.  **Создайте** новый файл `check_plugin_status.php` в корне вашего сайта (там же, где и `wp-config.php`).
2.  **Скопируйте** в него код, который я предоставил.
3.  **Загрузите** этот файл на сервер в корневую директорию сайта.

### ШАГ 2: Запустите скрипт

1.  Откройте в браузере ссылку:
    **`https://mos-buketik.ru/check_plugin_status.php`**

### ШАГ 3: Пришлите мне результат

1.  Вы увидите текстовый отчет с диагностической информацией.
2.  **Скопируйте весь этот текст** и пришлите его мне в чат.

### ШАГ 4: Удалите скрипт (ОЧЕНЬ ВАЖНО!)

1.  После того как вы скопировали результат, **обязательно удалите файл `check_plugin_status.php`** с вашего сервера. Оставлять его небезопасно.

---

## 🤔 Что мы ожидаем увидеть?

Скрипт покажет:
-   Все плагины TableCRM, которые установлены на сайте.
-   Какой из них **АКТИВЕН**.
-   **Версию** активного плагина.

**Идеальный результат:**
```
✅ Активный плагин: TableCRM Integration (Fixed v3 - Security Enhanced) (Версия: 1.0.15)
🎉 ПОЗДРАВЛЯЕМ! Активна правильная версия v1.0.15.
```

**Вероятный результат (если проблема осталась):**
```
❌ ВНИМАНИЕ! Активна старая версия (1.0.14 или ниже). Необходимо активировать версию 1.0.15.
```

Эта информация позволит нам точно определить причину проблемы и устранить ее. 