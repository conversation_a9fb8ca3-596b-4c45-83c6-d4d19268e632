$s  = 'bekflom3.beget.tech'
$u  = 'bekflom3_wz'
$p  = 'zl2Shj!e&6ng'
$d  = 'server_logs_latest'

if (!(Test-Path $d)) { New-Item -ItemType Directory -Path $d | Out-Null }

$uri = "ftp://$s/public_html/wp-content/uploads/tablecrm_integration_complete.log"
$dst = "$d/tablecrm_integration_complete_latest.log"

$wc = New-Object System.Net.WebClient
$wc.Credentials = New-Object System.Net.NetworkCredential($u,$p)
$wc.DownloadFile($uri,$dst)

Write-Host '=== LAST 40 LINES ==='
Get-Content $dst -Tail 40 