<?php
/**
 * Диагностика TableCRM Integration Plugin
 * Версия: 1.0.1
 */

// Прямой запуск без подключения WordPress
echo "🔍 ДИАГНОСТИКА ПЛАГИНА TABLECRM\n";
echo "===============================\n\n";

$wp_path = '/home/<USER>/bekflom3/mosbuketik.ru/public_html';
$plugins_dir = $wp_path . '/wp-content/plugins';

echo "📂 Проверка структуры:\n";
echo "WordPress путь: $wp_path\n";
echo "Плагины: $plugins_dir\n\n";

// Поиск файлов плагина
echo "🔍 ПОИСК ФАЙЛОВ ПЛАГИНА:\n";
echo "========================\n";

$search_files = [
    'tablecrm-integration.php',
    'tablecrm-integration/tablecrm-integration.php', 
    'tablecrm-integration-complete-v1.0.24.php',
    'tablecrm-integration-fixed-v2.php'
];

$found_files = [];
foreach ($search_files as $file) {
    $path = $plugins_dir . '/' . $file;
    if (file_exists($path)) {
        $found_files[] = $file;
        echo "✅ $file\n";
        echo "   Размер: " . number_format(filesize($path)) . " байт\n";
        echo "   Изменен: " . date('Y-m-d H:i:s', filemtime($path)) . "\n";
        
        // Читаем версию из файла
        $content = file_get_contents($path, false, null, 0, 1500);
        if (preg_match('/Version:\s*(.+)/i', $content, $m)) {
            echo "   Версия: " . trim($m[1]) . "\n";
        }
        if (preg_match('/Plugin Name:\s*(.+)/i', $content, $m)) {
            echo "   Название: " . trim($m[1]) . "\n";
        }
        echo "\n";
    }
}

if (empty($found_files)) {
    echo "❌ НИ ОДИН ФАЙЛ ПЛАГИНА НЕ НАЙДЕН!\n\n";
}

// Проверка активных плагинов в базе
echo "💾 ПРОВЕРКА БАЗЫ ДАННЫХ:\n";
echo "========================\n";

// Подключение к базе через wp-config
if (file_exists($wp_path . '/wp-config.php')) {
    include_once($wp_path . '/wp-config.php');
    
    try {
        $pdo = new PDO(
            'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8',
            DB_USER,
            DB_PASSWORD
        );
        
        // Проверяем активные плагины
        $stmt = $pdo->prepare("SELECT option_value FROM {$table_prefix}options WHERE option_name = 'active_plugins'");
        $stmt->execute();
        $active_plugins = $stmt->fetchColumn();
        
        if ($active_plugins) {
            $plugins = unserialize($active_plugins);
            echo "Всего активных плагинов: " . count($plugins) . "\n";
            
            foreach ($plugins as $plugin) {
                if (stripos($plugin, 'tablecrm') !== false) {
                    echo "✅ АКТИВЕН: $plugin\n";
                }
            }
        }
        
        // Проверяем настройки TableCRM
        echo "\n⚙️ НАСТРОЙКИ TABLECRM:\n";
        $settings = [
            'tablecrm_api_token',
            'tablecrm_api_url',
            'tablecrm_warehouse_id',
            'tablecrm_organization_id'
        ];
        
        foreach ($settings as $setting) {
            $stmt = $pdo->prepare("SELECT option_value FROM {$table_prefix}options WHERE option_name = ?");
            $stmt->execute([$setting]);
            $value = $stmt->fetchColumn();
            echo "$setting: " . ($value ?: 'НЕ УСТАНОВЛЕНО') . "\n";
        }
        
    } catch (Exception $e) {
        echo "❌ Ошибка подключения к БД: " . $e->getMessage() . "\n";
    }
} else {
    echo "❌ wp-config.php не найден\n";
}

echo "\n";

// Проверка логов
echo "📋 АНАЛИЗ ЛОГОВ:\n";
echo "===============\n";

$log_files = [
    $wp_path . '/wp-content/debug.log',
    $wp_path . '/debug.log'
];

foreach ($log_files as $log) {
    if (file_exists($log)) {
        echo "✅ " . basename($log) . "\n";
        echo "   Размер: " . number_format(filesize($log)) . " байт\n";
        echo "   Последнее изменение: " . date('Y-m-d H:i:s', filemtime($log)) . "\n";
        
        // Последние записи TableCRM
        $lines = file($log);
        $tablecrm_lines = array_filter($lines, function($line) {
            return stripos($line, 'tablecrm') !== false;
        });
        
        $recent = array_slice($tablecrm_lines, -5);
        if (!empty($recent)) {
            echo "   Последние записи:\n";
            foreach ($recent as $line) {
                echo "   > " . trim($line) . "\n";
            }
        }
        echo "\n";
    }
}

echo "🏁 Диагностика завершена: " . date('Y-m-d H:i:s') . "\n";
?> 