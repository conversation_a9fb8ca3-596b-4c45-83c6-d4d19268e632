<?php
// Debug goods issue
echo "=== ДИАГНОСТИКА ПРОБЛЕМЫ С ТОВАРАМИ ===\n";

$wp_path = '/home/<USER>/bekflom3/mosbuketik.ru/public_html';
require_once($wp_path . '/wp-config.php');
require_once($wp_path . '/wp-load.php');

echo "WordPress загружен успешно\n\n";

// Test the order from logs
$test_order_id = 21775;
echo "=== ПРОВЕРКА ЗАКАЗА #$test_order_id (из логов) ===\n";
$order = wc_get_order($test_order_id);

if ($order) {
    echo "✅ Заказ найден\n";
    echo "Статус: " . $order->get_status() . "\n";
    
    // Get order items
    $items = $order->get_items();
    echo "Количество товаров: " . count($items) . "\n";
    
    if (!empty($items)) {
        echo "ТОВАРЫ В ЗАКАЗЕ:\n";
        foreach ($items as $item_id => $item) {
            echo "  - ID: $item_id, Название: " . $item->get_name() . " (кол-во: " . $item->get_quantity() . ")\n";
            echo "    Цена за единицу: " . $order->get_item_total($item, false, false) . "\n";
        }
    } else {
        echo "❌ ТОВАРОВ НЕТ!\n";
    }
    
    echo "Стоимость доставки: " . $order->get_shipping_total() . "\n";
    echo "Общая сумма: " . $order->get_total() . "\n";
    
    // Check if sent to TableCRM
    $doc_id = get_post_meta($test_order_id, '_tablecrm_complete_document_id', true);
    if ($doc_id) {
        echo "Отправлен в TableCRM: Document ID $doc_id\n";
    } else {
        echo "НЕ отправлен в TableCRM\n";
    }
    
    // Test the adapt_data_format method to see what's being sent
    echo "\n=== ТЕСТ ФОРМИРОВАНИЯ ДАННЫХ ДЛЯ API ===\n";
    
    global $tablecrm_integration_complete;
    if (isset($tablecrm_integration_complete)) {
        $warehouse_id = get_option('tablecrm_complete_warehouse_id');
        $organization_id = get_option('tablecrm_complete_organization_id');
        $paybox_id = get_option('tablecrm_complete_cashbox_id');
        $contragent_id = 286675; // From logs
        
        // Create nomenclature map for items
        $nomenclature_ids = array();
        foreach ($items as $item_id => $item) {
            $nomenclature_ids[$item_id] = 12345; // Dummy ID for test
        }
        
        // Use reflection to call private method
        $reflection = new ReflectionClass($tablecrm_integration_complete);
        $method = $reflection->getMethod('adapt_data_format');
        $method->setAccessible(true);
        
        $adapted_data = $method->invoke(
            $tablecrm_integration_complete, 
            $order, 
            $warehouse_id, 
            $organization_id, 
            $paybox_id, 
            $contragent_id, 
            $nomenclature_ids
        );
        
        if ($adapted_data) {
            echo "Данные для API сформированы:\n";
            echo "Количество товаров в goods: " . count($adapted_data[0]['goods']) . "\n";
            
            foreach ($adapted_data[0]['goods'] as $i => $good) {
                echo "  Товар " . ($i+1) . ": nomenclature=" . $good['nomenclature'] . 
                     ", price=" . $good['price'] . 
                     ", quantity=" . $good['quantity'] . "\n";
            }
        } else {
            echo "❌ Ошибка формирования данных\n";
        }
    }
    
} else {
    echo "❌ Заказ не найден\n";
}

echo "\n=== ДИАГНОСТИКА ЗАВЕРШЕНА ===\n";
?> 