#!/bin/bash

# Скрипт для обновления исправленного плагина TableCRM Integration на сервере
# Исправляет проблему с несуществующей организацией ID 38

echo "=== Обновление плагина TableCRM Integration ==="
echo "Исправление: убираем жестко прописанные ID организации 38"
echo ""

# Установка утилит если нужно
if ! command -v expect &> /dev/null; then
    echo "Установка expect..."
    sudo apt-get update && sudo apt-get install -y expect
fi

# SFTP соединение для обновления файла плагина
expect << 'EOF'
set timeout 60
spawn sftp -o StrictHostKeyChecking=no <EMAIL>
expect "password:"
send "2WMzGV5r\r"
expect "sftp>"

# Переходим в директорию плагина
send "cd mosbuketik.ru/public_html/wp-content/plugins/tablecrm-integration\r"
expect "sftp>"

# Загружаем исправленный файл плагина
send "put tablecrm-integration.php\r"
expect "sftp>"

# Проверяем загрузку
send "ls -la tablecrm-integration.php\r"
expect "sftp>"

send "exit\r"
expect eof
EOF

echo ""
echo "✅ Плагин обновлен на сервере!"
echo ""
echo "ВАЖНО: Теперь нужно в админ-панели WordPress указать правильные ID:"
echo ""
echo "1. Войдите в админ-панель WordPress: https://mosbuketik.ru/wp-admin/"
echo "2. Перейдите в Настройки → TableCRM Integration"
echo "3. Укажите корректные значения:"
echo "   - ID Организации: (посмотрите в выпадающем списке TableCRM)"
echo "   - ID Кассы: 1 (обычно)"
echo "   - ID Менеджера: (ваш ID пользователя в TableCRM)"
echo ""
echo "Для поиска ID организации:"
echo "1. Откройте TableCRM → Продажи → Новая продажа"
echo "2. В поле 'Введите организацию' посмотрите доступные варианты"
echo "3. Выберите нужную организацию и запомните её ID"
echo ""
echo "После настройки используйте кнопку 'Тестировать соединение' для проверки!" 