#!/bin/bash

echo "🔍 Ручная проверка логов TableCRM Integration"
echo "============================================="

echo ""
echo "📋 Для проверки логов выполните следующие команды:"
echo ""

echo "1️⃣ Подключение к серверу через SFTP:"
echo "   sftp <EMAIL>"
echo "   Пароль: zl2Shj!e&6ng"
echo ""

echo "2️⃣ Переход в папку WordPress:"
echo "   cd public_html"
echo ""

echo "3️⃣ Проверка debug.log:"
echo "   ls -la wp-content/debug.log"
echo "   get wp-content/debug.log"
echo "   (затем локально: tail -50 debug.log | grep -i tablecrm)"
echo ""

echo "4️⃣ Проверка error.log:"
echo "   ls -la wp-content/error.log"
echo "   get wp-content/error.log"
echo ""

echo "5️⃣ Проверка плагина:"
echo "   ls -la wp-content/plugins/tablecrm-integration/"
echo ""

echo "6️⃣ Проверка конфигурации:"
echo "   get wp-config.php"
echo "   (затем локально: grep -i debug wp-config.php)"
echo ""

echo "7️⃣ Выход из SFTP:"
echo "   quit"
echo ""

echo "🚀 Автоматическая проверка через lftp:"
echo ""

# Простая автоматическая проверка
lftp -u bekflom3_wz,zl2Shj!e%266ng -e "
set ssl:verify-certificate no;
cd public_html;
echo '📁 Текущая папка:';
pwd;
echo '📋 Содержимое wp-content:';
ls wp-content/;
echo '📋 Плагины:';
ls wp-content/plugins/ | grep -i tablecrm || echo 'TableCRM плагин не найден';
echo '📋 Проверка debug.log:';
ls -l wp-content/debug.log || echo 'debug.log не найден';
echo '📋 Проверка error.log:';
ls -l wp-content/error.log || echo 'error.log не найден';
quit
" bekflom3.beget.tech

echo ""
echo "✅ Автоматическая проверка завершена!"
echo ""
echo "📝 Инструкции для включения логирования:"
echo ""
echo "Если debug.log не найден, добавьте в wp-config.php:"
echo "define('WP_DEBUG', true);"
echo "define('WP_DEBUG_LOG', true);"
echo "define('WP_DEBUG_DISPLAY', false);"
echo ""
echo "📞 Для полной диагностики используйте:"
echo "./check_plugins.sh - проверка активности плагинов"
echo "./update_plugin.sh - обновление плагина" 