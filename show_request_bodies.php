<?php
// Show request bodies for TableCRM API
echo "=== ТЕЛА ЗАПРОСОВ К TABLECRM API ===\n";

$wp_path = '/home/<USER>/bekflom3/mosbuketik.ru/public_html';
require_once($wp_path . '/wp-config.php');
require_once($wp_path . '/wp-load.php');

echo "WordPress загружен успешно\n\n";

// Test with order 21780 (has UTM data)
$order_id = 21780;
$order = wc_get_order($order_id);

if (!$order) {
    echo "❌ Заказ #$order_id не найден\n";
    exit;
}

echo "✅ Тестируем заказ #$order_id\n";
echo "Клиент: " . $order->get_formatted_billing_full_name() . "\n";
echo "Сумма: " . $order->get_total() . " руб.\n\n";

// Get plugin settings
$api_url = get_option('tablecrm_complete_api_url', 'https://app.tablecrm.com/api/v1/');
$api_token = get_option('tablecrm_complete_api_key');
$warehouse_id = get_option('tablecrm_complete_warehouse_id');
$organization_id = get_option('tablecrm_complete_organization_id');
$paybox_id = get_option('tablecrm_complete_cashbox_id');

echo "=== НАСТРОЙКИ API ===\n";
echo "API URL: $api_url\n";
echo "Organization ID: $organization_id\n";
echo "Warehouse ID: $warehouse_id\n";
echo "Paybox ID: $paybox_id\n";
echo "API Token: " . substr($api_token, 0, 20) . "...\n\n";

// Simulate contragent creation
echo "=== 1. СОЗДАНИЕ КОНТРАГЕНТА ===\n";
$phone = $order->get_billing_phone();
$normalized_phone = preg_replace('/[^0-9]/', '', $phone);
if (strlen($normalized_phone) == 11 && substr($normalized_phone, 0, 1) == '8') {
    $normalized_phone = '7' . substr($normalized_phone, 1);
}
if (strlen($normalized_phone) == 10) {
    $normalized_phone = '7' . $normalized_phone;
}
$search_phone = '+' . $normalized_phone;

$first_name = $order->get_billing_first_name();
$last_name = $order->get_billing_last_name();
$full_name = trim($first_name . ' ' . $last_name);
$email = $order->get_billing_email();

$contragent_data = array(
    'name' => $full_name,
    'phone' => $search_phone,
    'email' => $email,
    'our_cost' => false
);

echo "URL: POST {$api_url}contragents/?token=***\n";
echo "Content-Type: application/json; charset=utf-8\n";
echo "BODY:\n";
echo json_encode($contragent_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";

// Simulate nomenclature creation
echo "=== 2. СОЗДАНИЕ НОМЕНКЛАТУРЫ ===\n";
$items = $order->get_items();
$item = reset($items); // Get first item
$item_name = $item->get_name();
$unit_id = get_option('tablecrm_complete_unit_id', 116);

$nomenclature_data = array(array(
    'name' => $item_name,
    'unit' => $unit_id
));

echo "URL: POST {$api_url}nomenclature/?token=***\n";
echo "Content-Type: application/json; charset=utf-8\n";
echo "BODY:\n";
echo json_encode($nomenclature_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";

// Simulate delivery nomenclature creation
$shipping_total = $order->get_shipping_total();
if ($shipping_total > 0) {
    echo "=== 3. СОЗДАНИЕ НОМЕНКЛАТУРЫ ДОСТАВКИ ===\n";
    $delivery_nomenclature_data = array(array(
        'name' => 'Доставка',
        'unit' => $unit_id
    ));
    
    echo "URL: POST {$api_url}nomenclature/?token=***\n";
    echo "Content-Type: application/json; charset=utf-8\n";
    echo "BODY:\n";
    echo json_encode($delivery_nomenclature_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";
}

// Simulate order creation
echo "=== 4. СОЗДАНИЕ ЗАКАЗА ===\n";

// Mock nomenclature IDs (from previous successful tests)
$nomenclature_id = 45656; // Букет из 11 розовых пионов в крафте
$delivery_nomenclature_id = 45658; // Доставка
$contragent_id = 337150; // Mock contragent ID

$goods = array();

// Add main product
$price = (float) $order->get_item_total($item, false, false);
$goods[] = array(
    'price' => $price,
    'quantity' => $item->get_quantity(),
    'unit' => (int) $unit_id,
    'discount' => 0,
    'sum_discounted' => 0,
    'nomenclature' => (int) $nomenclature_id,
);

// Add delivery
if ($shipping_total > 0) {
    $goods[] = array(
        'price' => (float) $shipping_total,
        'quantity' => 1,
        'unit' => (int) $unit_id,
        'discount' => 0,
        'sum_discounted' => 0,
        'nomenclature' => (int) $delivery_nomenclature_id,
    );
}

$comment_text = "Заказ с сайта №{$order_id}";
if ($note = $order->get_customer_note()) {
    $comment_text .= ". Комментарий клиента: " . $note;
}

if ($shipping_total > 0) {
    $shipping_method = $order->get_shipping_method();
    if ($shipping_method) {
        $comment_text .= ". Способ доставки: " . $shipping_method;
    }
}

$order_data = array(array(
    'dated' => time(),
    'operation' => 'Заказ',
    'comment' => $comment_text,
    'tax_included' => true,
    'tax_active' => true,
    'goods' => $goods,
    'settings' => new stdClass(),
    'warehouse' => (int) $warehouse_id,
    'contragent' => (int) $contragent_id,
    'paybox' => (int) $paybox_id,
    'organization' => (int) $organization_id,
    'status' => false,
    'paid_rubles' => (string) $order->get_total(),
    'paid_lt' => 0,
));

echo "URL: POST {$api_url}docs_sales/?token=***\n";
echo "Content-Type: application/json; charset=utf-8\n";
echo "BODY:\n";
echo json_encode($order_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";

// Show UTM data
echo "=== 5. ОТПРАВКА UTM-ДАННЫХ (ОБНОВЛЕННАЯ СТРУКТУРА) ===\n";
$meta_data = $order->get_meta_data();
$utm_data = array();

foreach ($meta_data as $meta) {
    $key = $meta->get_data()['key'];
    $value = $meta->get_data()['value'];
    
    if (strpos($key, '_wc_order_attribution_') === 0) {
        $clean_key = str_replace('_wc_order_attribution_', '', $key);
        
        // Map to UTM format according to API documentation
        if ($clean_key == 'utm_source') $utm_data['utm_source'] = $value;
        if ($clean_key == 'utm_medium') $utm_data['utm_medium'] = $value;
        if ($clean_key == 'utm_campaign') $utm_data['utm_campaign'] = $value;
        if ($clean_key == 'utm_term') $utm_data['utm_term'] = array($value); // API expects array
        if ($clean_key == 'utm_content') $utm_data['utm_content'] = $value;
        if ($clean_key == 'referrer') $utm_data['referrer'] = $value;
        if ($clean_key == 'source_type') $utm_data['source_type'] = $value;
        if ($clean_key == 'device_type') $utm_data['device_type'] = $value;
        if ($clean_key == 'user_agent') $utm_data['user_agent'] = substr($value, 0, 255);
    }
}

// Add extended UTM fields if available
$extended_utm_example = array(
    'utm_name' => 'Анастасия',
    'utm_phone' => '+79179551570',
    'utm_email' => '<EMAIL>',
    'utm_leadid' => 'lead_12345',
    'utm_yclientid' => 'yandex_client_67890',
    'utm_gaclientid' => 'ga_client_54321'
);

// Merge with existing data (example values)
$utm_data = array_merge($utm_data, $extended_utm_example);

$document_id = 138285; // Mock document ID from successful test

if (!empty($utm_data)) {
    echo "URL: POST {$api_url}docs_sales/{$document_id}/utm/?token=***\n";
    echo "Content-Type: application/json; charset=utf-8\n";
    echo "BODY:\n";
    echo json_encode($utm_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";
} else {
    echo "UTM-данные отсутствуют\n\n";
}

// Show delivery info
echo "=== 6. ОТПРАВКА ИНФОРМАЦИИ О ДОСТАВКЕ (ОБНОВЛЕННАЯ СТРУКТУРА) ===\n";
if ($shipping_total > 0) {
    $delivery_data = array();
    
    // Address
    $shipping_address = array();
    if ($order->get_shipping_address_1()) {
        $shipping_address[] = $order->get_shipping_address_1();
    }
    if ($order->get_shipping_address_2()) {
        $shipping_address[] = $order->get_shipping_address_2();
    }
    if ($order->get_shipping_city()) {
        $shipping_address[] = $order->get_shipping_city();
    }
    if ($order->get_shipping_postcode()) {
        $shipping_address[] = $order->get_shipping_postcode();
    }
    
    if (!empty($shipping_address)) {
        $delivery_data['address'] = implode(', ', $shipping_address);
    }
    
    // If no shipping address, use billing
    if (empty($delivery_data['address'])) {
        $billing_address = array();
        if ($order->get_billing_address_1()) {
            $billing_address[] = $order->get_billing_address_1();
        }
        if ($order->get_billing_address_2()) {
            $billing_address[] = $order->get_billing_address_2();
        }
        if ($order->get_billing_city()) {
            $billing_address[] = $order->get_billing_city();
        }
        if ($order->get_billing_postcode()) {
            $billing_address[] = $order->get_billing_postcode();
        }
        
        if (!empty($billing_address)) {
            $delivery_data['address'] = implode(', ', $billing_address);
        }
    }
    
    // Delivery date (0 if not specified)
    $delivery_data['delivery_date'] = 0;
    
    // Recipient object according to API documentation
    $recipient = array();
    
    $first_name = $order->get_shipping_first_name() ?: $order->get_billing_first_name();
    $last_name = $order->get_shipping_last_name() ?: $order->get_billing_last_name();
    
    if (!empty($first_name)) {
        $recipient['name'] = $first_name;
    }
    if (!empty($last_name)) {
        $recipient['surname'] = $last_name;
    }
    
    $phone = $order->get_shipping_phone() ?: $order->get_billing_phone();
    if (!empty($phone)) {
        $recipient['phone'] = $phone;
    }
    
    if (!empty($recipient)) {
        $delivery_data['recipient'] = $recipient;
    }
    
    // Note with delivery method and comments
    $note_parts = array();
    
    $shipping_method = $order->get_shipping_method();
    if ($shipping_method) {
        $note_parts[] = "Способ доставки: " . $shipping_method;
    }
    
    if ($shipping_total > 0) {
        $note_parts[] = "Стоимость доставки: " . $shipping_total . " руб.";
    }
    
    $customer_note = $order->get_customer_note();
    if (!empty($customer_note)) {
        $note_parts[] = "Комментарий клиента: " . $customer_note;
    }
    
    if (!empty($note_parts)) {
        $delivery_data['note'] = implode('. ', $note_parts);
    }
    
    echo "URL: POST {$api_url}docs_sales/{$document_id}/delivery_info/?token=***\n";
    echo "Content-Type: application/json; charset=utf-8\n";
    echo "BODY:\n";
    echo json_encode($delivery_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";
} else {
    echo "Доставка отсутствует\n\n";
}

echo "=== АНАЛИЗ ЗАВЕРШЕН ===\n";
?> 