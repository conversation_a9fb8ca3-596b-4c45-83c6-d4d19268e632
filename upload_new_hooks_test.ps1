# Upload and run new hooks test
$server = "bekflom3.beget.tech"
$user = "bekflom3_wz"
$pass = "zl2Shj!e&6ng"

Write-Host "Uploading new hooks test script..." -ForegroundColor Green

try {
    $uri = "ftp://$server/public_html/test_new_order_hooks.php"
    $webclient = New-Object System.Net.WebClient
    $webclient.Credentials = New-Object System.Net.NetworkCredential($user, $pass)
    $webclient.UploadFile($uri, "test_new_order_hooks.php")
    Write-Host "New hooks test script uploaded!" -ForegroundColor Green
    
    # Run the test
    Write-Host "Running new order hooks test..." -ForegroundColor Yellow
    $result = Invoke-WebRequest -Uri "https://mosbuketik.ru/test_new_order_hooks.php" -UseBasicParsing
    Write-Host $result.Content -ForegroundColor White
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

$webclient.Dispose()
Write-Host "New hooks test completed!" -ForegroundColor Green 