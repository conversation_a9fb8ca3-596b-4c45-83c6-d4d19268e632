# Upload and run order test script
$server = "bekflom3.beget.tech"
$user = "bekflom3_wz"
$pass = "zl2Shj!e&6ng"

Write-Host "Uploading order test script..." -ForegroundColor Green

try {
    $uri = "ftp://$server/public_html/test_order_creation.php"
    $webclient = New-Object System.Net.WebClient
    $webclient.Credentials = New-Object System.Net.NetworkCredential($user, $pass)
    $webclient.UploadFile($uri, "test_order_creation.php")
    Write-Host "Test script uploaded!" -ForegroundColor Green
    
    # Run the test script
    Write-Host "Running order test..." -ForegroundColor Yellow
    $result = Invoke-WebRequest -Uri "https://mosbuketik.ru/test_order_creation.php" -UseBasicParsing
    Write-Host $result.Content -ForegroundColor White
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

$webclient.Dispose()
Write-Host "Order test completed!" -ForegroundColor Green 