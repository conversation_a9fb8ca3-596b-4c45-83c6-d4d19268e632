<?php
// Check all logs for errors and recent activity
echo "=== ПОЛНАЯ ПРОВЕРКА ЛОГОВ TABLECRM ===\n";

$wp_path = '/home/<USER>/bekflom3/mosbuketik.ru/public_html';
require_once($wp_path . '/wp-config.php');
require_once($wp_path . '/wp-load.php');

echo "WordPress загружен успешно\n\n";

$log_file = $wp_path . '/wp-content/uploads/tablecrm_integration_complete.log';

if (file_exists($log_file)) {
    echo "Файл логов найден\n";
    echo "Размер файла: " . round(filesize($log_file) / 1024, 2) . " KB\n\n";
    
    $log_content = file_get_contents($log_file);
    $lines = explode("\n", $log_content);
    
    echo "Общее количество строк в логе: " . count($lines) . "\n\n";
    
    // Поиск ошибок
    echo "=== ПОИСК ОШИБОК ===\n";
    $error_lines = array();
    foreach ($lines as $line_num => $line) {
        if (strpos($line, '[ERROR]') !== false || 
            strpos($line, '[WARNING]') !== false ||
            strpos($line, 'Ошибка') !== false ||
            strpos($line, 'ошибка') !== false ||
            strpos($line, 'Error') !== false ||
            strpos($line, 'Failed') !== false ||
            strpos($line, 'HTTP 4') !== false ||
            strpos($line, 'HTTP 5') !== false) {
            $error_lines[] = ($line_num + 1) . ": " . $line;
        }
    }
    
    if (!empty($error_lines)) {
        echo "НАЙДЕННЫЕ ОШИБКИ:\n";
        foreach (array_slice($error_lines, -10) as $error_line) { // Последние 10 ошибок
            echo $error_line . "\n";
        }
    } else {
        echo "✅ Ошибки не найдены\n";
    }
    
    // Поиск активности за последние 2 часа
    echo "\n=== АКТИВНОСТЬ ЗА ПОСЛЕДНИЕ 2 ЧАСА ===\n";
    $current_time = time();
    $two_hours_ago = $current_time - (2 * 60 * 60);
    
    $recent_lines = array();
    foreach ($lines as $line_num => $line) {
        if (preg_match('/\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\]/', $line, $matches)) {
            $log_time = strtotime($matches[1]);
            if ($log_time >= $two_hours_ago) {
                $recent_lines[] = ($line_num + 1) . ": " . $line;
            }
        }
    }
    
    if (!empty($recent_lines)) {
        echo "Активность за последние 2 часа (" . count($recent_lines) . " записей):\n";
        foreach (array_slice($recent_lines, -20) as $recent_line) { // Последние 20 записей
            echo $recent_line . "\n";
        }
    } else {
        echo "❌ Активности за последние 2 часа не найдено\n";
    }
    
    // Поиск новых заказов
    echo "\n=== ПОИСК НОВЫХ ЗАКАЗОВ ===\n";
    $order_lines = array();
    foreach ($lines as $line_num => $line) {
        if (strpos($line, 'Начинаем отправку заказа') !== false ||
            strpos($line, 'Заказ') !== false && strpos($line, 'успешно отправлен') !== false ||
            strpos($line, 'Document ID') !== false) {
            $order_lines[] = ($line_num + 1) . ": " . $line;
        }
    }
    
    if (!empty($order_lines)) {
        echo "НАЙДЕННЫЕ ЗАКАЗЫ:\n";
        foreach (array_slice($order_lines, -10) as $order_line) { // Последние 10 заказов
            echo $order_line . "\n";
        }
    } else {
        echo "❌ Записи о заказах не найдены\n";
    }
    
    // Показать последние 30 строк лога
    echo "\n=== ПОСЛЕДНИЕ 30 СТРОК ЛОГА ===\n";
    $last_lines = array_slice($lines, -30);
    foreach ($last_lines as $i => $line) {
        $line_num = count($lines) - 30 + $i + 1;
        echo "$line_num: $line\n";
    }
    
} else {
    echo "❌ Файл логов не найден: $log_file\n";
}

// Проверим также последние заказы в WooCommerce
echo "\n=== ПОСЛЕДНИЕ ЗАКАЗЫ WOOCOMMERCE ===\n";
$orders = wc_get_orders(array(
    'limit' => 5,
    'orderby' => 'date',
    'order' => 'DESC',
    'status' => array('processing', 'completed', 'pending', 'on-hold')
));

if (!empty($orders)) {
    foreach ($orders as $order) {
        $order_id = $order->get_id();
        $status = $order->get_status();
        $date = $order->get_date_created()->format('Y-m-d H:i:s');
        $total = $order->get_total();
        
        $doc_id = get_post_meta($order_id, '_tablecrm_complete_document_id', true);
        $sent_status = !empty($doc_id) ? "✅ Отправлен (ID: $doc_id)" : "❌ НЕ ОТПРАВЛЕН";
        
        echo "Заказ #$order_id - $date - $status - $total руб. - $sent_status\n";
    }
} else {
    echo "❌ Заказы не найдены\n";
}

echo "\n=== ПРОВЕРКА ЗАВЕРШЕНА ===\n";
?> 