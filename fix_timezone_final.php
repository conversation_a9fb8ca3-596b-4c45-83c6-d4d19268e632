<?php
// Final timezone fix with cache clearing
echo "=== ФИНАЛЬНОЕ ИСПРАВЛЕНИЕ ЧАСОВОГО ПОЯСА ===\n";

$wp_path = '/home/<USER>/bekflom3/mosbuketik.ru/public_html';
require_once($wp_path . '/wp-config.php');
require_once($wp_path . '/wp-load.php');

echo "WordPress загружен успешно\n\n";

// Устанавливаем правильные настройки времени
echo "=== УСТАНОВКА ПРАВИЛЬНЫХ НАСТРОЕК ===\n";

// Устанавливаем часовой пояс Москвы
$timezone_result = update_option('timezone_string', 'Europe/Moscow');
echo "timezone_string (Europe/Moscow): " . ($timezone_result ? "✅ УСТАНОВЛЕНО" : "❌ ОШИБКА") . "\n";

// Убираем GMT offset когда установлен timezone_string
$offset_result = update_option('gmt_offset', '');
echo "gmt_offset (очищен): " . ($offset_result ? "✅ УСТАНОВЛЕНО" : "❌ ОШИБКА") . "\n";

// Устанавливаем формат даты и времени
$date_format_result = update_option('date_format', 'd.m.Y');
echo "date_format: " . ($date_format_result ? "✅ УСТАНОВЛЕНО" : "❌ ОШИБКА") . "\n";

$time_format_result = update_option('time_format', 'H:i');
echo "time_format: " . ($time_format_result ? "✅ УСТАНОВЛЕНО" : "❌ ОШИБКА") . "\n";

// Очищаем кэш WordPress
if (function_exists('wp_cache_flush')) {
    wp_cache_flush();
    echo "WordPress кэш очищен ✅\n";
}

// Очищаем кэш объектов
if (function_exists('wp_cache_delete')) {
    wp_cache_delete('alloptions', 'options');
    echo "Кэш опций очищен ✅\n";
}

echo "\n=== ПРОВЕРКА РЕЗУЛЬТАТА ===\n";

// Проверяем установленные настройки
$new_timezone = get_option('timezone_string');
$new_offset = get_option('gmt_offset');
$new_date_format = get_option('date_format');
$new_time_format = get_option('time_format');

echo "Часовой пояс: " . ($new_timezone ?: 'НЕ УСТАНОВЛЕН') . "\n";
echo "GMT смещение: " . ($new_offset ?: 'НЕ УСТАНОВЛЕНО') . "\n";
echo "Формат даты: $new_date_format\n";
echo "Формат времени: $new_time_format\n\n";

// Показываем текущее время
echo "=== ТЕКУЩЕЕ ВРЕМЯ ПОСЛЕ ИСПРАВЛЕНИЯ ===\n";
echo "Серверное время (PHP): " . date('Y-m-d H:i:s') . "\n";
echo "WordPress время: " . current_time('Y-m-d H:i:s') . "\n";
echo "WordPress время (mysql): " . current_time('mysql') . "\n";

// Устанавливаем PHP timezone тоже
date_default_timezone_set('Europe/Moscow');
echo "PHP timezone установлен: " . date_default_timezone_get() . "\n";
echo "PHP время после установки: " . date('Y-m-d H:i:s') . "\n\n";

// Проверяем последние заказы
if (class_exists('WooCommerce')) {
    echo "=== ПРОВЕРКА ЗАКАЗОВ ПОСЛЕ ИСПРАВЛЕНИЯ ===\n";
    
    $orders = wc_get_orders(array(
        'limit' => 3,
        'orderby' => 'date',
        'order' => 'DESC'
    ));
    
    $current_timestamp = current_time('timestamp');
    echo "Текущий timestamp WordPress: $current_timestamp\n";
    echo "Текущее время WordPress: " . date('Y-m-d H:i:s', $current_timestamp) . "\n\n";
    
    foreach ($orders as $order) {
        $order_id = $order->get_id();
        $order_date = $order->get_date_created();
        $order_timestamp = $order_date->getTimestamp();
        
        echo "Заказ #$order_id:\n";
        echo "  Дата: " . $order_date->format('Y-m-d H:i:s') . "\n";
        echo "  Timestamp: $order_timestamp\n";
        echo "  Разница с текущим временем: " . ($current_timestamp - $order_timestamp) . " секунд\n";
        echo "  Время назад: " . human_time_diff($order_timestamp, $current_timestamp) . " назад\n";
        echo "  Статус: " . $order->get_status() . "\n\n";
    }
}

// Дополнительно - обновляем настройки в базе данных напрямую
global $wpdb;

echo "=== ПРЯМОЕ ОБНОВЛЕНИЕ В БАЗЕ ДАННЫХ ===\n";

$db_update1 = $wpdb->update(
    $wpdb->options,
    array('option_value' => 'Europe/Moscow'),
    array('option_name' => 'timezone_string')
);
echo "Прямое обновление timezone_string: " . ($db_update1 !== false ? "✅ УСПЕШНО" : "❌ ОШИБКА") . "\n";

$db_update2 = $wpdb->update(
    $wpdb->options,
    array('option_value' => ''),
    array('option_name' => 'gmt_offset')
);
echo "Прямое обновление gmt_offset: " . ($db_update2 !== false ? "✅ УСПЕШНО" : "❌ ОШИБКА") . "\n";

echo "\n=== ИСПРАВЛЕНИЕ ЗАВЕРШЕНО ===\n";
echo "Рекомендуется:\n";
echo "1. Обновить страницу админки WordPress\n";
echo "2. Проверить Настройки → Общие → Часовой пояс\n";
echo "3. Очистить кэш сайта если используется\n";
?> 