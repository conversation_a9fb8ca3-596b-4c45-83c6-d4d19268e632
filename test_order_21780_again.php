<?php
// Test order 21780 again with new logic
echo "=== ПОВТОРНАЯ ОТПРАВКА ЗАКАЗА #21780 ===\n";

$wp_path = '/home/<USER>/bekflom3/mosbuketik.ru/public_html';
require_once($wp_path . '/wp-config.php');
require_once($wp_path . '/wp-load.php');

echo "WordPress загружен успешно\n\n";

$order_id = 21780;
$order = wc_get_order($order_id);

if (!$order) {
    echo "❌ Заказ #$order_id не найден\n";
    exit;
}

echo "✅ Заказ найден\n";
echo "Статус: " . $order->get_status() . "\n";
echo "Клиент: " . $order->get_formatted_billing_full_name() . "\n";

// Clear previous document ID to allow re-sending
delete_post_meta($order_id, '_tablecrm_complete_document_id');
delete_post_meta($order_id, '_tablecrm_complete_sent_at');
echo "Очищены метки предыдущей отправки\n";

// Get the plugin instance
global $tablecrm_integration_complete;
if (isset($tablecrm_integration_complete)) {
    echo "Плагин найден, начинаем отправку...\n\n";
    
    $result = $tablecrm_integration_complete->send_order_to_tablecrm($order_id);
    
    if ($result) {
        echo "✅ ЗАКАЗ ОТПРАВЛЕН УСПЕШНО!\n";
        
        $doc_id = get_post_meta($order_id, '_tablecrm_complete_document_id', true);
        if ($doc_id) {
            echo "Document ID: $doc_id\n";
        }
    } else {
        echo "❌ ОШИБКА ОТПРАВКИ ЗАКАЗА\n";
    }
} else {
    echo "❌ Плагин не найден\n";
}

echo "\n=== ПРОВЕРКА ЗАВЕРШЕНА ===\n";
?> 