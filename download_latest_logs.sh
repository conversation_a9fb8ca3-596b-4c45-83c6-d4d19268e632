#!/bin/bash
echo "📥 СКАЧИВАЮ АКТУАЛЬНЫЕ ЛОГИ С СЕРВЕРА..."

# Создаем папку для логов
mkdir -p server_logs_latest

ftp -n bekflom3.beget.tech << 'EOF'
user bekflom3_wz zl2Shj!e&6ng
binary
cd public_html/wp-content
get debug.log server_logs_latest/debug_latest.log
cd uploads
get tablecrm_debug.log server_logs_latest/tablecrm_debug_latest.log
quit
EOF

echo "📋 АНАЛИЗ ПОСЛЕДНИХ ЗАПИСЕЙ В ЛОГАХ:"
echo ""
echo "=== ПОСЛЕДНИЕ 20 СТРОК DEBUG.LOG ==="
tail -20 server_logs_latest/debug_latest.log
echo ""
echo "=== ПОСЛЕДНИЕ 20 СТРОК TABLECRM DEBUG ==="
tail -20 server_logs_latest/tablecrm_debug_latest.log 2>/dev/null || echo "Файл tablecrm_debug.log не найден"
echo ""
echo "✅ ЛОГИ СКАЧАНЫ В ПАПКУ server_logs_latest/" 