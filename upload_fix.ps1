# Upload and run settings fix script
$server = "bekflom3.beget.tech"
$user = "bekflom3_wz"
$pass = "zl2Shj!e&6ng"

Write-Host "Uploading settings fix script..." -ForegroundColor Green

try {
    $uri = "ftp://$server/public_html/fix_settings_registration.php"
    $webclient = New-Object System.Net.WebClient
    $webclient.Credentials = New-Object System.Net.NetworkCredential($user, $pass)
    $webclient.UploadFile($uri, "fix_settings_registration.php")
    Write-Host "Fix script uploaded!" -ForegroundColor Green
    
    # Run the fix script
    Write-Host "Running settings fix..." -ForegroundColor Yellow
    $result = Invoke-WebRequest -Uri "https://mosbuketik.ru/fix_settings_registration.php" -UseBasicParsing
    Write-Host $result.Content -ForegroundColor White
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

$webclient.Dispose()
Write-Host "Settings fix completed!" -ForegroundColor Green 