# Upload and run UTM API test
$server = "bekflom3.beget.tech"
$user = "bekflom3_wz"
$pass = "zl2Shj!e&6ng"

Write-Host "Uploading UTM API test..." -ForegroundColor Green

try {
    $uri = "ftp://$server/public_html/test_utm_api.php"
    $webclient = New-Object System.Net.WebClient
    $webclient.Credentials = New-Object System.Net.NetworkCredential($user, $pass)
    $webclient.UploadFile($uri, "test_utm_api.php")
    Write-Host "UTM API test uploaded!" -ForegroundColor Green
    
    # Run the test
    Write-Host "Running UTM API test..." -ForegroundColor Yellow
    $result = Invoke-WebRequest -Uri "https://mosbuketik.ru/test_utm_api.php" -UseBasicParsing
    Write-Host $result.Content -ForegroundColor White
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

$webclient.Dispose()
Write-Host "UTM API test completed!" -ForegroundColor Green 