# Download TableCRM logs from server (ASCII only)
Write-Host "Downloading TableCRM log file from server..." -ForegroundColor Green

# FTP connection data
$ftpServer   = "bekflom3.beget.tech"
$ftpUser     = "bekflom3_wz"
$ftpPassword = '*rJ*a&7IF*gJ'
$remotePath  = "/public_html/wp-content/uploads/tablecrm_integration_complete.log"

# Local folder for logs
$logFolder = "server_logs_fresh"
if (!(Test-Path $logFolder)) {
    New-Item -ItemType Directory -Path $logFolder | Out-Null
}

$localLogFile = Join-Path $logFolder "tablecrm_integration_complete.log"

try {
    Write-Host "Connecting to FTP server..." -ForegroundColor Yellow
    $uri = "ftp://$ftpServer$remotePath"
    $wc = New-Object System.Net.WebClient
    $wc.Credentials = New-Object System.Net.NetworkCredential($ftpUser, $ftpPassword)
    $wc.DownloadFile($uri, $localLogFile)
    Write-Host "Log file downloaded to: $localLogFile" -ForegroundColor Green

    # Print last 30 lines
    if (Test-Path $localLogFile) {
        Write-Host "Showing last 30 lines:" -ForegroundColor Cyan
        Get-Content $localLogFile -Tail 30 | ForEach-Object { Write-Host $_ }

        # Simple search for payment related data
        Write-Host "\nSearch for payment keywords (paid/status/payment):" -ForegroundColor Cyan
        $keywords = Select-String -Path $localLogFile -Pattern "paid|status|payment" -SimpleMatch
        if ($keywords) {
            $keywords | Select-Object -Last 10 | ForEach-Object { Write-Host $_.Line -ForegroundColor Yellow }
        } else {
            Write-Host "No payment keyword lines found." -ForegroundColor Red
        }
    }
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
} 