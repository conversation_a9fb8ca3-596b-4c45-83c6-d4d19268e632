# Download all TableCRM logs from server
Write-Host "Downloading all TableCRM log files from server..." -ForegroundColor Green

$ftpServer = "bekflom3.beget.tech"
$ftpUser = "bekflom3_wz"
$ftpPassword = '*rJ*a&7IF*gJ'

# Log files to download
$logFiles = @(
    "/public_html/wp-content/uploads/tablecrm_integration_complete.log",
    "/public_html/wp-content/tablecrm_complete.log",
    "/public_html/wp-content/debug.log"
)

# Local folder for logs
$logFolder = "server_logs_fresh"
if (!(Test-Path $logFolder)) {
    New-Item -ItemType Directory -Path $logFolder | Out-Null
}

foreach ($remotePath in $logFiles) {
    try {
        $fileName = Split-Path $remotePath -Leaf
        $localFile = Join-Path $logFolder $fileName
        
        Write-Host "Downloading: $fileName" -ForegroundColor Yellow
        $uri = "ftp://$ftpServer$remotePath"
        $wc = New-Object System.Net.WebClient
        $wc.Credentials = New-Object System.Net.NetworkCredential($ftpUser, $ftpPassword)
        $wc.DownloadFile($uri, $localFile)
        
        Write-Host "Downloaded: $localFile" -ForegroundColor Green
        
        # Show last 10 lines of each file
        if (Test-Path $localFile) {
            Write-Host "Last 10 lines of $fileName :" -ForegroundColor Cyan
            Get-Content $localFile -Tail 10 | ForEach-Object { Write-Host "  $_" }
            Write-Host "" # Empty line
        }
    }
    catch {
        Write-Host "Error downloading $remotePath : $($_.Exception.Message)" -ForegroundColor Red
    }
} 