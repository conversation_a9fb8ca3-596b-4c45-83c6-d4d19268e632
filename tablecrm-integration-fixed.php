<?php
/**
 * Plugin Name: TableCRM Integration (Fixed)
 * Description: Интеграция WordPress/WooCommerce с TableCRM для отправки заявок через API - исправленная версия
 * Version: 1.0.1
 * Author: Your Name
 * Text Domain: tablecrm-integration
 */

// Предотвращение прямого доступа
if (!defined('ABSPATH')) {
    exit;
}

// Определение констант плагина
define('TABLECRM_PLUGIN_URL', plugin_dir_url(__FILE__));
define('TABLECRM_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('TABLECRM_VERSION', '1.0.1');

class TableCRM_Integration {
    
    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'register_settings'));
        
        // Хуки для WooCommerce
        add_action('woocommerce_new_order', array($this, 'send_order_to_tablecrm'));
        add_action('woocommerce_order_status_completed', array($this, 'update_order_in_tablecrm'));
        add_action('woocommerce_order_status_processing', array($this, 'update_order_in_tablecrm'));
        add_action('woocommerce_order_status_cancelled', array($this, 'update_order_in_tablecrm'));
        add_action('woocommerce_order_status_refunded', array($this, 'update_order_in_tablecrm'));
        
        // Хуки для Contact Form 7 (если установлен)
        add_action('wpcf7_mail_sent', array($this, 'send_cf7_to_tablecrm'));
        
        // AJAX хуки
        add_action('wp_ajax_test_tablecrm_connection', array($this, 'test_connection'));
        add_action('wp_ajax_nopriv_test_tablecrm_connection', array($this, 'test_connection'));
        
        // Хук для сохранения UTM данных
        add_action('woocommerce_checkout_create_order', array($this, 'save_utm_data_to_order'), 10, 2);
    }
    
    public function init() {
        load_plugin_textdomain('tablecrm-integration', false, dirname(plugin_basename(__FILE__)) . '/languages');
        
        // Добавляем скрипт для отслеживания UTM на фронтенде
        add_action('wp_enqueue_scripts', array($this, 'enqueue_utm_tracking_script'));
    }
    
    // Подключение скрипта отслеживания UTM
    public function enqueue_utm_tracking_script() {
        wp_enqueue_script('tablecrm-utm-tracking', TABLECRM_PLUGIN_URL . 'utm-tracking.js', array('jquery'), TABLECRM_VERSION, true);
    }
    
    // Добавление меню в админку
    public function add_admin_menu() {
        add_options_page(
            'TableCRM Integration Settings',
            'TableCRM Integration',
            'manage_options',
            'tablecrm-settings',
            array($this, 'admin_page')
        );
    }
    
    // Регистрация настроек
    public function register_settings() {
        register_setting('tablecrm_settings', 'tablecrm_api_url');
        register_setting('tablecrm_settings', 'tablecrm_api_key');
        register_setting('tablecrm_settings', 'tablecrm_project_id');
        register_setting('tablecrm_settings', 'tablecrm_organization_id');
        register_setting('tablecrm_settings', 'tablecrm_cashbox_id');
        register_setting('tablecrm_settings', 'tablecrm_warehouse_id');
        register_setting('tablecrm_settings', 'tablecrm_unit_id');
        register_setting('tablecrm_settings', 'tablecrm_enable_orders');
        register_setting('tablecrm_settings', 'tablecrm_enable_cf7');
        register_setting('tablecrm_settings', 'tablecrm_check_duplicates');
        register_setting('tablecrm_settings', 'tablecrm_debug_mode');
        register_setting('tablecrm_settings', 'tablecrm_order_statuses');
    }
    
    // Страница настроек (сокращенная версия)
    public function admin_page() {
        ?>
        <div class="wrap">
            <h1>TableCRM Integration Settings (Fixed)</h1>
            <form method="post" action="options.php">
                <?php
                settings_fields('tablecrm_settings');
                do_settings_sections('tablecrm_settings');
                ?>
                <table class="form-table">
                    <tr>
                        <th scope="row">API URL</th>
                        <td>
                            <input type="url" name="tablecrm_api_url" value="<?php echo esc_attr(get_option('tablecrm_api_url', 'https://app.tablecrm.com/api/v1/')); ?>" class="regular-text" />
                            <p class="description">URL API TableCRM</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">API Key</th>
                        <td>
                            <input type="password" name="tablecrm_api_key" value="<?php echo esc_attr(get_option('tablecrm_api_key')); ?>" class="regular-text" />
                            <p class="description">Ваш API ключ TableCRM</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Режим отладки</th>
                        <td>
                            <input type="checkbox" name="tablecrm_debug_mode" value="1" <?php checked(1, get_option('tablecrm_debug_mode', 1)); ?> />
                            <label>Включить подробное логирование</label>
                        </td>
                    </tr>
                </table>
                
                <p class="submit">
                    <input type="submit" name="submit" id="submit" class="button button-primary" value="Сохранить настройки" />
                </p>
            </form>
        </div>
        <?php
    }
    
    // Отправка заказа WooCommerce в TableCRM (ИСПРАВЛЕННАЯ ВЕРСИЯ)
    public function send_order_to_tablecrm($order_id) {
        $this->log_info("=== НАЧАЛО ОТПРАВКИ ЗАКАЗА $order_id В TABLECRM ===");
        
        if (!get_option('tablecrm_enable_orders', 1)) {
            $this->log_warning("Отправка заказов отключена в настройках плагина");
            return;
        }
        
        $order = wc_get_order($order_id);
        if (!$order) {
            $this->log_error("Заказ $order_id не найден в WooCommerce");
            return;
        }

        $this->log_info("Заказ $order_id найден. Статус: " . $order->get_status());

        // Проверяем, был ли заказ уже отправлен ранее
        $existing_doc_id = get_post_meta($order_id, '_tablecrm_document_id', true);
        if (!empty($existing_doc_id)) {
            $this->log_info("Заказ $order_id уже обработан. Document ID: $existing_doc_id");
            return;
        }
        
        // Собираем полные данные заказа включая shipping данные
        $data = array(
            // Основные данные
            'name' => $order->get_billing_first_name() . ' ' . $order->get_billing_last_name(),
            'email' => $order->get_billing_email(),
            'phone' => $order->get_billing_phone(),
            'order_id' => $order_id,
            'order_total' => $order->get_total(),
            'order_status' => $order->get_status(),
            'order_date' => $order->get_date_created() ? $order->get_date_created()->format('Y-m-d H:i:s') : current_time('mysql'),
            'source' => 'WooCommerce Order',
            'payment_method' => $order->get_payment_method_title(),
            'payment_status' => $order->is_paid() ? 'paid' : 'unpaid',
            'domain' => isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : '',
            
            // SHIPPING ДАННЫЕ для информации о доставке
            'shipping_first_name' => $order->get_shipping_first_name(),
            'shipping_last_name' => $order->get_shipping_last_name(),
            'shipping_company' => $order->get_shipping_company(),
            'shipping_address_1' => $order->get_shipping_address_1(),
            'shipping_address_2' => $order->get_shipping_address_2(),
            'shipping_city' => $order->get_shipping_city(),
            'shipping_state' => $order->get_shipping_state(),
            'shipping_postcode' => $order->get_shipping_postcode(),
            'shipping_country' => $order->get_shipping_country(),
            
            // Billing данные (fallback)
            'billing_address_1' => $order->get_billing_address_1(),
            'billing_address_2' => $order->get_billing_address_2(),
            'billing_city' => $order->get_billing_city(),
            'billing_state' => $order->get_billing_state(),
            'billing_postcode' => $order->get_billing_postcode(),
            'billing_country' => $order->get_billing_country(),
            
            // Дата доставки из мета-полей заказа
            'delivery_date_source' => $order->get_meta('_delivery_date') ?: '',
            'delivery_time_source' => $order->get_meta('_delivery_time') ?: '',
            
            'items' => array()
        );
        
        // Добавляем товары
        foreach ($order->get_items() as $item_id => $item) {
            $product = $item->get_product();
            $data['items'][] = array(
                'name' => $item->get_name(),
                'quantity' => $item->get_quantity(),
                'price' => $item->get_total(),
                'unit_price' => ($item->get_quantity() > 0) ? ($item->get_total() / $item->get_quantity()) : 0,
                'sku' => $product ? $product->get_sku() : '',
                'product_id' => $product ? $product->get_id() : ''
            );
        }
        
        $this->log_info("Отправляем основной документ заказа $order_id в TableCRM API...");
        $response = $this->make_api_request('docs_sales/', $data);
        
        if ($response['success']) {
            $document_id = $this->extract_document_id($response);
            
            if ($document_id !== 'unknown' && !empty($document_id)) {
                update_post_meta($order_id, '_tablecrm_document_id', $document_id);
                $this->log_success("Заказ $order_id успешно отправлен в TableCRM. Document ID: $document_id");
                
                // ИСПРАВЛЕННАЯ отправка информации о доставке
                $this->send_delivery_info($document_id, $data);
                
                $this->log_info("=== ЗАВЕРШЕНИЕ ОТПРАВКИ ЗАКАЗА $order_id (УСПЕШНО) ===");
            } else {
                $this->log_error("Заказ $order_id отправлен, но не удалось получить Document ID");
                $this->log_info("=== ЗАВЕРШЕНИЕ ОТПРАВКИ ЗАКАЗА $order_id (ОШИБКА ID) ===");
            }
        } else {
            $this->log_error("Ошибка отправки заказа $order_id в TableCRM: " . $response['message']);
            $this->log_info("=== ЗАВЕРШЕНИЕ ОТПРАВКИ ЗАКАЗА $order_id (ОШИБКА API) ===");
        }
    }
    
    // Извлечение Document ID из ответа API
    private function extract_document_id($response) {
        if (isset($response['document_id'])) {
            return $response['document_id'];
        } elseif (isset($response['lead_id'])) {
            return $response['lead_id'];
        } elseif (isset($response['data']) && is_array($response['data']) && !empty($response['data'][0]['id'])) {
            return $response['data'][0]['id'];
        }
        return 'unknown';
    }
    
    // ИСПРАВЛЕННАЯ функция отправки информации о доставке
    private function send_delivery_info($document_id, $data) {
        if (empty($document_id) || $document_id === 'unknown') {
            $this->log_debug("Отправка информации о доставке отменена: неверный document_id ($document_id).");
            return;
        }
        
        $delivery_payload = array();
        
        // 1. Формируем адрес доставки из shipping полей
        $address_parts = array();
        if (!empty($data['shipping_address_1'])) $address_parts[] = $data['shipping_address_1'];
        if (!empty($data['shipping_address_2'])) $address_parts[] = $data['shipping_address_2'];
        if (!empty($data['shipping_city'])) $address_parts[] = $data['shipping_city'];
        if (!empty($data['shipping_state'])) $address_parts[] = $data['shipping_state'];
        if (!empty($data['shipping_postcode'])) $address_parts[] = $data['shipping_postcode'];
        
        if (!empty($address_parts)) {
            $delivery_payload['address'] = trim(implode(', ', $address_parts));
        } else {
            // Fallback к billing адресу если shipping пустой
            $billing_parts = array();
            if (!empty($data['billing_address_1'])) $billing_parts[] = $data['billing_address_1'];
            if (!empty($data['billing_address_2'])) $billing_parts[] = $data['billing_address_2'];
            if (!empty($data['billing_city'])) $billing_parts[] = $data['billing_city'];
            
            if (!empty($billing_parts)) {
                $delivery_payload['address'] = trim(implode(', ', $billing_parts));
            }
        }
        
        // 2. Формируем дату доставки в формате TableCRM
        if (!empty($data['delivery_date_source'])) {
            $delivery_datetime_str = $data['delivery_date_source'];
            if (!empty($data['delivery_time_source'])) {
                $delivery_datetime_str .= ' ' . $data['delivery_time_source'];
            } else {
                $delivery_datetime_str .= ' 00:00:00';
            }
            
            try {
                $dt = new DateTime($delivery_datetime_str);
                // Формат как в TableCRM: "2025-05-27 06:00:00+00"
                $formatted_date = $dt->format('Y-m-d H:i:sP');
                if (str_ends_with($formatted_date, ':00')) {
                    $formatted_date = substr($formatted_date, 0, -3);
                }
                $delivery_payload['delivery_date'] = $formatted_date;
            } catch (Exception $e) {
                $this->log_warning("Некорректный формат даты доставки: $delivery_datetime_str");
            }
        }
        
        // 3. Формируем получателя как JSON объект
        $recipient_info = array();
        
        // Используем shipping имя, если есть, иначе billing
        $recipient_name = !empty($data['shipping_first_name']) ? $data['shipping_first_name'] : '';
        $recipient_surname = !empty($data['shipping_last_name']) ? $data['shipping_last_name'] : '';
        
        if (empty($recipient_name) && !empty($data['name'])) {
            $name_parts = explode(' ', $data['name'], 2);
            $recipient_name = $name_parts[0] ?? '';
            $recipient_surname = $name_parts[1] ?? '';
        }
        
        if (!empty($recipient_name)) $recipient_info['name'] = $recipient_name;
        if (!empty($recipient_surname)) $recipient_info['surname'] = $recipient_surname;
        if (!empty($data['phone'])) $recipient_info['phone'] = $data['phone'];
        
        if (!empty($recipient_info)) {
            $delivery_payload['recipient'] = $recipient_info;
        }
        
        if (empty($delivery_payload)) {
            $this->log_debug("Нет данных для отправки информации о доставке документа $document_id");
            return;
        }
        
        $this->log_info("Отправляем информацию о доставке для документа $document_id: " . json_encode($delivery_payload, JSON_UNESCAPED_UNICODE));
        
        $api_url = get_option('tablecrm_api_url', 'https://app.tablecrm.com/api/v1/');
        $api_key = get_option('tablecrm_api_key');
        
        if (empty($api_key)) {
            $this->log_error("Не настроен API ключ для отправки информации о доставке");
            return;
        }
        
        $delivery_url = rtrim($api_url, '/') . '/docs_sales/' . $document_id . '/delivery_info/?token=' . $api_key;
        
        $response = wp_remote_post($delivery_url, array(
            'method' => 'POST',
            'headers' => array(
                'Content-Type' => 'application/json; charset=utf-8',
                'User-Agent' => 'WordPress-TableCRM-Integration/1.0'
            ),
            'body' => json_encode($delivery_payload, JSON_UNESCAPED_UNICODE),
            'timeout' => 30,
            'sslverify' => true
        ));
        
        if (is_wp_error($response)) {
            $this->handle_wp_error("Ошибка отправки информации о доставке для документа $document_id", $response);
            return;
        }
        
        $status_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);
        
        if ($status_code >= 200 && $status_code < 300) {
            $this->log_success("Информация о доставке успешно отправлена для документа $document_id. Статус: $status_code");
        } else {
            $this->log_error("Ошибка отправки информации о доставке (HTTP $status_code) для документа $document_id: " . substr($response_body, 0, 500));
        }
    }
    
    // Базовая функция API запроса
    private function make_api_request($endpoint, $data = array()) {
        $api_url = get_option('tablecrm_api_url', 'https://app.tablecrm.com/api/v1/');
        $api_key = get_option('tablecrm_api_key');
        
        if (empty($api_key)) {
            return array('success' => false, 'message' => 'API ключ не настроен');
        }
        
        // Адаптируем данные для эндпоинта
        $adapted_data = $this->adapt_data_format($endpoint, $data);
        
        $url = rtrim($api_url, '/') . '/' . $endpoint . '?token=' . $api_key;
        
        $this->log_debug("Отправка запроса к: $url");
        $this->log_debug("Данные: " . json_encode($adapted_data, JSON_UNESCAPED_UNICODE));
        
        $response = wp_remote_post($url, array(
            'method' => 'POST',
            'headers' => array(
                'Content-Type' => 'application/json; charset=utf-8',
                'User-Agent' => 'WordPress-TableCRM-Integration/1.0'
            ),
            'body' => json_encode($adapted_data, JSON_UNESCAPED_UNICODE),
            'timeout' => 30,
            'sslverify' => true
        ));
        
        if (is_wp_error($response)) {
            $this->handle_wp_error('Ошибка WP_Error', $response);
            return array('success' => false, 'message' => $response->get_error_message());
        }
        
        $status_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        
        $this->log_debug("Ответ API: статус $status_code, тело: " . substr($body, 0, 500));
        
        if ($status_code >= 200 && $status_code < 300) {
            $decoded = json_decode($body, true);
            $this->log_success("✅ Успешный запрос к API! Эндпоинт: $endpoint");
            
            // Определяем ID документа из ответа
            $document_id = 'unknown';
            if (is_array($decoded) && !empty($decoded[0]['id'])) {
                $document_id = $decoded[0]['id'];
                $this->log_success("Создан документ продаж ID: $document_id");
            }
            
            return array(
                'success' => true, 
                'data' => $decoded,
                'document_id' => $document_id,
                'lead_id' => $document_id
            );
        } else {
            $this->log_error("Ошибка API (HTTP $status_code): " . substr($body, 0, 500));
            return array('success' => false, 'message' => "HTTP $status_code: " . $body);
        }
    }
    
    // Адаптация данных под формат TableCRM
    private function adapt_data_format($endpoint, $data) {
        if ($endpoint === 'docs_sales/') {
            $organization_id = get_option('tablecrm_organization_id', 38);
            $cashbox_id = get_option('tablecrm_cashbox_id', 218);
            $warehouse_id = get_option('tablecrm_warehouse_id', 39);
            $unit_id = get_option('tablecrm_unit_id', 116);
            
            // Формируем товары
            $goods = array();
            if (isset($data['items']) && !empty($data['items'])) {
                foreach ($data['items'] as $item) {
                    $goods[] = array(
                        'price' => floatval($item['unit_price'] ?? 0),
                        'quantity' => intval($item['quantity'] ?? 1),
                        'unit' => intval($unit_id),
                        'discount' => 0,
                        // Сумма позиции с учётом скидки (в нашем случае скидки нет, поэтому = price * quantity)
                        'sum_discounted' => number_format(
                            floatval($item['unit_price'] ?? 0) * intval($item['quantity'] ?? 1),
                            2,
                            '.',
                            ''
                        ),
                        'nomenclature' => 39697 // Фиксированная номенклатура
                    );
                }
            } else {
                $goods[] = array(
                    'price' => floatval($data['order_total'] ?? 500),
                    'quantity' => 1,
                    'unit' => intval($unit_id),
                    'discount' => 0,
                    // Для заказа без детализации берём общую сумму заказа
                    'sum_discounted' => number_format(floatval($data['order_total'] ?? 500), 2, '.', ''),
                    'nomenclature' => 39697
                );
            }
            
            $document_data = array(
                'dated' => time(),
                'operation' => 'Заказ',
                'comment' => $this->format_order_comment($data),
                'tax_included' => true,
                'tax_active' => true,
                'goods' => $goods,
                'settings' => (object)array(),
                'warehouse' => intval($warehouse_id),
                'contragent' => 330985, // Фиксированный контрагент
                'paybox' => intval($cashbox_id),
                'organization' => intval($organization_id),
                'status' => false,
                'paid_rubles' => number_format(floatval($data['order_total'] ?? 500), 2, '.', ''),
                'paid_lt' => 0
            );
            
            return array($document_data);
        }
        
        return array();
    }
    
    // Форматирование комментария
    private function format_order_comment($data) {
        $comment = "Заказ с сайта WordPress\n";
        if (isset($data['name'])) $comment .= "Клиент: " . $data['name'] . "\n";
        if (isset($data['email'])) $comment .= "Email: " . $data['email'] . "\n";
        if (isset($data['phone'])) $comment .= "Телефон: " . $data['phone'] . "\n";
        if (isset($data['order_date'])) $comment .= "Дата заказа: " . $data['order_date'] . "\n";
        if (isset($data['payment_method'])) $comment .= "Способ оплаты: " . $data['payment_method'] . "\n";
        
        // Добавляем адрес доставки
        if (!empty($data['shipping_address_1'])) {
            $comment .= "Адрес доставки: " . $data['shipping_address_1'];
            if (!empty($data['shipping_city'])) $comment .= ", " . $data['shipping_city'];
            $comment .= "\n";
        }
        
        return $comment;
    }
    
    /**
     * Log WP_Errors with extra notice for SSL certificate problems.
     */
    private function handle_wp_error($context, $error) {
        $message = $error->get_error_message();
        if (stripos($message, 'certificate') !== false || stripos($message, 'ssl') !== false) {
            $this->log_error("{$context}: SSL certificate error - {$message}");
        } else {
            $this->log_error("{$context}: {$message}");
        }
    }

    // Функции логирования
    private function log($message, $level = 'INFO') {
        if (get_option('tablecrm_debug_mode', 1)) {
            $timestamp = date('Y-m-d H:i:s');
            $log_message = "[{$timestamp}] [TableCRM Integration] [{$level}] {$message}";
            error_log($log_message);
        }
    }
    
    private function log_info($message) {
        $this->log($message, 'INFO');
    }
    
    private function log_warning($message) {
        $this->log($message, 'WARNING');
    }
    
    private function log_error($message) {
        $this->log($message, 'ERROR');
    }
    
    private function log_success($message) {
        $this->log($message, 'SUCCESS');
    }
    
    private function log_debug($message) {
        $this->log($message, 'DEBUG');
    }
    
    // Заглушки для совместимости
    public function update_order_in_tablecrm($order_id) {}
    public function send_cf7_to_tablecrm($contact_form) {}
    public function save_utm_data_to_order($order, $data) {}
}

// Инициализация плагина
if (class_exists('WooCommerce')) {
    new TableCRM_Integration();
}

// Хуки активации/деактивации
register_activation_hook(__FILE__, 'tablecrm_activation');
register_deactivation_hook(__FILE__, 'tablecrm_deactivation');

function tablecrm_activation() {
    // Включаем отладку по умолчанию при активации
    update_option('tablecrm_debug_mode', 1);
}

function tablecrm_deactivation() {
    // Ничего не делаем при деактивации
} 