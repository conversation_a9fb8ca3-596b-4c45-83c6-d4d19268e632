# Test CodeRockz delivery integration
$localFile = "test_coderockz_delivery.php"
$remoteFile = "/home/<USER>/bekflom3/mosbuketik.ru/public_html/test_coderockz_delivery.php"
$server = "bekflom3.beget.tech"
$username = "bekflom3"

Write-Host "=== TESTING CODEROCKZ DELIVERY INTEGRATION ===" -ForegroundColor Green

# Upload test file
Write-Host "Uploading CodeRockz test script..." -ForegroundColor Yellow
scp $localFile "${username}@${server}:${remoteFile}"

if ($LASTEXITCODE -eq 0) {
    Write-Host "Test script uploaded successfully" -ForegroundColor Green
    Write-Host ""
    Write-Host "This test will verify:" -ForegroundColor Cyan
    Write-Host "- CodeRockz delivery_date field parsing" -ForegroundColor White
    Write-Host "- delivery_time integration (12:30 - 14:00)" -ForegroundColor White
    Write-Host "- Proper date + time combination" -ForegroundColor White
    Write-Host "- delivery_type and other fields" -ForegroundColor White
    Write-Host "- Complete delivery data structure" -ForegroundColor White
    Write-Host ""
    Write-Host "Expected result: '2025-06-07 12:30:00'" -ForegroundColor Green
    Write-Host ""
    Write-Host "Run command: php test_coderockz_delivery.php" -ForegroundColor Cyan
} else {
    Write-Host "Error uploading test script" -ForegroundColor Red
}

Write-Host "=== COMPLETED ===" -ForegroundColor Green 