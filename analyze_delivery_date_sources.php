<?php
// Анализ всех возможных источников даты доставки
echo "=== АНАЛИЗ ИСТОЧНИКОВ ДАТЫ ДОСТАВКИ ===\n";

$wp_path = '/home/<USER>/bekflom3/mosbuketik.ru/public_html';
require_once($wp_path . '/wp-config.php');
require_once($wp_path . '/wp-load.php');

echo "WordPress загружен успешно\n\n";

// Тестируем заказ
$order_id = 21780;
$order = wc_get_order($order_id);

if (!$order) {
    echo "❌ Заказ #$order_id не найден\n";
    exit;
}

echo "✅ Заказ #$order_id найден\n";
echo "Дата создания заказа: " . $order->get_date_created()->format('Y-m-d H:i:s') . "\n";
echo "Дата изменения заказа: " . $order->get_date_modified()->format('Y-m-d H:i:s') . "\n\n";

// 1. СТАНДАРТНЫЕ ПОЛЯ WOOCOMMERCE
echo "=== 1. СТАНДАРТНЫЕ ПОЛЯ WOOCOMMERCE ===\n";
$standard_fields = array(
    'order_date' => $order->get_date_created(),
    'completed_date' => $order->get_date_completed(),
    'paid_date' => $order->get_date_paid(),
    'modified_date' => $order->get_date_modified()
);

foreach ($standard_fields as $field => $value) {
    if ($value) {
        echo "$field: " . $value->format('Y-m-d H:i:s') . "\n";
    } else {
        echo "$field: (не установлено)\n";
    }
}

// 2. ВСЕ МЕТА-ПОЛЯ ЗАКАЗА
echo "\n=== 2. ВСЕ МЕТА-ПОЛЯ ЗАКАЗА ===\n";
$all_meta = $order->get_meta_data();
$delivery_related_meta = array();

foreach ($all_meta as $meta) {
    $key = $meta->get_data()['key'];
    $value = $meta->get_data()['value'];
    
    // Ищем поля, связанные с датой/временем доставки
    if (stripos($key, 'delivery') !== false || 
        stripos($key, 'date') !== false || 
        stripos($key, 'time') !== false ||
        stripos($key, 'orddd') !== false ||
        stripos($key, 'shipping') !== false) {
        $delivery_related_meta[$key] = $value;
    }
}

if (!empty($delivery_related_meta)) {
    foreach ($delivery_related_meta as $key => $value) {
        if (is_array($value)) {
            echo "$key: " . json_encode($value, JSON_UNESCAPED_UNICODE) . "\n";
        } else {
            echo "$key: '$value'\n";
        }
    }
} else {
    echo "Мета-поля, связанные с доставкой, не найдены\n";
}

// 3. ПОПУЛЯРНЫЕ ПЛАГИНЫ ДОСТАВКИ
echo "\n=== 3. ПОПУЛЯРНЫЕ ПЛАГИНЫ ДОСТАВКИ ===\n";

$delivery_plugins_fields = array(
    // Order Delivery Date Pro
    '_orddd_timestamp' => 'Order Delivery Date Pro - timestamp',
    '_orddd_lite_timestamp' => 'Order Delivery Date Lite - timestamp', 
    '_orddd_time_slot' => 'Order Delivery Date Pro - time slot',
    '_orddd_date' => 'Order Delivery Date Pro - date',
    'orddd_delivery_date' => 'Order Delivery Date - delivery date',
    
    // WooCommerce Delivery Date
    '_delivery_date' => 'WooCommerce Delivery Date',
    '_delivery_time' => 'WooCommerce Delivery Time',
    'delivery_date' => 'Generic delivery date',
    'delivery_time' => 'Generic delivery time',
    
    // Advanced Shipment Tracking
    '_wc_shipment_tracking_items' => 'Advanced Shipment Tracking',
    
    // WooCommerce Shipping & Tax
    '_shipping_date' => 'Shipping Date',
    '_expected_delivery_date' => 'Expected Delivery Date',
    
    // Custom fields
    '_custom_delivery_date' => 'Custom Delivery Date',
    '_preferred_delivery_date' => 'Preferred Delivery Date',
    '_requested_delivery_date' => 'Requested Delivery Date'
);

foreach ($delivery_plugins_fields as $field => $description) {
    $value = $order->get_meta($field);
    if (!empty($value)) {
        echo "✅ $field ($description): '$value'\n";
    } else {
        echo "❌ $field ($description): (пусто)\n";
    }
}

// 4. АНАЛИЗ АКТИВНЫХ ПЛАГИНОВ
echo "\n=== 4. АКТИВНЫЕ ПЛАГИНЫ ДОСТАВКИ ===\n";
$active_plugins = get_option('active_plugins');
$delivery_plugins = array();

foreach ($active_plugins as $plugin) {
    if (stripos($plugin, 'delivery') !== false || 
        stripos($plugin, 'shipping') !== false ||
        stripos($plugin, 'orddd') !== false) {
        $delivery_plugins[] = $plugin;
    }
}

if (!empty($delivery_plugins)) {
    foreach ($delivery_plugins as $plugin) {
        echo "📦 $plugin\n";
    }
} else {
    echo "Плагины доставки не обнаружены\n";
}

// 5. ПРОВЕРКА CHECKOUT ПОЛЕЙ
echo "\n=== 5. CHECKOUT ПОЛЯ ===\n";
$checkout_fields = array(
    '_billing_delivery_date',
    '_shipping_delivery_date',
    '_checkout_delivery_date',
    '_woocommerce_delivery_date'
);

foreach ($checkout_fields as $field) {
    $value = $order->get_meta($field);
    if (!empty($value)) {
        echo "✅ $field: '$value'\n";
    }
}

// 6. РЕКОМЕНДАЦИИ
echo "\n=== 6. РЕКОМЕНДАЦИИ ПО ПОЛУЧЕНИЮ ДАТЫ ===\n";

$found_dates = array();

// Собираем все найденные даты
foreach ($delivery_related_meta as $key => $value) {
    if (!empty($value) && $value !== '0' && $value !== 0) {
        // Проверяем, похоже ли на дату
        if (is_numeric($value) && $value > 1000000000) { // Unix timestamp
            $found_dates[$key] = array(
                'value' => $value,
                'formatted' => date('Y-m-d H:i:s', $value),
                'type' => 'timestamp'
            );
        } elseif (strtotime($value) !== false) { // Строка даты
            $found_dates[$key] = array(
                'value' => $value,
                'formatted' => date('Y-m-d H:i:s', strtotime($value)),
                'type' => 'date_string'
            );
        }
    }
}

if (!empty($found_dates)) {
    echo "Найденные даты:\n";
    foreach ($found_dates as $key => $date_info) {
        echo "- $key: {$date_info['value']} → {$date_info['formatted']} ({$date_info['type']})\n";
    }
    
    echo "\nРекомендуемый порядок проверки:\n";
    echo "1. _orddd_timestamp (Order Delivery Date Pro)\n";
    echo "2. _delivery_date (WooCommerce Delivery Date)\n";
    echo "3. delivery_date (Generic)\n";
    echo "4. _expected_delivery_date (Expected delivery)\n";
    echo "5. Дата завершения заказа + 1-3 дня\n";
} else {
    echo "❌ Специальные даты доставки не найдены\n";
    echo "\nВарианты fallback:\n";
    echo "1. Дата создания заказа + 1-3 дня\n";
    echo "2. Дата завершения заказа (если есть)\n";
    echo "3. Не отправлять поле delivery_date вообще\n";
}

// 7. ИТОГОВАЯ ФУНКЦИЯ
echo "\n=== 7. ИТОГОВАЯ ФУНКЦИЯ ДЛЯ ПОЛУЧЕНИЯ ДАТЫ ===\n";

function get_proper_delivery_date($order) {
    // 1. Проверяем Order Delivery Date Pro
    $delivery_date = $order->get_meta('_orddd_timestamp');
    if (!empty($delivery_date) && is_numeric($delivery_date) && $delivery_date > 1000000000) {
        return date('Y-m-d H:i:s', $delivery_date);
    }
    
    // 2. Проверяем WooCommerce Delivery Date
    $delivery_date = $order->get_meta('_delivery_date');
    if (!empty($delivery_date) && $delivery_date !== '0' && strtotime($delivery_date) !== false) {
        return date('Y-m-d H:i:s', strtotime($delivery_date));
    }
    
    // 3. Проверяем generic delivery date
    $delivery_date = $order->get_meta('delivery_date');
    if (!empty($delivery_date) && $delivery_date !== '0' && strtotime($delivery_date) !== false) {
        return date('Y-m-d H:i:s', strtotime($delivery_date));
    }
    
    // 4. Проверяем expected delivery date
    $delivery_date = $order->get_meta('_expected_delivery_date');
    if (!empty($delivery_date) && $delivery_date !== '0' && strtotime($delivery_date) !== false) {
        return date('Y-m-d H:i:s', strtotime($delivery_date));
    }
    
    // 5. Fallback: дата завершения + 1 день (если заказ завершен)
    if ($order->get_date_completed()) {
        $completed_date = $order->get_date_completed();
        $delivery_date = $completed_date->modify('+1 day');
        return $delivery_date->format('Y-m-d H:i:s');
    }
    
    // 6. Fallback: дата создания + 3 дня
    $created_date = $order->get_date_created();
    $delivery_date = $created_date->modify('+3 days');
    return $delivery_date->format('Y-m-d H:i:s');
}

$proper_date = get_proper_delivery_date($order);
echo "Рекомендуемая дата доставки для заказа #$order_id: $proper_date\n";

echo "\n=== АНАЛИЗ ЗАВЕРШЕН ===\n";
?> 