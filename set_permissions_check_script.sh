#!/bin/bash

echo "Устанавливаем права 755 и проверяем файл check_plugin_status.php..."

expect << EOF
set timeout 45
spawn lftp sftp://<EMAIL>
expect {
    "Пароль:" {
        send "zl2Shj!e&6ng\r"
        exp_continue
    }
    "lftp*~>" {
        send "cd public_html/wp-content/plugins\r"
        expect "lftp*plugins>"
        send "chmod 755 check_plugin_status.php\r"
        expect "lftp*plugins>"
        send "echo --- Попытка ls check_plugin_status.php ---\r"
        expect "lftp*plugins>"
        send "ls check_plugin_status.php\r" 
        expect "lftp*plugins>"
        send "echo --- Попытка ls (все файлы) ---\r"
        expect "lftp*plugins>"
        send "ls\r"
        expect "lftp*plugins>"
        send "exit\r"
    }
    timeout {
        puts "Таймаут при подключении или выполнении команды."
        exit 1
    }
}
EOF

if [ $? -eq 0 ]; then
    echo "✅ Команды проверки файла выполнены."
    echo "Проверьте вывод выше, чтобы убедиться в наличии файла 'check_plugin_status.php' и его правах."
    echo "Если файл есть, попробуйте снова открыть в браузере:"
    echo "https://mosbuketik.ru/wp-content/plugins/check_plugin_status.php"
else
    echo "❌ Ошибка при выполнении скрипта проверки."
fi 