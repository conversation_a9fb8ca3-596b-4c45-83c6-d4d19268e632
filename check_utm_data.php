<?php
// Check UTM and analytics data in WooCommerce orders
echo "=== ПРОВЕРКА UTM И АНАЛИТИЧЕСКИХ ДАННЫХ ===\n";

$wp_path = '/home/<USER>/bekflom3/mosbuketik.ru/public_html';
require_once($wp_path . '/wp-config.php');
require_once($wp_path . '/wp-load.php');

echo "WordPress загружен успешно\n\n";

// Get recent orders
$orders = wc_get_orders(array(
    'limit' => 5,
    'orderby' => 'date',
    'order' => 'DESC',
    'status' => array('processing', 'completed', 'pending')
));

echo "Найдено заказов: " . count($orders) . "\n\n";

foreach ($orders as $order) {
    $order_id = $order->get_id();
    echo "=== ЗАКАЗ #$order_id ===\n";
    echo "Дата: " . $order->get_date_created()->format('Y-m-d H:i:s') . "\n";
    echo "Клиент: " . $order->get_formatted_billing_full_name() . "\n";
    echo "Сумма: " . $order->get_total() . " руб.\n";
    
    // Check all meta data
    $meta_data = $order->get_meta_data();
    echo "Мета-данные заказа:\n";
    
    $utm_found = false;
    $analytics_found = false;
    
    foreach ($meta_data as $meta) {
        $key = $meta->get_data()['key'];
        $value = $meta->get_data()['value'];
        
        // Look for UTM and analytics data
        if (strpos($key, 'utm') !== false || 
            strpos($key, 'source') !== false || 
            strpos($key, 'medium') !== false ||
            strpos($key, 'campaign') !== false ||
            strpos($key, 'referrer') !== false ||
            strpos($key, 'landing') !== false ||
            strpos($key, 'gclid') !== false ||
            strpos($key, 'fbclid') !== false) {
            
            echo "  📊 $key: $value\n";
            $utm_found = true;
        }
        
        // Look for other analytics
        if (strpos($key, 'analytics') !== false ||
            strpos($key, 'tracking') !== false ||
            strpos($key, 'ga_') !== false ||
            strpos($key, 'gtm_') !== false) {
            
            echo "  📈 $key: $value\n";
            $analytics_found = true;
        }
    }
    
    if (!$utm_found && !$analytics_found) {
        echo "  ❌ UTM и аналитические данные не найдены\n";
    }
    
    // Check session data
    echo "Данные сессии:\n";
    if (isset($_SESSION)) {
        foreach ($_SESSION as $key => $value) {
            if (strpos($key, 'utm') !== false || strpos($key, 'source') !== false) {
                echo "  🔗 SESSION[$key]: $value\n";
            }
        }
    }
    
    // Check cookies
    echo "Куки:\n";
    foreach ($_COOKIE as $key => $value) {
        if (strpos($key, 'utm') !== false || 
            strpos($key, 'source') !== false ||
            strpos($key, 'gclid') !== false ||
            strpos($key, 'fbclid') !== false) {
            echo "  🍪 COOKIE[$key]: " . substr($value, 0, 100) . "\n";
        }
    }
    
    // Check referer
    $referer = wp_get_referer();
    if ($referer) {
        echo "  🔗 Referer: $referer\n";
    }
    
    echo "\n";
}

// Check if there are any UTM tracking plugins
echo "=== ПРОВЕРКА ПЛАГИНОВ ОТСЛЕЖИВАНИЯ ===\n";
$active_plugins = get_option('active_plugins');
$utm_plugins = array();

foreach ($active_plugins as $plugin) {
    if (strpos($plugin, 'utm') !== false || 
        strpos($plugin, 'tracking') !== false ||
        strpos($plugin, 'analytics') !== false ||
        strpos($plugin, 'source') !== false) {
        $utm_plugins[] = $plugin;
    }
}

if (!empty($utm_plugins)) {
    echo "Найдены плагины отслеживания:\n";
    foreach ($utm_plugins as $plugin) {
        echo "  📦 $plugin\n";
    }
} else {
    echo "❌ Плагины отслеживания UTM не найдены\n";
}

echo "\n=== ПРОВЕРКА ЗАВЕРШЕНА ===\n";
?> 