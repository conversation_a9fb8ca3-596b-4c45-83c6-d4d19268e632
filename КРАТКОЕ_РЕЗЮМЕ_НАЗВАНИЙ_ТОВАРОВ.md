# 📝 КРАТКОЕ РЕЗЮМЕ: Проблема с названиями товаров

## 🔍 ПРОБЛЕМА
Названия товаров в TableCRM отображаются как "Testing" вместо реальных названий.

## 🎯 ПРИЧИНА
На сервере установлена **старая версия плагина** без исправления поля `name` для товаров.

### Анализ логов:
**Текущее состояние (НЕПРАВИЛЬНО):**
```json
"goods":[{"price":4999,"quantity":1,"unit":116,"discount":0,"sum_discounted":0,"nomenclature":39697}]
```

**Исправленная версия (ПРАВИЛЬНО):**
```json
"goods":[{"name":"Букет роз","price":4999,"quantity":1,"unit":116,"discount":0,"sum_discounted":0,"nomenclature":39697}]
```

## ✅ РЕШЕНИЕ
1. **Загрузить** `tablecrm-integration-enhanced-v1.0.15-FINAL.zip`
2. **Установить** через админку WordPress (Плагины → Добавить новый → Загрузить)
3. **Деактивировать** старую версию
4. **Активировать** новую версию v1.0.15
5. **Создать** тестовый заказ для проверки

## 🔧 КЛЮЧЕВОЕ ИСПРАВЛЕНИЕ
**Строка 866 в коде:**
```php
'name' => isset($item['name']) ? sanitize_text_field($item['name']) : 'Товар',
```

## 🎉 РЕЗУЛЬТАТ
После установки v1.0.15:
- ✅ Правильные названия товаров в TableCRM
- ✅ Вариации товаров (размер, цвет)
- ✅ Скидки и суммы
- ✅ Уникальные контрагенты
- ✅ UTM аналитика

## 📋 ПРОВЕРКА
Создайте тестовый заказ и найдите в логах строку с `"name":"Название товара"` - проблема решена! 🚀 