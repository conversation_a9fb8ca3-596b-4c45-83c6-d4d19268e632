<?php
// Test UTM API sending
echo "=== ТЕСТ ОТПРАВКИ UTM-ДАННЫХ ЧЕРЕЗ API ===\n";

$wp_path = '/home/<USER>/bekflom3/mosbuketik.ru/public_html';
require_once($wp_path . '/wp-config.php');
require_once($wp_path . '/wp-load.php');

echo "WordPress загружен успешно\n\n";

// Тестовые UTM-данные
$test_utm_data = array(
    "source_type" => "referral",
    "referrer" => "https://yandex.ru/",
    "utm_source" => "yandex.ru",
    "utm_medium" => "referral",
    "utm_content" => "/",
    "user_agent" => "Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Mobile/15E148 Safari/604.1",
    "device_type" => "Mobile",
    "utm_name" => "Анастасия",
    "utm_phone" => "+79179551570",
    "utm_email" => "<EMAIL>",
    "utm_leadid" => "lead_12345",
    "utm_yclientid" => "yandex_client_67890",
    "utm_gaclientid" => "ga_client_54321"
);

echo "=== ИСХОДНЫЕ UTM-ДАННЫЕ ===\n";
echo json_encode($test_utm_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";

// Получаем настройки API
$api_url = get_option('tablecrm_complete_api_url', 'https://app.tablecrm.com/api/v1/');
$api_key = get_option('tablecrm_complete_api_key');

if (empty($api_url) || empty($api_key)) {
    echo "❌ Не настроены API параметры\n";
    exit;
}

// Используем существующий Document ID для тестирования
$test_document_id = 138289; // Document ID из последнего успешного заказа

echo "=== ТЕСТ ОТПРАВКИ UTM ===\n";
echo "Document ID для тестирования: $test_document_id\n";
echo "API URL: $api_url\n";
echo "API Key: " . substr($api_key, 0, 10) . "...\n\n";

// Формируем URL для UTM API
$utm_url = rtrim($api_url, '/') . '/docs_sales/' . $test_document_id . '/utm/?token=' . $api_key;

echo "UTM API URL: $utm_url\n\n";

// Отправляем UTM-данные
$args = array(
    'method' => 'POST',
    'headers' => array(
        'Content-Type' => 'application/json; charset=utf-8',
        'User-Agent' => 'WordPress-TableCRM-Integration-Test/1.0'
    ),
    'body' => json_encode($test_utm_data, JSON_UNESCAPED_UNICODE),
    'timeout' => 30,
    'sslverify' => true
);

echo "=== ОТПРАВКА ЗАПРОСА ===\n";
echo "Данные для отправки:\n";
echo json_encode($test_utm_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";

$response = wp_remote_post($utm_url, $args);

if (is_wp_error($response)) {
    echo "❌ Ошибка HTTP запроса: " . $response->get_error_message() . "\n";
    exit;
}

$status_code = wp_remote_retrieve_response_code($response);
$response_body = wp_remote_retrieve_body($response);

echo "=== РЕЗУЛЬТАТ ЗАПРОСА ===\n";
echo "HTTP Status: $status_code\n";
echo "Response Body: $response_body\n\n";

if ($status_code >= 200 && $status_code < 300) {
    echo "✅ UTM-данные успешно отправлены!\n";
    
    // Проверяем структуру ответа
    $decoded_response = json_decode($response_body, true);
    if ($decoded_response) {
        echo "Структура ответа:\n";
        echo json_encode($decoded_response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
    }
} else {
    echo "❌ Ошибка отправки UTM-данных\n";
    echo "Возможные проблемы:\n";
    
    if ($status_code == 400) {
        echo "- Неверный формат данных\n";
        echo "- Отсутствуют обязательные поля\n";
        echo "- Неправильная структура JSON\n";
    } elseif ($status_code == 401) {
        echo "- Неверный API ключ\n";
    } elseif ($status_code == 404) {
        echo "- Document ID не найден: $test_document_id\n";
        echo "- Неверный URL эндпоинта\n";
    } elseif ($status_code == 500) {
        echo "- Внутренняя ошибка сервера TableCRM\n";
    }
}

echo "\n=== ПРОВЕРКА КАЖДОГО ПОЛЯ ===\n";
$required_fields = array('utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content');
$extended_fields = array('utm_name', 'utm_phone', 'utm_email', 'utm_leadid', 'utm_yclientid', 'utm_gaclientid');
$additional_fields = array('referrer', 'source_type', 'device_type', 'user_agent');

echo "Основные UTM поля:\n";
foreach ($required_fields as $field) {
    $status = isset($test_utm_data[$field]) ? "✅ Есть" : "❌ Отсутствует";
    $value = isset($test_utm_data[$field]) ? $test_utm_data[$field] : "N/A";
    echo "  $field: $status ($value)\n";
}

echo "\nРасширенные UTM поля:\n";
foreach ($extended_fields as $field) {
    $status = isset($test_utm_data[$field]) ? "✅ Есть" : "❌ Отсутствует";
    $value = isset($test_utm_data[$field]) ? $test_utm_data[$field] : "N/A";
    echo "  $field: $status ($value)\n";
}

echo "\nДополнительные поля:\n";
foreach ($additional_fields as $field) {
    $status = isset($test_utm_data[$field]) ? "✅ Есть" : "❌ Отсутствует";
    $value = isset($test_utm_data[$field]) ? $test_utm_data[$field] : "N/A";
    echo "  $field: $status ($value)\n";
}

echo "\n=== ТЕСТ ЗАВЕРШЕН ===\n";
?> 