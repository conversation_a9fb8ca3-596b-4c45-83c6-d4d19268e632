<?php
/**
 * Скрипт быстрой проверки API TableCRM
 * Версия: 1.1.0 - Обновлено с правильными эндпоинтами от Рушана
 * Дата: 2025-01-06
 */

echo "🔍 ТЕСТ API TABLECRM - ОБНОВЛЕННАЯ ДИАГНОСТИКА\n";
echo "===============================================\n\n";

// Правильный API ключ от Рушана
$api_key = 'af1874616430e04cfd4bce30035789907e899fc7c3a1a4bb27254828ff304a77';

// Базовый URL (подтвержден Рушаном)
$api_base_url = 'https://app.tablecrm.com/api/v1/';

// Правильные эндпоинты от Рушана
$test_endpoints = [
    // Основные эндпоинты для интеграции
    'docs_sales/' => 'POST',  // Создание заказов
    'contragents/' => 'GET',  // Поиск клиентов
    'nomenclature/' => 'GET', // Поиск товаров
    'nomenclatures/' => 'POST', // Создание товаров
    
    // Дополнительные эндпоинты
    'docs' => 'GET',          // Документация
    'docs#/docs_sales/create_utm_tag_docs_sales__idx__utm_post' => 'GET',
    'docs#/nomenclature/get_nomenclature_nomenclature__get' => 'GET',
    'docs#/contragents/read_contragents_meta_contragents__get' => 'GET'
];

echo "📋 ТЕСТИРУЕМЫЕ ПАРАМЕТРЫ:\n";
echo "API Key: " . substr($api_key, 0, 15) . "...\n";
echo "Базовый URL: {$api_base_url}\n";
echo "Эндпоинты: " . count($test_endpoints) . " вариантов\n\n";

function test_api_endpoint($base_url, $endpoint, $api_key, $method = 'GET') {
    // Формируем URL с токеном
    $full_url = rtrim($base_url, '/') . '/' . ltrim($endpoint, '/');
    
    // Добавляем токен в URL
    $separator = (strpos($full_url, '?') !== false) ? '&' : '?';
    $full_url .= $separator . 'token=' . $api_key;
    
    // Для тестовых параметров добавляем дополнительные параметры
    if ($endpoint === 'contragents/') {
        $full_url .= '&limit=100&offset=0&sort=created_at:desc&phone=79377799906';
    } elseif ($endpoint === 'nomenclature/') {
        $full_url .= '&name=хлеб&limit=100&offset=0&with_prices=false&with_balance=false&with_attributes=false&only_main_from_group=false';
    }
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $full_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 15);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'TableCRM-Test-Script/1.1.0');
    
    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Accept: application/json'
        ]);
        
        // Для POST запросов добавляем тестовые данные
        if ($endpoint === 'docs_sales/') {
            $test_data = json_encode([
                [
                    "dated" => time(),
                    "operation" => "Заказ",
                    "comment" => "Тестовый заказ",
                    "tax_included" => true,
                    "tax_active" => true,
                    "goods" => [
                        [
                            "price" => 100,
                            "quantity" => 1,
                            "unit" => 116,
                            "discount" => 0,
                            "sum_discounted" => 0,
                            "nomenclature" => 39697
                        ]
                    ],
                    "settings" => new stdClass(),
                    "warehouse" => 39,
                    "contragent" => 330985,
                    "paybox" => 218,
                    "organization" => 38,
                    "status" => false,
                    "paid_rubles" => "100.00",
                    "paid_lt" => 0
                ]
            ]);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $test_data);
        }
    }
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'url' => $full_url,
        'status' => $http_code,
        'response' => $response,
        'error' => $error
    ];
}

echo "🚀 НАЧИНАЕМ ТЕСТИРОВАНИЕ...\n";
echo "==============================\n\n";

$successful_responses = [];
$auth_errors = [];
$not_found_errors = [];

foreach ($test_endpoints as $endpoint => $method) {
    echo "🔗 Тестируем: {$method} {$endpoint}\n";
    
    $result = test_api_endpoint($api_base_url, $endpoint, $api_key, $method);
    
    $status_icon = '❌';
    $status_text = 'FAIL';
    
    if ($result['status'] == 200) {
        $status_icon = '✅';
        $status_text = 'SUCCESS';
        $successful_responses[] = $result;
    } elseif ($result['status'] == 201) {
        $status_icon = '✅';
        $status_text = 'CREATED';
        $successful_responses[] = $result;
    } elseif ($result['status'] == 401) {
        $status_icon = '🔑';
        $status_text = 'UNAUTHORIZED';
        $auth_errors[] = $result;
    } elseif ($result['status'] == 403) {
        $status_icon = '🚫';
        $status_text = 'FORBIDDEN';
        $auth_errors[] = $result;
    } elseif ($result['status'] == 404) {
        $status_icon = '❓';
        $status_text = 'NOT_FOUND';
        $not_found_errors[] = $result;
    } elseif ($result['status'] == 0) {
        $status_icon = '🌐';
        $status_text = 'CONNECTION_ERROR';
    } else {
        $status_icon = '⚠️';
        $status_text = 'HTTP_' . $result['status'];
    }
    
    printf("   %s [%d] %s\n", $status_icon, $result['status'], $status_text);
    
    // Показываем первые 150 символов ответа для диагностики
    if (!empty($result['response']) && $result['status'] != 404) {
        $response_preview = substr(str_replace(["\n", "\r", "\t"], ' ', $result['response']), 0, 150);
        echo "   Response: " . $response_preview . "...\n";
    }
    
    if (!empty($result['error'])) {
        echo "   Error: " . $result['error'] . "\n";
    }
    
    echo "\n";
}

echo "📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ:\n";
echo "============================\n";

if (count($successful_responses) > 0) {
    echo "✅ РАБОЧИЕ ЭНДПОИНТЫ (" . count($successful_responses) . "):\n";
    foreach ($successful_responses as $success) {
        echo "   - " . parse_url($success['url'], PHP_URL_PATH) . " [" . $success['status'] . "]\n";
    }
    echo "\n";
}

if (count($auth_errors) > 0) {
    echo "🔑 ПРОБЛЕМЫ С АВТОРИЗАЦИЕЙ (" . count($auth_errors) . "):\n";
    foreach ($auth_errors as $auth_error) {
        echo "   - " . parse_url($auth_error['url'], PHP_URL_PATH) . " [" . $auth_error['status'] . "]\n";
    }
    echo "\n";
}

if (count($not_found_errors) > 0) {
    echo "❓ ЭНДПОИНТЫ НЕ НАЙДЕНЫ (" . count($not_found_errors) . "):\n";
    foreach ($not_found_errors as $not_found) {
        echo "   - " . parse_url($not_found['url'], PHP_URL_PATH) . " [" . $not_found['status'] . "]\n";
    }
    echo "\n";
}

echo "🎯 ЗАКЛЮЧЕНИЕ:\n";
echo "==============\n";

if (count($successful_responses) > 0) {
    echo "✅ API TableCRM РАБОТАЕТ!\n";
    echo "   Найдены рабочие эндпоинты - интеграция должна функционировать\n\n";
} elseif (count($auth_errors) > 0) {
    echo "🔑 ПРОБЛЕМА С API КЛЮЧОМ\n";
    echo "   API отвечает, но есть проблемы с авторизацией\n";
    echo "   Проверьте правильность API токена\n\n";
} elseif (count($not_found_errors) == count($test_endpoints)) {
    echo "❌ ВСЕ ЭНДПОИНТЫ ВОЗВРАЩАЮТ 404\n";
    echo "   API изменился или недоступен\n\n";
} else {
    echo "⚠️ СМЕШАННЫЕ РЕЗУЛЬТАТЫ\n";
    echo "   Некоторые эндпоинты работают, некоторые - нет\n\n";
}

echo "📝 РЕКОМЕНДАЦИИ:\n";
echo "================\n";
echo "1. Если есть успешные ответы - обновите плагин с рабочими эндпоинтами\n";
echo "2. Если 401/403 ошибки - проверьте API ключ в настройках плагина\n";
echo "3. Если все 404 - свяжитесь с поддержкой TableCRM\n";
echo "4. Проверьте логи плагина после обновления\n\n";

echo "✅ ТЕСТИРОВАНИЕ ЗАВЕРШЕНО\n";
echo "Время: " . date('Y-m-d H:i:s') . "\n";
echo "Версия скрипта: 1.1.0\n"; 