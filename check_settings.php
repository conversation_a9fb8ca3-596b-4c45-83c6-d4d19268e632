<?php
/**
 * Диагностический скрипт для проверки настроек TableCRM Complete
 * Показывает какие параметры заполнены, а какие пустые
 */

echo "🔍 ДИАГНОСТИКА НАСТРОЕК TABLECRM COMPLETE\n";
echo "==========================================\n\n";

// Имитируем функцию get_option для проверки
function check_option($option_name, $default = '') {
    // Здесь мы не можем получить реальные значения из WordPress
    // Но можем показать какие параметры должны быть заполнены
    return $default;
}

// Список всех обязательных параметров
$required_settings = [
    'tablecrm_complete_api_url' => 'https://app.tablecrm.com/api/v1/',
    'tablecrm_complete_api_key' => 'af1874616430e04cfd4bce30035789907e899fc7c3a1a4bb27254828ff304a77',
    'tablecrm_complete_warehouse_id' => '39',
    'tablecrm_complete_organization_id' => '38', 
    'tablecrm_complete_cashbox_id' => '218',
    'tablecrm_complete_unit_id' => '116'
];

echo "📋 ОБЯЗАТЕЛЬНЫЕ ПАРАМЕТРЫ ДЛЯ ЗАПОЛНЕНИЯ:\n";
echo "=========================================\n\n";

foreach ($required_settings as $setting => $expected_value) {
    $display_name = str_replace('tablecrm_complete_', '', $setting);
    $display_name = str_replace('_', ' ', strtoupper($display_name));
    
    echo "✅ {$display_name}: {$expected_value}\n";
}

echo "\n";
echo "🚨 КРИТИЧЕСКИ ВАЖНО:\n";
echo "====================\n";
echo "В админке WordPress → Настройки → TableCRM Complete\n";
echo "ВСЕ эти поля должны быть заполнены ТОЧНО этими значениями!\n\n";

echo "🔧 ПРОВЕРЬТЕ КАЖДОЕ ПОЛЕ:\n";
echo "=========================\n";
echo "1. API URL: https://app.tablecrm.com/api/v1/\n";
echo "2. API Key: af1874616430e04cfd4bce30035789907e899fc7c3a1a4bb27254828ff304a77\n";
echo "3. Organization ID: 38\n";
echo "4. Warehouse ID: 39\n";
echo "5. Cashbox ID: 218\n";
echo "6. Unit ID: 116\n\n";

echo "⚠️  ВНИМАНИЕ:\n";
echo "=============\n";
echo "- Поля НЕ должны быть пустыми\n";
echo "- Поля НЕ должны содержать пробелы в начале/конце\n";
echo "- Числовые поля должны содержать только цифры\n";
echo "- API Key должен быть скопирован полностью\n\n";

echo "🎯 ПОСЛЕ ЗАПОЛНЕНИЯ:\n";
echo "====================\n";
echo "1. Нажмите 'Сохранить настройки'\n";
echo "2. Нажмите 'Тестировать соединение'\n";
echo "3. Создайте тестовый заказ\n";
echo "4. Проверьте логи\n\n";

echo "✅ ДИАГНОСТИКА ЗАВЕРШЕНА\n";
echo "Время: " . date('Y-m-d H:i:s') . "\n";
?> 