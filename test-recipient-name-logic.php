<?php
/**
 * Тестовый файл для проверки изменения логики имен получателя
 * Проверяем, что теперь используется имя заказчика (billing) вместо получателя (shipping)
 */

// Подключаем WordPress
require_once('wp-config.php');

echo "=== Тест логики имен получателя в доставке ===\n\n";

// Получаем заказы для тестирования
$orders = wc_get_orders(array(
    'limit' => 5,
    'orderby' => 'date',
    'order' => 'DESC'
));

if (empty($orders)) {
    echo "❌ Нет заказов для тестирования\n";
    exit;
}

foreach ($orders as $order) {
    $order_id = $order->get_id();
    
    echo "--- Заказ ID: {$order_id} ---\n";
    
    // Получаем данные заказчика (billing)
    $billing_first_name = $order->get_billing_first_name();
    $billing_last_name = $order->get_billing_last_name();
    $billing_full_name = trim($billing_first_name . ' ' . $billing_last_name);
    
    // Получаем данные получателя (shipping)
    $shipping_first_name = $order->get_shipping_first_name();
    $shipping_last_name = $order->get_shipping_last_name();
    $shipping_full_name = trim($shipping_first_name . ' ' . $shipping_last_name);
    
    echo "Данные заказчика (billing): '{$billing_full_name}'\n";
    echo "Данные получателя (shipping): '{$shipping_full_name}'\n";
    
    // Определяем, какое имя должно использоваться по новой логике
    $expected_name = !empty($billing_full_name) ? $billing_full_name : $shipping_full_name;
    if (empty($expected_name)) {
        $email = $order->get_billing_email();
        $expected_name = !empty($email) ? $email : 'Получатель';
    }
    
    echo "Ожидаемое имя по новой логике: '{$expected_name}'\n";
    
    // Проверяем приоритет
    if (!empty($billing_full_name) && !empty($shipping_full_name)) {
        if ($billing_full_name !== $shipping_full_name) {
            echo "✅ Разные имена - должно использоваться имя заказчика: '{$billing_full_name}'\n";
        } else {
            echo "ℹ️ Имена одинаковые: '{$billing_full_name}'\n";
        }
    } elseif (!empty($billing_full_name)) {
        echo "✅ Только имя заказчика: '{$billing_full_name}'\n";
    } elseif (!empty($shipping_full_name)) {
        echo "⚠️ Только имя получателя: '{$shipping_full_name}' (будет использовано как fallback)\n";
    } else {
        echo "⚠️ Нет имен - будет использован email или 'Получатель'\n";
    }
    
    echo "\n";
}

echo "=== Тест завершен ===\n";
echo "\nТеперь в CRM будет отправляться:\n";
echo "1. Имя заказчика (из платежных данных) - приоритет\n";
echo "2. Если пусто - имя получателя (из данных доставки)\n";
echo "3. Если и то пусто - email или 'Получатель'\n";
?>
