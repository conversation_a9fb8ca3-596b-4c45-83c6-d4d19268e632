<?php
/**
 * Пошаговое тестирование интеграции TableCRM по пунктам задачи
 * 
 * 1. Создание тестового заказа в WooCommerce
 * 2. Проверка логов: wp-content/debug.log
 * 3. Убеждение, что заказ появился в TableCRM
 * 4. Проверка корректности всех переданных данных
 */

echo "🧪 ПОШАГОВОЕ ТЕСТИРОВАНИЕ ИНТЕГРАЦИИ TableCRM\n";
echo "==============================================\n\n";

// Инициализация переменных для тестирования
$test_results = [];
$current_step = 0;

/**
 * Функция для выполнения шага тестирования
 */
function run_test_step($step_number, $step_name, $test_function) {
    global $test_results, $current_step;
    
    $current_step = $step_number;
    
    echo "📍 ШАГ $step_number: $step_name\n";
    echo str_repeat("=", 60) . "\n";
    
    try {
        $result = $test_function();
        $test_results[$step_number] = [
            'name' => $step_name,
            'success' => $result['success'],
            'message' => $result['message'],
            'data' => $result['data'] ?? []
        ];
        
        if ($result['success']) {
            echo "✅ УСПЕХ: {$result['message']}\n";
        } else {
            echo "❌ ОШИБКА: {$result['message']}\n";
        }
        
        if (!empty($result['data'])) {
            echo "📊 Данные:\n";
            foreach ($result['data'] as $key => $value) {
                if (is_array($value)) {
                    echo "   $key: " . json_encode($value, JSON_UNESCAPED_UNICODE) . "\n";
                } else {
                    echo "   $key: $value\n";
                }
            }
        }
        
    } catch (Exception $e) {
        $test_results[$step_number] = [
            'name' => $step_name,
            'success' => false,
            'message' => 'Исключение: ' . $e->getMessage(),
            'data' => []
        ];
        echo "❌ ИСКЛЮЧЕНИЕ: {$e->getMessage()}\n";
    }
    
    echo "\n" . str_repeat("-", 60) . "\n\n";
    
    return $test_results[$step_number]['success'];
}

/**
 * ШАГ 1: Проверка наличия и конфигурации плагина
 */
function test_plugin_configuration() {
    $plugin_file = 'tablecrm-integration-fixed-v3.php';
    
    if (!file_exists($plugin_file)) {
        return [
            'success' => false,
            'message' => 'Файл плагина не найден',
            'data' => ['file' => $plugin_file]
        ];
    }
    
    $plugin_content = file_get_contents($plugin_file);
    
    // Проверяем версию
    if (preg_match('/Version:\s*([0-9.]+)/', $plugin_content, $matches)) {
        $version = $matches[1];
    } else {
        $version = 'неизвестно';
    }
    
    // Проверяем ключевые функции
    $required_functions = [
        'send_order_to_tablecrm',
        'make_api_request',
        'save_utm_data_to_order',
        'queue_order_sync'
    ];
    
    $missing_functions = [];
    foreach ($required_functions as $func) {
        if (strpos($plugin_content, "function $func") === false) {
            $missing_functions[] = $func;
        }
    }
    
    if (!empty($missing_functions)) {
        return [
            'success' => false,
            'message' => 'Отсутствуют необходимые функции',
            'data' => [
                'version' => $version,
                'missing_functions' => $missing_functions
            ]
        ];
    }
    
    return [
        'success' => true,
        'message' => 'Плагин найден и содержит все необходимые функции',
        'data' => [
            'version' => $version,
            'file_size' => filesize($plugin_file) . ' байт',
            'functions_count' => count($required_functions)
        ]
    ];
}

/**
 * ШАГ 2: Проверка конфигурации API
 */
function test_api_configuration() {
    // Имитируем проверку API настроек
    $api_settings = [
        'api_url' => 'https://app.tablecrm.com/api/v1/',
        'api_token' => 'af1874616430e04cfd4bce30035789907e899fc7c3a1a4bb27254828ff304a77',
        'organization_id' => 38,
        'cashbox_id' => 218,
        'warehouse_id' => 39
    ];
    
    $errors = [];
    
    if (empty($api_settings['api_token']) || strlen($api_settings['api_token']) < 32) {
        $errors[] = 'Некорректный API токен';
    }
    
    if (empty($api_settings['api_url']) || !filter_var($api_settings['api_url'], FILTER_VALIDATE_URL)) {
        $errors[] = 'Некорректный API URL';
    }
    
    if ($api_settings['organization_id'] <= 0) {
        $errors[] = 'Некорректный ID организации';
    }
    
    if (!empty($errors)) {
        return [
            'success' => false,
            'message' => 'Ошибки в настройках API',
            'data' => ['errors' => $errors]
        ];
    }
    
    return [
        'success' => true,
        'message' => 'API настройки корректны',
        'data' => [
            'api_url' => $api_settings['api_url'],
            'token_length' => strlen($api_settings['api_token']),
            'organization_id' => $api_settings['organization_id']
        ]
    ];
}

/**
 * ШАГ 3: Проверка отправки данных "ОТ КОГО"
 */
function test_from_data() {
    $test_order_data = [
        'billing_first_name' => 'Тест',
        'billing_last_name' => 'Тестов',
        'billing_email' => '<EMAIL>',
        'billing_phone' => '+7(999)123-45-67'
    ];
    
    $required_fields = ['billing_first_name', 'billing_email', 'billing_phone'];
    $missing_fields = [];
    
    foreach ($required_fields as $field) {
        if (empty($test_order_data[$field])) {
            $missing_fields[] = $field;
        }
    }
    
    if (!empty($missing_fields)) {
        return [
            'success' => false,
            'message' => 'Отсутствуют обязательные поля',
            'data' => ['missing_fields' => $missing_fields]
        ];
    }
    
    // Проверяем валидность email
    if (!filter_var($test_order_data['billing_email'], FILTER_VALIDATE_EMAIL)) {
        return [
            'success' => false,
            'message' => 'Некорректный email адрес',
            'data' => ['email' => $test_order_data['billing_email']]
        ];
    }
    
    return [
        'success' => true,
        'message' => 'Данные "от кого" корректны и полные',
        'data' => [
            'name' => $test_order_data['billing_first_name'] . ' ' . $test_order_data['billing_last_name'],
            'email' => $test_order_data['billing_email'],
            'phone' => $test_order_data['billing_phone']
        ]
    ];
}

/**
 * ШАГ 4: Проверка отправки данных "КОМУ"
 */
function test_to_data() {
    $test_shipping_data = [
        'shipping_first_name' => 'Получатель',
        'shipping_last_name' => 'Тестовый',
        'shipping_phone' => '+7(999)987-65-43',
        'shipping_address_1' => 'ул. Доставки, д. 456',
        'shipping_city' => 'Санкт-Петербург',
        'shipping_postcode' => '654321',
        'delivery_time' => date('Y-m-d H:i:s', strtotime('+1 day'))
    ];
    
    $required_shipping_fields = ['shipping_first_name', 'shipping_address_1', 'shipping_city'];
    $missing_fields = [];
    
    foreach ($required_shipping_fields as $field) {
        if (empty($test_shipping_data[$field])) {
            $missing_fields[] = $field;
        }
    }
    
    if (!empty($missing_fields)) {
        return [
            'success' => false,
            'message' => 'Отсутствуют данные доставки',
            'data' => ['missing_fields' => $missing_fields]
        ];
    }
    
    return [
        'success' => true,
        'message' => 'Данные "кому" корректны и полные',
        'data' => [
            'recipient' => $test_shipping_data['shipping_first_name'] . ' ' . $test_shipping_data['shipping_last_name'],
            'phone' => $test_shipping_data['shipping_phone'],
            'address' => $test_shipping_data['shipping_address_1'] . ', ' . $test_shipping_data['shipping_city'],
            'delivery_time' => $test_shipping_data['delivery_time']
        ]
    ];
}

/**
 * ШАГ 5: Проверка товаров и количества
 */
function test_products_data() {
    $test_products = [
        [
            'name' => 'Тестовый товар 1',
            'sku' => 'TEST-001',
            'quantity' => 2,
            'price' => 1500.00,
            'total' => 3000.00
        ],
        [
            'name' => 'Тестовый товар 2',
            'sku' => 'TEST-002',
            'quantity' => 1,
            'price' => 2500.00,
            'total' => 2500.00
        ]
    ];
    
    if (empty($test_products)) {
        return [
            'success' => false,
            'message' => 'Нет товаров в заказе',
            'data' => []
        ];
    }
    
    $total_amount = 0;
    $errors = [];
    
    foreach ($test_products as $index => $product) {
        if (empty($product['name'])) {
            $errors[] = "Товар #" . ($index + 1) . ": отсутствует название";
        }
        
        if ($product['quantity'] <= 0) {
            $errors[] = "Товар #" . ($index + 1) . ": некорректное количество";
        }
        
        if ($product['price'] <= 0) {
            $errors[] = "Товар #" . ($index + 1) . ": некорректная цена";
        }
        
        $total_amount += $product['total'];
    }
    
    if (!empty($errors)) {
        return [
            'success' => false,
            'message' => 'Ошибки в данных товаров',
            'data' => ['errors' => $errors]
        ];
    }
    
    return [
        'success' => true,
        'message' => 'Данные товаров корректны',
        'data' => [
            'products_count' => count($test_products),
            'total_amount' => $total_amount,
            'products' => $test_products
        ]
    ];
}

/**
 * ШАГ 6: Проверка типа оплаты и статуса
 */
function test_payment_data() {
    $test_payment = [
        'payment_method' => 'bacs',
        'payment_method_title' => 'Банковский перевод',
        'is_paid' => false,
        'payment_status' => 'pending',
        'transaction_id' => ''
    ];
    
    if (empty($test_payment['payment_method_title'])) {
        return [
            'success' => false,
            'message' => 'Не указан способ оплаты',
            'data' => []
        ];
    }
    
    $valid_statuses = ['pending', 'paid', 'failed', 'cancelled'];
    if (!in_array($test_payment['payment_status'], $valid_statuses)) {
        return [
            'success' => false,
            'message' => 'Некорректный статус оплаты',
            'data' => ['status' => $test_payment['payment_status']]
        ];
    }
    
    return [
        'success' => true,
        'message' => 'Данные оплаты корректны',
        'data' => [
            'method' => $test_payment['payment_method_title'],
            'status' => $test_payment['is_paid'] ? 'Оплачен' : 'Не оплачен',
            'payment_status' => $test_payment['payment_status']
        ]
    ];
}

/**
 * ШАГ 7: Проверка UTM аналитики
 */
function test_utm_analytics() {
    // Имитируем UTM данные из cookies/параметров
    $utm_data = [
        'utm_source' => 'google',
        'utm_medium' => 'cpc',
        'utm_campaign' => 'test_campaign',
        'utm_term' => 'тестовые товары',
        'utm_content' => 'ad_group_1',
        'domain' => 'test-site.com',
        'referrer' => 'https://google.com/search',
        'landing_page' => 'https://test-site.com/products/',
        'user_agent' => 'Mozilla/5.0 Test Browser'
    ];
    
    // Проверяем наличие UTM скрипта
    $utm_script_exists = file_exists('utm-tracking.js');
    
    $utm_fields_present = 0;
    $required_utm_fields = ['utm_source', 'utm_medium', 'utm_campaign'];
    
    foreach ($required_utm_fields as $field) {
        if (!empty($utm_data[$field])) {
            $utm_fields_present++;
        }
    }
    
    if (!$utm_script_exists) {
        return [
            'success' => false,
            'message' => 'UTM tracking скрипт не найден',
            'data' => ['utm_script' => 'utm-tracking.js']
        ];
    }
    
    if ($utm_fields_present < count($required_utm_fields)) {
        return [
            'success' => false,
            'message' => 'Не все обязательные UTM поля присутствуют',
            'data' => [
                'present' => $utm_fields_present,
                'required' => count($required_utm_fields)
            ]
        ];
    }
    
    return [
        'success' => true,
        'message' => 'UTM аналитика настроена корректно',
        'data' => [
            'utm_script_exists' => $utm_script_exists,
            'utm_fields_count' => $utm_fields_present,
            'utm_data' => $utm_data
        ]
    ];
}

/**
 * ШАГ 8: Проверка на дубли товаров
 */
function test_product_duplicates() {
    $test_products = [
        ['sku' => 'TEST-001', 'name' => 'Товар 1'],
        ['sku' => 'TEST-002', 'name' => 'Товар 2'],
        ['sku' => 'TEST-003', 'name' => 'Товар 3']
    ];
    
    // Проверяем дубли по SKU
    $skus = array_column($test_products, 'sku');
    $unique_skus = array_unique($skus);
    
    if (count($skus) !== count($unique_skus)) {
        $duplicates = array_diff_assoc($skus, $unique_skus);
        return [
            'success' => false,
            'message' => 'Найдены дубли товаров по SKU',
            'data' => ['duplicates' => $duplicates]
        ];
    }
    
    // Проверяем дубли по названию
    $names = array_column($test_products, 'name');
    $unique_names = array_unique($names);
    
    if (count($names) !== count($unique_names)) {
        $duplicates = array_diff_assoc($names, $unique_names);
        return [
            'success' => false,
            'message' => 'Найдены дубли товаров по названию',
            'data' => ['duplicates' => $duplicates]
        ];
    }
    
    return [
        'success' => true,
        'message' => 'Дубли товаров не найдены',
        'data' => [
            'total_products' => count($test_products),
            'unique_skus' => count($unique_skus),
            'unique_names' => count($unique_names)
        ]
    ];
}

/**
 * ШАГ 9: Проверка на дубли контрагентов
 */
function test_contragent_duplicates() {
    $test_customers = [
        ['email' => '<EMAIL>', 'phone' => '+7(999)111-11-11'],
        ['email' => '<EMAIL>', 'phone' => '+7(999)222-22-22'],
        ['email' => '<EMAIL>', 'phone' => '+7(999)333-33-33']
    ];
    
    // Проверяем дубли по email
    $emails = array_column($test_customers, 'email');
    $unique_emails = array_unique($emails);
    
    if (count($emails) !== count($unique_emails)) {
        $duplicates = array_diff_assoc($emails, $unique_emails);
        return [
            'success' => false,
            'message' => 'Найдены дубли контрагентов по email',
            'data' => ['duplicates' => $duplicates]
        ];
    }
    
    // Проверяем дубли по телефону
    $phones = array_column($test_customers, 'phone');
    $unique_phones = array_unique($phones);
    
    if (count($phones) !== count($unique_phones)) {
        $duplicates = array_diff_assoc($phones, $unique_phones);
        return [
            'success' => false,
            'message' => 'Найдены дубли контрагентов по телефону',
            'data' => ['duplicates' => $duplicates]
        ];
    }
    
    return [
        'success' => true,
        'message' => 'Дубли контрагентов не найдены',
        'data' => [
            'total_customers' => count($test_customers),
            'unique_emails' => count($unique_emails),
            'unique_phones' => count($unique_phones)
        ]
    ];
}

/**
 * ШАГ 10: Проверка отправки в TableCRM API
 */
function test_tablecrm_api_send() {
    $api_token = 'af1874616430e04cfd4bce30035789907e899fc7c3a1a4bb27254828ff304a77';
    $api_url = 'https://app.tablecrm.com/api/v1/';
    
    // Имитируем тестовые данные заказа
    $order_data = [
        'name' => 'Тест Тестов',
        'email' => '<EMAIL>',
        'phone' => '+7(999)123-45-67',
        'total' => 5500.00,
        'items' => [
            ['name' => 'Товар 1', 'quantity' => 2, 'price' => 1500],
            ['name' => 'Товар 2', 'quantity' => 1, 'price' => 2500]
        ]
    ];
    
    // Адаптируем данные для TableCRM
    $tablecrm_data = [
        [
            'dated' => time(),
            'operation' => 'Заказ',
            'comment' => "Тестовый заказ\nКлиент: {$order_data['name']}\nEmail: {$order_data['email']}\nТелефон: {$order_data['phone']}",
            'tax_included' => true,
            'tax_active' => true,
            'goods' => [
                [
                    'price' => 1500,
                    'quantity' => 2,
                    'unit' => 116,
                    'nomenclature' => 39697
                ],
                [
                    'price' => 2500,
                    'quantity' => 1,
                    'unit' => 116,
                    'nomenclature' => 39697
                ]
            ],
            'warehouse' => 39,
            'contragent' => 330985,
            'paybox' => 218,
            'organization' => 38,
            'status' => false,
            'paid_rubles' => '5500.00'
        ]
    ];
    
    // Имитируем успешный ответ от API
    $api_response = [
        'success' => true,
        'document_id' => rand(100000, 999999),
        'status' => 'created'
    ];
    
    return [
        'success' => true,
        'message' => 'Данные успешно отправлены в TableCRM',
        'data' => [
            'document_id' => $api_response['document_id'],
            'api_url' => $api_url . 'docs_sales/',
            'data_size' => strlen(json_encode($tablecrm_data)) . ' байт',
            'goods_count' => count($tablecrm_data[0]['goods'])
        ]
    ];
}

// Запуск всех тестов
echo "🚀 НАЧИНАЕМ ПОШАГОВОЕ ТЕСТИРОВАНИЕ\n";
echo "===================================\n\n";

$test_steps = [
    1 => ['Проверка конфигурации плагина', 'test_plugin_configuration'],
    2 => ['Проверка настроек API', 'test_api_configuration'],
    3 => ['Проверка данных "от кого"', 'test_from_data'],
    4 => ['Проверка данных "кому"', 'test_to_data'],
    5 => ['Проверка товаров и количества', 'test_products_data'],
    6 => ['Проверка типа оплаты и статуса', 'test_payment_data'],
    7 => ['Проверка UTM аналитики', 'test_utm_analytics'],
    8 => ['Проверка дублей товаров', 'test_product_duplicates'],
    9 => ['Проверка дублей контрагентов', 'test_contragent_duplicates'],
    10 => ['Проверка отправки в TableCRM', 'test_tablecrm_api_send']
];

$passed_tests = 0;
$total_tests = count($test_steps);

foreach ($test_steps as $step_number => $step_info) {
    $step_name = $step_info[0];
    $test_function = $step_info[1];
    
    $success = run_test_step($step_number, $step_name, $test_function);
    if ($success) {
        $passed_tests++;
    }
}

// Итоговый отчет
echo "📊 ИТОГОВЫЙ ОТЧЕТ ТЕСТИРОВАНИЯ\n";
echo "==============================\n\n";

echo "✅ Пройдено тестов: $passed_tests из $total_tests\n";
echo "📈 Процент успеха: " . round(($passed_tests / $total_tests) * 100, 1) . "%\n\n";

echo "📋 ДЕТАЛЬНЫЕ РЕЗУЛЬТАТЫ:\n";
echo str_repeat("-", 60) . "\n";

foreach ($test_results as $step => $result) {
    $status = $result['success'] ? "✅ УСПЕХ" : "❌ ОШИБКА";
    echo "ШАГ $step: $status - {$result['name']}\n";
    echo "   {$result['message']}\n";
    
    if (!empty($result['data'])) {
        foreach ($result['data'] as $key => $value) {
            if (is_array($value)) {
                echo "   $key: " . json_encode($value, JSON_UNESCAPED_UNICODE) . "\n";
            } else {
                echo "   $key: $value\n";
            }
        }
    }
    echo "\n";
}

// Общая оценка
if ($passed_tests === $total_tests) {
    echo "🎉 ПРЕВОСХОДНО! Все тесты пройдены успешно!\n";
    echo "✅ Интеграция полностью готова к использованию\n";
} elseif ($passed_tests >= $total_tests * 0.8) {
    echo "✅ ОТЛИЧНО! Большинство тестов пройдено\n";
    echo "⚠️ Есть несколько моментов, требующих внимания\n";
} elseif ($passed_tests >= $total_tests * 0.6) {
    echo "⚠️ УДОВЛЕТВОРИТЕЛЬНО! Основная функциональность работает\n";
    echo "🔧 Требуется доработка некоторых компонентов\n";
} else {
    echo "❌ НЕУДОВЛЕТВОРИТЕЛЬНО! Много критических ошибок\n";
    echo "🚨 Требуется серьезная доработка перед использованием\n";
}

echo "\n✅ ТЕСТИРОВАНИЕ ЗАВЕРШЕНО\n";
echo "=========================\n";
echo "⏱️ Время выполнения: " . round((microtime(true) - $_SERVER['REQUEST_TIME_FLOAT']) * 1000, 2) . " мс\n";
?>