#!/bin/bash

echo "🔧 Прямая модификация wp-config.php на сервере..."

# Скачиваем текущий файл с сервера
echo "📥 Скачиваем wp-config.php с сервера..."
ftp -n bekflom3.beget.tech << 'EOF'
user bekflom3_wz zl2Shj!e&6ng
binary
cd public_html
get wp-config.php server-wp-config.php
quit
EOF

if [ ! -f "server-wp-config.php" ]; then
    echo "❌ Не удалось скачать wp-config.php с сервера!"
    exit 1
fi

echo "✅ Файл скачан. Создаем резервную копию..."
cp server-wp-config.php "server-wp-config-backup-$(date +%Y%m%d-%H%M%S).php"

echo "🔧 Отключаем логирование в файле..."

# Модифицируем файл - отключаем все настройки логирования
sed -i "s/define('WP_DEBUG', true);/define('WP_DEBUG', false);/g" server-wp-config.php
sed -i "s/define('WP_DEBUG_LOG', true);/define('WP_DEBUG_LOG', false);/g" server-wp-config.php
sed -i "s/define('WP_DEBUG_LOG_DB', true);/define('WP_DEBUG_LOG_DB', false);/g" server-wp-config.php
sed -i "s/define('SCRIPT_DEBUG', true);/define('SCRIPT_DEBUG', false);/g" server-wp-config.php

# Обновляем комментарий
sed -i "s/\/\/ Настройки отладки для TableCRM Integration$/\/\/ Настройки отладки для TableCRM Integration (отключены)/g" server-wp-config.php

echo "🔍 Проверяем изменения:"
grep -n "WP_DEBUG\|SCRIPT_DEBUG" server-wp-config.php

echo "📤 Загружаем измененный файл обратно на сервер..."
ftp -n bekflom3.beget.tech << 'EOF'
user bekflom3_wz zl2Shj!e&6ng
binary
cd public_html
put server-wp-config.php wp-config.php
quit
EOF

echo "✅ Файл wp-config.php успешно обновлен на сервере!"
echo "🎯 Логирование WordPress отключено:"
echo "   - WP_DEBUG: false"
echo "   - WP_DEBUG_LOG: false" 
echo "   - WP_DEBUG_LOG_DB: false"
echo "   - SCRIPT_DEBUG: false"

# Удаляем временные файлы
rm -f server-wp-config.php

echo "🧹 Временные файлы очищены." 