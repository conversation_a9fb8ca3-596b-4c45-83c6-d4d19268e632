<?php
/**
 * Простая проверка настроек плагина TableCRM
 * Работает без полного подключения к WordPress
 */

echo "🔍 ПРОВЕРКА НАСТРОЕК ПЛАГИНА TableCRM\n";
echo "====================================\n\n";

// Попробуем найти wp-config.php в разных местах
$wp_config_paths = array(
    __DIR__ . '/wp-config.php',
    __DIR__ . '/../wp-config.php', 
    __DIR__ . '/../../wp-config.php',
    __DIR__ . '/../../../wp-config.php',
    __DIR__ . '/../../../../wp-config.php'
);

$wp_config_found = false;
foreach ($wp_config_paths as $path) {
    if (file_exists($path)) {
        echo "✅ Найден wp-config.php: $path\n";
        $wp_config_found = true;
        
        // Читаем wp-config.php как текст
        $config_content = file_get_contents($path);
        
        // Извлекаем настройки базы данных
        if (preg_match("/define\s*\(\s*['\"]DB_NAME['\"]\s*,\s*['\"]([^'\"]*)['\"]/, $config_content, $matches)) {
            $db_name = $matches[1];
            echo "База данных: $db_name\n";
        }
        
        if (preg_match("/define\s*\(\s*['\"]DB_USER['\"]\s*,\s*['\"]([^'\"]*)['\"]/, $config_content, $matches)) {
            $db_user = $matches[1];
            echo "Пользователь БД: $db_user\n";
        }
        
        if (preg_match("/define\s*\(\s*['\"]DB_HOST['\"]\s*,\s*['\"]([^'\"]*)['\"]/, $config_content, $matches)) {
            $db_host = $matches[1];
            echo "Хост БД: $db_host\n";
        }
        
        break;
    }
}

if (!$wp_config_found) {
    echo "❌ wp-config.php не найден\n";
    echo "Текущая директория: " . __DIR__ . "\n";
    echo "Проверенные пути:\n";
    foreach ($wp_config_paths as $path) {
        echo "  - $path\n";
    }
    exit;
}

echo "\n2. ПРОВЕРКА ФАЙЛОВ ПЛАГИНА:\n";
echo "---------------------------\n";

$plugin_files = array(
    'tablecrm-update/tablecrm-integration-complete-v1.0.24.php',
    'tablecrm-integration-complete-v1.0.24.php'
);

$plugin_found = false;
foreach ($plugin_files as $file) {
    if (file_exists($file)) {
        echo "✅ Найден файл плагина: $file\n";
        $plugin_found = true;
        
        // Проверяем версию и дату изменения
        $file_time = filemtime($file);
        $file_size = filesize($file);
        echo "  Размер: " . number_format($file_size) . " байт\n";
        echo "  Изменен: " . date('Y-m-d H:i:s', $file_time) . "\n";
        
        // Проверяем наличие исправлений
        $content = file_get_contents($file);
        
        if (strpos($content, 'tablecrm_complete_paid_statuses') !== false) {
            echo "  ✅ Содержит исправления для оплаченных статусов\n";
        } else {
            echo "  ❌ НЕ содержит исправления для оплаченных статусов\n";
        }
        
        if (strpos($content, 'get_item_total($item, false, true)') !== false) {
            echo "  ✅ Содержит исправления для расчета скидок\n";
        } else {
            echo "  ❌ НЕ содержит исправления для расчета скидок\n";
        }
        
        if (strpos($content, 'ДЕТАЛЬНЫЙ АНАЛИЗ ОТПРАВЛЯЕМЫХ ДАННЫХ') !== false) {
            echo "  ✅ Содержит детальное логирование\n";
        } else {
            echo "  ❌ НЕ содержит детальное логирование\n";
        }
        
        break;
    }
}

if (!$plugin_found) {
    echo "❌ Файл плагина не найден\n";
}

echo "\n3. ПРОВЕРКА ЛОГОВ:\n";
echo "------------------\n";

$log_paths = array(
    'wp-content/uploads/tablecrm_integration_complete.log',
    '../wp-content/uploads/tablecrm_integration_complete.log',
    '../../wp-content/uploads/tablecrm_integration_complete.log',
    '../../../wp-content/uploads/tablecrm_integration_complete.log',
    '../../../../wp-content/uploads/tablecrm_integration_complete.log'
);

foreach ($log_paths as $log_path) {
    if (file_exists($log_path)) {
        echo "✅ Найден файл логов: $log_path\n";
        
        $log_size = filesize($log_path);
        echo "  Размер: " . number_format($log_size) . " байт\n";
        
        if ($log_size > 0) {
            $log_content = file_get_contents($log_path);
            $log_lines = explode("\n", $log_content);
            
            echo "  Последние 10 строк:\n";
            $recent_lines = array_slice($log_lines, -10);
            foreach ($recent_lines as $line) {
                if (trim($line)) {
                    echo "    " . substr($line, 0, 100) . "\n";
                }
            }
        } else {
            echo "  Файл пуст\n";
        }
        break;
    }
}

echo "\n4. ИНСТРУКЦИИ ПО ДИАГНОСТИКЕ:\n";
echo "------------------------------\n";
echo "1. Убедитесь что используется исправленная версия плагина\n";
echo "2. Создайте тестовый заказ и проверьте логи\n";
echo "3. Проверьте настройки плагина в админ-панели WordPress\n";
echo "4. Если проблема остается, проверьте что именно отправляется в TableCRM через логи\n";

echo "\n✅ Проверка завершена\n";
?> 