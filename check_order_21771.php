<?php
// Check why order #21773 was not sent to TableCRM
echo "=== ДИАГНОСТИКА ЗАКАЗА #21773 ===\n";

$wp_path = '/home/<USER>/bekflom3/mosbuketik.ru/public_html';
require_once($wp_path . '/wp-config.php');
require_once($wp_path . '/wp-load.php');

echo "WordPress загружен успешно\n\n";

$order_id = 21773;

// Check if WooCommerce is active
if (!class_exists('WooCommerce')) {
    echo "❌ WooCommerce не активен\n";
    exit;
}

// Get the order
$order = wc_get_order($order_id);
if (!$order) {
    echo "❌ Заказ #$order_id не найден\n";
    exit;
}

echo "✅ Заказ #$order_id найден\n";
echo "Статус заказа: " . $order->get_status() . "\n";
echo "Дата создания: " . $order->get_date_created()->format('Y-m-d H:i:s') . "\n";
echo "Email клиента: " . $order->get_billing_email() . "\n";
echo "Телефон клиента: " . $order->get_billing_phone() . "\n";
echo "Сумма заказа: " . $order->get_total() . "\n";

// Check order items
$items = $order->get_items();
echo "Товары в заказе (" . count($items) . "):\n";
foreach ($items as $item) {
    echo "  - " . $item->get_name() . " (кол-во: " . $item->get_quantity() . ")\n";
}

// Check if order was already sent
$existing_doc_id = get_post_meta($order_id, '_tablecrm_complete_document_id', true);
$sent_at = get_post_meta($order_id, '_tablecrm_complete_sent_at', true);

if ($existing_doc_id) {
    echo "\n✅ Заказ УЖЕ ОТПРАВЛЕН в TableCRM\n";
    echo "Document ID: $existing_doc_id\n";
    echo "Дата отправки: $sent_at\n";
} else {
    echo "\n❌ Заказ НЕ ОТПРАВЛЕН в TableCRM\n";
}

// Check plugin settings
echo "\n=== НАСТРОЙКИ ПЛАГИНА ===\n";
$enable_orders = get_option('tablecrm_complete_enable_orders', 0);
$trigger_statuses = get_option('tablecrm_complete_order_statuses', array());

echo "Отправка заказов включена: " . ($enable_orders ? 'ДА' : 'НЕТ') . "\n";
echo "Статусы для отправки: " . implode(', ', $trigger_statuses) . "\n";
echo "Текущий статус заказа: " . $order->get_status() . "\n";

// Check if current status should trigger sending
$should_send = in_array($order->get_status(), $trigger_statuses);
echo "Должен ли отправляться: " . ($should_send ? 'ДА' : 'НЕТ') . "\n";

// Check if plugin is available
global $tablecrm_integration_complete;
if (isset($tablecrm_integration_complete)) {
    echo "\n✅ Плагин TableCRM доступен\n";
    
    if (!$existing_doc_id && $enable_orders && $should_send) {
        echo "\n🔄 ПОПЫТКА ОТПРАВИТЬ ЗАКАЗ ВРУЧНУЮ...\n";
        $result = $tablecrm_integration_complete->send_order_to_tablecrm($order_id);
        
        if ($result) {
            echo "✅ УСПЕХ! Заказ отправлен\n";
            $new_doc_id = get_post_meta($order_id, '_tablecrm_complete_document_id', true);
            if ($new_doc_id) {
                echo "Document ID: $new_doc_id\n";
            }
        } else {
            echo "❌ ОШИБКА! Заказ не отправлен\n";
        }
    }
} else {
    echo "\n❌ Плагин TableCRM недоступен\n";
}

echo "\n=== ДИАГНОСТИКА ЗАВЕРШЕНА ===\n";
?> 