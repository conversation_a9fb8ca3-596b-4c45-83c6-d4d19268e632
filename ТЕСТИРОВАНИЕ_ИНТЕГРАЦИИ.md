# 🧪 ТЕСТИРОВАНИЕ ИНТЕГРАЦИИ TABLECRM

## ✅ **ГОТОВО К ТЕСТИРОВАНИЮ**

---

## 🎯 **БЫСТРЫЙ ТЕСТ (5 МИНУТ)**

### **1️⃣ Создайте тестовый заказ:**
**Сайт:** https://mosbuketik.ru

**Тестовые данные:**
```
Имя: Тестовый Покупатель 10589
Email: <EMAIL>
Телефон: +7 (999) 123-10-58
Адрес: ул. Тестовая, д. 10589, Москва, 123456
```

### **2️⃣ Проверьте результат:**

**Админ-панель WooCommerce:**
```
https://mosbuketik.ru/wp-admin/edit.php?post_type=shop_order
```

**Настройки плагина:**
```
https://mosbuketik.ru/wp-admin/options-general.php?page=tablecrm-settings
```

**TableCRM:**
```
https://app.tablecrm.com
```

### **3️⃣ Проверьте логи:**
```bash
./logs_manual_check.sh
```

---

## 📊 **ЧТО ПРОВЕРИТЬ**

### **✅ В WooCommerce:**
- [x] Заказ создан с правильными данными
- [x] Статус заказа: "В ожидании оплаты"
- [x] Email: <EMAIL>

### **✅ В логах WordPress:**
- [x] Записи `[TableCRM Integration]`
- [x] Сообщение "НАЧАЛО ОТПРАВКИ ЗАКАЗА"
- [x] Сообщение "успешно отправлен в TableCRM"
- [x] Lead ID получен

### **✅ В TableCRM:**
- [x] Новый лид с email: <EMAIL>
- [x] Источник: "WooCommerce Order"
- [x] Все данные заказа переданы

### **✅ В настройках плагина:**
- [x] API URL: `https://app.tablecrm.com/api/v1/`
- [x] API Key: `af1874616430e04cfd4bce30035789907e899fc7c3a1a4bb27254828ff304a77`
- [x] Отправка заказов: Включена
- [x] Режим отладки: Включен
- [x] Статус "pending": Выбран

---

## 🚨 **ЕСЛИ ЧТО-ТО НЕ РАБОТАЕТ**

### **❌ Нет логов:**
1. Включите логирование: `ВКЛЮЧЕНИЕ_ЛОГИРОВАНИЯ_WORDPRESS.md`
2. Активируйте плагин в админ-панели
3. Создайте новый заказ

### **❌ Логи есть, но ошибки API:**
1. Проверьте настройки плагина
2. Нажмите "Тестировать соединение"
3. Убедитесь в правильности API ключа

### **❌ Заказ не отправляется:**
1. Проверьте выбранные статусы заказов
2. Убедитесь что плагин активен
3. Проверьте настройки API

---

## 📋 **ПОЛНЫЕ ИНСТРУКЦИИ**

- **Создание заказа:** `СОЗДАНИЕ_ТЕСТОВОГО_ЗАКАЗА.md`
- **Включение логов:** `ВКЛЮЧЕНИЕ_ЛОГИРОВАНИЯ_WORDPRESS.md`
- **Диагностика:** `ДИАГНОСТИКА_ПРОБЛЕМ.md`
- **Настройка API:** `ИНСТРУКЦИЯ_API.md`

---

## 🎉 **ОЖИДАЕМЫЙ РЕЗУЛЬТАТ**

После успешного тестирования:

1. **✅ Заказ создан** в WooCommerce
2. **✅ Логи показывают** успешную отправку
3. **✅ Лид появился** в TableCRM
4. **✅ Интеграция работает** корректно

**🎯 Интеграция готова к работе!** 