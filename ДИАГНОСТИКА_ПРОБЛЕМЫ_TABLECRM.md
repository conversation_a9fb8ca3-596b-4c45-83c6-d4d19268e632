# ДИАГНОСТИКА ПРОБЛЕМЫ ОТПРАВКИ ЗАКАЗОВ WooCommerce В TableCRM

## 🔍 ОБНАРУЖЕННЫЕ ПРОБЛЕМЫ

### 1. ОСНОВНАЯ ПРОБЛЕМА: API ЭНДПОИНТЫ ВОЗВРАЩАЮТ 404

Из анализа логов `server_logs_latest/debug_latest.log` видно, что **ВСЕ** эндпоинты TableCRM API возвращают **404 Not Found**:

```
[TableCRM Integration] [DEBUG] Эндпоинт: leads/check-duplicate, Вариант 1 - Status: 404
[TableCRM Integration] [DEBUG] Response: {"detail":"Not Found"}
[TableCRM Integration] [DEBUG] Эндпоинт: leads/add, Вариант 1 - Status: 404
[TableCRM Integration] [DEBUG] Response: {"detail":"Not Found"}
[TableCRM Integration] [DEBUG] Эндпоинт: crm/objects/contact, Вариант 1 - Status: 404
[TableCRM Integration] [DEBUG] Response: {"detail":"Not Found"}
```

### 2. ПРОБЛЕМЫ С ДАННЫМИ ЗАКАЗА

Из логов видно, что заказ содержит **ПУСТЫЕ ДАННЫЕ**:
- Имя: (пустое)
- Email: (пустое) 
- Телефон: (пустое)
- Сумма заказа: 0
- Количество товаров: 0

### 3. НЕКОРРЕКТНАЯ БАЗОВАЯ URL API

Плагин использует URL: `https://app.tablecrm.com/api/v1/`
Но этот URL может быть устаревшим или неправильным.

## 🔧 ПЛАН ИСПРАВЛЕНИЯ

### Шаг 1: Проверить правильность API URL TableCRM

1. **Найти актуальную документацию API TableCRM**
2. **Проверить правильность эндпоинтов**
3. **Обновить базовый URL в настройках плагина**

### Шаг 2: Исправить API эндпоинты

Судя по найденной информации, TableCRM может использовать другие эндпоинты:
- Вместо `leads/add` → возможно `records` или `objects`
- Вместо `contacts` → возможно другой формат

### Шаг 3: Проверить настройки плагина

В файле `tablecrm-integration-complete-v1.0.24.php` проверить:
1. **API URL** (строка 38): может быть неверным
2. **API Key** - убедиться что ключ действителен
3. **Organization ID, Warehouse ID** и другие параметры

### Шаг 4: Проверить данные заказа

Заказ передается с пустыми данными, нужно:
1. Проверить корректность извлечения данных из WooCommerce
2. Убедиться что заказ содержит необходимые поля

## 🛠️ НЕМЕДЛЕННЫЕ ДЕЙСТВИЯ

### 1. Проверить актуальную документацию TableCRM API

**КРИТИЧНО**: Нужно найти актуальную документацию API TableCRM 2024-2025.
Возможные варианты:
- Обратиться в поддержку TableCRM
- Проверить административную панель TableCRM на наличие документации API
- Уточнить правильные эндпоинты

### 2. Временное решение: Отключить проверку дубликатов

В настройках плагина можно временно отключить проверку дубликатов, чтобы упростить процесс отправки.

### 3. Обновить конфигурацию API

```php
// Возможно нужно изменить базовый URL на:
// https://api.tablecrm.com/v1/ 
// или
// https://tablecrm.com/api/
// или другой вариант согласно документации
```

## 📋 ПРОВЕРОЧНЫЙ СПИСОК

- [ ] Связаться с поддержкой TableCRM для получения актуальной документации API
- [ ] Проверить действительность API ключа
- [ ] Проверить правильность базовой URL API  
- [ ] Протестировать подключение к API вручную (например, через Postman)
- [ ] Убедиться что WooCommerce заказы содержат необходимые данные
- [ ] Обновить эндпоинты в плагине согласно актуальной документации

## ⚠️ ВАЖНО

**Проблема критическая**: TableCRM API либо изменил свою структуру эндпоинтов, либо используется неправильная URL. Без исправления API эндпоинтов интеграция работать не будет.

## 🔗 СЛЕДУЮЩИЕ ШАГИ

1. **Срочно связаться с TableCRM** для получения актуальной API документации
2. **Обновить плагин** с правильными эндпоинтами  
3. **Протестировать** отправку тестового заказа
4. **Мониторить логи** для подтверждения исправления 