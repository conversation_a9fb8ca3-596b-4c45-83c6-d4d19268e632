<?php
// Тест исправленного API доставки
echo "=== ТЕСТ ИСПРАВЛЕННОГО API ДОСТАВКИ ===\n";

$wp_path = '/home/<USER>/bekflom3/mosbuketik.ru/public_html';
require_once($wp_path . '/wp-config.php');
require_once($wp_path . '/wp-load.php');

echo "WordPress загружен успешно\n\n";

// Тестируем заказ из скриншота
$order_id = 21780;
$order = wc_get_order($order_id);

if (!$order) {
    echo "❌ Заказ #$order_id не найден\n";
    exit;
}

echo "✅ Заказ #$order_id найден\n";
echo "Статус: " . $order->get_status() . "\n";
echo "Стоимость доставки: " . $order->get_shipping_total() . "\n\n";

// Проверяем, есть ли активный плагин TableCRM
if (class_exists('TableCRM_Integration_Complete')) {
    echo "✅ Плагин TableCRM Integration Complete активен\n";
    
    // Получаем экземпляр плагина
    global $tablecrm_integration_complete;
    
    if ($tablecrm_integration_complete) {
        echo "✅ Экземпляр плагина найден\n";
        
        // Тестируем отправку заказа
        echo "\n=== ТЕСТ ОТПРАВКИ ЗАКАЗА ===\n";
        
        try {
            // Вызываем метод отправки заказа
            $result = $tablecrm_integration_complete->send_order_to_tablecrm($order_id);
            
            if ($result) {
                echo "✅ Заказ успешно отправлен с исправленным API доставки!\n";
                
                // Проверяем document_id
                $document_id = get_post_meta($order_id, '_tablecrm_complete_document_id', true);
                if ($document_id) {
                    echo "Document ID: $document_id\n";
                }
            } else {
                echo "❌ Ошибка отправки заказа\n";
            }
            
        } catch (Exception $e) {
            echo "❌ Исключение при отправке: " . $e->getMessage() . "\n";
        }
        
    } else {
        echo "❌ Экземпляр плагина не найден\n";
    }
    
} else {
    echo "❌ Плагин TableCRM Integration Complete не активен\n";
}

echo "\n=== ПРОВЕРКА ЛОГОВ ===\n";
$log_file = WP_CONTENT_DIR . '/uploads/tablecrm_integration_complete.log';
if (file_exists($log_file)) {
    echo "Последние записи в логе:\n";
    $log_content = file_get_contents($log_file);
    $log_lines = explode("\n", $log_content);
    $recent_lines = array_slice($log_lines, -10);
    foreach ($recent_lines as $line) {
        if (!empty(trim($line))) {
            echo $line . "\n";
        }
    }
} else {
    echo "Лог файл не найден\n";
}

echo "\n=== ТЕСТ ЗАВЕРШЕН ===\n";
?> 