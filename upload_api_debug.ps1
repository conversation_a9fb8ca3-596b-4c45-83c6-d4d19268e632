# Upload and run API debug
$server = "bekflom3.beget.tech"
$user = "bekflom3_wz"
$pass = "zl2Shj!e&6ng"

Write-Host "Uploading API debug script..." -ForegroundColor Green

try {
    $uri = "ftp://$server/public_html/debug_api_request.php"
    $webclient = New-Object System.Net.WebClient
    $webclient.Credentials = New-Object System.Net.NetworkCredential($user, $pass)
    $webclient.UploadFile($uri, "debug_api_request.php")
    Write-Host "API debug script uploaded!" -ForegroundColor Green
    
    # Run the test
    Write-Host "Running API debug..." -ForegroundColor Yellow
    $result = Invoke-WebRequest -Uri "https://mosbuketik.ru/debug_api_request.php" -UseBasicParsing
    Write-Host $result.Content -ForegroundColor White
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

$webclient.Dispose()
Write-Host "API debug completed!" -ForegroundColor Green 