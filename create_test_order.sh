#!/bin/bash

echo "🛒 СОЗДАНИЕ ТЕСТОВОГО ЗАКАЗА WOOCOMMERCE"
echo "========================================"

# Данные для подключения к WooCommerce API
SITE_URL="https://mosbuketik.ru"
# Эти ключи нужно будет получить из WooCommerce -> Настройки -> Дополнительно -> REST API
# Для тестирования создам заказ через прямой HTTP запрос

echo "📋 Подготовка тестовых данных заказа..."

# Генерируем случайные данные для тестового заказа
RANDOM_ID=$((RANDOM % 9999 + 1000))
TEST_EMAIL="test${RANDOM_ID}@example.com"
TEST_PHONE="+7 (999) 123-${RANDOM_ID:0:2}-${RANDOM_ID:2:2}"
TEST_NAME="Тестовый Покупатель ${RANDOM_ID}"

echo "✅ Тестовые данные:"
echo "   Имя: $TEST_NAME"
echo "   Email: $TEST_EMAIL"
echo "   Телефон: $TEST_PHONE"

echo ""
echo "🔧 Создание заказа через WordPress..."

# Создаем PHP скрипт для создания заказа
cat > /tmp/create_test_order.php << EOF
<?php
// Подключаемся к WordPress
require_once('/home/<USER>/bekflom3/mosbuketik.ru/public_html/wp-config.php');
require_once('/home/<USER>/bekflom3/mosbuketik.ru/public_html/wp-load.php');

// Проверяем, что WooCommerce активен
if (!class_exists('WooCommerce')) {
    echo "❌ WooCommerce не найден\n";
    exit(1);
}

echo "✅ WooCommerce найден\n";

// Создаем новый заказ
\$order = wc_create_order();

if (!\$order) {
    echo "❌ Не удалось создать заказ\n";
    exit(1);
}

echo "✅ Заказ создан с ID: " . \$order->get_id() . "\n";

// Устанавливаем данные покупателя
\$order->set_billing_first_name('Тестовый');
\$order->set_billing_last_name('Покупатель ${RANDOM_ID}');
\$order->set_billing_email('${TEST_EMAIL}');
\$order->set_billing_phone('${TEST_PHONE}');
\$order->set_billing_address_1('ул. Тестовая, д. ${RANDOM_ID}');
\$order->set_billing_city('Москва');
\$order->set_billing_postcode('123456');
\$order->set_billing_country('RU');

// Устанавливаем адрес доставки (копируем с биллинга)
\$order->set_shipping_first_name('Тестовый');
\$order->set_shipping_last_name('Покупатель ${RANDOM_ID}');
\$order->set_shipping_address_1('ул. Тестовая, д. ${RANDOM_ID}');
\$order->set_shipping_city('Москва');
\$order->set_shipping_postcode('123456');
\$order->set_shipping_country('RU');

echo "✅ Данные покупателя установлены\n";

// Получаем первый доступный товар
\$products = wc_get_products(array(
    'limit' => 1,
    'status' => 'publish'
));

if (empty(\$products)) {
    echo "❌ Товары не найдены\n";
    \$order->delete(true);
    exit(1);
}

\$product = \$products[0];
echo "✅ Найден товар: " . \$product->get_name() . " (ID: " . \$product->get_id() . ")\n";

// Добавляем товар в заказ
\$item_id = \$order->add_product(\$product, 2); // Количество: 2

if (!\$item_id) {
    echo "❌ Не удалось добавить товар в заказ\n";
    \$order->delete(true);
    exit(1);
}

echo "✅ Товар добавлен в заказ (количество: 2)\n";

// Устанавливаем способ оплаты
\$order->set_payment_method('cod'); // Наложенный платеж
\$order->set_payment_method_title('Наложенный платеж');

// Добавляем UTM метки для тестирования
\$order->update_meta_data('_utm_source', 'test_script');
\$order->update_meta_data('_utm_medium', 'api');
\$order->update_meta_data('_utm_campaign', 'tablecrm_test');
\$order->update_meta_data('_referrer', 'https://google.com');
\$order->update_meta_data('_landing_page', 'https://mosbuketik.ru/test');

echo "✅ UTM метки добавлены\n";

// Пересчитываем итоги заказа
\$order->calculate_totals();

// Сохраняем заказ
\$order->save();

echo "✅ Заказ сохранен\n";
echo "📊 Сумма заказа: " . \$order->get_total() . " руб.\n";

// Устанавливаем статус заказа (это должно триггернуть отправку в TableCRM)
\$order->update_status('pending', 'Тестовый заказ создан автоматически');

echo "✅ Статус заказа установлен: pending\n";
echo "🎯 Заказ готов! ID: " . \$order->get_id() . "\n";
echo "📧 Email: ${TEST_EMAIL}\n";
echo "📞 Телефон: ${TEST_PHONE}\n";

// Выводим информацию для проверки
echo "\n🔍 ИНФОРМАЦИЯ ДЛЯ ПРОВЕРКИ:\n";
echo "1. Проверьте админ-панель WooCommerce: https://mosbuketik.ru/wp-admin/edit.php?post_type=shop_order\n";
echo "2. Проверьте логи: wp-content/debug.log\n";
echo "3. Проверьте TableCRM на наличие нового лида\n";

exit(0);
EOF

echo "📤 Загружаем скрипт на сервер и выполняем..."

# Загружаем скрипт на сервер и выполняем
lftp -u 'bekflom3_wz,zl2Shj!e&6ng' -e "
set ssl:verify-certificate no;
set sftp:auto-confirm yes;
cd public_html;
put /tmp/create_test_order.php;
quit
" bekflom3.beget.tech

if [ $? -eq 0 ]; then
    echo "✅ Скрипт create_test_order.php загружен на сервер"
    
    # Создаем простой веб-скрипт для выполнения
    cat > /tmp/run_test.php << 'EOF'
<?php
// Простой веб-интерфейс для выполнения создания заказа
if (isset($_GET['action']) && $_GET['action'] === 'create_test_order') {
    echo "<h1>Результат создания тестового заказа:</h1>";
    echo "<pre>";
    // Выполняем наш скрипт
    ob_start();
    // Устанавливаем рабочую директорию, если необходимо, хотя wp-load.php должен справиться
    // chdir(__DIR__); 
    include 'create_test_order.php';
    $output = ob_get_clean();
    
    echo htmlspecialchars($output); // Экранируем вывод для безопасного отображения в HTML
    echo "</pre>";
    
    // Удаляем временные файлы после выполнения
    @unlink('create_test_order.php');
    @unlink('run_test.php');
    echo "<p>Временные скрипты create_test_order.php и run_test.php удалены с сервера.</p>";
} else {
    echo "<h2>Создание тестового заказа WooCommerce</h2>";
    echo "<p><a href='?action=create_test_order'>Нажмите для создания тестового заказа</a></p>";
    echo "<p>После нажатия, скрипт create_test_order.php будет выполнен, а затем оба скрипта (run_test.php и create_test_order.php) будут удалены с сервера.</p>";
}
?>
EOF

    echo "📤 Загружаем веб-скрипт run_test.php на сервер..."
    # Загружаем веб-скрипт
    lftp -u 'bekflom3_wz,zl2Shj!e&6ng' -e "
    set ssl:verify-certificate no;
    set sftp:auto-confirm yes;
    cd public_html;
    put /tmp/run_test.php;
    quit
    " bekflom3.beget.tech
    
    if [ $? -eq 0 ]; then
        echo "✅ Веб-скрипт run_test.php загружен"
        echo ""
        WEB_RUN_URL="https://mosbuketik.ru/run_test.php?action=create_test_order"
        echo "🚀 Автоматически запускаем создание заказа через: $WEB_RUN_URL"
        
        # Используем curl для запуска и получения вывода
        # Добавляем -L для следования редиректам, -s для тихого режима curl, -k для игнорирования ошибок SSL (если они есть на тестовом URL)
        RAW_RESPONSE=$(curl -skL "$WEB_RUN_URL")
        CURL_EXIT_CODE=$?

        if [ $CURL_EXIT_CODE -eq 0 ]; then
            echo "✅ Запрос на создание заказа отправлен. Ответ сервера (HTML):"
            # Показываем только часть ответа, чтобы не засорять вывод, или можно распарсить <pre>
            # echo "$RAW_RESPONSE" # Полный HTML ответ
            
            # Извлекаем содержимое тега <pre>
            PRE_CONTENT=$(echo "$RAW_RESPONSE" | awk -v RS='</pre>' '/<pre>/{gsub(/.*<pre>/,"");print;exit}')
            
            if [ -n "$PRE_CONTENT" ]; then
                echo "📄 Вывод скрипта create_test_order.php:"
                echo "$PRE_CONTENT"
            else
                echo "⚠️ Не удалось извлечь чистый вывод из <pre> тега. Полный ответ был слишком длинным или <pre> не найден."
                echo "Проверьте результат по ссылке: https://mosbuketik.ru/run_test.php (если скрипт еще там)" 
                echo "или вручную в админке и логах."
            fi
            
            # Проверяем ключевые фразы в выводе скрипта
            if echo "$PRE_CONTENT" | grep -q "🎯 Заказ готов! ID:"; then
                ORDER_ID=$(echo "$PRE_CONTENT" | grep "🎯 Заказ готов! ID:" | sed 's/.*ID: //')
                echo "🎉🎉🎉 Тестовый заказ (ID: $ORDER_ID) успешно создан! 🎉🎉🎉"
            elif echo "$PRE_CONTENT" | grep -q "❌"; then
                echo "❗ Внимание: В процессе создания заказа возникли ошибки (см. вывод выше)."
            else
                 echo "🤔 Не удалось однозначно определить успех. Проверьте логи и админку."
            fi

        else
            echo "❌ Не удалось выполнить запрос на $WEB_RUN_URL (curl код ошибки: $CURL_EXIT_CODE)"
            echo "Пожалуйста, попробуйте перейти по ссылке https://mosbuketik.ru/run_test.php и нажать 'Нажмите для создания тестового заказа' вручную."
        fi
        echo ""
        echo "🔍 ПРОВЕРКА РЕЗУЛЬТАТА (ДАЖЕ ЕСЛИ АВТОМАТИЧЕСКИЙ ЗАПУСК НЕ УДАЛСЯ):"
        echo "1. Админ-панель: https://mosbuketik.ru/wp-admin/edit.php?post_type=shop_order"
        echo "2. Логи: ./logs_manual_check.sh"
        echo "3. TableCRM: https://app.tablecrm.com"
        
    else
        echo "❌ Ошибка загрузки скрипта run_test.php на сервер"
        echo ""
        echo "📋 РУЧНОЕ СОЗДАНИЕ ЗАКАЗА:"
        echo "1. Перейдите на сайт: https://mosbuketik.ru"
        echo "2. Добавьте товар в корзину"
        echo "3. Оформите заказ с тестовыми данными:"
        echo "   - Имя: $TEST_NAME"
        echo "   - Email: $TEST_EMAIL"
        echo "   - Телефон: $TEST_PHONE"
        echo "   - Адрес: ул. Тестовая, д. $RANDOM_ID, Москва"
    fi

else
    echo "❌ Ошибка загрузки скрипта на сервер"
    echo ""
    echo "📋 РУЧНОЕ СОЗДАНИЕ ЗАКАЗА:"
    echo "1. Перейдите на сайт: https://mosbuketik.ru"
    echo "2. Добавьте товар в корзину"
    echo "3. Оформите заказ с тестовыми данными:"
    echo "   - Имя: $TEST_NAME"
    echo "   - Email: $TEST_EMAIL"
    echo "   - Телефон: $TEST_PHONE"
    echo "   - Адрес: ул. Тестовая, д. $RANDOM_ID, Москва"
fi

# Очищаем временные файлы
rm -f /tmp/create_test_order.php /tmp/run_test.php

echo ""
echo "✅ ТЕСТОВЫЙ ЗАКАЗ ГОТОВ К СОЗДАНИЮ!"
echo ""
echo "📊 После создания заказа проверьте:"
echo "1. 📧 Логи WordPress: wp-content/debug.log"
echo "2. 🛒 Админ-панель WooCommerce"
echo "3. 📈 TableCRM на наличие нового лида"
echo "4. ⚙️ Настройки плагина TableCRM Integration" 