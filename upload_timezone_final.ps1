# Upload and run final timezone fix
$server = "bekflom3.beget.tech"
$user = "bekflom3_wz"
$pass = "zl2Shj!e&6ng"

Write-Host "Uploading final timezone fix..." -ForegroundColor Green

try {
    $uri = "ftp://$server/public_html/fix_timezone_final.php"
    $webclient = New-Object System.Net.WebClient
    $webclient.Credentials = New-Object System.Net.NetworkCredential($user, $pass)
    $webclient.UploadFile($uri, "fix_timezone_final.php")
    Write-Host "Final timezone fix uploaded!" -ForegroundColor Green
    
    # Run the fix
    Write-Host "Running final timezone fix..." -ForegroundColor Yellow
    $result = Invoke-WebRequest -Uri "https://mosbuketik.ru/fix_timezone_final.php" -UseBasicParsing
    Write-Host $result.Content -ForegroundColor White
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

$webclient.Dispose()
Write-Host "Final timezone fix completed!" -ForegroundColor Green 