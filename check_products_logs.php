<?php
/**
 * Скрипт для проверки логов названий товаров в TableCRM интеграции
 * Запускать через браузер: https://mosbuketik.ru/check_products_logs.php
 */

echo "<h1>🛍️ Проверка логов названий товаров TableCRM</h1>";
echo "<style>
body{font-family:Arial;margin:20px;} 
.success{color:green;background:#e8f5e8;padding:5px;border-radius:3px;} 
.error{color:red;background:#ffe8e8;padding:5px;border-radius:3px;} 
.warning{color:orange;background:#fff3cd;padding:5px;border-radius:3px;} 
.info{color:blue;background:#e8f4fd;padding:5px;border-radius:3px;} 
pre{background:#f5f5f5;padding:10px;border-radius:5px;font-size:12px;overflow-x:auto;}
.highlight{background:yellow;font-weight:bold;}
</style>";

// Функция для безопасного отображения данных
function safe_output($data) {
    return htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
}

// Функция подсветки ключевых слов
function highlight_keywords($text, $keywords) {
    foreach ($keywords as $keyword) {
        $text = str_ireplace($keyword, "<span class='highlight'>$keyword</span>", $text);
    }
    return $text;
}

echo "<h2>📦 Информация о последних заказах с товарами</h2>";

try {
    require_once('wp-config.php');
    require_once('wp-load.php');
    
    echo "<div class='success'>✅ WordPress загружен успешно</div>";
    
    // Получаем последние 3 заказа
    $args = array(
        'limit' => 3,
        'orderby' => 'date',
        'order' => 'DESC',
        'status' => array('pending', 'processing', 'completed', 'on-hold')
    );
    
    $orders = wc_get_orders($args);
    
    if (!empty($orders)) {
        echo "<div class='success'>📦 Найдено " . count($orders) . " последних заказов</div>";
        
        foreach ($orders as $order) {
            echo "<h3>Заказ #{$order->get_id()}</h3>";
            echo "<pre>";
            echo "ID заказа: " . $order->get_id() . "\n";
            echo "Дата: " . $order->get_date_created()->format('Y-m-d H:i:s') . "\n";
            echo "Статус: " . $order->get_status() . "\n";
            echo "Покупатель: " . safe_output($order->get_billing_first_name() . ' ' . $order->get_billing_last_name()) . "\n";
            echo "Сумма: " . $order->get_total() . " руб.\n";
            
            // Показываем товары
            echo "\nТОВАРЫ В ЗАКАЗЕ:\n";
            foreach ($order->get_items() as $item_id => $item) {
                $product = $item->get_product();
                echo "  - ID товара: $item_id\n";
                echo "    Название: '" . safe_output($item->get_name()) . "'\n";
                if ($product) {
                    echo "    Название продукта: '" . safe_output($product->get_title()) . "'\n";
                    echo "    SKU: '" . safe_output($product->get_sku() ?: 'НЕТ SKU') . "'\n";
                    echo "    Product ID: " . $product->get_id() . "\n";
                }
                echo "    Количество: " . $item->get_quantity() . "\n";
                echo "    Цена: " . $item->get_total() . " руб.\n\n";
            }
            
            // Проверяем мета-поля TableCRM
            $tablecrm_v3_id = $order->get_meta('_tablecrm_v3_document_id');
            $tablecrm_v3_sent = $order->get_meta('_tablecrm_v3_sent_at');
            $tablecrm_action_id = $order->get_meta('_tablecrm_v3_action_id');
            
            echo "TableCRM v3 данные:\n";
            echo "- Document ID: " . ($tablecrm_v3_id ? safe_output($tablecrm_v3_id) : 'НЕ УСТАНОВЛЕН') . "\n";
            echo "- Время отправки: " . ($tablecrm_v3_sent ? safe_output($tablecrm_v3_sent) : 'НЕ ОТПРАВЛЕН') . "\n";
            echo "- Action ID: " . ($tablecrm_action_id ? safe_output($tablecrm_action_id) : 'НЕТ') . "\n";
            echo "</pre>";
        }
        
    } else {
        echo "<div class='error'>❌ Заказы не найдены</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Ошибка при загрузке WordPress: " . safe_output($e->getMessage()) . "</div>";
}

echo "<h2>📄 Анализ логов товаров в debug.log</h2>";

$debug_log_path = 'wp-content/debug.log';
if (file_exists($debug_log_path)) {
    echo "<div class='success'>✅ debug.log найден (размер: " . round(filesize($debug_log_path)/1024, 2) . " KB)</div>";
    
    $log_content = file_get_contents($debug_log_path);
    $log_lines = explode("\n", $log_content);
    
    // Ключевые слова для поиска
    $product_keywords = array(
        'ТОВАР',
        'название товара',
        'get_name',
        'get_title', 
        'product_name',
        'АДАПТАЦИЯ ТОВАРОВ',
        'JSON ДЛЯ TableCRM',
        'Testing',
        'goods',
        'name',
        'nomenclature'
    );
    
    // Ищем записи о товарах
    $product_logs = array();
    foreach ($log_lines as $line_num => $line) {
        foreach ($product_keywords as $keyword) {
            if (stripos($line, $keyword) !== false && stripos($line, 'TableCRM') !== false) {
                $product_logs[] = array(
                    'line' => $line_num + 1,
                    'content' => $line,
                    'keyword' => $keyword
                );
                break; // Найден хотя бы один ключевой word
            }
        }
    }
    
    if (!empty($product_logs)) {
        echo "<div class='info'>📝 Найдено " . count($product_logs) . " записей о товарах в логах</div>";
        echo "<h3>🔍 Логи товаров (последние 20 записей):</h3>";
        echo "<pre>";
        $recent_product_logs = array_slice($product_logs, -20);
        foreach ($recent_product_logs as $log_entry) {
            $highlighted_content = highlight_keywords($log_entry['content'], $product_keywords);
            echo "Строка {$log_entry['line']}: " . $highlighted_content . "\n";
        }
        echo "</pre>";
    } else {
        echo "<div class='warning'>⚠️ Записи о товарах в логах не найдены</div>";
    }
    
    // Ищем JSON с данными для TableCRM
    echo "<h3>🔍 JSON данные для TableCRM:</h3>";
    $json_logs = array();
    foreach ($log_lines as $line) {
        if (stripos($line, 'JSON payload') !== false || 
            stripos($line, 'ИТОГОВЫЙ JSON') !== false ||
            (stripos($line, 'goods') !== false && stripos($line, '[{') !== false)) {
            $json_logs[] = $line;
        }
    }
    
    if (!empty($json_logs)) {
        echo "<div class='success'>✅ Найдено " . count($json_logs) . " JSON записей</div>";
        echo "<pre>";
        $recent_json = array_slice($json_logs, -5);
        foreach ($recent_json as $json_line) {
            echo safe_output($json_line) . "\n";
        }
        echo "</pre>";
    } else {
        echo "<div class='warning'>⚠️ JSON записи не найдены</div>";
    }
    
    // Последние записи TableCRM v3
    echo "<h3>📋 Последние записи TableCRM v3:</h3>";
    $tablecrm_v3_logs = array();
    foreach ($log_lines as $line) {
        if (stripos($line, 'TableCRM v3') !== false) {
            $tablecrm_v3_logs[] = $line;
        }
    }
    
    if (!empty($tablecrm_v3_logs)) {
        echo "<div class='success'>✅ Найдено " . count($tablecrm_v3_logs) . " записей TableCRM v3</div>";
        echo "<pre>";
        $recent_v3_logs = array_slice($tablecrm_v3_logs, -15);
        foreach ($recent_v3_logs as $v3_line) {
            echo safe_output($v3_line) . "\n";
        }
        echo "</pre>";
    } else {
        echo "<div class='error'>❌ Записи TableCRM v3 не найдены</div>";
    }
    
} else {
    echo "<div class='error'>❌ debug.log не найден в wp-content/</div>";
    echo "<div class='info'>💡 Возможно, логирование отключено в wp-config.php</div>";
}

echo "<h2>⚙️ Проверка настроек TableCRM v3</h2>";

try {
    // Проверяем настройки плагина v3
    $api_url = get_option('tablecrm_fixed_v3_api_url', '');
    $api_key = get_option('tablecrm_fixed_v3_api_key', '');
    $debug_mode = get_option('tablecrm_fixed_v3_debug_mode', '');
    $enable_orders = get_option('tablecrm_fixed_v3_enable_orders', '');
    
    echo "<pre>";
    echo "API URL: " . ($api_url ? safe_output($api_url) : 'НЕ УСТАНОВЛЕН') . "\n";
    echo "API Key: " . ($api_key ? '***установлен*** (длина: ' . strlen($api_key) . ')' : 'НЕ УСТАНОВЛЕН') . "\n";
    echo "Режим отладки: " . ($debug_mode ? 'ВКЛЮЧЕН' : 'ОТКЛЮЧЕН') . "\n";
    echo "Отправка заказов: " . ($enable_orders ? 'ВКЛЮЧЕНА' : 'ОТКЛЮЧЕНА') . "\n";
    echo "</pre>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Ошибка при получении настроек v3: " . safe_output($e->getMessage()) . "</div>";
}

echo "<h2>🔧 Проверка статуса логирования</h2>";

if (file_exists('wp-config.php')) {
    $wp_config = file_get_contents('wp-config.php');
    
    echo "<pre>";
    if (strpos($wp_config, "define('WP_DEBUG', true)") !== false) {
        echo "✅ WP_DEBUG включен\n";
    } else {
        echo "❌ WP_DEBUG отключен\n";
    }
    
    if (strpos($wp_config, "define('WP_DEBUG_LOG', true)") !== false) {
        echo "✅ WP_DEBUG_LOG включен\n";
    } else {
        echo "❌ WP_DEBUG_LOG отключен\n";
    }
    
    if (strpos($wp_config, "define('WP_DEBUG_DISPLAY', false)") !== false) {
        echo "✅ WP_DEBUG_DISPLAY отключен (правильно)\n";
    } else {
        echo "⚠️ WP_DEBUG_DISPLAY не найден\n";
    }
    echo "</pre>";
} else {
    echo "<div class='error'>❌ wp-config.php не найден</div>";
}

echo "<hr>";
echo "<div style='margin-top:20px; padding:15px; background:#e7f3ff; border-radius:5px;'>";
echo "<h3>🎯 Диагностика проблемы с названиями товаров:</h3>";
echo "<ol>";
echo "<li><strong>Создайте новый тестовый заказ</strong> с товарами, имеющими понятные названия</li>";
echo "<li><strong>Подождите 2-3 минуты</strong> для обработки через Action Scheduler</li>";
echo "<li><strong>Обновите эту страницу</strong> для проверки новых логов</li>";
echo "<li><strong>Проверьте логи</strong> на наличие записей с исходными названиями товаров</li>";
echo "<li><strong>Сравните</strong> с тем, что отображается в TableCRM</li>";
echo "</ol>";

echo "<p><strong>🔍 Что искать в логах:</strong></p>";
echo "<ul>";
echo "<li>Записи 'ТОВАР #X:' с исходными названиями</li>";
echo "<li>Записи 'АДАПТАЦИЯ ТОВАРОВ ДЛЯ TableCRM'</li>";
echo "<li>JSON payload с полными данными</li>";
echo "<li>Любые упоминания 'Testing' или 'nomenclature'</li>";
echo "</ul>";
echo "</div>";

echo "<div style='margin-top:10px; padding:10px; background:#fff3cd; border-radius:5px;'>";
echo "<strong>⚠️ Если проблема в API TableCRM:</strong><br>";
echo "Возможно, поле 'nomenclature' в JSON переопределяет название товара.<br>";
echo "Мы уже убрали это поле из кода, но нужно проверить логи для подтверждения.";
echo "</div>";
?> 