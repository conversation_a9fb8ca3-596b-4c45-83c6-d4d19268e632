<?php
// Check specific order data
echo "=== ПРОВЕРКА ДАННЫХ ЗАКАЗА ===\n";

$wp_path = '/home/<USER>/bekflom3/mosbuketik.ru/public_html';
require_once($wp_path . '/wp-config.php');
require_once($wp_path . '/wp-load.php');

echo "WordPress загружен успешно\n\n";

// Проверяем последний заказ с проблемой
$order_id = 21780; // Заказ из логов с проблемой
$order = wc_get_order($order_id);

if (!$order) {
    echo "❌ Заказ #$order_id не найден\n";
    exit;
}

echo "✅ Заказ #$order_id найден\n";
echo "Статус: " . $order->get_status() . "\n";
echo "Дата: " . $order->get_date_created()->format('Y-m-d H:i:s') . "\n";
echo "Сумма: " . $order->get_total() . " руб.\n\n";

echo "=== BILLING ДАННЫЕ ===\n";
echo "billing_first_name: '" . $order->get_billing_first_name() . "'\n";
echo "billing_last_name: '" . $order->get_billing_last_name() . "'\n";
echo "billing_phone: '" . $order->get_billing_phone() . "'\n";
echo "billing_email: '" . $order->get_billing_email() . "'\n";

echo "\n=== SHIPPING ДАННЫЕ ===\n";
echo "shipping_first_name: '" . $order->get_shipping_first_name() . "'\n";
echo "shipping_last_name: '" . $order->get_shipping_last_name() . "'\n";
echo "shipping_phone: '" . $order->get_shipping_phone() . "'\n";

echo "\n=== ЛОГИКА ФОРМИРОВАНИЯ ПОЛУЧАТЕЛЯ ===\n";
$first_name = $order->get_shipping_first_name() ?: $order->get_billing_first_name();
$last_name = $order->get_shipping_last_name() ?: $order->get_billing_last_name();
$phone = $order->get_shipping_phone() ?: $order->get_billing_phone();

echo "Итоговое имя: '$first_name'\n";
echo "Итоговая фамилия: '$last_name'\n";
echo "Итоговый телефон: '$phone'\n";

echo "\n=== ПРОВЕРКА МЕТА-ПОЛЕЙ ===\n";
$meta_data = $order->get_meta_data();
foreach ($meta_data as $meta) {
    $key = $meta->get_data()['key'];
    $value = $meta->get_data()['value'];
    
    if (strpos($key, 'billing') !== false || strpos($key, 'shipping') !== false) {
        echo "$key: '$value'\n";
    }
}

echo "\n=== ПРОВЕРКА ВСЕХ МЕТА-ПОЛЕЙ С ТЕЛЕФОНАМИ ===\n";
foreach ($meta_data as $meta) {
    $key = $meta->get_data()['key'];
    $value = $meta->get_data()['value'];
    
    if (is_string($value) && (strpos($value, '+7') !== false || strpos($value, '8(') !== false || preg_match('/\d{3}.*\d{3}.*\d{2}.*\d{2}/', $value))) {
        echo "$key: '$value'\n";
    }
}

echo "\n=== ПРОВЕРКА ЗАВЕРШЕНА ===\n";
?> 