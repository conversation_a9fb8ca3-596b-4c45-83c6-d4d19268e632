<?php
// Debug exact API request data
echo "=== ОТЛАДКА ДАННЫХ API ЗАПРОСА ===\n";

$wp_path = '/home/<USER>/bekflom3/mosbuketik.ru/public_html';
require_once($wp_path . '/wp-config.php');
require_once($wp_path . '/wp-load.php');

echo "WordPress загружен успешно\n\n";

$order_id = 21780;
$order = wc_get_order($order_id);

if (!$order) {
    echo "❌ Заказ #$order_id не найден\n";
    exit;
}

// Get the plugin instance
global $tablecrm_integration_complete;
if (!isset($tablecrm_integration_complete)) {
    echo "❌ Плагин не найден\n";
    exit;
}

// Get settings
$api_url = get_option('tablecrm_complete_api_url', 'https://app.tablecrm.com/api/v1/');
$api_token = get_option('tablecrm_complete_api_key');
$warehouse_id = get_option('tablecrm_complete_warehouse_id');
$organization_id = get_option('tablecrm_complete_organization_id');
$paybox_id = get_option('tablecrm_complete_cashbox_id');
$api_url = trailingslashit($api_url);

echo "=== НАСТРОЙКИ ===\n";
echo "API URL: $api_url\n";
echo "Warehouse ID: $warehouse_id\n";
echo "Organization ID: $organization_id\n";
echo "Paybox ID: $paybox_id\n\n";

// Get contragent
$reflection = new ReflectionClass($tablecrm_integration_complete);
$contragent_method = $reflection->getMethod('get_or_create_contragent');
$contragent_method->setAccessible(true);
$contragent_id = $contragent_method->invoke($tablecrm_integration_complete, $order, $api_url, $api_token);

echo "Контрагент ID: $contragent_id\n\n";

// Get nomenclatures
$nomenclature_ids = array();
$items = $order->get_items();

$nomenclature_method = $reflection->getMethod('get_or_create_nomenclature');
$nomenclature_method->setAccessible(true);

foreach ($items as $item_id => $item) {
    $product_name = $item->get_name();
    $nomenclature_id = $nomenclature_method->invoke($tablecrm_integration_complete, $product_name, $api_url, $api_token);
    $nomenclature_ids[$item_id] = $nomenclature_id;
    echo "Товар '$product_name': nomenclature ID $nomenclature_id\n";
}

// Get adapted data
$adapt_method = $reflection->getMethod('adapt_data_format');
$adapt_method->setAccessible(true);

$adapted_data = $adapt_method->invoke(
    $tablecrm_integration_complete,
    $order,
    $warehouse_id,
    $organization_id,
    $paybox_id,
    $contragent_id,
    $nomenclature_ids
);

echo "\n=== ДАННЫЕ ДЛЯ ОТПРАВКИ ===\n";
echo "JSON данные:\n";
echo json_encode($adapted_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";

echo "\n=== ОТЛАДКА ЗАВЕРШЕНА ===\n";
?> 