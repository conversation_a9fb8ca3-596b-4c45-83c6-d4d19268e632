<?php
/**
 * Тестирование улучшенного механизма дедупликации TableCRM Integration v1.0.11
 * 
 * Этот скрипт проверяет:
 * 1. Атомарную защиту от concurrency
 * 2. Автоматическую очистку зависших маркеров
 * 3. Корректную работу timeout'ов
 */

// Подключение WordPress
require_once('../../../wp-config.php');

class DeduplicationTester {
    
    private $order_id;
    private $processing_key = '_tablecrm_v3_processing';
    private $timeout = 300; // 5 минут
    
    public function __construct($order_id) {
        $this->order_id = $order_id;
        echo "🧪 Начинаем тестирование механизма дедупликации для заказа #{$order_id}\n\n";
    }
    
    /**
     * Тест 1: Проверка атомарной установки маркера
     */
    public function test_atomic_marker() {
        echo "🔧 Тест 1: Атомарная установка маркера\n";
        echo "=====================================\n";
        
        // Очищаем предыдущие маркеры
        delete_post_meta($this->order_id, $this->processing_key);
        
        $current_time = time();
        
        // Первая попытка - должна быть успешной
        $result1 = add_post_meta($this->order_id, $this->processing_key, $current_time, true);
        echo "Первая попытка установки маркера: " . ($result1 ? "✅ УСПЕШНО" : "❌ НЕУДАЧА") . "\n";
        
        // Вторая попытка - должна неудачной (маркер уже есть)
        $result2 = add_post_meta($this->order_id, $this->processing_key, $current_time + 1, true);
        echo "Вторая попытка установки маркера: " . (!$result2 ? "✅ ПРАВИЛЬНО ЗАБЛОКИРОВАНА" : "❌ ОШИБКА - НЕ ЗАБЛОКИРОВАНА") . "\n";
        
        // Проверяем, что значение не изменилось
        $saved_value = get_post_meta($this->order_id, $this->processing_key, true);
        echo "Сохраненное значение маркера: $saved_value (ожидается: $current_time)\n";
        echo "Значение корректно: " . ($saved_value == $current_time ? "✅ ДА" : "❌ НЕТ") . "\n\n";
        
        return $result1 && !$result2 && ($saved_value == $current_time);
    }
    
    /**
     * Тест 2: Проверка обнаружения зависших маркеров
     */
    public function test_stale_marker_detection() {
        echo "🕐 Тест 2: Обнаружение зависших маркеров\n";
        echo "========================================\n";
        
        // Очищаем предыдущие маркеры
        delete_post_meta($this->order_id, $this->processing_key);
        
        // Устанавливаем старый маркер (10 минут назад)
        $stale_time = time() - 600; // 10 минут назад
        add_post_meta($this->order_id, $this->processing_key, $stale_time, true);
        echo "Установлен старый маркер: $stale_time (" . date('Y-m-d H:i:s', $stale_time) . ")\n";
        
        // Проверяем логику обнаружения зависших маркеров
        $current_time = time();
        $existing_time = get_post_meta($this->order_id, $this->processing_key, true);
        $time_diff = $current_time - intval($existing_time);
        
        echo "Текущее время: $current_time (" . date('Y-m-d H:i:s', $current_time) . ")\n";
        echo "Возраст маркера: {$time_diff} секунд\n";
        echo "Превышает timeout ({$this->timeout}с): " . ($time_diff > $this->timeout ? "✅ ДА" : "❌ НЕТ") . "\n";
        
        // Симулируем очистку зависшего маркера
        if ($time_diff > $this->timeout) {
            delete_post_meta($this->order_id, $this->processing_key);
            echo "Зависший маркер очищен: ✅ УСПЕШНО\n";
            
            // Проверяем, что маркер действительно удален
            $after_cleanup = get_post_meta($this->order_id, $this->processing_key, true);
            echo "Маркер после очистки: " . (empty($after_cleanup) ? "✅ УДАЛЕН" : "❌ ВСЕ ЕЩЕ ЕСТЬ") . "\n";
            
            return empty($after_cleanup);
        } else {
            echo "❌ Маркер не считается зависшим - ошибка в логике\n";
            return false;
        }
        
        echo "\n";
    }
    
    /**
     * Тест 3: Проверка активного маркера (не зависшего)
     */
    public function test_active_marker() {
        echo "⚡ Тест 3: Проверка активного маркера\n";
        echo "====================================\n";
        
        // Очищаем предыдущие маркеры
        delete_post_meta($this->order_id, $this->processing_key);
        
        // Устанавливаем свежий маркер (1 минуту назад)
        $recent_time = time() - 60; // 1 минута назад
        add_post_meta($this->order_id, $this->processing_key, $recent_time, true);
        echo "Установлен свежий маркер: $recent_time (" . date('Y-m-d H:i:s', $recent_time) . ")\n";
        
        // Проверяем логику для активного маркера
        $current_time = time();
        $existing_time = get_post_meta($this->order_id, $this->processing_key, true);
        $time_diff = $current_time - intval($existing_time);
        
        echo "Текущее время: $current_time (" . date('Y-m-d H:i:s', $current_time) . ")\n";
        echo "Возраст маркера: {$time_diff} секунд\n";
        echo "Превышает timeout ({$this->timeout}с): " . ($time_diff > $this->timeout ? "❌ ДА (ОШИБКА)" : "✅ НЕТ") . "\n";
        
        // Симулируем попытку установки нового маркера при активном
        $new_marker_result = add_post_meta($this->order_id, $this->processing_key, $current_time, true);
        echo "Попытка установки нового маркера при активном: " . (!$new_marker_result ? "✅ ПРАВИЛЬНО ЗАБЛОКИРОВАНА" : "❌ ОШИБКА") . "\n";
        
        // Проверяем, что значение не изменилось
        $final_value = get_post_meta($this->order_id, $this->processing_key, true);
        echo "Итоговое значение маркера: $final_value (ожидается: $recent_time)\n";
        echo "Маркер не изменился: " . ($final_value == $recent_time ? "✅ ДА" : "❌ НЕТ") . "\n\n";
        
        return ($time_diff <= $this->timeout) && (!$new_marker_result) && ($final_value == $recent_time);
    }
    
    /**
     * Тест 4: Симуляция конкурентного доступа
     */
    public function test_concurrent_access() {
        echo "⚔️ Тест 4: Симуляция конкурентного доступа\n";
        echo "===========================================\n";
        
        // Очищаем предыдущие маркеры
        delete_post_meta($this->order_id, $this->processing_key);
        
        $current_time = time();
        $results = array();
        
        // Симулируем 5 параллельных процессов
        for ($i = 1; $i <= 5; $i++) {
            $process_time = $current_time + $i;
            $result = add_post_meta($this->order_id, $this->processing_key, $process_time, true);
            $results[] = $result;
            echo "Процесс #$i (время: $process_time): " . ($result ? "✅ УСПЕХ" : "❌ ЗАБЛОКИРОВАН") . "\n";
        }
        
        // Подсчитываем успешные установки
        $successful_count = array_sum($results);
        echo "\nОбщее количество успешных установок: $successful_count\n";
        echo "Ожидается: 1 (только первый процесс должен быть успешным)\n";
        echo "Результат: " . ($successful_count === 1 ? "✅ КОРРЕКТНО" : "❌ ОШИБКА") . "\n";
        
        // Проверяем финальное значение
        $final_value = get_post_meta($this->order_id, $this->processing_key, true);
        echo "Финальное значение маркера: $final_value\n";
        echo "Ожидается: " . ($current_time + 1) . " (время первого процесса)\n";
        echo "Значение корректно: " . ($final_value == ($current_time + 1) ? "✅ ДА" : "❌ НЕТ") . "\n\n";
        
        return ($successful_count === 1) && ($final_value == ($current_time + 1));
    }
    
    /**
     * Тест 5: Проверка очистки маркеров
     */
    public function test_cleanup() {
        echo "🧹 Тест 5: Проверка очистки маркеров\n";
        echo "====================================\n";
        
        // Устанавливаем несколько типов маркеров
        $markers = array(
            '_tablecrm_v3_processing' => time(),
            '_tablecrm_v3_update_processing' => time(),
            '_tablecrm_v3_processing_123456' => time() - 1000, // старый маркер
        );
        
        foreach ($markers as $key => $value) {
            add_post_meta($this->order_id, $key, $value, true);
            echo "Установлен маркер $key: $value\n";
        }
        
        // Симулируем метод cleanup_processing_meta
        delete_post_meta($this->order_id, '_tablecrm_v3_processing');
        delete_post_meta($this->order_id, '_tablecrm_v3_update_processing');
        
        // Очистка старых маркеров через SQL (как в методе cleanup_processing_meta)
        global $wpdb;
        $wpdb->query(
            $wpdb->prepare(
                "DELETE FROM {$wpdb->postmeta} 
                 WHERE post_id = %d 
                 AND meta_key LIKE '_tablecrm_v3_processing_%'",
                $this->order_id
            )
        );
        
        echo "\nОчистка выполнена. Проверяем результаты:\n";
        
        // Проверяем, что все маркеры удалены
        $all_cleaned = true;
        foreach ($markers as $key => $value) {
            $remaining = get_post_meta($this->order_id, $key, true);
            $is_clean = empty($remaining);
            echo "Маркер $key: " . ($is_clean ? "✅ УДАЛЕН" : "❌ ОСТАЛСЯ ($remaining)") . "\n";
            if (!$is_clean) $all_cleaned = false;
        }
        
        echo "\nВсе маркеры очищены: " . ($all_cleaned ? "✅ ДА" : "❌ НЕТ") . "\n\n";
        
        return $all_cleaned;
    }
    
    /**
     * Запуск всех тестов
     */
    public function run_all_tests() {
        echo "🚀 ЗАПУСК ПОЛНОГО НАБОРА ТЕСТОВ ДЕДУПЛИКАЦИИ\n";
        echo "=============================================\n\n";
        
        $tests = array(
            'Атомарная установка маркера' => $this->test_atomic_marker(),
            'Обнаружение зависших маркеров' => $this->test_stale_marker_detection(),
            'Проверка активного маркера' => $this->test_active_marker(),
            'Конкурентный доступ' => $this->test_concurrent_access(),
            'Очистка маркеров' => $this->test_cleanup(),
        );
        
        echo "📊 ИТОГОВЫЕ РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ\n";
        echo "===================================\n";
        
        $passed = 0;
        $total = count($tests);
        
        foreach ($tests as $test_name => $result) {
            echo sprintf("%-35s %s\n", $test_name . ':', $result ? '✅ ПРОЙДЕН' : '❌ ПРОВАЛЕН');
            if ($result) $passed++;
        }
        
        echo "\n";
        echo "Пройдено тестов: $passed из $total\n";
        echo "Процент успеха: " . round(($passed / $total) * 100, 1) . "%\n";
        
        if ($passed === $total) {
            echo "\n🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ! Механизм дедупликации работает корректно.\n";
        } else {
            echo "\n⚠️  ЕСТЬ ПРОВАЛЕННЫЕ ТЕСТЫ! Требуется доработка механизма дедупликации.\n";
        }
        
        // Финальная очистка
        delete_post_meta($this->order_id, $this->processing_key);
        delete_post_meta($this->order_id, '_tablecrm_v3_update_processing');
        
        return $passed === $total;
    }
}

// Проверяем аргументы командной строки
if (isset($argv[1]) && is_numeric($argv[1])) {
    $order_id = intval($argv[1]);
} else {
    echo "❌ Ошибка: Укажите ID заказа для тестирования\n";
    echo "Использование: php test_enhanced_deduplication.php ORDER_ID\n";
    echo "Пример: php test_enhanced_deduplication.php 123\n";
    exit(1);
}

// Проверяем, что заказ существует
if (!wc_get_order($order_id)) {
    echo "❌ Ошибка: Заказ #$order_id не найден в WooCommerce\n";
    exit(1);
}

// Запускаем тесты
$tester = new DeduplicationTester($order_id);
$success = $tester->run_all_tests();

exit($success ? 0 : 1); 