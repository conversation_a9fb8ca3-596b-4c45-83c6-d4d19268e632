<?php
// Test phone filtering in delivery recipient data
echo "=== ТЕСТ ФИЛЬТРАЦИИ ТЕЛЕФОНОВ В ДАННЫХ ПОЛУЧАТЕЛЯ ===\n";

$wp_path = '/home/<USER>/bekflom3/mosbuketik.ru/public_html';
require_once($wp_path . '/wp-config.php');
require_once($wp_path . '/wp-load.php');

echo "WordPress загружен успешно\n\n";

// Тестируем заказ #21780 с проблемой телефона в фамилии
$order_id = 21780;
$order = wc_get_order($order_id);

if (!$order) {
    echo "❌ Заказ #$order_id не найден\n";
    exit;
}

echo "✅ Заказ #$order_id найден\n";
echo "Статус: " . $order->get_status() . "\n";

echo "\n=== ИСХОДНЫЕ ДАННЫЕ ===\n";
echo "billing_first_name: '" . $order->get_billing_first_name() . "'\n";
echo "billing_last_name: '" . $order->get_billing_last_name() . "'\n";
echo "billing_phone: '" . $order->get_billing_phone() . "'\n";

// Тестируем функцию фильтрации телефонов
$is_phone = function($str) {
    if (empty($str)) return false;
    // Проверяем на наличие телефонных паттернов
    return preg_match('/^[\+]?[0-9\(\)\-\s]{7,}$/', $str) || 
           strpos($str, '+7') === 0 || 
           strpos($str, '8(') === 0 ||
           preg_match('/\d{3}.*\d{3}.*\d{2}.*\d{2}/', $str);
};

echo "\n=== ТЕСТ ФУНКЦИИ ФИЛЬТРАЦИИ ===\n";
$first_name = $order->get_billing_first_name();
$last_name = $order->get_billing_last_name();

echo "Имя '$first_name' - телефон? " . ($is_phone($first_name) ? "ДА" : "НЕТ") . "\n";
echo "Фамилия '$last_name' - телефон? " . ($is_phone($last_name) ? "ДА" : "НЕТ") . "\n";

echo "\n=== РЕЗУЛЬТАТ ФИЛЬТРАЦИИ ===\n";
$recipient = array();

// Очищаем имя от телефонов
if (!empty($first_name) && !$is_phone($first_name)) {
    $recipient['name'] = $first_name;
    echo "✅ Имя добавлено: '$first_name'\n";
} else {
    echo "❌ Имя отфильтровано (телефон): '$first_name'\n";
}

// Очищаем фамилию от телефонов
if (!empty($last_name) && !$is_phone($last_name)) {
    $recipient['surname'] = $last_name;
    echo "✅ Фамилия добавлена: '$last_name'\n";
} else {
    echo "❌ Фамилия отфильтрована (телефон): '$last_name'\n";
}

// Телефон получателя
$phone = $order->get_billing_phone();
if (!empty($phone)) {
    $recipient['phone'] = $phone;
    echo "✅ Телефон добавлен: '$phone'\n";
}

echo "\n=== ИТОГОВЫЕ ДАННЫЕ ПОЛУЧАТЕЛЯ ===\n";
echo json_encode($recipient, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";

echo "\n=== ТЕСТ ЗАВЕРШЕН ===\n";
?> 