<?php
/**
 * Простое создание тестового заказа WooCommerce
 * Совместимо с PHP 5.6+
 */

echo "🛒 Создание тестового заказа WooCommerce\n";
echo "=========================================\n";

// Генерируем уникальные данные для заказа
$random_id = rand(1000, 9999);
$test_data = array(
    'first_name' => 'Тестовый',
    'last_name' => 'Покупатель ' . $random_id,
    'email' => 'test_' . $random_id . '@example.com',
    'phone' => '+7 (999) 123-' . substr($random_id, 0, 2) . '-' . substr($random_id, 2, 2),
    'address' => 'ул. Тестовая, д. ' . $random_id,
    'city' => 'Москва',
    'postcode' => '123456'
);

echo "📝 Данные тестового заказа:\n";
echo "Имя: " . $test_data['first_name'] . " " . $test_data['last_name'] . "\n";
echo "Email: " . $test_data['email'] . "\n";
echo "Телефон: " . $test_data['phone'] . "\n";
echo "Адрес: " . $test_data['address'] . ", " . $test_data['city'] . "\n\n";

// Создаем веб-скрипт для выполнения на сервере
$web_script = '<?php
// Подключение к WordPress
require_once("wp-config.php");

// Проверяем WooCommerce
if (!function_exists("wc_create_order")) {
    die("ERROR: WooCommerce не активен");
}

echo "✅ WooCommerce подключен<br>";

try {
    // Создаем заказ
    $order = wc_create_order();
    
    if (!$order || is_wp_error($order)) {
        throw new Exception("Не удалось создать заказ");
    }
    
    $order_id = $order->get_id();
    echo "📦 Создан заказ ID: $order_id<br>";
    
    // Устанавливаем данные покупателя
    $order->set_billing_first_name("' . $test_data['first_name'] . '");
    $order->set_billing_last_name("' . $test_data['last_name'] . '");
    $order->set_billing_email("' . $test_data['email'] . '");
    $order->set_billing_phone("' . $test_data['phone'] . '");
    $order->set_billing_address_1("' . $test_data['address'] . '");
    $order->set_billing_city("' . $test_data['city'] . '");
    $order->set_billing_postcode("' . $test_data['postcode'] . '");
    $order->set_billing_country("RU");
    
    // Дублируем для доставки
    $order->set_shipping_first_name("' . $test_data['first_name'] . '");
    $order->set_shipping_last_name("' . $test_data['last_name'] . '");
    $order->set_shipping_address_1("' . $test_data['address'] . '");
    $order->set_shipping_city("' . $test_data['city'] . '");
    $order->set_shipping_postcode("' . $test_data['postcode'] . '");
    $order->set_shipping_country("RU");
    
    echo "👤 Данные покупателя установлены<br>";
    
    // Ищем товар
    $products = wc_get_products(array(
        "limit" => 1,
        "status" => "publish"
    ));
    
    if (!empty($products)) {
        $product = $products[0];
        $order->add_product($product, 1);
        echo "🌸 Добавлен товар: " . $product->get_name() . "<br>";
    } else {
        echo "⚠️ Товары не найдены, создаем заказ без товаров<br>";
    }
    
    // Устанавливаем способ оплаты
    $order->set_payment_method("cod");
    $order->set_payment_method_title("Наложенный платеж");
    
    // Добавляем UTM метки для тестирования
    $order->update_meta_data("_utm_source", "test_api");
    $order->update_meta_data("_utm_medium", "direct");
    $order->update_meta_data("_utm_campaign", "tablecrm_integration_test");
    
    // Сохраняем и пересчитываем
    $order->calculate_totals();
    $order->save();
    
    echo "💰 Сумма заказа: " . $order->get_total() . " руб.<br>";
    
    // Устанавливаем статус (триггер для TableCRM)
    $order->update_status("pending", "Тестовый заказ для проверки интеграции TableCRM");
    
    echo "✅ Заказ создан успешно!<br>";
    echo "📊 Статус: " . $order->get_status() . "<br>";
    
    // Ждем немного и проверяем TableCRM ID
    sleep(3);
    
    $tablecrm_id = get_post_meta($order_id, "_tablecrm_lead_id", true);
    
    if ($tablecrm_id && $tablecrm_id !== "unknown") {
        echo "<div style=\"color: green; font-weight: bold;\">✅ TableCRM ID получен: $tablecrm_id</div>";
        echo "<div style=\"color: green;\">✅ Интеграция работает корректно!</div>";
    } else {
        echo "<div style=\"color: orange;\">⚠️ TableCRM ID пока не получен (может потребоваться время)</div>";
        echo "<div style=\"color: blue;\">ℹ️ Проверьте логи через несколько минут</div>";
    }
    
    echo "<hr>";
    echo "<h3>📋 Итоговая информация:</h3>";
    echo "<ul>";
    echo "<li><strong>ID заказа:</strong> $order_id</li>";
    echo "<li><strong>Покупатель:</strong> ' . $test_data['first_name'] . ' ' . $test_data['last_name'] . '</li>";
    echo "<li><strong>Email:</strong> ' . $test_data['email'] . '</li>";
    echo "<li><strong>Телефон:</strong> ' . $test_data['phone'] . '</li>";
    echo "<li><strong>Сумма:</strong> " . $order->get_total() . " руб.</li>";
    echo "<li><strong>Статус:</strong> " . $order->get_status() . "</li>";
    echo "<li><strong>TableCRM ID:</strong> " . ($tablecrm_id ? $tablecrm_id : "НЕ УСТАНОВЛЕН") . "</li>";
    echo "</ul>";
    
    echo "<p><a href=\"/wp-admin/post.php?post=$order_id&action=edit\">Посмотреть заказ в админке</a></p>";
    
} catch (Exception $e) {
    echo "<div style=\"color: red;\">❌ ОШИБКА: " . $e->getMessage() . "</div>";
}

// Удаляем этот файл после выполнения
@unlink(__FILE__);
?>';

// Подключение к серверу и создание заказа
echo "🔌 Подключаемся к серверу...\n";

$ssh_host = getenv("TABLECRM_SSH_HOST");
$ssh_user = getenv("TABLECRM_SSH_USER");
$ssh_pass = getenv("TABLECRM_SSH_PASS");
if (!$ssh_host || !$ssh_user || !$ssh_pass) {
    die("❌ Env variables TABLECRM_SSH_HOST, TABLECRM_SSH_USER and TABLECRM_SSH_PASS required\n");
}

$connection = ssh2_connect($ssh_host, 22);
if (!$connection) {
    die("❌ Ошибка подключения к серверу\n");
}

if (!ssh2_auth_password($connection, $ssh_user, $ssh_pass)) {
    die("❌ Ошибка авторизации\n");
}


$sftp = ssh2_sftp($connection);
echo "✅ Подключение установлено\n";

// Создаем временный файл на сервере
$remote_file = '/home/<USER>/bekflom3/mosbuketik.ru/public_html/create_test_order_simple.php';

echo "📤 Загружаем скрипт на сервер...\n";
$stream = fopen("ssh2.sftp://$sftp$remote_file", 'w');
if (!$stream) {
    die("❌ Не удалось создать файл на сервере\n");
}

fwrite($stream, $web_script);
fclose($stream);

echo "✅ Скрипт загружен\n";

// Запускаем через curl
$url = 'https://mosbuketik.ru/create_test_order_simple.php';
echo "🚀 Запускаем создание заказа: $url\n";
echo str_repeat("=", 50) . "\n";

$curl = curl_init();
curl_setopt($curl, CURLOPT_URL, $url);
curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
curl_setopt($curl, CURLOPT_TIMEOUT, 30);
curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);

$response = curl_exec($curl);
$http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
curl_close($curl);

if ($response && $http_code == 200) {
    echo strip_tags($response) . "\n";
} else {
    echo "❌ Ошибка выполнения: HTTP код $http_code\n";
    echo "Ответ: " . substr($response, 0, 200) . "\n";
}

echo str_repeat("=", 50) . "\n";
echo "🏁 Создание заказа завершено\n\n";

echo "📋 Что проверить далее:\n";
echo "1. Админ-панель WooCommerce: https://mosbuketik.ru/wp-admin/edit.php?post_type=shop_order\n";
echo "2. Логи WordPress: wp-content/debug.log\n";
echo "3. TableCRM на наличие нового лида\n";
echo "4. Настройки плагина: https://mosbuketik.ru/wp-admin/options-general.php?page=tablecrm-integration\n";
?>