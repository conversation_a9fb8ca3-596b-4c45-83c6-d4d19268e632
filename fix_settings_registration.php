<?php
// Fix TableCRM Complete plugin settings
echo "=== ИСПРАВЛЕНИЕ НАСТРОЕК TABLECRM COMPLETE ===\n";

$wp_path = '/home/<USER>/bekflom3/mosbuketik.ru/public_html';
require_once($wp_path . '/wp-config.php');
require_once($wp_path . '/wp-load.php');

echo "WordPress загружен успешно\n\n";

// Force set all required settings
$settings = array(
    'tablecrm_complete_api_url' => 'https://app.tablecrm.com/api/v1/',
    'tablecrm_complete_api_key' => 'af1874616430e04cfd4bce30035789907e899fc7c3a1a4bb27254828ff304a77',
    'tablecrm_complete_organization_id' => 38,
    'tablecrm_complete_cashbox_id' => 218,
    'tablecrm_complete_warehouse_id' => 39,
    'tablecrm_complete_unit_id' => 116,
    'tablecrm_complete_enable_orders' => 1,
    'tablecrm_complete_debug_mode' => 1,
    'tablecrm_complete_order_statuses' => array('processing', 'completed')
);

echo "Принудительная установка настроек:\n";
foreach ($settings as $option => $value) {
    $result = update_option($option, $value);
    echo sprintf("%-35s: %s\n", $option, $result ? 'УСТАНОВЛЕНО' : 'ОШИБКА');
}

echo "\nПроверка установленных настроек:\n";
foreach ($settings as $option => $expected_value) {
    $actual_value = get_option($option, 'НЕ_НАЙДЕНО');
    $status = ($actual_value !== 'НЕ_НАЙДЕНО') ? 'ОК' : 'ОШИБКА';
    echo sprintf("%-35s: %s\n", $option, $status);
}

// Also try to trigger plugin activation hook manually
echo "\nПопытка вызвать хук активации плагина...\n";
if (class_exists('TableCRM_Integration_Complete')) {
    $plugin_instance = new TableCRM_Integration_Complete();
    if (method_exists($plugin_instance, 'activate_plugin')) {
        $plugin_instance->activate_plugin();
        echo "✅ Хук активации выполнен\n";
    } else {
        echo "❌ Метод activate_plugin не найден\n";
    }
} else {
    echo "❌ Класс плагина не найден\n";
}

echo "\n=== ИСПРАВЛЕНИЕ ЗАВЕРШЕНО ===\n";
echo "Теперь проверьте настройки в админ-панели WordPress!\n";
?> 