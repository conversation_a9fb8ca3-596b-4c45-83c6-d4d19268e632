<?php
// Тестирование популярных API эндпоинтов TableCRM

$api_url = 'https://app.tablecrm.com/api/v1/';
$api_key = 'af1874616430e04cfd4bce30035789907e899fc7c3a1a4bb27254828ff304a77';

echo "🔍 ТЕСТИРОВАНИЕ API ЭНДПОИНТОВ TABLECRM\n";
echo "======================================\n\n";

// Популярные эндпоинты для CRM систем
$test_endpoints = [
    // Общие эндпоинты
    'lead',
    'leads', 
    'contact',
    'contacts',
    'customer',
    'customers',
    'client',
    'clients',
    'prospect',
    'prospects',
    
    // Создание записей
    'lead/create',
    'leads/create',
    'contact/create', 
    'contacts/create',
    'customer/create',
    'customers/create',
    
    // Добавление записей
    'lead/add',
    'leads/add',
    'contact/add',
    'contacts/add',
    'customer/add',
    'customers/add',
    
    // Новые записи
    'lead/new',
    'leads/new',
    'contact/new',
    'contacts/new',
    
    // TableCRM специфичные
    'table/lead',
    'table/contact',
    'table/customer',
    'crm/lead',
    'crm/contact',
    'crm/customer',
    
    // Веб-хуки и формы
    'webhook',
    'webhooks',
    'form',
    'forms',
    'submit',
    'integration',
    
    // Альтернативные версии
    'v1/lead',
    'v1/contact',
    'v2/lead',
    'v2/contact',
    
    // Известный рабочий эндпоинт
    'health'
];

// Тестируем различные варианты авторизации
$auth_variants = [
    [
        'headers' => [
            'Authorization: Bearer ' . $api_key,
            'Content-Type: application/json'
        ]
    ],
    [
        'headers' => [
            'X-API-Key: ' . $api_key,
            'Content-Type: application/json'
        ]
    ],
    [
        'headers' => [
            'Authorization: ' . $api_key,
            'Content-Type: application/json'
        ]
    ],
    [
        'headers' => [
            'Token: ' . $api_key,
            'Content-Type: application/json'
        ]
    ]
];

$successful_endpoints = [];

foreach ($test_endpoints as $endpoint) {
    foreach ($auth_variants as $auth_idx => $auth) {
        $url = $api_url . $endpoint;
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $auth['headers']);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo sprintf(
            "Эндпоинт: %-20s | Авторизация: %d | HTTP: %d",
            $endpoint,
            $auth_idx + 1,
            $http_code
        );
        
        if ($http_code != 404) {
            echo " ✅ НАЙДЕН!";
            if ($response) {
                $decoded = json_decode($response, true);
                if ($decoded) {
                    echo " | Ответ: " . substr(json_encode($decoded), 0, 100) . "...";
                } else {
                    echo " | Ответ: " . substr($response, 0, 50) . "...";
                }
            }
            $successful_endpoints[] = [
                'endpoint' => $endpoint,
                'auth' => $auth_idx + 1,
                'http_code' => $http_code,
                'response' => $response
            ];
        }
        echo "\n";
    }
}

echo "\n🎯 НАЙДЕННЫЕ РАБОЧИЕ ЭНДПОИНТЫ:\n";
echo "===============================\n";

if (empty($successful_endpoints)) {
    echo "❌ Не найдено рабочих эндпоинтов (кроме health)\n";
} else {
    foreach ($successful_endpoints as $success) {
        echo sprintf(
            "✅ %s (авторизация %d, HTTP %d)\n",
            $success['endpoint'],
            $success['auth'],
            $success['http_code']
        );
    }
}

echo "\n📝 РЕКОМЕНДАЦИИ:\n";
echo "================\n";
echo "1. Обратитесь к документации TableCRM\n";
echo "2. Проверьте API ключ и права доступа\n";
echo "3. Возможно нужна регистрация webhook URL\n";
echo "4. Попробуйте связаться с поддержкой TableCRM\n";
?> 