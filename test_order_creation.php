<?php
// Test order creation and TableCRM integration
echo "=== ТЕСТ СОЗДАНИЯ ЗАКАЗА И ИНТЕГРАЦИИ TABLECRM ===\n";

$wp_path = '/home/<USER>/bekflom3/mosbuketik.ru/public_html';
require_once($wp_path . '/wp-config.php');
require_once($wp_path . '/wp-load.php');

echo "WordPress загружен успешно\n\n";

// Check current settings
echo "=== ТЕКУЩИЕ НАСТРОЙКИ ===\n";
$settings = array(
    'tablecrm_complete_api_url',
    'tablecrm_complete_api_key',
    'tablecrm_complete_organization_id',
    'tablecrm_complete_cashbox_id',
    'tablecrm_complete_warehouse_id',
    'tablecrm_complete_unit_id',
    'tablecrm_complete_enable_orders',
    'tablecrm_complete_debug_mode'
);

foreach ($settings as $setting) {
    $value = get_option($setting, 'НЕ_НАЙДЕНО');
    echo sprintf("%-35s: %s\n", $setting, $value);
}

// Check if WooCommerce is active
echo "\n=== ПРОВЕРКА WOOCOMMERCE ===\n";
if (class_exists('WooCommerce')) {
    echo "✅ WooCommerce активен\n";
    
    // Get latest orders
    $orders = wc_get_orders(array(
        'limit' => 3,
        'orderby' => 'date',
        'order' => 'DESC'
    ));
    
    echo "Последние заказы:\n";
    foreach ($orders as $order) {
        $tablecrm_doc_id = get_post_meta($order->get_id(), '_tablecrm_complete_document_id', true);
        echo sprintf("  Заказ #%d | Статус: %s | TableCRM ID: %s\n", 
            $order->get_id(), 
            $order->get_status(),
            $tablecrm_doc_id ?: 'НЕТ'
        );
    }
} else {
    echo "❌ WooCommerce не активен\n";
}

// Check if TableCRM plugin is working
echo "\n=== ПРОВЕРКА ПЛАГИНА TABLECRM ===\n";
if (class_exists('TableCRM_Integration_Complete')) {
    echo "✅ Класс плагина найден\n";
    
    global $tablecrm_integration_complete;
    if (isset($tablecrm_integration_complete)) {
        echo "✅ Экземпляр плагина создан\n";
        
        // Try to test API connection
        if (method_exists($tablecrm_integration_complete, 'make_api_request')) {
            echo "Тестирование API соединения...\n";
            $test_response = $tablecrm_integration_complete->make_api_request('health', array(), 'GET');
            if ($test_response['success']) {
                echo "✅ API соединение работает\n";
            } else {
                echo "❌ API соединение не работает: " . $test_response['message'] . "\n";
            }
        }
    } else {
        echo "❌ Экземпляр плагина не создан\n";
    }
} else {
    echo "❌ Класс плагина не найден\n";
}

// Simulate order status change for latest order
echo "\n=== СИМУЛЯЦИЯ ОБРАБОТКИ ЗАКАЗА ===\n";
if (class_exists('WooCommerce') && !empty($orders)) {
    $latest_order = $orders[0];
    echo "Обрабатываем заказ #{$latest_order->get_id()}\n";
    
    // Check if order was already sent
    $existing_doc_id = get_post_meta($latest_order->get_id(), '_tablecrm_complete_document_id', true);
    if ($existing_doc_id) {
        echo "⚠️ Заказ уже отправлен в TableCRM (ID: $existing_doc_id)\n";
    } else {
        echo "Заказ еще не отправлен в TableCRM\n";
        
        // Try to send manually
        if (isset($tablecrm_integration_complete) && method_exists($tablecrm_integration_complete, 'send_order_to_tablecrm')) {
            echo "Попытка отправить заказ в TableCRM...\n";
            $result = $tablecrm_integration_complete->send_order_to_tablecrm($latest_order->get_id());
            echo "Результат отправки: " . ($result ? 'УСПЕХ' : 'ОШИБКА') . "\n";
        }
    }
}

echo "\n=== ТЕСТ ЗАВЕРШЕН ===\n";
?> 