# 🔍 АНАЛИЗ ПРОБЛЕМЫ С ПЕРЕДАЧЕЙ ТОВАРОВ В TableCRM

## ❓ ПРОБЛЕМА
Вы спрашиваете, почему **"Товары (Список товаров и количество)"** не передаются в CRM.

## ✅ РЕЗУЛЬТАТ АНАЛИЗА КОДА

### 📦 **ТОВАРЫ ДОЛЖНЫ ПЕРЕДАВАТЬСЯ!** 

Анализ кода плагина `tablecrm-integration-fixed-v3.php` показывает:

### 1️⃣ **Сбор товаров из WooCommerce (строки 647-661)**
```php
// Добавляем товары
foreach ($order->get_items() as $item_id => $item) {
    $product = $item->get_product();
    if ($product) {
        $data['items'][] = array(
            'name' => $item->get_name(),
            'quantity' => $item->get_quantity(),
            'price' => $item->get_total(),
            'unit_price' => ($item->get_quantity() > 0) ? ($item->get_total() / $item->get_quantity()) : 0,
            'sku' => $product->get_sku() ?: '',
            'product_id' => $product->get_id()
        );
    }
}
```

✅ **Плагин корректно собирает:**
- Название товара
- Количество
- Цену
- SKU
- ID товара

### 2️⃣ **Адаптация для TableCRM API (строки 814-825)**
```php
// Формируем товары
$goods = array();
if (!empty($data['items'])) {
    foreach ($data['items'] as $item) {
        $goods[] = array(
            'price' => floatval($item['unit_price']),
            'quantity' => intval($item['quantity']),
            'unit' => intval($unit_id),
            'discount' => 0,
            'sum_discounted' => 0,
            'nomenclature' => 39697 // Стандартная номенклатура
        );
    }
}
```

✅ **Плагин формирует массив `goods[]` с:**
- `price` - цена за единицу
- `quantity` - количество
- `unit` - единица измерения (ID: 116)
- `nomenclature` - номенклатура (ID: 39697)

### 3️⃣ **Отправка в TableCRM (строки 830-847)**
```php
return array(array(
    'dated' => time(),
    'operation' => 'Заказ',
    'comment' => "Заказ #{$data['order_id']}...",
    'tax_included' => true,
    'tax_active' => true,
    'goods' => $goods,  // ← ТОВАРЫ ВКЛЮЧЕНЫ В ДОКУМЕНТ!
    'warehouse' => intval($warehouse_id),
    'contragent' => 330985,
    'paybox' => intval($cashbox_id),
    'organization' => intval($organization_id),
    'status' => false,
    'paid_rubles' => number_format(floatval($data['order_total']), 2, '.', ''),
));
```

✅ **Товары включаются в поле `goods` документа продажи!**

## 🔍 ВОЗМОЖНЫЕ ПРИЧИНЫ, ПОЧЕМУ ВЫ НЕ ВИДИТЕ ТОВАРЫ

### 1️⃣ **Неправильный раздел в TableCRM**
❌ **Вы сейчас в разделе "Платежи"** - там товары НЕ отображаются!

✅ **Нужно перейти в:** `Продажи` → `Продажи`

### 2️⃣ **Товары есть, но скрыты в интерфейсе**
В документе продажи нужно:
1. Открыть конкретный документ
2. Найти вкладку **"Товары"** или **"Спецификация"**
3. Проверить содержимое

### 3️⃣ **Проблемы с номенклатурой**
Плагин использует **фиксированную номенклатуру ID: 39697**

❓ **Возможно эта номенклатура:**
- Не существует в вашей TableCRM
- Скрыта или неактивна
- Требует других прав доступа

### 4️⃣ **Проблемы с единицей измерения**
Плагин использует **единицу измерения ID: 116**

❓ **Возможно эта единица:**
- Не существует в вашей TableCRM
- Имеет другой ID

## 🧪 ПРОВЕРКА ПЕРЕДАЧИ ТОВАРОВ

### Из наших тестов видно:
```json
{
  "goods": [
    {
      "price": 1500,
      "quantity": 2,
      "unit": 116,
      "nomenclature": 39697
    },
    {
      "price": 2500,
      "quantity": 1,
      "unit": 116,
      "nomenclature": 39697
    }
  ]
}
```

✅ **Товары ОТПРАВЛЯЮТСЯ в API!**

## 🎯 ЧТО ДЕЛАТЬ ДАЛЬШЕ

### 1️⃣ **Проверьте правильный раздел**
- Перейдите в `Продажи` → `Продажи`
- Найдите документ с суммой 5500 руб.
- Откройте документ
- Найдите вкладку с товарами

### 2️⃣ **Проверьте номенклатуру 39697**
- Перейдите в `Товары и услуги` → `Номенклатура`
- Найдите товар с ID 39697
- Убедитесь что он активен

### 3️⃣ **Проверьте единицу измерения 116**
- Найдите в настройках единицы измерения
- Убедитесь что ID 116 существует

### 4️⃣ **Создайте реальный тестовый заказ**
- Создайте заказ в WooCommerce
- Проследите его отправку в TableCRM
- Проверьте логи: `wp-content/debug.log`

## 🚨 ВАЖНО!

**Товары ДОЛЖНЫ передаваться в TableCRM!** 

Если вы их не видите - проблема НЕ в плагине, а в:
1. Интерфейсе TableCRM (неправильный раздел)
2. Настройках номенклатуры/единиц измерения
3. Правах доступа к товарам в API

## 📋 КРАТКИЕ ВЫВОДЫ

| Компонент | Статус | Описание |
|-----------|---------|----------|
| **Сбор товаров из WooCommerce** | ✅ РАБОТАЕТ | Плагин корректно извлекает товары |
| **Формирование массива goods** | ✅ РАБОТАЕТ | Товары адаптируются для API |
| **Отправка в TableCRM** | ✅ РАБОТАЕТ | Товары включены в JSON |
| **Проблема** | ❓ В ОТОБРАЖЕНИИ | Товары есть, но не видны в интерфейсе |

**Перейдите в раздел "Продажи" → "Продажи" и найдите там товары!** 