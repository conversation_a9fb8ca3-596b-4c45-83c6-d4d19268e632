# 🛠️ ИСПРАВЛЕНИЕ НАЗВАНИЙ ТОВАРОВ - TableCRM Integration v1.0.16

## ❌ ПРОБЛЕМА
Из анализа логов обнаружена **критическая ошибка** в отправке товаров в TableCRM:

### Что НЕ работало:
```json
{
  "goods": [
    {
      "price": 4999,
      "quantity": 1,
      "unit": 116,
      "discount": 0,
      "sum_discounted": 0,
      "nomenclature": 39697
      // ❌ ОТСУТСТВУЕТ поле "name" с названием товара!
    }
  ]
}
```

### Ошибка API:
```
HTTP 422: {"detail":[{"loc":["body","__root__",0,"goods",0,"nomenclature"],"msg":"field required","type":"value_error.missing"}]}
```

## ✅ РЕШЕНИЕ

### 1. Добавлено поле `name` в структуру товаров
```php
$goods[] = array(
    'name' => $item_name, // ✅ ДОБАВЛЕНО: Название товара
    'price' => $item_price,
    'quantity' => $item_quantity,
    'unit' => intval($unit_id),
    'discount' => 0,
    'sum_discounted' => $item_total, // ✅ ИСПРАВЛЕНО: Корректная сумма
    'nomenclature' => 39697,
    'sku' => isset($item['sku']) ? sanitize_text_field($item['sku']) : '' // ✅ ДОБАВЛЕНО: Артикул
);
```

### 2. Улучшено логирование товаров
```php
$this->log_info("ТОВАР #$item_id:");
$this->log_info("  - Название товара (get_name): '$item_name'");
$this->log_info("  - Название продукта (get_title): '" . $product->get_title() . "'");
$this->log_info("  - SKU: '$product_sku'");
$this->log_info("  - Количество: $item_quantity");
$this->log_info("  - Цена: $item_price");
```

### 3. Добавлено логирование итогового JSON
```php
$this->log_info("=== ИТОГОВЫЙ JSON ДЛЯ TableCRM ===");
$this->log_info("JSON payload: " . json_encode($result, JSON_UNESCAPED_UNICODE));
$this->log_info("=== КОНЕЦ JSON ===");
```

## 📦 УСТАНОВКА

### Шаг 1: Деактивировать старый плагин
```bash
# В админке WordPress:
Плагины → TableCRM Integration → Деактивировать
```

### Шаг 2: Установить новую версию
```bash
# Загрузить: tablecrm-integration-enhanced-v1.0.16.zip
Плагины → Добавить новый → Загрузить плагин → Выбрать файл
```

### Шаг 3: Активировать новый плагин
```bash
Плагины → TableCRM Integration Enhanced v1.0.16 → Активировать
```

## 🧪 ТЕСТИРОВАНИЕ

### 1. Создать тестовый заказ
- Добавить товары с понятными названиями в корзину
- Оформить заказ с реальными данными
- Подождать 2-3 минуты (Action Scheduler)

### 2. Проверить логи
```bash
# В debug.log найти:
[TableCRM v3] [INFO] ТОВАР #X:
[TableCRM v3] [INFO]   - Название товара (get_name): 'Название товара'
[TableCRM v3] [INFO] === ИТОГОВЫЙ JSON ДЛЯ TableCRM ===
```

### 3. Проверить в TableCRM
- Перейти в Документы → Продажи
- Найти новый заказ
- Убедиться что товары отображаются с корректными названиями

## 📊 ОЖИДАЕМЫЙ РЕЗУЛЬТАТ

### В логах должно быть:
```json
{
  "goods": [
    {
      "name": "Букет из 39 альстромерий", // ✅ НАЗВАНИЕ ТОВАРА
      "price": 4999,
      "quantity": 1,
      "unit": 116,
      "discount": 0,
      "sum_discounted": 4999,
      "nomenclature": 39697,
      "sku": ""
    }
  ]
}
```

### В TableCRM будет:
- ✅ Документ продажи создается успешно (HTTP 200/201)
- ✅ Товары отображаются с корректными названиями
- ✅ Количество и суммы корректны
- ✅ Нет ошибок HTTP 422

## 🔧 ДОПОЛНИТЕЛЬНЫЕ УЛУЧШЕНИЯ

1. **Корректная сумма товара**: `sum_discounted = price * quantity`
2. **Обработка SKU**: Добавлено поле `sku` для артикулов
3. **Расширенный комментарий**: Добавлен способ оплаты в комментарий
4. **Название для общего товара**: Если нет товаров, используется "Заказ #ID"

## ⚠️ ВАЖНЫЕ ПРИМЕЧАНИЯ

- **Сохранность настроек**: Все настройки API сохраняются при обновлении
- **Обратная совместимость**: Плагин работает с существующими заказами
- **Action Scheduler**: Сохраняется асинхронная обработка через очереди
- **Идемпотентность**: Сохраняется защита от дублирования заказов

## 📋 ВЕРСИИ

| Версия | Описание |
|--------|----------|
| v1.0.12 | Старая версия без названий товаров |
| **v1.0.16** | **Исправленная версия с названиями товаров** |

---

**✅ ГОТОВО К ИСПОЛЬЗОВАНИЮ**  
Файл: `tablecrm-integration-enhanced-v1.0.16.zip` 