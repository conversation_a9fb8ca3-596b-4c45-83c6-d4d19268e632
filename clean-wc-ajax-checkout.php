<?php
add_action( 'init', function () {
	if (
		defined( 'DOING_AJAX' ) &&
		DOING_AJAX &&
		isset( $_REQUEST['wc-ajax'] ) &&
		$_REQUEST['wc-ajax'] === 'checkout'
	) {
		// Буферизуем весь вывод и вырезаем всё до JSON
		ob_start( function( $buffer ) {
			// Находим начало JSON
			$json_pos = strpos( $buffer, '{' );
			if ( $json_pos === false ) {
				// ничего не найдено — возвращаем пустой JSON
				error_log('[WooCheckoutClean] JSON не найден, весь буфер: ' . $buffer);
				return '{}';
			}
			// Отсекаем всё до JSON
			return substr( $buffer, $json_pos );
		}, 0 );

		// Отключаем все формы вывода ошибок
		@ini_set( 'display_errors', 0 );
		define( 'WP_DEBUG_DISPLAY', false );
		add_filter( 'wp_php_error_display', '__return_false', 100 );
	}
}, 0 );
