# Upload and run hooks test
$server = "bekflom3.beget.tech"
$user = "bekflom3_wz"
$pass = "zl2Shj!e&6ng"

Write-Host "Uploading hooks test script..." -ForegroundColor Green

try {
    $uri = "ftp://$server/public_html/test_automatic_hooks.php"
    $webclient = New-Object System.Net.WebClient
    $webclient.Credentials = New-Object System.Net.NetworkCredential($user, $pass)
    $webclient.UploadFile($uri, "test_automatic_hooks.php")
    Write-Host "Hooks test script uploaded!" -ForegroundColor Green
    
    # Run the test
    Write-Host "Running automatic hooks test..." -ForegroundColor Yellow
    $result = Invoke-WebRequest -Uri "https://mosbuketik.ru/test_automatic_hooks.php" -UseBasicParsing
    Write-Host $result.Content -ForegroundColor White
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

$webclient.Dispose()
Write-Host "Hooks test completed!" -ForegroundColor Green 