<?php
// Загружаем окружение WordPress
require_once( __DIR__ . '/wp-load.php' );

// Проверяем, что у пользователя есть права администратора
if ( ! current_user_can( 'manage_options' ) ) {
    wp_die( 'Доступ запрещен. Пожалуйста, войдите как администратор.' );
}

header( 'Content-Type: text/html; charset=utf-8' );
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <title>Проверка API ключа TableCRM</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif; line-height: 1.6; color: #333; max-width: 800px; margin: 40px auto; padding: 20px; border: 1px solid #ddd; box-shadow: 0 2px 5px rgba(0,0,0,0.05); }
        h1 { color: #23282d; }
        .key { 
            background-color: #f0f0f0; 
            padding: 10px; 
            border: 1px solid #ccc; 
            border-radius: 4px; 
            font-family: monospace; 
            word-wrap: break-word;
            margin-top: 10px;
        }
        .notice { padding: 15px; border-left: 4px solid; margin-bottom: 20px; }
        .notice-info { border-color: #0073aa; background-color: #f7f7f7; }
        .notice-success { border-color: #46b450; background-color: #f7fff7; }
        .notice-warning { border-color: #ffb900; background-color: #fffaf0; }
    </style>
</head>
<body>
    <h1>Диагностика API ключа TableCRM</h1>
    <div class="notice notice-info">
        <p>Этот скрипт проверяет, какой API ключ плагина 'TableCRM Integration Complete' сохранен в базе данных WordPress.</p>
    </div>

    <?php
    // Получаем ключ через стандартную функцию get_option (может быть закеширован)
    $api_key_from_options = get_option( 'tablecrm_complete_api_key' );

    // Получаем ключ напрямую из базы данных, чтобы обойти кеш объектов
    global $wpdb;
    $table_name = $wpdb->prefix . 'options';
    // Используем prepared statement для безопасности, хотя здесь это не критично
    $raw_key_from_db = $wpdb->get_var( $wpdb->prepare(
        "SELECT option_value FROM $table_name WHERE option_name = %s",
        'tablecrm_complete_api_key'
    ) );
    
    function display_key_info($key, $label) {
        if ( ! empty( $key ) ) {
            $length = strlen($key);
            $first_chars = substr($key, 0, 4);
            $last_chars = substr($key, -4);
            echo "<h2>$label</h2>";
            echo "<div class='key'>{$first_chars}........................................{$last_chars}</div>";
            echo "<p><strong>Длина ключа:</strong> {$length} символов.</p>";
        } else {
             echo "<h2>$label</h2>";
             echo "<p class='notice notice-warning'><strong>Ключ не найден или пуст.</strong></p>";
        }
    }
    
    display_key_info($api_key_from_options, "Ключ, полученный через get_option() (может быть из кеша):");
    echo "<hr style='margin: 30px 0;'>";
    display_key_info($raw_key_from_db, "Ключ, полученный НАПРЯМУЮ из базы данных (без кеша):");

    echo "<hr style='margin: 30px 0;'>";

    if ($api_key_from_options === $raw_key_from_db) {
        echo "<div class='notice notice-success'><strong>Вывод:</strong> Ключ в кеше и в базе данных совпадает. Если это старый ключ, значит новый ключ не сохраняется. Проверьте, нажимаете ли вы кнопку 'Сохранить настройки'.</div>";
    } else {
        echo "<div class='notice notice-warning'><strong>Вывод:</strong> Ключ в кеше отличается от ключа в базе данных! Это однозначно указывает на проблему с кешированием. Вам необходимо сбросить кеш объектов на вашем сервере (Object Cache).</div>";
    }
    ?>

</body>
</html> 