<?php
// Test shipping cost inclusion
echo "=== ТЕСТ ОТПРАВКИ ЗАКАЗА С ДОСТАВКОЙ ===\n";

$wp_path = '/home/<USER>/bekflom3/mosbuketik.ru/public_html';
require_once($wp_path . '/wp-config.php');
require_once($wp_path . '/wp-load.php');

echo "WordPress загружен успешно\n\n";

// Test order with shipping
$test_order_id = 21773;
$order = wc_get_order($test_order_id);

if ($order) {
    echo "=== ИНФОРМАЦИЯ О ЗАКАЗЕ #$test_order_id ===\n";
    echo "Статус заказа: " . $order->get_status() . "\n";
    echo "Сумма товаров: " . $order->get_subtotal() . "\n";
    echo "Стоимость доставки: " . $order->get_shipping_total() . "\n";
    echo "Общая сумма: " . $order->get_total() . "\n";
    
    $shipping_method = $order->get_shipping_method();
    if ($shipping_method) {
        echo "Способ доставки: " . $shipping_method . "\n";
    }
    
    // Clear existing data to test again
    delete_post_meta($test_order_id, '_tablecrm_complete_document_id');
    delete_post_meta($test_order_id, '_tablecrm_complete_sent_at');
    echo "\nМетаданные очищены для повторного теста\n";
    
    // Test sending with shipping
    global $tablecrm_integration_complete;
    if (isset($tablecrm_integration_complete)) {
        echo "\n=== ТЕСТ ОТПРАВКИ С ДОСТАВКОЙ ===\n";
        $result = $tablecrm_integration_complete->send_order_to_tablecrm($test_order_id);
        
        if ($result) {
            $new_doc_id = get_post_meta($test_order_id, '_tablecrm_complete_document_id', true);
            echo "✅ УСПЕХ! Заказ отправлен с доставкой, Document ID: $new_doc_id\n";
        } else {
            echo "❌ ОШИБКА! Заказ не отправлен\n";
        }
    } else {
        echo "❌ Плагин не загружен\n";
    }
} else {
    echo "❌ Заказ #$test_order_id не найден\n";
}

echo "\n=== ТЕСТ ЗАВЕРШЕН ===\n";
?> 