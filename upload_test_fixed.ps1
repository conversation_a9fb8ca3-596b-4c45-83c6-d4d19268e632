# Upload and run test for fixed delivery API
$localFile = "test_fixed_delivery.php"
$remoteFile = "/home/<USER>/bekflom3/mosbuketik.ru/public_html/test_fixed_delivery.php"
$server = "bekflom3.beget.tech"
$username = "bekflom3"

Write-Host "=== TESTING FIXED DELIVERY API ===" -ForegroundColor Green

# Upload test file
Write-Host "Uploading test file..." -ForegroundColor Yellow
scp $localFile "${username}@${server}:${remoteFile}"

if ($LASTEXITCODE -eq 0) {
    Write-Host "Test file uploaded successfully" -ForegroundColor Green
    Write-Host "You can now run the test manually on the server" -ForegroundColor Cyan
    Write-Host "Command: php test_fixed_delivery.php" -ForegroundColor White
} else {
    Write-Host "Error uploading test file" -ForegroundColor Red
}

Write-Host "=== COMPLETED ===" -ForegroundColor Green 