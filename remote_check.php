<?php
echo "🔍 БЫСТРАЯ ДИАГНОСТИКА TABLECRM\n";
echo "===============================\n\n";

$wp_path = '/home/<USER>/bekflom3/mosbuketik.ru/public_html';
$plugins_dir = $wp_path . '/wp-content/plugins';

// Поиск файлов плагина
echo "📋 ФАЙЛЫ ПЛАГИНА:\n";
$files = glob($plugins_dir . '/tablecrm*');
foreach ($files as $file) {
    echo "✅ " . basename($file) . "\n";
    echo "   Размер: " . number_format(filesize($file)) . " байт\n";
    echo "   Изменен: " . date('Y-m-d H:i:s', filemtime($file)) . "\n";
    
    if (is_file($file)) {
        $content = file_get_contents($file, false, null, 0, 1000);
        if (preg_match('/Version:\s*(.+)/i', $content, $m)) {
            echo "   Версия: " . trim($m[1]) . "\n";
        }
    }
    echo "\n";
}

// Проверка логов
echo "📊 ПОСЛЕДНИЕ ЛОГИ:\n";
$debug_log = $wp_path . '/wp-content/debug.log';
if (file_exists($debug_log)) {
    echo "Размер debug.log: " . number_format(filesize($debug_log)) . " байт\n";
    echo "Последнее изменение: " . date('Y-m-d H:i:s', filemtime($debug_log)) . "\n";
    
    $lines = file($debug_log);
    $recent = array_slice($lines, -10);
    echo "Последние 10 строк:\n";
    foreach ($recent as $line) {
        if (stripos($line, 'tablecrm') !== false) {
            echo "→ " . trim($line) . "\n";
        }
    }
}

echo "\n🏁 Готово: " . date('Y-m-d H:i:s') . "\n";
?>
