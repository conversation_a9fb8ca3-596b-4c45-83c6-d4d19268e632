# Настройка GitHub репозитория

## 📋 Подготовка к загрузке

### 1. Создание репозитория на GitHub

1. Войдите в свой аккаунт GitHub
2. Нажмите **"New repository"**
3. Заполните данные:
   - **Repository name**: `tablecrm-wordpress-integration`
   - **Description**: `WordPress/WooCommerce integration plugin for TableCRM API`
   - **Visibility**: Public (или Private по желанию)
   - ✅ **Add a README file**: НЕ отмечайте (у нас уже есть README.md)
   - ✅ **Add .gitignore**: НЕ отмечайте (у нас уже есть .gitignore)
   - ✅ **Choose a license**: НЕ отмечайте (у нас уже есть LICENSE)

### 2. Инициализация локального репозитория

Выполните команды в папке проекта:

```bash
# Инициализация Git репозитория
git init

# Добавление remote origin
git remote add origin https://github.com/YOUR_USERNAME/tablecrm-wordpress-integration.git

# Проверка статуса
git status
```

### 3. Подготовка файлов для коммита

```bash
# Добавить основные файлы плагина
git add README.md
git add LICENSE
git add CHANGELOG.md
git add INSTALL.md
git add .gitignore

# Добавить основные файлы плагина
git add tablecrm-integration.php
git add tablecrm-integration-fixed.php
git add check_integration_logs.php
git add utm-tracking.js

# Добавить тестовые скрипты
git add simple_test_order_creation.php

# Проверить что будет добавлено
git status
```

### 4. Создание первого коммита

```bash
# Первый коммит
git commit -m "🎉 Initial release: TableCRM WordPress Integration Plugin

✨ Features:
- Automatic WooCommerce orders sync to TableCRM
- Correct delivery info formatting with recipient JSON structure  
- Support for shipping and billing addresses
- Detailed logging system
- Admin panel configuration
- Fixed delivery data structure for TableCRM API compatibility

📁 Files included:
- Main plugin files (tablecrm-integration.php, tablecrm-integration-fixed.php)
- Comprehensive documentation (README.md, INSTALL.md, CHANGELOG.md)
- Integration testing tools (check_integration_logs.php)
- UTM tracking support (utm-tracking.js)

🔧 Version: 1.0.1
🏷️ Ready for production use"
```

### 5. Отправка на GitHub

```bash
# Отправить в основную ветку
git branch -M main
git push -u origin main
```

## 🏷️ Создание релиза

### 1. Создание тега версии

```bash
# Создание тега для версии 1.0.1
git tag -a v1.0.1 -m "Release v1.0.1: Fixed delivery info structure

🔧 Fixed:
- Delivery info API structure for TableCRM compatibility
- Recipient JSON formatting (name, surname, phone)
- Address formatting from shipping/billing data
- Date formatting for delivery_date field

✨ Improved:
- Enhanced logging for delivery info debugging
- Better error handling for API requests
- Fallback mechanisms for missing shipping data"

# Отправить тег на GitHub
git push origin v1.0.1
```

### 2. Создание релиза в GitHub веб-интерфейсе

1. Перейдите в репозиторий на GitHub
2. Нажмите **"Releases"** → **"Create a new release"**
3. Заполните форму:
   - **Tag version**: `v1.0.1`
   - **Release title**: `TableCRM WordPress Integration v1.0.1 - Fixed Delivery Structure`
   - **Description**: 
   ```markdown
   ## 🔧 Исправления в версии 1.0.1
   
   ### Основные улучшения:
   - ✅ **Исправлена структура данных доставки** для полной совместимости с TableCRM API
   - ✅ **JSON форматирование получателя** с корректными полями `name`, `surname`, `phone`
   - ✅ **Улучшено форматирование адресов** с поддержкой shipping и billing адресов
   - ✅ **Исправлен формат даты доставки** в соответствии с требованиями TableCRM
   
   ### Технические улучшения:
   - 🔍 Расширенное логирование для отладки процесса доставки
   - 🛡️ Улучшенная обработка ошибок API запросов
   - 🔄 Fallback механизмы для отсутствующих shipping данных
   
   ### Файлы для установки:
   - `tablecrm-integration.php` - основной файл плагина
   - `check_integration_logs.php` - скрипт проверки логов
   - Полная документация в README.md и INSTALL.md
   
   ### Совместимость:
   - WordPress 5.0+
   - WooCommerce 3.0+
   - PHP 7.0+
   - TableCRM API v1
   ```

4. **Attach binaries**: Загрузите ZIP архив с плагином
5. Нажмите **"Publish release"**

## 📦 Создание ZIP архива для релиза

```bash
# Создать архив только с необходимыми файлами плагина
zip -r tablecrm-integration-v1.0.1.zip \
  tablecrm-integration.php \
  tablecrm-integration-fixed.php \
  utm-tracking.js \
  check_integration_logs.php \
  simple_test_order_creation.php \
  README.md \
  INSTALL.md \
  CHANGELOG.md \
  LICENSE

# Архив готов для загрузки в релиз
ls -la tablecrm-integration-v1.0.1.zip
```

## 🔄 Настройка для разработки

### Создание ветки разработки

```bash
# Создать ветку разработки
git checkout -b develop
git push -u origin develop

# Создать ветку для новой функции
git checkout -b feature/utm-tracking-enhancement
```

### Настройка Git flow (опционально)

```bash
# Установить git-flow
# Ubuntu/Debian: sudo apt-get install git-flow
# macOS: brew install git-flow

# Инициализация git flow
git flow init

# Создание новой функции
git flow feature start new-feature-name
```

## 📋 Чек-лист для релиза

- [ ] ✅ Все файлы добавлены в Git
- [ ] ✅ README.md обновлен с актуальной информацией
- [ ] ✅ CHANGELOG.md содержит изменения новой версии
- [ ] ✅ Версия обновлена в главном файле плагина
- [ ] ✅ Все тесты пройдены успешно
- [ ] ✅ Создан тег версии
- [ ] ✅ Релиз опубликован на GitHub
- [ ] ✅ ZIP архив прикреплен к релизу
- [ ] ✅ Документация проверена на актуальность

## 🚀 После публикации

1. **Обновите ссылки** в README.md на актуальный GitHub репозиторий
2. **Поделитесь** ссылкой на репозиторий с командой
3. **Настройте уведомления** GitHub для отслеживания issues и PR
4. **Добавьте темы** (topics) к репозиторию: `wordpress`, `woocommerce`, `tablecrm`, `crm-integration`, `php`

---

**Репозиторий готов к использованию! 🎉** 