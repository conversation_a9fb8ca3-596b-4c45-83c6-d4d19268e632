<?php
// Тестовый файл для проверки форматирования адреса и телефона
require_once(__DIR__ . '/wp-load.php');

// Получаем последний заказ
$orders = wc_get_orders(array(
    'limit' => 1,
    'orderby' => 'date',
    'order' => 'DESC',
));

if (empty($orders)) {
    die("Заказы не найдены.\n");
}

$order = $orders[0];
$order_id = $order->get_id();

echo "=== Тестирование заказа #{$order_id} ===\n";

// Получаем данные заказа
echo "\nДанные заказчика:\n";
echo "Имя заказчика: " . $order->get_billing_first_name() . " " . $order->get_billing_last_name() . "\n";
echo "Телефон заказчика: " . $order->get_billing_phone() . "\n";

echo "\nДанные получателя:\n";
echo "Имя получателя: " . $order->get_shipping_first_name() . " " . $order->get_shipping_last_name() . "\n";
echo "Телефон получателя (get_shipping_phone): " . $order->get_shipping_phone() . "\n";
echo "Телефон получателя (мета shipping_phone): " . $order->get_meta('shipping_phone') . "\n";
echo "Телефон получателя (мета _shipping_phone): " . $order->get_meta('_shipping_phone') . "\n";

echo "\nАдрес доставки:\n";
$shipping_address = array();
if ($order->get_shipping_address_1()) $shipping_address[] = $order->get_shipping_address_1();
if ($order->get_shipping_address_2()) $shipping_address[] = $order->get_shipping_address_2();
if ($order->get_shipping_city()) $shipping_address[] = $order->get_shipping_city();
if ($order->get_shipping_state()) $shipping_address[] = $order->get_shipping_state();
if ($order->get_shipping_postcode()) $shipping_address[] = $order->get_shipping_postcode();

echo "Полный адрес: " . implode(", ", $shipping_address) . "\n";

// Инициализируем интеграцию
$integration = new TableCRM_Integration_Complete();

// Получаем Document ID
$document_id = get_post_meta($order_id, '_tablecrm_complete_document_id', true);
if (empty($document_id)) {
    die("\nDocument ID не найден для заказа. Сначала создайте заказ в TableCRM.\n");
}

echo "\nDocument ID: " . $document_id . "\n";

// Тестируем отправку
echo "\nОтправка данных в TableCRM...\n";
$result = $integration->send_delivery_info($document_id, $order);

echo "Результат отправки: " . ($result ? "Успешно" : "Ошибка") . "\n";

// Проверяем лог
$log_file = WP_CONTENT_DIR . '/uploads/tablecrm_integration_complete.log';
if (file_exists($log_file)) {
    echo "\n=== Последние записи лога ===\n";
    $log_content = file_get_contents($log_file);
    $lines = array_slice(explode("\n", $log_content), -20); // последние 20 строк
    echo implode("\n", $lines) . "\n";
} 