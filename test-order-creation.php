<?php
/**
 * Тестовый файл для проверки создания заказа в TableCRM
 * Этот файл поможет проверить, исправлена ли ошибка 500
 */

// Подключаем WordPress
require_once('wp-config.php');

// Получаем последний заказ для тестирования
$orders = wc_get_orders(array(
    'limit' => 1,
    'orderby' => 'date',
    'order' => 'DESC',
    'status' => array('wc-pending', 'wc-processing', 'wc-completed')
));

if (empty($orders)) {
    echo "Нет заказов для тестирования\n";
    exit;
}

$order = $orders[0];
$order_id = $order->get_id();

echo "Тестируем заказ ID: {$order_id}\n";
echo "Статус заказа: " . $order->get_status() . "\n";
echo "Сумма заказа: " . $order->get_total() . "\n";

// Получаем экземпляр плагина
$plugin = TableCRM_Integration_Complete::get_instance();

// Проверяем настройки
$api_url = get_option('tablecrm_complete_api_url');
$api_token = get_option('tablecrm_complete_api_token');

echo "API URL: {$api_url}\n";
echo "API Token: " . substr($api_token, 0, 10) . "...\n";

if (empty($api_url) || empty($api_token)) {
    echo "ОШИБКА: API настройки не заданы\n";
    exit;
}

// Тестируем отправку заказа
echo "\n=== Начинаем тестовую отправку заказа ===\n";

try {
    // Вызываем метод отправки заказа
    $result = $plugin->send_order_to_tablecrm($order_id);
    
    if ($result) {
        echo "✅ Заказ успешно отправлен в TableCRM\n";
        
        // Проверяем, сохранился ли Document ID
        $document_id = get_post_meta($order_id, '_tablecrm_document_id', true);
        if ($document_id) {
            echo "✅ Document ID сохранен: {$document_id}\n";
            
            // Тестируем отправку информации о доставке
            echo "\n=== Тестируем отправку информации о доставке ===\n";
            $delivery_result = $plugin->send_delivery_info($document_id, $order);
            
            if ($delivery_result) {
                echo "✅ Информация о доставке успешно отправлена\n";
            } else {
                echo "❌ Ошибка при отправке информации о доставке\n";
            }
        } else {
            echo "⚠️ Document ID не сохранен\n";
        }
    } else {
        echo "❌ Ошибка при отправке заказа в TableCRM\n";
    }
    
} catch (Exception $e) {
    echo "❌ Исключение: " . $e->getMessage() . "\n";
}

echo "\n=== Тест завершен ===\n";
?>
