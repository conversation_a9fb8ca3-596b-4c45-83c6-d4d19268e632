<?php
/**
 * Тестовый файл для проверки исправления ошибки 405 Method Not Allowed
 * Проверяем, что заказы с существующим Document ID обрабатываются корректно
 */

// Подключаем WordPress
require_once('wp-config.php');

echo "=== Тест исправления ошибки 405 Method Not Allowed ===\n\n";

// Получаем заказ, который уже был отправлен в CRM (имеет Document ID)
$orders = wc_get_orders(array(
    'limit' => 10,
    'orderby' => 'date',
    'order' => 'DESC',
    'meta_query' => array(
        array(
            'key' => '_tablecrm_complete_document_id',
            'compare' => 'EXISTS'
        )
    )
));

if (empty($orders)) {
    echo "❌ Нет заказов с существующим Document ID для тестирования\n";
    echo "Создаем новый тестовый заказ...\n";

    // Получаем любой заказ для тестирования
    $orders = wc_get_orders(array(
        'limit' => 1,
        'orderby' => 'date',
        'order' => 'DESC'
    ));

    if (empty($orders)) {
        echo "❌ Нет заказов для тестирования\n";
        exit;
    }
}

$order = $orders[0];
$order_id = $order->get_id();
$existing_doc_id = get_post_meta($order_id, '_tablecrm_complete_document_id', true);

echo "Тестируем заказ ID: {$order_id}\n";
echo "Статус заказа: " . $order->get_status() . "\n";
echo "Существующий Document ID: " . ($existing_doc_id ? $existing_doc_id : 'отсутствует') . "\n";
echo "Сумма заказа: " . $order->get_total() . "\n\n";

// Получаем экземпляр плагина
$plugin = TableCRM_Integration_Complete::get_instance();

// Проверяем настройки
$api_url = get_option('tablecrm_complete_api_url');
$api_token = get_option('tablecrm_complete_api_key');

echo "API URL: {$api_url}\n";
echo "API Token: " . substr($api_token, 0, 10) . "...\n\n";

if (empty($api_url) || empty($api_token)) {
    echo "❌ ОШИБКА: API настройки не заданы\n";
    exit;
}

// Тестируем отправку заказа
echo "=== Начинаем тестовую отправку заказа ===\n";

try {
    // Вызываем метод отправки заказа
    $result = $plugin->send_order_to_tablecrm($order_id);

    if ($result) {
        echo "✅ Заказ успешно обработан\n";

        // Проверяем, сохранился ли Document ID
        $document_id = get_post_meta($order_id, '_tablecrm_complete_document_id', true);
        if ($document_id) {
            echo "✅ Document ID: {$document_id}\n";
            echo "✅ Исправление работает - нет ошибки 405!\n";
        } else {
            echo "⚠️ Document ID не сохранен\n";
        }
    } else {
        echo "❌ Ошибка при обработке заказа\n";
    }

} catch (Exception $e) {
    echo "❌ Исключение: " . $e->getMessage() . "\n";
}

echo "\n=== Тест завершен ===\n";
echo "Проверьте логи плагина для подробностей.\n";
?>
