# Upload and run new order test
$server = "bekflom3.beget.tech"
$user = "bekflom3_wz"
$pass = "zl2Shj!e&6ng"

Write-Host "Uploading new order test script..." -ForegroundColor Green

try {
    $uri = "ftp://$server/public_html/test_new_order.php"
    $webclient = New-Object System.Net.WebClient
    $webclient.Credentials = New-Object System.Net.NetworkCredential($user, $pass)
    $webclient.UploadFile($uri, "test_new_order.php")
    Write-Host "New order test script uploaded!" -ForegroundColor Green
    
    # Run the test
    Write-Host "Running new order test..." -ForegroundColor Yellow
    $result = Invoke-WebRequest -Uri "https://mosbuketik.ru/test_new_order.php" -UseBasicParsing
    Write-Host $result.Content -ForegroundColor White
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

$webclient.Dispose()
Write-Host "New order test completed!" -ForegroundColor Green 