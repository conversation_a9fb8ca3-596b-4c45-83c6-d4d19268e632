<?php
// Debug API data for order 21780
echo "=== АНАЛИЗ ДАННЫХ API ДЛЯ ЗАКАЗА #21780 ===\n";

$wp_path = '/home/<USER>/bekflom3/mosbuketik.ru/public_html';
require_once($wp_path . '/wp-config.php');
require_once($wp_path . '/wp-load.php');

echo "WordPress загружен успешно\n\n";

$order_id = 21780;
$order = wc_get_order($order_id);

if (!$order) {
    echo "❌ Заказ #$order_id не найден\n";
    exit;
}

echo "=== ИНФОРМАЦИЯ О ЗАКАЗЕ ===\n";
echo "ID: $order_id\n";
echo "Статус: " . $order->get_status() . "\n";
echo "Клиент: " . $order->get_formatted_billing_full_name() . "\n";

// Get items
$items = $order->get_items();
echo "\nТОВАРЫ (" . count($items) . "):\n";
foreach ($items as $item_id => $item) {
    echo "  ID: $item_id - " . $item->get_name() . " x" . $item->get_quantity() . " = " . $item->get_total() . " руб.\n";
}

echo "Доставка: " . $order->get_shipping_total() . " руб.\n";
echo "Итого: " . $order->get_total() . " руб.\n";

// Test what would be sent to API
global $tablecrm_integration_complete;
if (isset($tablecrm_integration_complete)) {
    echo "\n=== СИМУЛЯЦИЯ ОТПРАВКИ В API ===\n";
    
    $api_url = get_option('tablecrm_complete_api_url', 'https://app.tablecrm.com/api/v1/');
    $api_token = get_option('tablecrm_complete_api_key');
    $api_url = trailingslashit($api_url);
    
    // Get settings
    $warehouse_id = get_option('tablecrm_complete_warehouse_id');
    $organization_id = get_option('tablecrm_complete_organization_id');
    $paybox_id = get_option('tablecrm_complete_cashbox_id');
    $contragent_id = 337150; // From logs
    
    // Create nomenclature map for items
    $nomenclature_ids = array();
    $reflection = new ReflectionClass($tablecrm_integration_complete);
    $nomenclature_method = $reflection->getMethod('get_or_create_nomenclature');
    $nomenclature_method->setAccessible(true);
    
    foreach ($items as $item_id => $item) {
        $product_name = $item->get_name();
        echo "Получение номенклатуры для: '$product_name'\n";
        
        $nomenclature_id = $nomenclature_method->invoke($tablecrm_integration_complete, $product_name, $api_url, $api_token);
        
        if ($nomenclature_id) {
            echo "  ✅ Номенклатура: ID $nomenclature_id\n";
            $nomenclature_ids[$item_id] = $nomenclature_id;
        } else {
            echo "  ❌ Ошибка получения номенклатуры\n";
        }
    }
    
    // Test data formation
    $adapt_method = $reflection->getMethod('adapt_data_format');
    $adapt_method->setAccessible(true);
    
    $adapted_data = $adapt_method->invoke(
        $tablecrm_integration_complete,
        $order,
        $warehouse_id,
        $organization_id,
        $paybox_id,
        $contragent_id,
        $nomenclature_ids
    );
    
    if ($adapted_data) {
        echo "\n=== ДАННЫЕ ДЛЯ ОТПРАВКИ ===\n";
        echo "Количество товаров в goods: " . count($adapted_data[0]['goods']) . "\n";
        
        foreach ($adapted_data[0]['goods'] as $i => $good) {
            echo "Товар " . ($i+1) . ":\n";
            echo "  nomenclature: " . $good['nomenclature'] . "\n";
            echo "  price: " . $good['price'] . "\n";
            echo "  quantity: " . $good['quantity'] . "\n";
            echo "  ---\n";
        }
        
        echo "Общая сумма (paid_rubles): " . $adapted_data[0]['paid_rubles'] . "\n";
        echo "Комментарий: " . $adapted_data[0]['comment'] . "\n";
    } else {
        echo "❌ Ошибка формирования данных\n";
    }
}

echo "\n=== АНАЛИЗ ЗАВЕРШЕН ===\n";
?> 