<?php
/**
 * Реальная диагностика настроек TableCRM Complete
 * Подключается к базе данных WordPress и показывает фактические значения
 */

echo "🔍 РЕАЛЬНАЯ ДИАГНОСТИКА НАСТРОЕК TABLECRM\n";
echo "=========================================\n\n";

// Попытка подключения к WordPress
$wp_config_paths = [
    '../wp-config.php',
    '../../wp-config.php', 
    '../../../wp-config.php',
    './wp-config.php'
];

$wp_loaded = false;
foreach ($wp_config_paths as $path) {
    if (file_exists($path)) {
        require_once($path);
        
        // Подключение к базе данных
        try {
            $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8", DB_USER, DB_PASSWORD);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $wp_loaded = true;
            echo "✅ Подключение к базе данных WordPress: УСПЕШНО\n";
            echo "📊 База данных: " . DB_NAME . " на " . DB_HOST . "\n\n";
            break;
        } catch (PDOException $e) {
            echo "❌ Ошибка подключения к БД: " . $e->getMessage() . "\n";
        }
    }
}

if (!$wp_loaded) {
    echo "❌ Не удалось найти wp-config.php\n";
    echo "Попробуйте запустить этот скрипт из папки с WordPress\n";
    exit;
}

// Получение префикса таблиц
$table_prefix = isset($table_prefix) ? $table_prefix : 'wp_';
$options_table = $table_prefix . 'options';

echo "🔧 Таблица настроек: {$options_table}\n\n";

// Список всех настроек TableCRM
$tablecrm_options = [
    'tablecrm_complete_api_url',
    'tablecrm_complete_api_key', 
    'tablecrm_complete_warehouse_id',
    'tablecrm_complete_organization_id',
    'tablecrm_complete_cashbox_id',
    'tablecrm_complete_unit_id'
];

echo "📋 ФАКТИЧЕСКИЕ ЗНАЧЕНИЯ ИЗ БАЗЫ ДАННЫХ:\n";
echo "======================================\n\n";

$all_filled = true;
$empty_fields = [];

foreach ($tablecrm_options as $option_name) {
    try {
        $stmt = $pdo->prepare("SELECT option_value FROM {$options_table} WHERE option_name = ?");
        $stmt->execute([$option_name]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $display_name = str_replace('tablecrm_complete_', '', $option_name);
        $display_name = str_replace('_', ' ', strtoupper($display_name));
        
        if ($result && !empty(trim($result['option_value']))) {
            $value = trim($result['option_value']);
            // Скрываем часть API ключа для безопасности
            if ($option_name === 'tablecrm_complete_api_key') {
                $masked_value = substr($value, 0, 10) . '...' . substr($value, -10);
                echo "✅ {$display_name}: {$masked_value} (длина: " . strlen($value) . " символов)\n";
            } else {
                echo "✅ {$display_name}: {$value}\n";
            }
        } else {
            echo "❌ {$display_name}: ПУСТОЕ ПОЛЕ!\n";
            $all_filled = false;
            $empty_fields[] = $display_name;
        }
    } catch (PDOException $e) {
        echo "❌ Ошибка при получении {$display_name}: " . $e->getMessage() . "\n";
        $all_filled = false;
    }
}

echo "\n";

if ($all_filled) {
    echo "🎉 ВСЕ ОБЯЗАТЕЛЬНЫЕ ПОЛЯ ЗАПОЛНЕНЫ!\n";
    echo "===================================\n\n";
    
    echo "🔍 ДОПОЛНИТЕЛЬНАЯ ДИАГНОСТИКА:\n";
    echo "==============================\n";
    
    // Проверяем последние логи
    echo "1. Проверим последние ошибки в логах...\n";
    
    // Проверяем активность плагина
    $stmt = $pdo->prepare("SELECT option_value FROM {$options_table} WHERE option_name = 'active_plugins'");
    $stmt->execute();
    $active_plugins = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($active_plugins) {
        $plugins = unserialize($active_plugins['option_value']);
        $tablecrm_active = false;
        foreach ($plugins as $plugin) {
            if (strpos($plugin, 'tablecrm') !== false) {
                $tablecrm_active = true;
                echo "✅ Плагин TableCRM активен: {$plugin}\n";
                break;
            }
        }
        if (!$tablecrm_active) {
            echo "❌ Плагин TableCRM НЕ найден в списке активных плагинов!\n";
        }
    }
    
} else {
    echo "🚨 НАЙДЕНЫ ПУСТЫЕ ПОЛЯ!\n";
    echo "=======================\n";
    echo "Пустые поля: " . implode(', ', $empty_fields) . "\n\n";
    
    echo "🛠️ РЕШЕНИЕ:\n";
    echo "============\n";
    echo "1. Зайдите в WordPress Админка → Настройки → TableCRM Complete\n";
    echo "2. Заполните пустые поля\n";
    echo "3. Нажмите 'Сохранить настройки'\n";
}

echo "\n";
echo "⏰ Время проверки: " . date('Y-m-d H:i:s') . "\n";
echo "🔍 Диагностика завершена.\n";
?> 