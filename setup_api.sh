#!/bin/bash

echo "=========================================="
echo "Быстрая настройка TableCRM API"
echo "=========================================="

API_KEY="af1874616430e04cfd4bce30035789907e899fc7c3a1a4bb27254828ff304a77"

echo "📝 Настройка плагина с предоставленным API ключом..."
echo ""
echo "🔑 API Key: $API_KEY"
echo "🌐 API URL: https://app.tablecrm.com/api/v1/"
echo ""

# Загрузка обновленного плагина
echo "📁 Загрузка обновленного плагина..."

expect << 'EOF'
spawn lftp sftp://<EMAIL>
expect "Пароль:"
send "zl2Shj!e&6ng\r"
expect "lftp"

# Переход в папку плагина
send "cd public_html/wp-content/plugins/tablecrm-integration\r"
expect "lftp"

# Загрузка основного файла плагина
send "put tablecrm-integration.php\r"
expect "lftp"

# Проверка загрузки
send "ls -la tablecrm-integration.php\r"
expect "lftp"

send "quit\r"
expect eof
EOF

echo ""
echo "✅ Плагин обновлен с настройками API!"
echo ""
echo "📋 Следующие шаги:"
echo "1. Перейдите в админ-панель WordPress: /wp-admin/"
echo "2. Активируйте плагин 'TableCRM Integration'"
echo "3. Перейдите в Настройки → TableCRM Integration"
echo "4. Введите настройки:"
echo "   - API URL: https://app.tablecrm.com/api/v1/"
echo "   - API Key: $API_KEY"
echo "   - Project ID: [ваш Project ID из TableCRM]"
echo "5. Нажмите 'Тестировать соединение'"
echo "6. Сохраните настройки"
echo ""
echo "🎯 После настройки плагин будет автоматически:"
echo "• Отправлять заказы WooCommerce в TableCRM"
echo "• Проверять дубликаты товаров и клиентов"
echo "• Собирать UTM-данные и аналитику"
echo "• Передавать данные о доставке и оплате"
echo ""
echo "=========================================="
echo "Настройка завершена!"
echo "==========================================" 