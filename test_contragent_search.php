<?php
// Test contragent search in TableCRM API
echo "=== ТЕСТ ПОИСКА КОНТРАГЕНТОВ В TABLECRM ===\n";

$api_url = 'https://app.tablecrm.com/api/v1/';
$api_token = 'af1874616430e04cfd4bce30035789907e899fc7c3a1a4bb27254828ff304a77';

// Test 1: Get all contragents
echo "1. Получение всех контрагентов (первые 10):\n";
$url1 = $api_url . 'contragents/?token=' . $api_token . '&limit=10&offset=0';
$response1 = file_get_contents($url1);
$data1 = json_decode($response1, true);

if ($data1) {
    echo "Статус: УСПЕХ\n";
    if (isset($data1['result'])) {
        echo "Найдено контрагентов: " . count($data1['result']) . "\n";
        echo "Общее количество: " . $data1['count'] . "\n";
        echo "Первые 3 контрагента:\n";
        foreach (array_slice($data1['result'], 0, 3) as $i => $contragent) {
            echo "  " . ($i+1) . ". ID: {$contragent['id']}, Имя: {$contragent['name']}, Телефон: " . 
                 (isset($contragent['phone']) ? $contragent['phone'] : 'НЕТ') . "\n";
        }
    } else {
        echo "Формат ответа: " . print_r($data1, true) . "\n";
    }
} else {
    echo "Ошибка получения данных\n";
}

// Test 2: Search for specific phone using phone parameter
echo "\n2. Поиск контрагента с телефоном +79377799906 (с параметром phone):\n";
$search_phone = '79377799906'; // Нормализованный номер без +
$url2 = $api_url . 'contragents/?token=' . $api_token . 
        '&limit=100&offset=0&sort=created_at:desc&phone=' . urlencode($search_phone);

echo "URL запроса: $url2\n";
$response2 = file_get_contents($url2);
$data2 = json_decode($response2, true);

if ($data2) {
    echo "Статус: УСПЕХ\n";
    if (isset($data2['result'])) {
        echo "Найдено контрагентов: " . count($data2['result']) . "\n";
        if (!empty($data2['result'])) {
            foreach ($data2['result'] as $i => $contragent) {
                echo "  " . ($i+1) . ". ID: {$contragent['id']}, Имя: {$contragent['name']}, Телефон: " . 
                     (isset($contragent['phone']) ? $contragent['phone'] : 'НЕТ') . "\n";
            }
            echo "✅ КОНТРАГЕНТ НАЙДЕН!\n";
        } else {
            echo "❌ Контрагент не найден\n";
        }
    } else {
        echo "Формат ответа: " . print_r($data2, true) . "\n";
    }
} else {
    echo "❌ Ошибка получения данных\n";
}

// Test 3: Try different phone formats
echo "\n3. Тест разных форматов номера:\n";
$phone_formats = array(
    '+79377799906',
    '79377799906', 
    '9377799906',
    '+7(937)779-99-06'
);

foreach ($phone_formats as $format) {
    echo "Тестируем формат: $format\n";
    $test_url = $api_url . 'contragents/?token=' . $api_token . 
                '&limit=10&offset=0&phone=' . urlencode($format);
    $test_response = file_get_contents($test_url);
    $test_data = json_decode($test_response, true);
    
    if ($test_data && isset($test_data['result'])) {
        echo "  Найдено: " . count($test_data['result']) . " контрагентов\n";
    } else {
        echo "  Ошибка или пустой результат\n";
    }
}

echo "\n=== ТЕСТ ЗАВЕРШЕН ===\n";
?> 