<?php
// Тест исправленной логики отправки данных доставки
echo "=== ТЕСТ ИСПРАВЛЕННОЙ DELIVERY API ===\n";

$wp_path = '/home/<USER>/bekflom3/mosbuketik.ru/public_html';
require_once($wp_path . '/wp-config.php');
require_once($wp_path . '/wp-load.php');

echo "WordPress загружен успешно\n\n";

// Тестируем заказ
$order_id = 21789; // Последний заказ из логов
$order = wc_get_order($order_id);

if (!$order) {
    echo "❌ Заказ #$order_id не найден\n";
    exit;
}

echo "✅ Заказ #$order_id найден\n\n";

// Проверяем данные заказа
echo "=== ДАННЫЕ ЗАКАЗА ===\n";
echo "Статус: " . $order->get_status() . "\n";
echo "Дата создания: " . $order->get_date_created()->format('Y-m-d H:i:s') . "\n";

// Проверяем данные доставки
$delivery_date = $order->get_meta('delivery_date');
$delivery_time = $order->get_meta('delivery_time');
$delivery_type = $order->get_meta('delivery_type');

echo "delivery_date: '$delivery_date'\n";
echo "delivery_time: '$delivery_time'\n";
echo "delivery_type: '$delivery_type'\n\n";

// Тестируем исправленную логику
echo "=== ТЕСТ ИСПРАВЛЕННОЙ ЛОГИКИ ===\n";

function test_fixed_delivery_data($order) {
    // HPOS-совместимое получение мета-данных
    $get_order_meta = function($order, $key) {
        if (method_exists($order, 'get_meta')) {
            return $order->get_meta($key);
        } else {
            return get_post_meta($order->get_id(), $key, true);
        }
    };
    
    $shipping_total = $order->get_shipping_total();
    if ($shipping_total <= 0) {
        return array('error' => 'Нет доставки');
    }
    
    $delivery_data = array();
    
    // Адрес доставки
    $shipping_address = array();
    if ($order->get_shipping_address_1()) {
        $shipping_address[] = $order->get_shipping_address_1();
    }
    if ($order->get_shipping_address_2()) {
        $shipping_address[] = $order->get_shipping_address_2();
    }
    if ($order->get_shipping_city()) {
        $shipping_address[] = $order->get_shipping_city();
    }
    if ($order->get_shipping_postcode()) {
        $shipping_address[] = $order->get_shipping_postcode();
    }
    
    if (!empty($shipping_address)) {
        $delivery_data['address'] = implode(', ', $shipping_address);
    }
    
    // Если нет адреса доставки, используем адрес выставления счета
    if (empty($delivery_data['address'])) {
        $billing_address = array();
        if ($order->get_billing_address_1()) {
            $billing_address[] = $order->get_billing_address_1();
        }
        if ($order->get_billing_address_2()) {
            $billing_address[] = $order->get_billing_address_2();
        }
        if ($order->get_billing_city()) {
            $billing_address[] = $order->get_billing_city();
        }
        if ($order->get_billing_postcode()) {
            $billing_address[] = $order->get_billing_postcode();
        }
        
        if (!empty($billing_address)) {
            $delivery_data['address'] = implode(', ', $billing_address);
        }
    }
    
    // ИСПРАВЛЕНО: Дата доставки как timestamp (integer)
    $delivery_date = $get_order_meta($order, 'delivery_date');
    if (!empty($delivery_date) && $delivery_date !== '0' && strtotime($delivery_date) !== false) {
        $delivery_time = $get_order_meta($order, 'delivery_time');
        if (!empty($delivery_time)) {
            $time_parts = explode(' - ', $delivery_time);
            if (!empty($time_parts[0])) {
                $full_datetime = $delivery_date . ' ' . trim($time_parts[0]) . ':00';
            } else {
                $full_datetime = $delivery_date . ' 12:00:00';
            }
        } else {
            $full_datetime = $delivery_date . ' 12:00:00';
        }
        
        // Конвертируем в timestamp
        $timestamp = strtotime($full_datetime);
        if ($timestamp !== false && $timestamp > 0) {
            $delivery_data['delivery_date'] = $timestamp;
            echo "Дата доставки: $full_datetime → timestamp: $timestamp\n";
        }
    }
    
    // ИСПРАВЛЕНО: Получатель как объект (dict)
    $first_name = $order->get_shipping_first_name() ?: $order->get_billing_first_name();
    $last_name = $order->get_shipping_last_name() ?: $order->get_billing_last_name();
    $phone = $order->get_shipping_phone() ?: $order->get_billing_phone();
    
    // Функция для проверки телефона
    $is_phone = function($str) {
        if (empty($str)) return false;
        return preg_match('/^[\+]?[0-9\(\)\-\s]{7,}$/', $str) || 
               strpos($str, '+7') === 0 || 
               strpos($str, '8(') === 0 ||
               preg_match('/\d{3}.*\d{3}.*\d{2}.*\d{2}/', $str);
    };
    
    $clean_first_name = (!empty($first_name) && !$is_phone($first_name)) ? $first_name : '';
    $clean_last_name = (!empty($last_name) && !$is_phone($last_name)) ? $last_name : '';
    
    // Формируем объект получателя
    $recipient = array();
    if (!empty($clean_first_name)) {
        $recipient['name'] = $clean_first_name;
    }
    if (!empty($clean_last_name)) {
        $recipient['surname'] = $clean_last_name;
    }
    if (!empty($phone)) {
        $recipient['phone'] = $phone;
    }
    
    if (!empty($recipient)) {
        $delivery_data['recipient'] = $recipient;
    }
    
    // Примечание
    $note_parts = array();
    
    $shipping_method = $order->get_shipping_method();
    if ($shipping_method) {
        $note_parts[] = "Способ доставки: " . $shipping_method;
    }
    
    if ($shipping_total > 0) {
        $note_parts[] = "Стоимость доставки: " . $shipping_total . " руб.";
    }
    
    if (!empty($delivery_time)) {
        $note_parts[] = "Время доставки: " . $delivery_time;
    }
    
    if (!empty($delivery_type)) {
        $note_parts[] = "Тип доставки: " . $delivery_type;
    }
    
    $customer_note = $order->get_customer_note();
    if (!empty($customer_note)) {
        $note_parts[] = "Комментарий клиента: " . $customer_note;
    }
    
    if (!empty($note_parts)) {
        $delivery_data['note'] = implode('. ', $note_parts);
    }
    
    return $delivery_data;
}

$fixed_delivery_data = test_fixed_delivery_data($order);

echo "\n=== ИСПРАВЛЕННЫЕ ДАННЫЕ ДОСТАВКИ ===\n";
echo json_encode($fixed_delivery_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";

echo "=== КЛЮЧЕВЫЕ ИСПРАВЛЕНИЯ ===\n";
echo "✅ delivery_date теперь timestamp (integer): " . (isset($fixed_delivery_data['delivery_date']) ? $fixed_delivery_data['delivery_date'] : 'не установлено') . "\n";
echo "✅ recipient теперь объект (dict): " . (isset($fixed_delivery_data['recipient']) ? 'да' : 'нет') . "\n";

if (isset($fixed_delivery_data['recipient'])) {
    echo "   - name: " . (isset($fixed_delivery_data['recipient']['name']) ? $fixed_delivery_data['recipient']['name'] : 'не установлено') . "\n";
    echo "   - surname: " . (isset($fixed_delivery_data['recipient']['surname']) ? $fixed_delivery_data['recipient']['surname'] : 'не установлено') . "\n";
    echo "   - phone: " . (isset($fixed_delivery_data['recipient']['phone']) ? $fixed_delivery_data['recipient']['phone'] : 'не установлено') . "\n";
}

echo "\n=== СРАВНЕНИЕ С ОШИБКОЙ ===\n";
echo "❌ БЫЛО (ошибка 422):\n";
echo "   delivery_date: \"2025-06-10 08:00:00\" (string)\n";
echo "   recipient: \"Рушанн\" (string)\n\n";

echo "✅ СТАЛО (должно работать):\n";
echo "   delivery_date: " . (isset($fixed_delivery_data['delivery_date']) ? $fixed_delivery_data['delivery_date'] . " (integer)" : 'не установлено') . "\n";
echo "   recipient: объект с полями name, surname, phone\n";

echo "\n=== ТЕСТ ЗАВЕРШЕН ===\n";
?> 