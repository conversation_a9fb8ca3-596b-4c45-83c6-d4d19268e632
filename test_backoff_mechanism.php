<?php
/**
 * Тестовый скрипт для проверки экспоненциального back-off механизма
 * Версия: 1.1.1
 * 
 * Использование:
 * 1. Поместите этот файл в корень WordPress
 * 2. Откройте в браузере: your-site.com/test_backoff_mechanism.php
 * 3. Проверьте работу с разными типами ошибок
 */

// Предотвращение прямого доступа
if (!defined('ABSPATH')) {
    // Эмулируем окружение WordPress для тестирования
    define('ABSPATH', dirname(__FILE__) . '/');
    
    // Если файл запущен напрямую, подключаем WordPress
    if (file_exists(ABSPATH . 'wp-config.php')) {
        require_once(ABSPATH . 'wp-config.php');
    } else {
        echo "<h2>❌ Ошибка: WordPress не найден</h2>";
        echo "<p>Поместите этот файл в корень WordPress сайта</p>";
        exit;
    }
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>🧪 Тест экспоненциального back-off v1.1.1</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f0f0f0; }
        .container { background: white; padding: 20px; border-radius: 8px; max-width: 800px; }
        .test-case { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .warning { background: #fff3cd; border-color: #ffeaa7; }
        .info { background: #d1ecf1; border-color: #bee5eb; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .log-entry { margin: 5px 0; padding: 5px; border-left: 3px solid #007cba; }
        button { padding: 8px 16px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #005a87; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Тест экспоненциального back-off механизма</h1>
        <p><strong>Версия плагина:</strong> TableCRM Integration v1.1.1</p>
        
        <?php
        
        // Проверяем наличие плагина
        if (!class_exists('TableCRM_Integration')) {
            echo '<div class="test-case error">';
            echo '<h3>❌ Плагин TableCRM Integration не активен</h3>';
            echo '<p>Активируйте плагин TableCRM Integration v1.1.1 для тестирования</p>';
            echo '</div></body></html>';
            exit;
        }
        
        // Получаем экземпляр плагина
        $tablecrm = new TableCRM_Integration();
        
        // Проверяем версию
        $version = defined('TABLECRM_VERSION') ? TABLECRM_VERSION : 'неизвестная';
        echo "<div class='test-case info'>";
        echo "<h3>ℹ️ Информация о плагине</h3>";
        echo "<p><strong>Версия:</strong> $version</p>";
        echo "<p><strong>HPOS:</strong> " . (method_exists($tablecrm, 'init_hpos_support') ? '✅ Поддерживается' : '❌ Не поддерживается') . "</p>";
        echo "</div>";
        
        if (isset($_GET['test'])) {
            $test_type = sanitize_text_field($_GET['test']);
            
            echo "<div class='test-case warning'>";
            echo "<h3>🔄 Выполняется тест: " . ucfirst($test_type) . "</h3>";
            echo "<p>Пожалуйста, подождите... (может занять до 8 минут)</p>";
            echo "</div>";
            
            // Буферизация вывода для показа логов в реальном времени
            ob_start();
            
            switch ($test_type) {
                case 'network_error':
                    test_network_error($tablecrm);
                    break;
                case 'server_error':
                    test_server_error($tablecrm);
                    break;
                case 'auth_error':
                    test_auth_error($tablecrm);
                    break;
                case 'success':
                    test_success($tablecrm);
                    break;
                default:
                    echo "<div class='test-case error'><h3>❌ Неизвестный тип теста</h3></div>";
            }
            
            ob_end_flush();
        } else {
            // Показываем меню тестов
            echo "<div class='test-case'>";
            echo "<h3>🎯 Выберите тип теста:</h3>";
            echo "<p>Каждый тест проверяет разные сценарии работы экспоненциального back-off:</p>";
            echo "<button onclick=\"location.href='?test=network_error'\">🌐 Тест сетевых ошибок</button>";
            echo "<button onclick=\"location.href='?test=server_error'\">🖥️ Тест серверных ошибок</button>";
            echo "<button onclick=\"location.href='?test=auth_error'\">🔑 Тест ошибок авторизации</button>";
            echo "<button onclick=\"location.href='?test=success'\">✅ Тест успешного запроса</button>";
            echo "</div>";
            
            echo "<div class='test-case info'>";
            echo "<h3>📊 Ожидаемое поведение:</h3>";
            echo "<ul>";
            echo "<li><strong>Сетевые ошибки:</strong> 3 попытки с задержками 0→30→120 сек</li>";
            echo "<li><strong>Серверные ошибки (5xx):</strong> 3 попытки с задержками</li>";
            echo "<li><strong>Ошибки авторизации (401/403):</strong> Без повторов (fail fast)</li>";
            echo "<li><strong>Успешный запрос:</strong> 1 попытка, успех</li>";
            echo "</ul>";
            echo "</div>";
        }
        
        /**
         * Тест сетевых ошибок (timeout, connection failed)
         */
        function test_network_error($tablecrm) {
            echo "<div class='test-case warning'>";
            echo "<h3>🌐 Тест сетевых ошибок</h3>";
            echo "<p>Попытка подключения к несуществующему хосту...</p>";
            echo "</div>";
            
            // Подменяем URL на несуществующий хост для имитации сетевой ошибки
            $fake_url = 'https://non-existent-host-for-testing.invalid/api/v1/';
            $fake_key = 'test_key_12345';
            
            $test_data = array(
                'name' => 'Тестовый пользователь',
                'email' => '<EMAIL>',
                'phone' => '+7(999)123-45-67',
                'order_id' => 'TEST_' . time()
            );
            
            $start_time = time();
            echo "<div class='test-case'>";
            echo "<pre id='log-output'>";
            echo "[" . date('H:i:s') . "] Начало теста сетевых ошибок\n";
            
            // Используем рефлексию для вызова приватного метода
            $reflection = new ReflectionClass($tablecrm);
            $method = $reflection->getMethod('make_api_request');
            $method->setAccessible(true);
            
            $result = $method->invoke($tablecrm, 'docs_sales/', $test_data, $fake_url, $fake_key);
            
            $end_time = time();
            $duration = $end_time - $start_time;
            
            echo "[" . date('H:i:s') . "] Завершение теста\n";
            echo "[" . date('H:i:s') . "] Общее время выполнения: {$duration} секунд\n";
            echo "</pre>";
            echo "</div>";
            
            // Анализ результата
            echo "<div class='test-case " . ($result['success'] ? 'error' : 'success') . "'>";
            echo "<h4>" . ($result['success'] ? '❌ Неожиданный успех' : '✅ Ожидаемая неудача') . "</h4>";
            echo "<p><strong>Результат:</strong> " . ($result['success'] ? 'Успех' : 'Неудача') . "</p>";
            echo "<p><strong>Сообщение:</strong> " . htmlspecialchars($result['message']) . "</p>";
            echo "<p><strong>Время выполнения:</strong> {$duration} секунд (ожидалось ~150+ секунд для 3 попыток)</p>";
            echo "</div>";
        }
        
        /**
         * Тест серверных ошибок (5xx)
         */
        function test_server_error($tablecrm) {
            echo "<div class='test-case warning'>";
            echo "<h3>🖥️ Тест серверных ошибок</h3>";
            echo "<p>Имитация ошибки 500 Internal Server Error...</p>";
            echo "</div>";
            
            // Используем httpbin.org для имитации ошибки 500
            $fake_url = 'https://httpbin.org/status/500';
            $fake_key = 'test_key_12345';
            
            $test_data = array('test' => 'data');
            
            $start_time = time();
            echo "<div class='test-case'>";
            echo "<pre>";
            echo "[" . date('H:i:s') . "] Начало теста серверных ошибок\n";
            
            $reflection = new ReflectionClass($tablecrm);
            $method = $reflection->getMethod('make_api_request');
            $method->setAccessible(true);
            
            $result = $method->invoke($tablecrm, 'test', $test_data, $fake_url, $fake_key);
            
            $end_time = time();
            $duration = $end_time - $start_time;
            
            echo "[" . date('H:i:s') . "] Завершение теста\n";
            echo "[" . date('H:i:s') . "] Общее время выполнения: {$duration} секунд\n";
            echo "</pre>";
            echo "</div>";
            
            echo "<div class='test-case " . ($result['success'] ? 'error' : 'success') . "'>";
            echo "<h4>" . ($result['success'] ? '❌ Неожиданный успех' : '✅ Ожидаемая неудача') . "</h4>";
            echo "<p><strong>Результат:</strong> " . ($result['success'] ? 'Успех' : 'Неудача') . "</p>";
            echo "<p><strong>Сообщение:</strong> " . htmlspecialchars($result['message']) . "</p>";
            echo "<p><strong>Время выполнения:</strong> {$duration} секунд</p>";
            echo "</div>";
        }
        
        /**
         * Тест ошибок авторизации (должны завершаться быстро без повторов)
         */
        function test_auth_error($tablecrm) {
            echo "<div class='test-case warning'>";
            echo "<h3>🔑 Тест ошибок авторизации</h3>";
            echo "<p>Имитация ошибки 401 Unauthorized...</p>";
            echo "</div>";
            
            // Используем httpbin.org для имитации ошибки 401
            $fake_url = 'https://httpbin.org/status/401';
            $fake_key = 'invalid_key';
            
            $test_data = array('test' => 'data');
            
            $start_time = time();
            echo "<div class='test-case'>";
            echo "<pre>";
            echo "[" . date('H:i:s') . "] Начало теста ошибок авторизации\n";
            
            $reflection = new ReflectionClass($tablecrm);
            $method = $reflection->getMethod('make_api_request');
            $method->setAccessible(true);
            
            $result = $method->invoke($tablecrm, 'test', $test_data, $fake_url, $fake_key);
            
            $end_time = time();
            $duration = $end_time - $start_time;
            
            echo "[" . date('H:i:s') . "] Завершение теста\n";
            echo "[" . date('H:i:s') . "] Общее время выполнения: {$duration} секунд\n";
            echo "</pre>";
            echo "</div>";
            
            echo "<div class='test-case " . ($duration < 10 ? 'success' : 'warning') . "'>";
            echo "<h4>" . ($duration < 10 ? '✅ Быстрый отказ (fail fast)' : '⚠️ Медленный отказ') . "</h4>";
            echo "<p><strong>Результат:</strong> " . ($result['success'] ? 'Успех' : 'Неудача') . "</p>";
            echo "<p><strong>Сообщение:</strong> " . htmlspecialchars($result['message']) . "</p>";
            echo "<p><strong>Время выполнения:</strong> {$duration} секунд (ожидалось < 10 секунд для fail fast)</p>";
            echo "</div>";
        }
        
        /**
         * Тест успешного запроса
         */
        function test_success($tablecrm) {
            echo "<div class='test-case warning'>";
            echo "<h3>✅ Тест успешного запроса</h3>";
            echo "<p>Проверка реального API TableCRM...</p>";
            echo "</div>";
            
            // Используем реальные настройки плагина
            $api_url = get_option('tablecrm_api_url');
            $api_key = function_exists('tablecrm_get_api_key') ? tablecrm_get_api_key() : '';
            
            if (empty($api_url) || empty($api_key)) {
                echo "<div class='test-case error'>";
                echo "<h4>❌ API не настроен</h4>";
                echo "<p>Для этого теста необходимо настроить API URL и ключ в настройках плагина</p>";
                echo "</div>";
                return;
            }
            
            $start_time = time();
            echo "<div class='test-case'>";
            echo "<pre>";
            echo "[" . date('H:i:s') . "] Начало теста успешного запроса\n";
            echo "[" . date('H:i:s') . "] API URL: " . substr($api_url, 0, 30) . "...\n";
            echo "[" . date('H:i:s') . "] API Key: " . substr($api_key, 0, 10) . "...\n";
            
            $reflection = new ReflectionClass($tablecrm);
            $method = $reflection->getMethod('make_api_request');
            $method->setAccessible(true);
            
            // Тестируем health эндпоинт
            $result = $method->invoke($tablecrm, 'health', array());
            
            $end_time = time();
            $duration = $end_time - $start_time;
            
            echo "[" . date('H:i:s') . "] Завершение теста\n";
            echo "[" . date('H:i:s') . "] Общее время выполнения: {$duration} секунд\n";
            echo "</pre>";
            echo "</div>";
            
            echo "<div class='test-case " . ($result['success'] ? 'success' : 'error') . "'>";
            echo "<h4>" . ($result['success'] ? '✅ Успешное подключение' : '❌ Ошибка подключения') . "</h4>";
            echo "<p><strong>Результат:</strong> " . ($result['success'] ? 'Успех' : 'Неудача') . "</p>";
            echo "<p><strong>Сообщение:</strong> " . htmlspecialchars($result['message']) . "</p>";
            echo "<p><strong>Время выполнения:</strong> {$duration} секунд</p>";
            echo "</div>";
        }
        
        ?>
        
        <div class="test-case info">
            <h3>📝 Анализ логов</h3>
            <p>Для детального анализа работы back-off механизма проверьте логи WordPress:</p>
            <pre>tail -f /wp-content/debug.log | grep "TableCRM"</pre>
            <p>Или откройте файл прямо в админке WordPress через плагины управления логами.</p>
        </div>
        
        <div class="test-case">
            <h3>🔄 Новый тест</h3>
            <button onclick="location.href='test_backoff_mechanism.php'">🔙 Вернуться к выбору тестов</button>
            <button onclick="location.reload()">🔄 Перезагрузить страницу</button>
        </div>
    </div>
</body>
</html> 