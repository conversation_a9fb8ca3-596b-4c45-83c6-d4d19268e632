# 🔧 РЕШЕНИЕ ПРОБЛЕМЫ С НАЗВАНИЯМИ ТОВАРОВ В TABLECRM

## 📋 ДИАГНОСТИКА ПРОБЛЕМЫ

### Что обнаружено:
- ✅ Плагин TableCRM Integration v1.0.15 создан и содержит исправления
- ✅ Исправление названий товаров присутствует в коде (строка 866)
- ❌ На сервере установлена СТАРАЯ версия плагина БЕЗ исправления названий
- ❌ В логах видно, что товары отправляются без поля `"name"`

### Анализ логов:
```
"goods":[{"price":4999,"quantity":1,"unit":116,"discount":0,"sum_discounted":0,"nomenclature":39697}]
```
**Проблема:** Отсутствует поле `"name"` в данных товаров!

**Должно быть:**
```
"goods":[{"name":"Букет роз","price":4999,"quantity":1,"unit":116,"discount":0,"sum_discounted":0,"nomenclature":39697}]
```

## 🚀 РЕШЕНИЕ

### Шаг 1: Загрузка исправленного плагина
1. Скачайте файл `tablecrm-integration-enhanced-v1.0.15-FINAL.zip`
2. Войдите в админку WordPress: `https://mos-buketik.ru/wp-admin/`
3. Перейдите в **Плагины** → **Добавить новый** → **Загрузить плагин**
4. Выберите файл `tablecrm-integration-enhanced-v1.0.15-FINAL.zip`
5. Нажмите **Установить**

### Шаг 2: Активация нового плагина
1. В списке плагинов найдите **TableCRM Integration (Fixed v3 - Security Enhanced)**
2. **ДЕАКТИВИРУЙТЕ** старую версию (если активна)
3. **АКТИВИРУЙТЕ** новую версию v1.0.15

### Шаг 3: Проверка настроек
1. Перейдите в **Настройки** → **TableCRM Integration (Fixed v3)**
2. Убедитесь что настройки сохранены:
   - API URL: `https://app.tablecrm.com/api/v1/`
   - API Key: ваш ключ
   - Organization ID: `38`

### Шаг 4: Тестирование
1. Создайте тестовый заказ на сайте
2. Проверьте логи в TableCRM
3. Убедитесь что названия товаров отображаются

## 🔍 КЛЮЧЕВЫЕ ИСПРАВЛЕНИЯ В v1.0.15

### 1. Названия товаров (строка 866):
```php
'name' => isset($item['name']) ? sanitize_text_field($item['name']) : 'Товар',
```

### 2. Обработка вариаций товаров:
```php
$product_name = $item->get_name();
if (!empty($variation_data)) {
    $product_name .= ' (' . implode(', ', $variation_data) . ')';
}
```

### 3. Исправление email валидации:
```php
if (empty($billing_email)) {
    if (!empty($billing_phone)) {
        $clean_phone = preg_replace('/[^0-9]/', '', $billing_phone);
        $billing_email = $clean_phone . '@phone.mosbuketik.ru';
    } else {
        $billing_email = '<EMAIL>';
    }
}
```

### 4. Динамические контрагенты:
```php
'contragent' => intval($contragent_id), // Вместо фиксированного 330985
```

## 📊 ОЖИДАЕМЫЙ РЕЗУЛЬТАТ

После установки v1.0.15 в TableCRM будут отображаться:
- ✅ **Правильные названия товаров** вместо "Testing"
- ✅ Размеры и цвета товаров (вариации)
- ✅ Скидки и суммы со скидкой
- ✅ Адреса доставки в комментариях
- ✅ UTM аналитика
- ✅ Уникальные контрагенты для каждого клиента

## 🚨 ВАЖНО

Если после активации v1.0.15 проблема не решится:
1. Очистите кэш WordPress
2. Деактивируйте/активируйте плагин повторно
3. Проверьте конфликты с другими плагинами
4. Создайте новый тестовый заказ (старые заказы не изменятся)

## 📋 КОНТРОЛЬНАЯ ПРОВЕРКА

Создайте тестовый заказ и убедитесь что в логах появилась строка:
```
"goods":[{"name":"Название товара","price":...}]
```

Если поле `"name"` появилось - проблема решена! 🎉 