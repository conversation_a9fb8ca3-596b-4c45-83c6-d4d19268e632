# Upload settings check script
$server = "bekflom3.beget.tech"
$user = "bekflom3_wz"
$pass = "zl2Shj!e&6ng"

Write-Host "Uploading check_plugin_settings.php..." -ForegroundColor Green

try {
    $uri = "ftp://$server/public_html/check_plugin_settings.php"
    $webclient = New-Object System.Net.WebClient
    $webclient.Credentials = New-Object System.Net.NetworkCredential($user, $pass)
    $webclient.UploadFile($uri, "check_plugin_settings.php")
    Write-Host "File uploaded successfully!" -ForegroundColor Green
    
    # Now run the script
    Write-Host "Running the check script..." -ForegroundColor Yellow
    $result = Invoke-WebRequest -Uri "https://mosbuketik.ru/check_plugin_settings.php" -UseBasicParsing
    Write-Host $result.Content -ForegroundColor White
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

$webclient.Dispose()
Write-Host "Done!" -ForegroundColor Green 