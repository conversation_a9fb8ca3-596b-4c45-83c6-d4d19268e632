<?php
// Исправление API доставки - диагностика и тест
echo "=== ДИАГНОСТИКА И ИСПРАВЛЕНИЕ API ДОСТАВКИ ===\n";

$wp_path = '/home/<USER>/bekflom3/mosbuketik.ru/public_html';
require_once($wp_path . '/wp-config.php');
require_once($wp_path . '/wp-load.php');

echo "WordPress загружен успешно\n\n";

// Тестируем заказ из скриншота
$order_id = 21780; // Заказ из скриншота
$order = wc_get_order($order_id);

if (!$order) {
    echo "❌ Заказ #$order_id не найден\n";
    exit;
}

echo "✅ Заказ #$order_id найден\n";
echo "Статус: " . $order->get_status() . "\n";
echo "Стоимость доставки: " . $order->get_shipping_total() . "\n\n";

// Получаем данные заказа
echo "=== ДАННЫЕ ЗАКАЗА ===\n";
echo "billing_first_name: '" . $order->get_billing_first_name() . "'\n";
echo "billing_last_name: '" . $order->get_billing_last_name() . "'\n";
echo "billing_phone: '" . $order->get_billing_phone() . "'\n";
echo "shipping_first_name: '" . $order->get_shipping_first_name() . "'\n";
echo "shipping_last_name: '" . $order->get_shipping_last_name() . "'\n";
echo "shipping_phone: '" . $order->get_shipping_phone() . "'\n\n";

// Функция для проверки, является ли строка телефоном
$is_phone = function($str) {
    if (empty($str)) return false;
    // Проверяем на наличие телефонных паттернов
    return preg_match('/^[\+]?[0-9\(\)\-\s]{7,}$/', $str) || 
           strpos($str, '+7') === 0 || 
           strpos($str, '8(') === 0 ||
           preg_match('/\d{3}.*\d{3}.*\d{2}.*\d{2}/', $str);
};

// Формируем данные доставки как в текущем коде
$delivery_data = array();

// Адрес доставки
$shipping_address = array();
if ($order->get_shipping_address_1()) {
    $shipping_address[] = $order->get_shipping_address_1();
}
if ($order->get_shipping_address_2()) {
    $shipping_address[] = $order->get_shipping_address_2();
}
if ($order->get_shipping_city()) {
    $shipping_address[] = $order->get_shipping_city();
}
if ($order->get_shipping_postcode()) {
    $shipping_address[] = $order->get_shipping_postcode();
}

if (!empty($shipping_address)) {
    $delivery_data['address'] = implode(', ', $shipping_address);
}

// Если нет адреса доставки, используем адрес выставления счета
if (empty($delivery_data['address'])) {
    $billing_address = array();
    if ($order->get_billing_address_1()) {
        $billing_address[] = $order->get_billing_address_1();
    }
    if ($order->get_billing_address_2()) {
        $billing_address[] = $order->get_billing_address_2();
    }
    if ($order->get_billing_city()) {
        $billing_address[] = $order->get_billing_city();
    }
    if ($order->get_billing_postcode()) {
        $billing_address[] = $order->get_billing_postcode();
    }
    
    if (!empty($billing_address)) {
        $delivery_data['address'] = implode(', ', $billing_address);
    }
}

// Дата доставки
$delivery_data['delivery_date'] = '1970-01-01 00:00:00+00'; // Как в скриншоте

// ИСПРАВЛЕННЫЙ получатель - отправляем как строку, а не как объект
$first_name = $order->get_shipping_first_name() ?: $order->get_billing_first_name();
$last_name = $order->get_shipping_last_name() ?: $order->get_billing_last_name();
$phone = $order->get_shipping_phone() ?: $order->get_billing_phone();

// Очищаем имя и фамилию от телефонов
$clean_first_name = (!empty($first_name) && !$is_phone($first_name)) ? $first_name : '';
$clean_last_name = (!empty($last_name) && !$is_phone($last_name)) ? $last_name : '';

// Формируем строку получателя
$recipient_parts = array();
if (!empty($clean_first_name)) {
    $recipient_parts[] = $clean_first_name;
}
if (!empty($clean_last_name)) {
    $recipient_parts[] = $clean_last_name;
}

$recipient_name = implode(' ', $recipient_parts);

// ВАРИАНТ 1: Отправляем recipient как строку (как ожидает API)
$delivery_data['recipient'] = $recipient_name;

// Примечание с телефоном
$note_parts = array();

$shipping_method = $order->get_shipping_method();
if ($shipping_method) {
    $note_parts[] = "Способ доставки: " . $shipping_method;
}

$shipping_total = $order->get_shipping_total();
if ($shipping_total > 0) {
    $note_parts[] = "Стоимость доставки: " . $shipping_total . " руб.";
}

if (!empty($phone)) {
    $note_parts[] = "Телефон: " . $phone;
}

$customer_note = $order->get_customer_note();
if (!empty($customer_note)) {
    $note_parts[] = "Комментарий клиента: " . $customer_note;
}

if (!empty($note_parts)) {
    $delivery_data['note'] = implode('. ', $note_parts);
}

echo "=== ИСПРАВЛЕННЫЕ ДАННЫЕ ДОСТАВКИ (ВАРИАНТ 1 - recipient как строка) ===\n";
echo json_encode($delivery_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";

// ВАРИАНТ 2: Отправляем recipient как объект, но с правильной структурой
$delivery_data_v2 = $delivery_data;
$recipient_object = array();

if (!empty($clean_first_name)) {
    $recipient_object['name'] = $clean_first_name;
}
if (!empty($clean_last_name)) {
    $recipient_object['surname'] = $clean_last_name;
}
if (!empty($phone)) {
    $recipient_object['phone'] = $phone;
}

if (!empty($recipient_object)) {
    $delivery_data_v2['recipient'] = $recipient_object;
} else {
    unset($delivery_data_v2['recipient']);
}

echo "=== ИСПРАВЛЕННЫЕ ДАННЫЕ ДОСТАВКИ (ВАРИАНТ 2 - recipient как объект) ===\n";
echo json_encode($delivery_data_v2, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";

// Тестируем отправку (если есть document_id)
$document_id = get_post_meta($order_id, '_tablecrm_document_id', true);
if (empty($document_id)) {
    $document_id = 138304; // Из скриншота
}

echo "=== ТЕСТ ОТПРАВКИ ===\n";
echo "Document ID: $document_id\n";

// Получаем настройки API
$api_url = get_option('tablecrm_complete_api_url', 'https://app.tablecrm.com/api/v1/');
$api_key = get_option('tablecrm_complete_api_key');

if (empty($api_key)) {
    echo "❌ API ключ не настроен\n";
    exit;
}

$delivery_url = rtrim($api_url, '/') . '/docs_sales/' . $document_id . '/delivery_info/?token=' . $api_key;

echo "URL: $delivery_url\n";

// Тестируем ВАРИАНТ 1 (recipient как строка)
echo "\n--- ТЕСТ ВАРИАНТА 1 (recipient как строка) ---\n";
$response1 = wp_remote_post($delivery_url, array(
    'method' => 'POST',
    'headers' => array(
        'Content-Type' => 'application/json; charset=utf-8',
        'User-Agent' => 'WordPress-TableCRM-Integration-Complete/1.0.24'
    ),
    'body' => json_encode($delivery_data, JSON_UNESCAPED_UNICODE),
    'timeout' => 30,
    'sslverify' => true
));

if (is_wp_error($response1)) {
    echo "❌ Ошибка HTTP: " . $response1->get_error_message() . "\n";
} else {
    $status1 = wp_remote_retrieve_response_code($response1);
    $body1 = wp_remote_retrieve_body($response1);
    echo "HTTP Status: $status1\n";
    echo "Response: " . substr($body1, 0, 500) . "\n";
    
    if ($status1 >= 200 && $status1 < 300) {
        echo "✅ ВАРИАНТ 1 УСПЕШЕН!\n";
    } else {
        echo "❌ ВАРИАНТ 1 НЕУСПЕШЕН\n";
    }
}

// Небольшая пауза
sleep(1);

// Тестируем ВАРИАНТ 2 (recipient как объект)
echo "\n--- ТЕСТ ВАРИАНТА 2 (recipient как объект) ---\n";
$response2 = wp_remote_post($delivery_url, array(
    'method' => 'POST',
    'headers' => array(
        'Content-Type' => 'application/json; charset=utf-8',
        'User-Agent' => 'WordPress-TableCRM-Integration-Complete/1.0.24'
    ),
    'body' => json_encode($delivery_data_v2, JSON_UNESCAPED_UNICODE),
    'timeout' => 30,
    'sslverify' => true
));

if (is_wp_error($response2)) {
    echo "❌ Ошибка HTTP: " . $response2->get_error_message() . "\n";
} else {
    $status2 = wp_remote_retrieve_response_code($response2);
    $body2 = wp_remote_retrieve_body($response2);
    echo "HTTP Status: $status2\n";
    echo "Response: " . substr($body2, 0, 500) . "\n";
    
    if ($status2 >= 200 && $status2 < 300) {
        echo "✅ ВАРИАНТ 2 УСПЕШЕН!\n";
    } else {
        echo "❌ ВАРИАНТ 2 НЕУСПЕШЕН\n";
    }
}

echo "\n=== ДИАГНОСТИКА ЗАВЕРШЕНА ===\n";
echo "Проверьте результаты в TableCRM и выберите подходящий вариант для исправления.\n";
?> 