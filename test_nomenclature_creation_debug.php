<?php
/**
 * Диагностический скрипт для проверки создания номенклатуры TableCRM
 * Загрузите этот файл на сервер и запустите через браузер
 */

// Подключение WordPress
require_once(dirname(__FILE__) . '/../../../wp-config.php');

echo "<h1>🔧 Диагностика создания номенклатуры TableCRM</h1>";

// Получение настроек
$api_url = get_option('tablecrm_fixed_v3_api_url', 'https://app.tablecrm.com/api/v1/');
$api_key = get_option('tablecrm_fixed_v3_api_key');

echo "<h2>⚙️ Настройки:</h2>";
echo "<p><strong>API URL:</strong> " . esc_html($api_url) . "</p>";
echo "<p><strong>API Key:</strong> " . (empty($api_key) ? "❌ НЕ УСТАНОВЛЕН" : "✅ Установлен (длина: " . strlen($api_key) . ")") . "</p>";

if (empty($api_key)) {
    echo "<p style='color: red;'><strong>ОШИБКА:</strong> API ключ не настроен!</p>";
    exit;
}

echo "<h2>🧪 Тест создания номенклатуры:</h2>";

// Тестовые данные для номенклатуры
$test_data = array(
    'name' => 'ТЕСТ - Букет из 39 альстромерий',
    'price' => 4999,
    'sku' => 'test_product_' . time(),
    'unit' => 116,
    'tax' => 2
);

echo "<h3>📝 Данные для создания:</h3>";
echo "<pre>" . json_encode($test_data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "</pre>";

// Формирование запроса
$create_url = rtrim($api_url, '/') . '/nomenclatures/?token=' . $api_key;
$request_data = array($test_data);

echo "<h3>🌐 API запрос:</h3>";
echo "<p><strong>URL:</strong> " . esc_html($create_url) . "</p>";
echo "<p><strong>Метод:</strong> POST</p>";
echo "<p><strong>Body:</strong></p>";
echo "<pre>" . json_encode($request_data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "</pre>";

// Выполнение запроса
$response = wp_remote_post($create_url, array(
    'timeout' => 30,
    'sslverify' => true,
    'headers' => array(
        'Content-Type' => 'application/json; charset=utf-8',
        'User-Agent' => 'WordPress-TableCRM-Integration-v3/1.0.22-debug'
    ),
    'body' => json_encode($request_data, JSON_UNESCAPED_UNICODE)
));

echo "<h3>📡 Ответ API:</h3>";

if (is_wp_error($response)) {
    $error_message = $response->get_error_message();
    echo "<p style='color: red;'><strong>ОШИБКА HTTP:</strong> " . esc_html($error_message) . "</p>";
} else {
    $status_code = wp_remote_retrieve_response_code($response);
    $body = wp_remote_retrieve_body($response);
    $headers = wp_remote_retrieve_headers($response);
    
    echo "<p><strong>HTTP статус:</strong> " . $status_code . "</p>";
    
    if ($status_code === 201 || $status_code === 200) {
        echo "<p style='color: green;'><strong>✅ УСПЕХ!</strong> Номенклатура создана.</p>";
    } else {
        echo "<p style='color: red;'><strong>❌ ОШИБКА!</strong> Номенклатура не создана.</p>";
    }
    
    echo "<h4>📄 Тело ответа:</h4>";
    echo "<pre>" . esc_html($body) . "</pre>";
    
    echo "<h4>📋 Заголовки ответа:</h4>";
    echo "<pre>";
    foreach ($headers as $header => $value) {
        echo esc_html($header) . ": " . esc_html($value) . "\n";
    }
    echo "</pre>";
    
    // Попытка декодирования JSON
    $decoded = json_decode($body, true);
    if ($decoded !== null) {
        echo "<h4>🔍 Декодированный JSON:</h4>";
        echo "<pre>" . print_r($decoded, true) . "</pre>";
        
        if (is_array($decoded) && count($decoded) > 0 && isset($decoded[0]['id'])) {
            $nomenclature_id = $decoded[0]['id'];
            echo "<p style='color: green;'><strong>✅ ID созданной номенклатуры:</strong> " . $nomenclature_id . "</p>";
        } elseif (isset($decoded['id'])) {
            $nomenclature_id = $decoded['id'];
            echo "<p style='color: green;'><strong>✅ ID созданной номенклатуры:</strong> " . $nomenclature_id . "</p>";
        } else {
            echo "<p style='color: orange;'><strong>⚠️ ВНИМАНИЕ:</strong> В ответе нет ID номенклатуры.</p>";
        }
    } else {
        echo "<p style='color: red;'><strong>❌ ОШИБКА:</strong> Ответ не является валидным JSON.</p>";
    }
}

echo "<h2>🔍 Тест поиска номенклатуры:</h2>";

// Тест поиска существующей номенклатуры
$search_sku = 'product_id_20092';
$search_url = rtrim($api_url, '/') . '/nomenclatures/?token=' . $api_key . '&sku=' . urlencode($search_sku);

echo "<p><strong>Поиск SKU:</strong> " . esc_html($search_sku) . "</p>";
echo "<p><strong>URL поиска:</strong> " . esc_html($search_url) . "</p>";

$search_response = wp_remote_get($search_url, array(
    'timeout' => 30,
    'sslverify' => true,
    'headers' => array(
        'User-Agent' => 'WordPress-TableCRM-Integration-v3/1.0.22-debug'
    )
));

if (is_wp_error($search_response)) {
    $error_message = $search_response->get_error_message();
    echo "<p style='color: red;'><strong>ОШИБКА ПОИСКА:</strong> " . esc_html($error_message) . "</p>";
} else {
    $search_status = wp_remote_retrieve_response_code($search_response);
    $search_body = wp_remote_retrieve_body($search_response);
    
    echo "<p><strong>HTTP статус поиска:</strong> " . $search_status . "</p>";
    echo "<h4>📄 Результат поиска:</h4>";
    echo "<pre>" . esc_html($search_body) . "</pre>";
    
    $search_decoded = json_decode($search_body, true);
    if ($search_decoded !== null) {
        echo "<h4>🔍 Декодированный результат поиска:</h4>";
        echo "<pre>" . print_r($search_decoded, true) . "</pre>";
        
        if (isset($search_decoded['results']) && is_array($search_decoded['results']) && count($search_decoded['results']) > 0) {
            echo "<p style='color: green;'><strong>✅ Найдена номенклатура:</strong> ID " . $search_decoded['results'][0]['id'] . "</p>";
        } elseif (is_array($search_decoded) && count($search_decoded) > 0 && isset($search_decoded[0]['id'])) {
            echo "<p style='color: green;'><strong>✅ Найдена номенклатура:</strong> ID " . $search_decoded[0]['id'] . "</p>";
        } else {
            echo "<p style='color: orange;'><strong>⚠️ Номенклатура не найдена.</strong></p>";
        }
    }
}

echo "<h2>💡 Рекомендации:</h2>";
echo "<ul>";
echo "<li>Если создание номенклатуры не работает, проверьте права доступа API ключа в TableCRM</li>";
echo "<li>Убедитесь, что API ключ имеет права на создание номенклатуры</li>";
echo "<li>Проверьте, что все обязательные поля заполнены правильно</li>";
echo "<li>Возможно, в TableCRM есть ограничения на создание номенклатуры</li>";
echo "</ul>";

echo "<hr>";
echo "<p><em>Диагностический скрипт v1.0.22 - " . date('Y-m-d H:i:s') . "</em></p>";
?> 