<?php
/**
 * Диагностический скрипт для поиска источника имени "Айгуль"
 * Запуск: https://mosbuketik.ru/wp-content/plugins/tablecrm-integration/debug_aigul_name.php
 */

// Подключение к WordPress
require_once('../../../wp-config.php');
require_once('../../../wp-load.php');

// Проверка прав доступа
if (!current_user_can('manage_options')) {
    echo '<h1 style="color: red;">❌ Доступ запрещен</h1>';
    echo '<p>Необходимо войти в админ-панель WordPress как администратор.</p>';
    exit;
}

echo '<html><head><title>🔍 Диагностика имени "Айгуль"</title></head><body>';
echo '<h1>🔍 Диагностика источника имени "Айгуль"</h1>';
echo '<hr>';

echo '<h2>1. 📋 Поиск заказов WooCommerce с именем "Айгуль"</h2>';

if (function_exists('wc_get_orders')) {
    // Поиск заказов с именем Айгуль в billing данных
    $orders = wc_get_orders(array(
        'limit' => 50,
        'orderby' => 'date',
        'order' => 'DESC',
        'status' => array('wc-pending', 'wc-processing', 'wc-completed', 'wc-on-hold', 'wc-cancelled', 'wc-refunded', 'wc-failed')
    ));
    
    $found_orders = array();
    
    foreach ($orders as $order) {
        $first_name = $order->get_billing_first_name();
        $last_name = $order->get_billing_last_name();
        $full_name = $first_name . ' ' . $last_name;
        
        if (stripos($full_name, 'Айгуль') !== false || stripos($full_name, 'Aigul') !== false) {
            $found_orders[] = array(
                'id' => $order->get_id(),
                'name' => $full_name,
                'email' => $order->get_billing_email(),
                'phone' => $order->get_billing_phone(),
                'status' => $order->get_status(),
                'date' => $order->get_date_created()->format('Y-m-d H:i:s'),
                'total' => $order->get_total()
            );
        }
    }
    
    if (!empty($found_orders)) {
        echo '<div style="background: #ffe6e6; padding: 15px; border: 1px solid #ff0000; margin: 10px 0;">';
        echo '<h3>🚨 Найдены заказы с именем "Айгуль":</h3>';
        echo '<table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width: 100%;">';
        echo '<tr style="background: #f0f0f0;"><th>ID заказа</th><th>Имя</th><th>Email</th><th>Телефон</th><th>Статус</th><th>Дата</th><th>Сумма</th></tr>';
        
        foreach ($found_orders as $order_data) {
            echo '<tr>';
            echo '<td><a href="' . admin_url('post.php?post=' . $order_data['id'] . '&action=edit') . '" target="_blank">' . $order_data['id'] . '</a></td>';
            echo '<td><strong>' . esc_html($order_data['name']) . '</strong></td>';
            echo '<td>' . esc_html($order_data['email']) . '</td>';
            echo '<td>' . esc_html($order_data['phone']) . '</td>';
            echo '<td>' . esc_html($order_data['status']) . '</td>';
            echo '<td>' . esc_html($order_data['date']) . '</td>';
            echo '<td>' . esc_html($order_data['total']) . ' руб.</td>';
            echo '</tr>';
        }
        echo '</table>';
        echo '</div>';
    } else {
        echo '<div style="background: #e6ffe6; padding: 15px; border: 1px solid #00aa00; margin: 10px 0;">';
        echo '<p>✅ В последних 50 заказах WooCommerce имя "Айгуль" не найдено.</p>';
        echo '</div>';
    }
} else {
    echo '<div style="background: #fff3cd; padding: 15px; border: 1px solid #ffc107; margin: 10px 0;">';
    echo '<p>⚠️ WooCommerce не активен или функция wc_get_orders недоступна.</p>';
    echo '</div>';
}

echo '<h2>2. 👥 Поиск пользователей WordPress с именем "Айгуль"</h2>';

$users = get_users(array(
    'search' => '*Айгуль*',
    'search_columns' => array('display_name', 'user_nicename', 'user_login')
));

$users2 = get_users(array(
    'search' => '*Aigul*',
    'search_columns' => array('display_name', 'user_nicename', 'user_login')
));

$all_users = array_merge($users, $users2);

if (!empty($all_users)) {
    echo '<div style="background: #ffe6e6; padding: 15px; border: 1px solid #ff0000; margin: 10px 0;">';
    echo '<h3>🚨 Найдены пользователи с именем "Айгуль":</h3>';
    echo '<table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width: 100%;">';
    echo '<tr style="background: #f0f0f0;"><th>ID</th><th>Логин</th><th>Отображаемое имя</th><th>Email</th><th>Роль</th></tr>';
    
    foreach ($all_users as $user) {
        echo '<tr>';
        echo '<td>' . $user->ID . '</td>';
        echo '<td>' . esc_html($user->user_login) . '</td>';
        echo '<td><strong>' . esc_html($user->display_name) . '</strong></td>';
        echo '<td>' . esc_html($user->user_email) . '</td>';
        echo '<td>' . implode(', ', $user->roles) . '</td>';
        echo '</tr>';
    }
    echo '</table>';
    echo '</div>';
} else {
    echo '<div style="background: #e6ffe6; padding: 15px; border: 1px solid #00aa00; margin: 10px 0;">';
    echo '<p>✅ Пользователей WordPress с именем "Айгуль" не найдено.</p>';
    echo '</div>';
}

echo '<h2>3. 📝 Поиск в метаданных заказов</h2>';

global $wpdb;

// Поиск в метаданных заказов
$meta_results = $wpdb->get_results($wpdb->prepare("
    SELECT post_id, meta_key, meta_value 
    FROM {$wpdb->postmeta} 
    WHERE meta_value LIKE %s OR meta_value LIKE %s
    ORDER BY post_id DESC
    LIMIT 20
", '%Айгуль%', '%Aigul%'));

if (!empty($meta_results)) {
    echo '<div style="background: #ffe6e6; padding: 15px; border: 1px solid #ff0000; margin: 10px 0;">';
    echo '<h3>🚨 Найдены записи в метаданных с именем "Айгуль":</h3>';
    echo '<table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width: 100%;">';
    echo '<tr style="background: #f0f0f0;"><th>Post ID</th><th>Meta Key</th><th>Meta Value</th></tr>';
    
    foreach ($meta_results as $meta) {
        echo '<tr>';
        echo '<td>' . $meta->post_id . '</td>';
        echo '<td>' . esc_html($meta->meta_key) . '</td>';
        echo '<td><strong>' . esc_html($meta->meta_value) . '</strong></td>';
        echo '</tr>';
    }
    echo '</table>';
    echo '</div>';
} else {
    echo '<div style="background: #e6ffe6; padding: 15px; border: 1px solid #00aa00; margin: 10px 0;">';
    echo '<p>✅ В метаданных WordPress имя "Айгуль" не найдено.</p>';
    echo '</div>';
}

echo '<h2>4. 📊 Последние записи в логах TableCRM</h2>';

$log_file = WP_CONTENT_DIR . '/debug.log';
if (file_exists($log_file)) {
    $lines = file($log_file);
    $tablecrm_lines = array();
    
    // Ищем последние 50 записей TableCRM
    foreach (array_reverse($lines) as $line) {
        if (stripos($line, 'TableCRM Integration') !== false) {
            $tablecrm_lines[] = $line;
            if (count($tablecrm_lines) >= 20) break;
        }
    }
    
    // Ищем упоминания Айгуль в логах
    $aigul_logs = array();
    foreach ($tablecrm_lines as $line) {
        if (stripos($line, 'Айгуль') !== false || stripos($line, 'Aigul') !== false) {
            $aigul_logs[] = $line;
        }
    }
    
    if (!empty($aigul_logs)) {
        echo '<div style="background: #ffe6e6; padding: 15px; border: 1px solid #ff0000; margin: 10px 0;">';
        echo '<h3>🚨 Найдены записи в логах с именем "Айгуль":</h3>';
        echo '<pre style="background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; overflow-x: auto;">';
        foreach ($aigul_logs as $log_line) {
            echo esc_html($log_line);
        }
        echo '</pre>';
        echo '</div>';
    } else {
        echo '<div style="background: #e6ffe6; padding: 15px; border: 1px solid #00aa00; margin: 10px 0;">';
        echo '<p>✅ В логах TableCRM имя "Айгуль" не найдено.</p>';
        echo '</div>';
    }
    
    // Показываем последние 10 записей логов для анализа
    echo '<h3>📋 Последние записи в логах TableCRM:</h3>';
    echo '<pre style="background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; overflow-x: auto; max-height: 300px; overflow-y: auto;">';
    foreach (array_slice($tablecrm_lines, 0, 10) as $log_line) {
        echo esc_html($log_line);
    }
    echo '</pre>';
} else {
    echo '<div style="background: #fff3cd; padding: 15px; border: 1px solid #ffc107; margin: 10px 0;">';
    echo '<p>⚠️ Файл логов не найден: ' . $log_file . '</p>';
    echo '</div>';
}

echo '<h2>5. 🔧 Рекомендации по решению проблемы</h2>';

echo '<div style="background: #d1ecf1; padding: 15px; border: 1px solid #bee5eb; margin: 10px 0;">';
echo '<h3>💡 Что делать если найдено имя "Айгуль":</h3>';
echo '<ol>';
echo '<li><strong>Если найдены тестовые заказы:</strong> Удалите их через админ-панель WooCommerce → Заказы</li>';
echo '<li><strong>Если найдены тестовые пользователи:</strong> Удалите их через Пользователи → Все пользователи</li>';
echo '<li><strong>Если проблема в TableCRM:</strong> Проверьте настройки контрагентов в TableCRM</li>';
echo '<li><strong>Очистите кэш плагина:</strong> Деактивируйте и активируйте плагин заново</li>';
echo '</ol>';
echo '</div>';

echo '<div style="background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; margin: 10px 0;">';
echo '<h3>✅ Что делать если имя "Айгуль" не найдено:</h3>';
echo '<p>Проблема может быть в TableCRM API. Имя "Айгуль" может приходить как значение по умолчанию от TableCRM при создании нового контрагента.</p>';
echo '<p><strong>Решение:</strong> Проверьте настройки TableCRM и убедитесь, что все ID настроены правильно:</p>';
echo '<ul>';
echo '<li>ID Организации: ' . get_option('tablecrm_organization_id', 'не установлен') . '</li>';
echo '<li>ID Кассы: ' . get_option('tablecrm_cashbox_id', 'не установлен') . '</li>';
echo '<li>ID Склада: ' . get_option('tablecrm_warehouse_id', 'не установлен') . '</li>';
echo '<li>ID Единицы измерения: ' . get_option('tablecrm_unit_id', 'не установлен') . '</li>';
echo '</ul>';
echo '</div>';

echo '<hr>';
echo '<p><small>Скрипт выполнен: ' . date('Y-m-d H:i:s') . '</small></p>';
echo '</body></html>';
?> 