<?php
// Detailed flow debug
echo "=== ДЕТАЛЬНАЯ ДИАГНОСТИКА ПРОЦЕССА ОТПРАВКИ ===\n";

$wp_path = '/home/<USER>/bekflom3/mosbuketik.ru/public_html';
require_once($wp_path . '/wp-config.php');
require_once($wp_path . '/wp-load.php');

echo "WordPress загружен успешно\n\n";

// Test order
$test_order_id = 21775;
$order = wc_get_order($test_order_id);

if (!$order) {
    echo "❌ Заказ #$test_order_id не найден\n";
    exit;
}

echo "=== АНАЛИЗ ЗАКАЗА #$test_order_id ===\n";
echo "Статус: " . $order->get_status() . "\n";

// Get items
$items = $order->get_items();
echo "Количество товаров: " . count($items) . "\n";

if (empty($items)) {
    echo "❌ В заказе нет товаров!\n";
    exit;
}

echo "\nТОВАРЫ В ЗАКАЗЕ:\n";
foreach ($items as $item_id => $item) {
    echo "  ID: $item_id\n";
    echo "  Название: " . $item->get_name() . "\n";
    echo "  Количество: " . $item->get_quantity() . "\n";
    echo "  Цена: " . $order->get_item_total($item, false, false) . "\n";
    echo "  ---\n";
}

// Clear previous data
delete_post_meta($test_order_id, '_tablecrm_complete_document_id');
delete_post_meta($test_order_id, '_tablecrm_complete_sent_at');
echo "Метаданные очищены\n";

// Test step by step
global $tablecrm_integration_complete;
if (!isset($tablecrm_integration_complete)) {
    echo "❌ Плагин не загружен\n";
    exit;
}

echo "\n=== ПОШАГОВЫЙ ТЕСТ ===\n";

// Get API settings
$api_url = get_option('tablecrm_complete_api_url', 'https://app.tablecrm.com/api/v1/');
$api_token = get_option('tablecrm_complete_api_key');
$api_url = trailingslashit($api_url);

echo "API URL: $api_url\n";
echo "API Token: " . (strlen($api_token) > 0 ? 'установлен' : 'НЕ УСТАНОВЛЕН') . "\n";

// Step 1: Test contragent creation
echo "\n--- ШАГ 1: Создание контрагента ---\n";
$reflection = new ReflectionClass($tablecrm_integration_complete);
$contragent_method = $reflection->getMethod('get_or_create_contragent');
$contragent_method->setAccessible(true);

$contragent_id = $contragent_method->invoke($tablecrm_integration_complete, $order, $api_url, $api_token);
if ($contragent_id) {
    echo "✅ Контрагент: ID $contragent_id\n";
} else {
    echo "❌ Ошибка создания контрагента\n";
    exit;
}

// Step 2: Test nomenclature creation for each item
echo "\n--- ШАГ 2: Создание номенклатур ---\n";
$nomenclature_method = $reflection->getMethod('get_or_create_nomenclature');
$nomenclature_method->setAccessible(true);

$nomenclature_ids = array();
foreach ($items as $item_id => $item) {
    $product_name = $item->get_name();
    echo "Создание номенклатуры для: '$product_name'\n";
    
    $nomenclature_id = $nomenclature_method->invoke($tablecrm_integration_complete, $product_name, $api_url, $api_token);
    
    if ($nomenclature_id) {
        echo "✅ Номенклатура создана: ID $nomenclature_id\n";
        $nomenclature_ids[$item_id] = $nomenclature_id;
    } else {
        echo "❌ Ошибка создания номенклатуры для '$product_name'\n";
        exit;
    }
}

echo "Всего создано номенклатур: " . count($nomenclature_ids) . "\n";

// Step 3: Test data adaptation
echo "\n--- ШАГ 3: Формирование данных ---\n";
$warehouse_id = get_option('tablecrm_complete_warehouse_id');
$organization_id = get_option('tablecrm_complete_organization_id');
$paybox_id = get_option('tablecrm_complete_cashbox_id');

$adapt_method = $reflection->getMethod('adapt_data_format');
$adapt_method->setAccessible(true);

$adapted_data = $adapt_method->invoke(
    $tablecrm_integration_complete,
    $order,
    $warehouse_id,
    $organization_id,
    $paybox_id,
    $contragent_id,
    $nomenclature_ids
);

if ($adapted_data) {
    echo "✅ Данные сформированы\n";
    echo "Количество товаров в goods: " . count($adapted_data[0]['goods']) . "\n";
    
    foreach ($adapted_data[0]['goods'] as $i => $good) {
        echo "  Товар " . ($i+1) . ":\n";
        echo "    nomenclature: " . $good['nomenclature'] . "\n";
        echo "    price: " . $good['price'] . "\n";
        echo "    quantity: " . $good['quantity'] . "\n";
    }
} else {
    echo "❌ Ошибка формирования данных\n";
    exit;
}

// Step 4: Test API request
echo "\n--- ШАГ 4: Отправка в API ---\n";
$api_method = $reflection->getMethod('make_api_request');
$api_method->setAccessible(true);

$response = $api_method->invoke($tablecrm_integration_complete, 'docs_sales/', $adapted_data, 'POST');

if ($response['success']) {
    echo "✅ API запрос успешен\n";
    echo "Status: " . $response['status_code'] . "\n";
    
    $extract_method = $reflection->getMethod('extract_document_id');
    $extract_method->setAccessible(true);
    $document_id = $extract_method->invoke($tablecrm_integration_complete, $response);
    
    echo "Document ID: $document_id\n";
} else {
    echo "❌ Ошибка API: " . $response['message'] . "\n";
}

echo "\n=== ТЕСТ ЗАВЕРШЕН ===\n";
?> 