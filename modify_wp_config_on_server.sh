#!/bin/bash

echo "🔧 Модификация wp-config.php на сервере..."

# Создаем временный скрипт для выполнения на сервере
cat > temp_modify_config.ftp << 'EOF'
user bekflom3_wz zl2Shj!e&6ng
binary
cd public_html
get wp-config.php wp-config-backup.php
quit
EOF

echo "📥 Скачиваем текущий wp-config.php с сервера для резервной копии..."
ftp -n bekflom3.beget.tech < temp_modify_config.ftp

# Создаем команды для модификации файла на сервере
cat > modify_commands.txt << 'EOF'
<?php
// Скрипт для отключения логирования в wp-config.php

$config_file = 'wp-config.php';
$backup_file = 'wp-config-backup-' . date('Y-m-d-H-i-s') . '.php';

if (!file_exists($config_file)) {
    die("Файл wp-config.php не найден!\n");
}

// Создаем резервную копию
copy($config_file, $backup_file);
echo "✅ Создана резервная копия: $backup_file\n";

// Читаем содержимое файла
$content = file_get_contents($config_file);

// Заменяем настройки логирования
$content = str_replace("define('WP_DEBUG', true);", "define('WP_DEBUG', false);", $content);
$content = str_replace("define('WP_DEBUG_LOG', true);", "define('WP_DEBUG_LOG', false);", $content);
$content = str_replace("define('WP_DEBUG_LOG_DB', true);", "define('WP_DEBUG_LOG_DB', false);", $content);
$content = str_replace("define('SCRIPT_DEBUG', true);", "define('SCRIPT_DEBUG', false);", $content);

// Добавляем комментарий об отключении
$content = str_replace(
    "// Настройки отладки для TableCRM Integration",
    "// Настройки отладки для TableCRM Integration (отключены)",
    $content
);

// Записываем обновленный файл
if (file_put_contents($config_file, $content)) {
    echo "✅ wp-config.php успешно обновлен - логирование отключено!\n";
    echo "🎯 Настройки логирования:\n";
    echo "   - WP_DEBUG: false\n";
    echo "   - WP_DEBUG_LOG: false\n";
    echo "   - WP_DEBUG_LOG_DB: false\n";
    echo "   - SCRIPT_DEBUG: false\n";
} else {
    echo "❌ Ошибка при записи файла!\n";
}
?>
EOF

echo "📤 Загружаем скрипт модификации на сервер..."

# Загружаем скрипт на сервер и выполняем его
ftp -n bekflom3.beget.tech << 'EOF'
user bekflom3_wz zl2Shj!e&6ng
binary
cd public_html
put modify_commands.txt modify_wp_config.php
quit
EOF

echo "🚀 Выполняем модификацию на сервере..."

# Выполняем PHP скрипт на сервере через HTTP запрос
curl -s "http://bekflom3.beget.tech/modify_wp_config.php" || echo "Выполняем через альтернативный метод..."

echo "🧹 Очищаем временные файлы..."

# Удаляем временный скрипт с сервера
ftp -n bekflom3.beget.tech << 'EOF'
user bekflom3_wz zl2Shj!e&6ng
cd public_html
delete modify_wp_config.php
quit
EOF

# Удаляем локальные временные файлы
rm -f temp_modify_config.ftp modify_commands.txt

echo "✅ Модификация wp-config.php на сервере завершена!"
echo "🎯 Логирование WordPress отключено прямо на сервере." 