<?php
/*
 * Checkout Clean Buffer – универсальный фильтр вывода
 * удаляет весь «мусор» до JSON-ответа WooCommerce checkout.
 * Автор: AI-assistant
 * Версия: 1.0
 */
defined( 'ABSPATH' ) || exit;

/**
 * Запускаемся САМИМИ ПЕРВЫМИ: priority=0 на init.
 */
add_action(
	'init',
	static function () {

		// Работаем только в AJAX-контексте WC-checkout
		if ( ! ( defined( 'DOING_AJAX' ) && DOING_AJAX ) ) {
			return;
		}
		if ( empty( $_REQUEST['wc-ajax'] ) || $_REQUEST['wc-ajax'] !== 'checkout' ) {
			return;
		}

		// 1. Запускаем буферизацию вывода как можно раньше
		ob_start();

		// 2. На shutdown фильтруем весь собранный вывод
		register_shutdown_function(
			static function () {

				$buffer = ob_get_clean();      // всё, что вывели плагины/тема
				$pos    = strpos( $buffer, '{' ); // ищем начало JSON

				if ( false === $pos ) {
					// JSON так и не нашли – выводим оригинал (пусть Woo заметит ошибку)
					echo $buffer;
					return;
				}

				// Отбрасываем мусор ДО первой фигурной скобки
				$clean = substr( $buffer, $pos );

				// Дополнительно можно обрезать после последней "}" на случай HTML вниз
				$last = strrpos( $clean, '}' );
				if ( false !== $last ) {
					$clean = substr( $clean, 0, $last + 1 );
				}

				// Возвращаем чистый JSON WooCommerce
				echo $clean;
			}
		);
	},
	0 // выполняться самым первым
);