# Upload plugin to server
$ftpRequest = [System.Net.FtpWebRequest]::Create("ftp://bekflom3.ftp.ukraine.com.ua/public_html/wp-content/plugins/tablecrm-complete/tablecrm-integration-complete.php")
$ftpRequest.Method = [System.Net.WebRequestMethods+Ftp]::UploadFile
$ftpRequest.Credentials = New-Object System.Net.NetworkCredential("bekflom3", "Ololo2019")
$ftpRequest.UseBinary = $true
$ftpRequest.UsePassive = $true

$fileContent = [System.IO.File]::ReadAllBytes("tablecrm-integration-complete-v1.0.24.php")
$requestStream = $ftpRequest.GetRequestStream()
$requestStream.Write($fileContent, 0, $fileContent.Length)
$requestStream.Close()

Write-Host "Plugin uploaded successfully" -ForegroundColor Green 