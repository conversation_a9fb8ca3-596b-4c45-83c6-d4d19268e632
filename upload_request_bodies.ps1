# Upload and run request bodies analysis
$server = "bekflom3.beget.tech"
$user = "bekflom3_wz"
$pass = "zl2Shj!e&6ng"

Write-Host "Uploading request bodies analysis script..." -ForegroundColor Green

try {
    $uri = "ftp://$server/public_html/show_request_bodies.php"
    $webclient = New-Object System.Net.WebClient
    $webclient.Credentials = New-Object System.Net.NetworkCredential($user, $pass)
    $webclient.UploadFile($uri, "show_request_bodies.php")
    Write-Host "Request bodies analysis script uploaded!" -ForegroundColor Green
    
    # Run the analysis
    Write-Host "Running request bodies analysis..." -ForegroundColor Yellow
    $result = Invoke-WebRequest -Uri "https://mosbuketik.ru/show_request_bodies.php" -UseBasicParsing
    Write-Host $result.Content -ForegroundColor White
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

$webclient.Dispose()
Write-Host "Request bodies analysis completed!" -ForegroundColor Green 