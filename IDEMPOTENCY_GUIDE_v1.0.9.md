# 🔐 Руководство по Idempotency-Key в TableCRM Integration v1.0.9

**Дата создания**: 5 июня 2025  
**Версия плагина**: 1.0.9  
**Важность**: 🚨 КРИТИЧЕСКАЯ для стабильности интеграции

---

## 🎯 ЧТО ТАКОЕ IDEMPOTENCY-KEY

**Idempotency-Key** - это уникальный идентификатор HTTP запроса, который позволяет TableCRM API безопасно обрабатывать повторные запросы без создания дублирующих записей.

### ❌ Проблема ДО версии 1.0.9:
```
Заказ #1234 → API запрос 1 → Сделка A создана ✅
Заказ #1234 → API запрос 2 → Сделка B создана ❌ (дубль!)
Заказ #1234 → API запрос 3 → Сделка C создана ❌ (еще дубль!)
```

### ✅ Решение В версии 1.0.9:
```
Заказ #1234 → API запрос с Idempotency-Key: order-1234 → Сделка A создана ✅
Заказ #1234 → API запрос с Idempotency-Key: order-1234 → Возвращает сделку A ✅
Заказ #1234 → API запрос с Idempotency-Key: order-1234 → Возвращает сделку A ✅
```

---

## 🔧 ТЕХНИЧЕСКАЯ РЕАЛИЗАЦИЯ

### Автоматическое добавление заголовка:
```php
// Пример HTTP запроса с idempotency-key:
POST https://app.tablecrm.com/api/v1/docs_sales/?token=YOUR_API_KEY
Content-Type: application/json
Idempotency-Key: order-1234

{
  "order_data": "..."
}
```

### Типы idempotency-key:
| Тип операции | Формат ключа | Пример |
|--------------|-------------|--------|
| **Создание заказа** | `order-{order_id}` | `order-1234` |
| **Обновление заказа** | `order-{order_id}` | `order-1234` |
| **Создание лида CF7** | `lead-{hash}` | `lead-a1b2c3d4e5f6` |

---

## 📊 ВЛИЯНИЕ НА СИСТЕМУ

### Преимущества:
- ✅ **Устранение фантомных сделок** - повторы не создают дубли
- ✅ **Безопасные повторы** - Action Scheduler может повторять без риска  
- ✅ **Стабильность CRM** - чистая база данных без дублирующих записей
- ✅ **Соответствие стандартам** - RFC 5789 compliant

### Производительность:
- 🚀 **Нет замедления** - добавление заголовка не влияет на скорость
- 📈 **Улучшение логики** - TableCRM быстрее обрабатывает повторы
- 💾 **Экономия ресурсов** - меньше записей в CRM = лучше производительность

---

## 🔍 МОНИТОРИНГ И ДИАГНОСТИКА

### Логи WordPress:
```bash
# Поиск idempotency записей в логах:
tail -f /wp-content/debug.log | grep "Idempotency-Key"

# Пример успешного лога:
[2025-06-05 15:30:25] [TableCRM v3] [INFO] Добавлен Idempotency-Key: order-1234
[2025-06-05 15:30:26] [TableCRM v3] [INFO] API запрос: POST https://app.tablecrm.com/api/v1/docs_sales/
[2025-06-05 15:30:27] [TableCRM v3] [SUCCESS] Заказ 1234 успешно отправлен. Document ID: 12345
```

### Проверка работы:
1. **Создать тестовый заказ** в WooCommerce
2. **Проверить логи** на наличие `Idempotency-Key: order-{ID}`
3. **Повторно отправить заказ** (если нужно через admin)
4. **Убедиться** что новая сделка не создалась в TableCRM

---

## 🚀 ИНСТРУКЦИИ ПО ОБНОВЛЕНИЮ

### Шаг 1: Загрузка новой версии
```bash
# Скачать архив:
wget tablecrm-integration-fixed-v3-idempotency-v1.0.9.zip

# Или использовать основной файл:
wget tablecrm-integration-fixed-v3.php  # версия 1.0.9
```

### Шаг 2: Установка через WordPress
1. **Админ-панель** → **Плагины** → **Добавить новый**
2. **Загрузить плагин** → выбрать `.zip` архив
3. **Установить сейчас** → **Активировать**

### Шаг 3: Проверка версии
1. **Настройки** → **TableCRM Integration (Fixed v3)**
2. Убедиться что отображается **"v1.0.9"** в заголовке
3. Проверить что **User-Agent** обновился до `v3/1.0.9`

### Шаг 4: Тестирование
1. Создать тестовый заказ в WooCommerce
2. Проверить логи: `tail -f /wp-content/debug.log | grep "TableCRM"`
3. Найти сообщение: `"Добавлен Idempotency-Key: order-XXX"`
4. Убедиться что заказ появился в TableCRM

---

## ⚠️ ВАЖНЫЕ ЗАМЕЧАНИЯ

### Совместимость:
- ✅ **WordPress**: ≥ 5.0 (без изменений)
- ✅ **WooCommerce**: ≥ 8.5 (без изменений)  
- ✅ **PHP**: ≥ 7.4 (без изменений)
- ✅ **TableCRM API**: текущая версия с поддержкой idempotency

### Обратная совместимость:
- ✅ Старые настройки сохраняются
- ✅ Существующие заказы обрабатываются корректно
- ✅ Нет необходимости в миграции данных

### Безопасность:
- 🔒 Idempotency-key НЕ содержит чувствительных данных
- 🔒 Передается только ID заказа и префикс `order-`
- 🔒 Безопасен для логирования и мониторинга

---

## 🔧 УСТРАНЕНИЕ ПРОБЛЕМ

### Проблема: Idempotency-key не появляется в логах
**Решение:**
1. Убедиться что **Debug Mode** включен в настройках плагина
2. Проверить что `WP_DEBUG` и `WP_DEBUG_LOG` включены в `wp-config.php`
3. Пересоздать тестовый заказ

### Проблема: Дубли все еще создаются
**Решение:**
1. Проверить что обновление до версии 1.0.9 прошло успешно
2. Убедиться что в логах есть строки с `Idempotency-Key`
3. Связаться с поддержкой TableCRM для проверки настроек API

### Проблема: HTTP ошибки после обновления
**Решение:**
1. Проверить что TableCRM API поддерживает idempotency заголовки
2. Временно отключить plgun и проверить работу сайта
3. Обратиться к технической поддержке

---

## 📞 ТЕХНИЧЕСКАЯ ПОДДЕРЖКА

### При обращении в поддержку укажите:
- ✅ **Версия плагина**: 1.0.9
- ✅ **WordPress версия**: 
- ✅ **WooCommerce версия**:
- ✅ **PHP версия**: 
- ✅ **Фрагмент логов** с idempotency записями
- ✅ **Описание проблемы** и шаги для воспроизведения

### Полезные команды для диагностики:
```bash
# Проверка версии плагина:
grep "Version:" wp-content/plugins/tablecrm-integration/tablecrm-integration-fixed-v3.php

# Поиск idempotency в логах:
grep -i "idempotency" wp-content/debug.log

# Последние 50 записей TableCRM:
tail -50 wp-content/debug.log | grep "TableCRM v3"
```

---

**Обновление до версии 1.0.9 настоятельно рекомендуется для всех пользователей!** 🚀 