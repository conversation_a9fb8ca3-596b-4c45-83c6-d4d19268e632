<?php
// Check order 21780
echo "=== ПРОВЕРКА ЗАКАЗА #21780 ===\n";

$wp_path = '/home/<USER>/bekflom3/mosbuketik.ru/public_html';
require_once($wp_path . '/wp-config.php');
require_once($wp_path . '/wp-load.php');

echo "WordPress загружен успешно\n\n";

$order_id = 21780;
$order = wc_get_order($order_id);

if ($order) {
    echo "✅ Заказ найден\n";
    echo "Статус: " . $order->get_status() . "\n";
    echo "Клиент: " . $order->get_formatted_billing_full_name() . "\n";
    echo "Телефон: " . $order->get_billing_phone() . "\n";
    
    // Get order items
    $items = $order->get_items();
    echo "Количество товаров: " . count($items) . "\n";
    
    if (!empty($items)) {
        echo "\nТОВАРЫ В ЗАКАЗЕ:\n";
        foreach ($items as $item_id => $item) {
            echo "  - ID: $item_id, Название: " . $item->get_name() . " (кол-во: " . $item->get_quantity() . ")\n";
            echo "    Цена: " . $order->get_item_total($item, false, false) . " руб.\n";
        }
    } else {
        echo "❌ В ЗАКАЗЕ НЕТ ТОВАРОВ!\n";
    }
    
    echo "\nСтоимость доставки: " . $order->get_shipping_total() . " руб.\n";
    echo "Общая сумма: " . $order->get_total() . " руб.\n";
    
    // Check if sent to TableCRM
    $doc_id = get_post_meta($order_id, '_tablecrm_complete_document_id', true);
    if ($doc_id) {
        echo "Отправлен в TableCRM: Document ID $doc_id\n";
    } else {
        echo "НЕ отправлен в TableCRM\n";
    }
    
    // Check shipping items
    $shipping_items = $order->get_items('shipping');
    echo "\nИнформация о доставке:\n";
    foreach ($shipping_items as $shipping_item) {
        echo "  - Способ: " . $shipping_item->get_method_title() . "\n";
        echo "  - Стоимость: " . $shipping_item->get_total() . " руб.\n";
    }
    
} else {
    echo "❌ Заказ #$order_id не найден\n";
}

echo "\n=== ПРОВЕРКА ЗАВЕРШЕНА ===\n";
?> 