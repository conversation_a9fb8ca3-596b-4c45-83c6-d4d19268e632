# Критическое обновление: Правильная интеграция с API TableCRM v1.0.23

## Суть проблемы

Анализ официальной документации API TableCRM выявил, что наш предыдущий подход был фундаментально неверным. API требует многошаговой процедуры, а не простой отправки одного запроса.

## Корневая причина проблемы "Testing"

```json
// Наши предыдущие запросы
{
    "goods": [{
        "nomenclature": 39697,  // ← Жестко заданный ID существующего товара "Testing"
        "name": "Букет из 39 альстромерий"  // ← Игнорируется API
    }]
}
```

API TableCRM:
1. Получает `nomenclature: 39697`
2. Находит в своей базе товар с ID 39697 = "Testing"
3. Использует название из базы, игнорируя поле `name`
4. Результат: все заказы показывают "Testing"

## Правильная архитектура (v1.0.23)

### Шаг 1: Обработка контрагента
```php
// 1. Поиск существующего клиента
GET /contragents/?phone=+79377799906

// 2. Создание нового (если не найден)
POST /contragents/
{
    "name": "Иван Петров",
    "phone": "+79377799906",
    "email": "<EMAIL>"
}
```

### Шаг 2: Обработка номенклатуры
```php
// Для каждого товара в корзине:

// 1. Поиск существующего товара
GET /nomenclature/?name=Букет из 39 альстромерий

// 2. Создание нового (если не найден)
POST /nomenclatures/
{
    "name": "Букет из 39 альстромерий",
    "unit": 116
}
```

### Шаг 3: Создание заказа
```php
// Только после получения всех ID
POST /docs_sales/
{
    "goods": [{
        "nomenclature": 45123,  // ← Динамический ID созданного товара
        "name": "Букет из 39 альстромерий",
        "price": 500,
        "quantity": 1
    }],
    "contragent": 330985  // ← Динамический ID клиента
}
```

## Ключевые функции v1.0.23

### 1. Динамическое создание номенклатуры
- Автоматический поиск товаров по названию
- Создание новых позиций при отсутствии
- Корректная привязка ID к товарам

### 2. Динамическое создание контрагентов
- Поиск клиентов по номеру телефона
- Автоматическое создание новых записей
- Полная синхронизация данных клиента

### 3. Улучшенное логирование
- Пошаговая запись всех операций
- Детальная отладочная информация
- Отслеживание успешности каждого этапа

## Миграция с предыдущих версий

### Что изменилось:
- ✅ Добавлены новые функции для работы с API
- ✅ Изменена логика `tablecrm_send_order_to_api()`
- ✅ Обновлена функция `adapt_data_format()`
- ✅ Сохранены все существующие настройки

### Что НЕ изменилось:
- ✅ Интерфейс администратора
- ✅ Параметры конфигурации
- ✅ Триггеры отправки заказов
- ✅ Система логирования

## Ожидаемый результат

После обновления на v1.0.23:

**До:**
- Все товары → "Testing"
- Клиенты не создаются автоматически

**После:**
- "Букет из 39 альстромерий" → "Букет из 39 альстромерий"
- Автоматическое создание клиентов и товаров
- Полная синхронизация данных

## Инструкция по обновлению

1. **Загрузите** `tablecrm-integration-fallback-v1.0.23.php`
2. **Замените** текущий файл плагина
3. **Создайте** тестовый заказ
4. **Проверьте** логи и результат в TableCRM

## Техническая поддержка

При возникновении проблем проверьте:
- Доступность API endpoints
- Корректность токена доступа
- Наличие прав на создание номенклатуры и контрагентов
- Логи плагина для диагностики

Эта версия решает фундаментальную проблему неправильного отображения названий товаров в TableCRM. 