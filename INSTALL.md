# Инструкция по установке TableCRM WordPress Integration

## 📋 Системные требования

- **WordPress**: версия 5.0 или выше
- **WooCommerce**: версия 3.0 или выше  
- **PHP**: версия 7.0 или выше (рекомендуется 7.4+)
- **TableCRM**: активная учетная запись с API доступом
- **Action Scheduler**: библиотека для асинхронных задач (идет в составе WooCommerce или устанавливается отдельно)

## 🚀 Быстрая установка

### Шаг 1: Загрузка плагина

#### Вариант A: Через Git
```bash
cd /path/to/your/wordpress/wp-content/plugins/
git clone https://github.com/your-username/tablecrm-wordpress-integration.git tablecrm-integration
```

#### Вариант B: Загрузка архива
1. Скачайте архив с GitHub
2. Распакуйте в папку `wp-content/plugins/tablecrm-integration/`

### Шаг 2: Активация плагина
1. Войдите в админ-панель WordPress
2. Перейдите в **Плагины** → **Установленные плагины**
3. Найдите "TableCRM Integration" и нажмите **Активировать**
4. Убедитесь, что активирован плагин **Action Scheduler** (обычно устанавливается вместе с WooCommerce)

### Шаг 3: Базовая настройка
1. Перейдите в **Настройки** → **TableCRM Integration**
2. Заполните обязательные поля:
   - **API URL**: `https://app.tablecrm.com/api/v1/`
   - **API Key**: Ваш ключ из TableCRM
3. Нажмите **Сохранить настройки**

### Шаг 4: Проверка работы
1. Создайте тестовый заказ в WooCommerce
2. Проверьте логи в `wp-content/debug.log`
3. Убедитесь, что заказ появился в TableCRM

## ⚙️ Подробная настройка

### Получение API ключа TableCRM

1. Войдите в вашу учетную запись TableCRM
2. Перейдите в раздел **Настройки** → **API**
3. Создайте новый API ключ или скопируйте существующий
4. Сохраните ключ в безопасном месте

### Настройка идентификаторов

Для корректной работы плагина необходимо настроить следующие ID:

#### Organization ID (ID Организации)
1. В TableCRM перейдите в список организаций
2. Найдите нужную организацию
3. Скопируйте её ID (обычно число, например: 38)

#### Cashbox ID (ID Кассы)  
1. В TableCRM найдите раздел с кассами
2. Выберите нужную кассу для заказов
3. Скопируйте ID кассы (например: 218)

#### Warehouse ID (ID Склада)
1. Перейдите в управление складами в TableCRM
2. Выберите основной склад
3. Скопируйте ID склада (например: 39)

#### Unit ID (ID Единицы измерения)
1. В справочниках TableCRM найдите единицы измерения
2. Выберите "штуки" или другую подходящую единицу
3. Скопируйте ID (например: 116 для штук)

### Настройка статусов заказов

В разделе "Статусы заказов для отправки" выберите, при каких статусах отправлять заказы в TableCRM:

- ✅ **Ожидает оплаты** (`pending`) - рекомендуется
- ✅ **В обработке** (`processing`) - рекомендуется  
- ✅ **Выполнен** (`completed`) - опционально
- ❌ **Отменен** (`cancelled`) - не рекомендуется
- ❌ **Возвращен** (`refunded`) - не рекомендуется

### Включение отладки

Для диагностики проблем:

1. Включите **Режим отладки** в настройках плагина
2. В `wp-config.php` добавьте:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
```

## 🔧 Проверка и диагностика

### Проверка логов

Логи записываются в файл `wp-content/debug.log`. Для удобства используйте скрипт проверки:

1. Загрузите `check_integration_logs.php` в корень сайта
2. Откройте в браузере: `https://your-site.com/check_integration_logs.php`

### Создание тестового заказа

Для тестирования используйте скрипт:

1. Загрузите `create_test_order_simple.php` в корень сайта
2. Откройте в браузере: `https://your-site.com/create_test_order_simple.php`

### Типичные проблемы и решения

#### Заказы не отправляются
```
Проблема: В логах нет записей о TableCRM
Решение:
1. Проверьте активацию плагина
2. Убедитесь, что включена отладка
3. Проверьте правильность API ключа
```

#### Ошибка API ключа
```
Проблема: [ERROR] API ключ не настроен
Решение:
1. Проверьте правильность ввода API ключа
2. Убедитесь, что ключ активен в TableCRM
3. Проверьте права доступа ключа
```

#### Некорректная структура данных доставки
```
Проблема: [ERROR] Ошибка отправки информации о доставке
Решение:
1. Убедитесь, что в заказе заполнены адреса
2. Проверьте формат даты доставки
3. Обновите плагин до версии 1.0.1+
```

## 🚨 Устранение неполадок

### Включение расширенного логирования

Добавьте в `wp-config.php`:
```php
// Дополнительное логирование для TableCRM
ini_set('log_errors', 1);
ini_set('error_log', ABSPATH . 'wp-content/debug.log');
```

### Проверка прав доступа

Убедитесь, что у файлов плагина правильные права:
```bash
chmod 644 tablecrm-integration.php
chmod 644 check_integration_logs.php
chmod 755 wp-content/plugins/tablecrm-integration/
```

### Очистка логов

Для очистки старых логов:
```bash
# Создать резервную копию
cp wp-content/debug.log wp-content/debug.log.backup

# Очистить лог
> wp-content/debug.log
```

## 📞 Получение поддержки

1. **Документация**: Изучите `README.md` и `CHANGELOG.md`
2. **Issues**: Создайте issue на GitHub с подробным описанием проблемы
3. **Логи**: Всегда прикладывайте релевантные логи из `debug.log`
4. **Версия**: Укажите версию WordPress, WooCommerce и плагина

## 🔄 Обновление

### Через Git
```bash
cd wp-content/plugins/tablecrm-integration/
git pull origin main
```

### Вручную
1. Создайте резервную копию текущего плагина
2. Замените файлы новой версией
3. Проверьте настройки в админ-панели

---

**Готово!** После выполнения всех шагов плагин должен автоматически отправлять заказы WooCommerce в TableCRM. 🎉 