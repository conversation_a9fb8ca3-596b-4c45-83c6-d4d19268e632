# 🚀 TableCRM Integration Complete v1.0.24

**Полная интеграция WordPress/WooCommerce с TableCRM через API**  
**С динамическим созданием номенклатуры, кэшированием и усиленной безопасностью**  
**🆕 ОБНОВЛЕНО 19.06.2025: Исправлены критические проблемы со скидками и оформлением заказов**

[![Version](https://img.shields.io/badge/version-1.0.24-green.svg)](https://github.com/your-repo/tablecrm-integration)
[![WordPress](https://img.shields.io/badge/WordPress-5.0+-blue.svg)](https://wordpress.org)
[![WooCommerce](https://img.shields.io/badge/WooCommerce-8.5+-orange.svg)](https://woocommerce.com)
[![PHP](https://img.shields.io/badge/PHP-7.4+-purple.svg)](https://php.net)
[![Security](https://img.shields.io/badge/Security-Enhanced-red.svg)](#-безопасность)
[![Performance](https://img.shields.io/badge/Performance-Optimized-yellow.svg)](#-производительность)

---

## 📋 СОДЕРЖАНИЕ

- [🎯 Основные возможности](#-основные-возможности)
- [🔄 Механика работы плагина](#-механика-работы-плагина)
- [🆕 Новое в версии 1.0.24](#-новое-в-версии-1024)
- [⚡ Быстрый старт](#-быстрый-старт)
- [📖 Подробная настройка](#-подробная-настройка)
- [🔍 Диагностика](#-диагностика)
- [📚 Дополнительная документация](#-дополнительная-документация)

---

## 🎯 ОСНОВНЫЕ ВОЗМОЖНОСТИ

### ✅ Полностью реализованные функции:

1. **📦 Автоматическая отправка заказов WooCommerce в TableCRM**
   - Синхронизация данных клиентов, товаров, сумм заказов
   - 🆕 **Динамическое создание номенклатуры** - автоматическое создание товаров в TableCRM
   - 🆕 **Автоматическое создание контрагентов** - поиск и создание клиентов по телефону
   - 🆕 **Синхронизация статуса оплаты** - автоматическое обновление при изменении статуса
   - Поддержка всех статусов заказов
   - Валидация данных перед отправкой
   - Защита от дублирования заказов

2. **📧 Интеграция с Contact Form 7**
   - Автоматическое создание лидов из контактных форм
   - Сохранение UTM данных и метаинформации
   - Асинхронная обработка через WordPress cron

3. **📊 UTM аналитика и отслеживание**
   - Автоматическое сохранение UTM параметров в заказы
   - Отслеживание источников трафика (referrer, landing page)
   - Сохранение IP адресов и user agent
   - JavaScript скрипт для отслеживания посетителей
   - 🆕 **Отправка UTM данных в TableCRM** через специальный API эндпоинт

4. **🚚 Информация о доставке**
   - 🆕 **Автоматическая отправка данных доставки** в TableCRM
   - Поддержка CodeRockz WooCommerce Delivery Date Time Pro
   - Поддержка Order Delivery Date Pro
   - HPOS совместимость для получения данных доставки

5. **⚡ Фоновая обработка через WordPress Cron**
   - Идемпотентность задач с защитой от дублирования
   - Автоматические повторы при ошибках
   - 🆕 **Автоочистка старых cron задач** (старше 5 минут)
   - Защита от потери задач при перезагрузке сервера

6. **🛡️ Защита от дублирования**
   - Тройная проверка на каждом этапе
   - Маркеры обработки в базе данных
   - Проверка очереди WordPress cron
   - 🆕 **Временные блокировки параллельных процессов**

7. **💾 Кэширование и производительность**
   - 🆕 **Кэширование номенклатуры** - снижение API запросов до 75%
   - 🆕 **Умное использование API** - повторные товары не запрашиваются
   - 🆕 **Валидация JSON ответов** - проверка корректности API данных

---

## 🔄 МЕХАНИКА РАБОТЫ ПЛАГИНА

### **⏰ Когда происходит проверка статуса:**

1. **При создании нового заказа** (`woocommerce_checkout_order_processed`)
   - Заказ добавляется в очередь на отправку
   - Выполняется в фоновом режиме после ответа пользователю

2. **При изменении статуса заказа** (`woocommerce_order_status_changed`)
   - Проверяется: стал ли заказ оплаченным ИЛИ новый статус в списке триггеров
   - Если да → заказ добавляется в очередь на отправку/обновление

### **🔢 Сколько раз происходит проверка:**

- **1 раз при создании заказа**
- **1 раз при каждой смене статуса** (например: pending → processing → completed = 2 проверки)

### **🔄 Логика отправки:**

---

## 🆕 НОВОЕ В ВЕРСИИ 1.0.24 (Обновлено 30.06.2025)

### 🔄 Обработка имени получателя
- Добавлен приоритетный порядок выбора имени получателя для доставки:
  1. Имя из данных доставки (shipping_first_name + shipping_last_name)
  2. Имя из платежных данных (billing_first_name + billing_last_name)
  3. Стандартное имя получателя из настроек
- Улучшено логирование процесса выбора имени
- Исправлена синхронизация имени получателя между данными доставки и TableCRM

## Ранее в версии 1.0.24 (19.06.2025)

### 🚨 **КРИТИЧЕСКИЕ ИСПРАВЛЕНИЯ:**

#### 🎯 **Проблема со скидками в CRM - РЕШЕНА:**
- **Убрана передача скидок** - больше нет сложных расчетов
- **Поле discount всегда 0** - упрощенная схема данных
- **Исправлено отображение 100% скидки** в TableCRM
- **Передается только итоговая цена** товара (уже включающая скидку)

#### 🚀 **Проблема с оформлением заказов - РЕШЕНА:**
- **Исправлена желтая плашка** при первом оформлении заказа
- **Автоматическая очистка буфера** вывода для предотвращения AJAX ошибок
- **Заказы оформляются с первого раза** - больше не нужно повторно нажимать кнопку

#### ⚙️ **Раздельные настройки статусов - ДОБАВЛЕНО:**
- **Статусы для отправки** - какие заказы отправлять в CRM
- **Оплаченные статусы** - какие заказы считать оплаченными в CRM
- **Динамическая загрузка** всех доступных статусов WooCommerce
- **Корректная передача статуса оплаты** в TableCRM

### 🔒 **УЛУЧШЕНИЯ БЕЗОПАСНОСТИ:**

#### 🔒 **Усиленная валидация API ключа:**
- **Минимум 32 символа** - проверка длины токена
- **Фильтрация символов** - только буквы, цифры, тире и подчеркивания
- **Защита от injection** - полная санитизация входных данных
- **XSS защита** - экранирование HTML тегов

#### 🔍 **Улучшенная валидация API:**
- **Проверка формата URL** - валидация корректности API URL
- **JSON валидация** - проверка корректности ответов API
- **Детальное логирование ошибок** - расширенная диагностика

### ⚡ **ПРОРЫВ В ПРОИЗВОДИТЕЛЬНОСТИ:**

#### 💾 **Интеллектуальное кэширование номенклатуры:**
```php
// Пример оптимизации:
Заказ с 10 одинаковыми товарами:
БЕЗ кэша: 12 API запросов
С кэшем: 3 API запроса (75% экономия!)
```

#### 🧹 **Автоматическая оптимизация cron задач:**
- Очистка "мертвых" задач старше 5 минут
- Предотвращение накопления в очереди
- Более стабильная работа фоновых процессов

### 🎯 **НОВЫЕ ВОЗМОЖНОСТИ:**

#### 🚚 **Расширенная интеграция доставки:**
- Автоматическая отправка информации о доставке в TableCRM
- Поддержка множественных плагинов доставки
- HPOS совместимость для WooCommerce 8.5+
- Валидация данных доставки перед отправкой

#### 📊 **Улучшенная аналитика:**
- Отправка UTM данных через специальный API эндпоинт TableCRM
- Расширенное логирование производительности
- Мониторинг эффективности кэша

---

## ⚡ БЫСТРЫЙ СТАРТ

### 1️⃣ **Установка плагина**

```bash
# Скачать файл плагина
wget https://path-to-your-plugin/tablecrm-integration-complete-v1.0.24.php

# Или готовый архив
wget https://path-to-your-plugin/tablecrm-integration-complete-v1.0.24.zip
```

**Через админ-панель WordPress:**
1. **Плагины** → **Добавить новый** → **Загрузить плагин**
2. Выбрать файл `tablecrm-integration-complete-v1.0.24.zip`
3. **Установить сейчас** → **Активировать плагин**

### 2️⃣ **Базовая настройка (2 минуты)**

1. Перейти в **Настройки** → **TableCRM Complete**
2. Заполнить обязательные поля:
   - **API URL**: `https://app.tablecrm.com/api/v1/`
   - **API Key**: ваш ключ из TableCRM (**минимум 32 символа!**)
   - **Organization ID**: ID вашей организации (например: 38)
   - **Cashbox ID**: ID кассы (например: 218)
   - **Warehouse ID**: ID склада (например: 39)
3. **Сохранить настройки**
4. **Протестировать соединение** (теперь с расширенной валидацией)

### 3️⃣ **Проверка работы**

```bash
# Проверить логи плагина
tail -f /wp-content/uploads/tablecrm_integration_complete.log

# Создать тестовый заказ в WooCommerce
# Проверить появление заказа в TableCRM с корректными названиями товаров
```

---

## 📖 ПОДРОБНАЯ НАСТРОЙКА

### 🔧 **Обязательные настройки**

| Параметр | Описание | Пример | 🆕 Требования |
|----------|----------|---------|---------------|
| **API URL** | URL API TableCRM | `https://app.tablecrm.com/api/v1/` | Валидация формата |
| **API Key** | Ваш API ключ | `your-secret-api-key` | **Мин. 32 символа** |
| **Organization ID** | ID организации в TableCRM | `38` | Числовое значение |
| **Cashbox ID** | ID кассы для заказов | `218` | Числовое значение |
| **Warehouse ID** | ID склада | `39` | Числовое значение |
| **Unit ID** | ID единицы измерения | `116` | По умолчанию для номенклатуры |

### ⚙️ **Дополнительные настройки**

| Настройка | По умолчанию | Описание | 🆕 Особенности |
|-----------|--------------|----------|----------------|
| **Включить заказы** | ✅ Да | Отправка заказов WooCommerce | Динамическое создание товаров |
| **Режим отладки** | ✅ Да | Подробное логирование | Мониторинг кэша и производительности |
| **Статусы заказов** | processing, completed | Триггерные статусы | Настраиваемый список |

### 📊 **Настройка статусов заказов**

По умолчанию отправляются заказы со статусами:
- `completed` (Выполнен)
- `processing` (В обработке)

Можно настроить дополнительные статусы:
- `pending` (Ожидает оплаты)
- `on-hold` (На удержании)
- `cancelled` (Отменен)
- `refunded` (Возвращен)

---

## 🔍 ДИАГНОСТИКА

### 📋 **Проверка логов**

```bash
# Основные логи плагина (новое расположение)
tail -f /wp-content/uploads/tablecrm_integration_complete.log

# Мониторинг производительности кэша
grep "кэш" /wp-content/uploads/tablecrm_integration_complete.log

# Поиск ошибок безопасности
grep "ERROR\|валидация\|символа" /wp-content/uploads/tablecrm_integration_complete.log

# Мониторинг API запросов
grep "API запрос\|API ответ" /wp-content/uploads/tablecrm_integration_complete.log
```

### 🔧 **Улучшенное тестирование соединения**

1. **Настройки** → **TableCRM Complete**
2. **Тестировать соединение** - теперь проверяет:
   - ✅ Длину API ключа (мин. 32 символа)
   - ✅ Корректность формата URL
   - ✅ Доступность API эндпоинта
   - ✅ Валидность JSON ответа

### 📊 **Мониторинг WordPress Cron**

```bash
# Проверка запланированных задач
wp cron event list | grep tablecrm_complete

# Запуск задач вручную для тестирования
wp cron event run tablecrm_complete_schedule_send
```

### 🛠️ **Распространенные проблемы и решения**

| Проблема | Статус | Решение | 🆕 Дополнительно |
|----------|--------|---------|------------------|
| **Желтая плашка при оформлении заказа** | ✅ **ИСПРАВЛЕНО** | Автоматическая очистка буфера | Заказы создаются с первого раза |
| **100% скидка в TableCRM** | ✅ **ИСПРАВЛЕНО** | Убрана передача скидок | Корректное отображение итогов |
| **Неверный статус оплаты** | ✅ **ИСПРАВЛЕНО** | Раздельные настройки статусов | Настройте "Оплаченные статусы" |
| "API Key должен содержать минимум 32 символа" | ⚠️ Требует действий | Получите новый токен в TableCRM | Проверьте отсутствие пробелов |
| "Некорректный формат API URL" | ⚠️ Требует действий | Используйте полный URL с https:// | Проверьте наличие trailing slash |
| "Некорректный ответ API (не JSON)" | ⚠️ Внешняя проблема | Проблема с сервером TableCRM | Обратитесь в техподдержку |
| Медленная работа с большими заказами | ✅ Автоматически решено | Кэширование номенклатуры | Мониторинг в логах |
| Накопление cron задач | ✅ Автоматически решено | Автоочистка каждые 5 мин | Мониторинг через wp cron |

### 🔧 ДИАГНОСТИКА ПРОБЛЕМ С ДОСТАВКОЙ

### Почему до версии v1.0.24 информация о доставке не отправлялась?

**Корневая причина:** Функция `send_delivery_info()` существовала и вызывалась, но данные "терялись" в функции `adapt_data_format()`.

#### Старая проблемная логика:

```php
private function adapt_data_format($endpoint, $data) {
    if ($endpoint === 'docs_sales/') {
        // Работало только для создания документа
        return array($document_data);
    }
    
    // ❌ Для delivery_info возвращался пустой массив!
    return array();
}
```

#### Исправленная логика:

```php
private function adapt_data_format($endpoint, $data) {
    if ($endpoint === 'docs_sales/') {
        // Адаптация для создания документа
        return array($document_data);
    }
    
    // ✅ Для остальных эндпоинтов возвращаем данные без изменений
    return $data;
}
```

### Как диагностировать проблему:

#### Признаки старой проблемной версии:
```bash
# 1. Пустые данные в запросах
grep "Данные: \[\]" /wp-content/uploads/tablecrm_integration_complete.log

# 2. Ошибки 422 Validation
grep "HTTP 422.*recipient.*required" /wp-content/uploads/tablecrm_integration_complete.log

# 3. Функция вызывается, но без успеха
grep "send_delivery_info" /wp-content/uploads/tablecrm_integration_complete.log
```

#### Признаки исправленной версии:
```bash
# 1. Детальные данные доставки
grep "Подготовленные данные для delivery_info" /wp-content/uploads/tablecrm_integration_complete.log

# 2. Успешные отправки
grep "Информация о доставке.*успешно отправлена" /wp-content/uploads/tablecrm_integration_complete.log

# 3. Поддержка плагинов доставки
grep "CodeRockz.*timestamp" /wp-content/uploads/tablecrm_integration_complete.log
```

### Что исправлено в v1.0.24:

1. ✅ **Логика adapt_data_format()** - теперь не обнуляет данные доставки
2. ✅ **Детальное логирование** - видно какие данные отправляются
3. ✅ **Поддержка плагинов доставки** - CodeRockz, Order Delivery Date Pro и др.
4. ✅ **HPOS совместимость** - работа с новой системой хранения заказов
5. ✅ **Нормализация телефонов** - приведение к формату +7XXXXXXXXXX
6. ✅ **Fallback адресов** - shipping → billing автоматически

**Подробная диагностика:** См. `ДИАГНОСТИКА_ПРОБЛЕМЫ_ДОСТАВКИ_API.md`

---

## 💾 ПРОИЗВОДИТЕЛЬНОСТЬ

### 📊 **Улучшения производительности v1.0.24:**

| Сценарий | Без кэша | С кэшем | Улучшение |
|----------|----------|---------|-----------|
| Заказ с 1 уникальным товаром | 3 API | 3 API | 0% |
| Заказ с 5 одинаковыми товарами | 7 API | 3 API | **57%** |
| Заказ с 10 разными товарами | 12 API | 12 API | 0% |
| Заказ с 10 повторяющимися товарами | 12 API | 3 API | **75%** |

### 🚀 **Рекомендации для максимальной производительности:**

1. **Стандартизируйте названия товаров** - повышает эффективность кэша
2. **Группируйте похожие товары** - снижает количество уникальных номенклатур  
3. **Мониторьте логи** - отслеживайте статистику кэша
4. **Используйте режим отладки** - для анализа производительности

---

## 🔒 БЕЗОПАСНОСТЬ

### 🛡️ **Базовые меры безопасности:**
- ✅ Проверка токенов безопасности для AJAX
- ✅ Санитизация всех входных данных
- ✅ Проверка прав доступа пользователей
- ✅ Безопасное хранение API ключей

### 🆕 **Новые усиленные меры безопасности:**
- 🔒 **Строгая валидация API ключа** - минимум 32 символа
- 🔒 **Фильтрация символов** - только разрешенные символы
- 🔒 **Валидация URL** - проверка корректности формата
- 🔒 **Проверка JSON** - валидация ответов API
- 🔒 **Защита от XSS** - полная санитизация HTML
- 🔒 **Защита от injection** - фильтрация SQL и других инъекций

---

## 📚 ДОПОЛНИТЕЛЬНАЯ ДОКУМЕНТАЦИЯ

### 📖 **Детальные руководства:**
- [ПОЛНАЯ_ВЕРСИЯ_v1.0.24_РУКОВОДСТВО.md](./ПОЛНАЯ_ВЕРСИЯ_v1.0.24_РУКОВОДСТВО.md) - Подробное руководство
- [README_plugin.md](./README_plugin.md) - Техническая документация
- [CHANGELOG.md](./CHANGELOG.md) - История изменений

### 🔧 **Для разработчиков:**
- [API документация TableCRM](https://app.tablecrm.com/docs/api)
- [WordPress Plugin Development](https://developer.wordpress.org/plugins/)
- [WooCommerce Developer Resources](https://woocommerce.github.io/code-reference/)

### 📞 **Техническая поддержка:**
- Логи: `/wp-content/uploads/tablecrm_integration_complete.log`
- Email: <EMAIL>
- Документация: [Wiki проекта](https://github.com/your-repo/wiki)

---

## 🎉 **Заключение**

**TableCRM Integration Complete v1.0.24** - это самая продвинутая версия интеграции, которая предлагает:

- 🚀 **Максимальную производительность** благодаря кэшированию
- 🔒 **Высочайший уровень безопасности** с многоуровневой валидацией
- 🎯 **Полную автоматизацию** создания товаров и клиентов
- 📊 **Детальную аналитику** с UTM и данными доставки
- 🛡️ **Надежность** с автоочисткой и защитой от дублирования

**Готов к использованию в продакшене! 🚀**

[![Download](https://img.shields.io/badge/Download-v1.0.24-green.svg)](https://path-to-download/tablecrm-integration-complete-v1.0.24.zip)
[![Documentation](https://img.shields.io/badge/Docs-Read%20More-blue.svg)](./ПОЛНАЯ_ВЕРСИЯ_v1.0.24_РУКОВОДСТВО.md)

## Интеграция API информации о доставке

### Описание

Плагин автоматически отправляет информацию о доставке в TableCRM сразу после создания документа продаж. Используется специальный эндпоинт `POST /docs_sales/{id}/delivery_info/` согласно официальной API документации TableCRM.

### Структура отправляемых данных

```json
{
  "recipient": {
    "name": "Имя Получателя",
    "phone": "+79123456789"
  },
  "address": "Улица, дом, город, почтовый код",
  "note": "Комментарий клиента к заказу",
  "delivery_date": 1672531200
}
```

### Логика работы

1. **Создание документа продаж** → API `docs_sales/`
2. **Получение document_id** из ответа TableCRM
3. **Автоматический вызов** `send_delivery_info($document_id, $order)`
4. **Отправка данных доставки** → API `docs_sales/{document_id}/delivery_info/`
5. **Логирование результата** и добавление заметки к заказу

### Поддерживаемые источники данных

#### Дата доставки
- **CodeRockz WooCommerce Delivery Date Time Pro**: мета-поля `delivery_date`, `delivery_time`
- **Order Delivery Date Pro**: мета-поле `_orddd_time_slot`
- **WooCommerce Delivery Time**: мета-поле `_delivery_time`
- **Стандартные поля**: `_delivery_date`

#### Данные получателя
- **Имя**: приоритет shipping → billing адрес
- **Телефон**: приоритет shipping_phone → billing_phone
- **Адрес**: полная сборка из shipping/billing полей

#### Дополнительная информация
- **Комментарий клиента**: `$order->get_customer_note()`
- **Дата в Unix timestamp**: автоматическое преобразование форматов

### Реализация в версиях плагина

#### tablecrm-integration-complete-v1.0.24.php
```php
// Вызов после создания документа (строка 518)
$this->send_delivery_info($document_id, $order);

// Полная реализация метода (строка 1267)
private function send_delivery_info($document_id, $order) {
    // Формирование данных доставки
    $delivery_data = [
        'recipient' => [
            'name'  => trim($order->get_shipping_first_name() . ' ' . $order->get_shipping_last_name()) ?: trim($order->get_billing_first_name() . ' ' . $order->get_billing_last_name()),
            'phone' => $this->get_recipient_phone($order),
        ],
        'address' => $this->get_recipient_address($order),
        'note'    => $order->get_customer_note(),
    ];
    
    // Добавление даты доставки если найдена
    if ($delivery_timestamp = $this->get_delivery_date($order)) {
        $delivery_data['delivery_date'] = $delivery_timestamp;
    }
    
    // Отправка через API
    $response = $this->make_api_request("docs_sales/{$document_id}/delivery_info/", $delivery_data, 'POST');
}
```

#### tablecrm-integration-fixed-v3-secure.php
```php
// Вызов после создания документа (строка 584)
$this->send_delivery_info($document_id, $order);

// Полная реализация метода (строка 854)
private function send_delivery_info($document_id, $order) {
    // Аналогичная логика с упрощённой структурой
    $delivery_data = array(
        'recipient' => array(
            'name'  => trim($order->get_shipping_first_name() . ' ' . $order->get_shipping_last_name()) ?: trim($order->get_billing_first_name() . ' ' . $order->get_billing_last_name()),
            'phone' => $this->get_recipient_phone($order),
        ),
        'address'   => $this->get_recipient_address($order),
        'note'      => $order->get_customer_note(),
    );
    
    // Отправка через make_api_request
    $response = $this->make_api_request("docs_sales/{document_id}/delivery_info/", $delivery_data);
}
```

### Обработка ошибок

При возникновении ошибок:
- **422 Validation Error**: детальное логирование полей с ошибками
- **Сетевые ошибки**: WP_Error обработка  
- **API недоступность**: защита через Circuit Breaker
- **Логирование**: все операции записываются в лог

### Проверка работы

#### В логах WordPress
```
[2024-01-15 10:30:15] [INFO] Запуск функции send_delivery_info для заказа ID: 12345
[2024-01-15 10:30:15] [INFO] Подготовленные данные для API доставки: {"recipient":{"name":"Иван Петров","phone":"+79123456789"},"address":"ул. Ленина, 1, Москва, 101000","note":"Доставить после 18:00"}
[2024-01-15 10:30:16] [SUCCESS] Информация о доставке успешно отправлена в TableCRM. Document ID: 67890
```

#### В заметках заказа WooCommerce
- ✅ "Информация о доставке успешно отправлена в TableCRM."
- ❌ "Не удалось отправить информацию о доставке в TableCRM. Причина: ..."

#### В TableCRM
В карточке документа будет заполнен блок **"Доставка"** с:
- Данными получателя (имя, телефон)
- Адресом доставки
- Датой и временем доставки
- Комментарием клиента

### HPOS совместимость

Все методы работают с новой системой хранения заказов WordPress:

```php
private function get_order_meta($order, $key) {
    return method_exists($order, 'get_meta') ? $order->get_meta($key, true) : get_post_meta($order->get_id(), $key, true);
}
```

### Тестирование

1. Создайте тестовый заказ в WooCommerce
2. Убедитесь что заказ отправлен в TableCRM
3. Проверьте логи на наличие сообщений `send_delivery_info`
4. Откройте документ в TableCRM и проверьте секцию "Доставка"

## Установка

1. Загрузите файл плагина на сервер
2. Настройте API ключи TableCRM
3. Активируйте плагин в WordPress
4. Создайте тестовый заказ для проверки

## Настройка

В админ-панели WordPress → Настройки → TableCRM Complete:
- API URL: `https://app.tablecrm.com/api/v1/`
- API Key: ваш токен TableCRM
- Organization ID, Cashbox ID, Warehouse ID
- Включить отправку заказов
- Режим отладки

## Поддержка

При возникновении проблем проверьте:
1. Логи WordPress (`/wp-content/uploads/tablecrm_integration_complete.log`)
2. Настройки API в админ-панели
3. Статус плагинов WooCommerce и Action Scheduler
4. Права доступа к файлам и директориям 