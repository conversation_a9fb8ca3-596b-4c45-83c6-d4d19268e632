<?php
// debug_send_last_request.php
// Usage: open in browser or CLI to resend last request body found in log
header('Content-Type: text/plain; charset=utf-8');

$wp_load = null; $dir = __DIR__;
for($i=0;$i<5;$i++){
  if(file_exists($dir.'/wp-load.php')){ $wp_load=$dir.'/wp-load.php'; break; }
  $dir = dirname($dir);
}
if(!$wp_load){ die("wp-load.php not found\n"); }
require_once $wp_load;

$log_file = WP_CONTENT_DIR . '/uploads/tablecrm_integration_complete.log';
if(!file_exists($log_file)){
  die("Log file not found: $log_file\n");
}

$lines = file($log_file, FILE_IGNORE_NEW_LINES|FILE_SKIP_EMPTY_LINES);
$lines = array_reverse($lines); // search from bottom
$body_json = null;
foreach($lines as $line){
  if(strpos($line, 'Request body:') !== false){
    $pos = strpos($line, 'Request body:');
    $body_json = trim(substr($line, $pos + 13));
    break;
  }
}
if(!$body_json){
  die("No 'Request body' found in log.\n");
}

echo "Found last Request body string (truncated to 300 chars):\n".substr($body_json,0,300)."\n\n";

// Decode JSON
$data = json_decode($body_json, true);
if(!$data){
  die("Failed to decode JSON from Request body.\n");
}

$api_url = rtrim(get_option('tablecrm_complete_api_url','https://app.tablecrm.com/api/v1/'),'/').'/';
$token   = get_option('tablecrm_complete_api_key');
if(empty($token)){
  die("API token empty in settings.\n");
}

$endpoint = $api_url . 'docs_sales/?token=' . $token;
$args = array(
  'method'  => 'POST',
  'headers' => array('Content-Type'=>'application/json; charset=utf-8','User-Agent'=>'TableCRM-Test-Manual'),
  'body'    => json_encode($data, JSON_UNESCAPED_UNICODE),
  'timeout' => 30,
);

$response = wp_remote_post($endpoint, $args);
if(is_wp_error($response)){
  echo "WP_Error: ".$response->get_error_message()."\n";
  exit;
}
$status = wp_remote_retrieve_response_code($response);
$body   = wp_remote_retrieve_body($response);

echo "HTTP status: $status\n";

echo "Response body (first 1000 chars):\n".substr($body,0,1000)."\n";
?> 