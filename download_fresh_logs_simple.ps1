# Простой скрипт для скачивания логов TableCRM
Write-Host "Скачиваю свежие логи с сервера..." -ForegroundColor Green

# Данные для подключения к серверу
$ftpServer = "vashurok.beget.tech"
$ftpUser = "vashurok_user"
$ftpPassword = "LnP@S3n2024"

# Создаем папку для логов
$logFolder = "server_logs_fresh"
if (!(Test-Path $logFolder)) {
    New-Item -ItemType Directory -Path $logFolder
}

Write-Host "Папка для логов: $logFolder" -ForegroundColor Yellow

# Пути к файлам логов на сервере
$logFiles = @(
    "/wp-content/uploads/tablecrm_integration_complete.log",
    "/wp-content/debug.log",
    "/wp-content/uploads/wc-logs/"
)

Write-Host "Попытка скачать логи..." -ForegroundColor Yellow

# Простое уведомление пользователю
Write-Host ""
Write-Host "⚠️  ВНИМАНИЕ!" -ForegroundColor Red
Write-Host "Этот скрипт требует настройки FTP данных." -ForegroundColor Yellow
Write-Host "Пожалуйста, укажите корректные данные FTP в переменных выше." -ForegroundColor Yellow
Write-Host ""
Write-Host "Альтернативные способы проверки логов:" -ForegroundColor Green
Write-Host "1. Войдите в админ-панель WordPress" -ForegroundColor White
Write-Host "2. Перейдите в TableCRM Settings" -ForegroundColor White
Write-Host "3. Создайте тестовый заказ" -ForegroundColor White
Write-Host "4. Проверьте файл wp-content/uploads/tablecrm_integration_complete.log" -ForegroundColor White
Write-Host ""
Write-Host "Или используйте cPanel/файловый менеджер хостинга" -ForegroundColor Green

# Скачиваем логи с сервера
try {
    # Создаем FTP клиент
    $request = [System.Net.FtpWebRequest]::Create("ftp://$ftpServer/wp-content/uploads/tablecrm_integration_complete.log")
    $request.Credentials = New-Object System.Net.NetworkCredential($ftpUser, $ftpPassword)
    $request.Method = [System.Net.WebRequestMethods+Ftp]::DownloadFile
    
    Write-Host "Подключаюсь к FTP серверу..." -ForegroundColor Yellow
    
    $response = $request.GetResponse()
    $responseStream = $response.GetResponseStream()
    
    $localLogFile = Join-Path $logFolder "tablecrm_integration_complete.log"
    $fileStream = [System.IO.File]::Create($localLogFile)
    
    Write-Host "Скачиваю главный лог файл..." -ForegroundColor Yellow
    $responseStream.CopyTo($fileStream)
    
    $fileStream.Close()
    $responseStream.Close()
    $response.Close()
    
    Write-Host "✅ Лог файл скачан: $localLogFile" -ForegroundColor Green
    
    # Показываем последние строки
    if (Test-Path $localLogFile) {
        Write-Host ""
        Write-Host "📋 ПОСЛЕДНИЕ 20 СТРОК ЛОГА:" -ForegroundColor Cyan
        Write-Host "================================" -ForegroundColor Cyan
        Get-Content $localLogFile -Tail 20 | ForEach-Object {
            Write-Host $_ -ForegroundColor White
        }
    }
    
} catch {
    Write-Host "❌ Ошибка скачивания: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Проверьте FTP данные и доступность сервера" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🔍 Для диагностики проблемы также проверьте:" -ForegroundColor Cyan
Write-Host "- Используется ли исправленная версия плагина на сервере" -ForegroundColor White
Write-Host "- Настройки 'Оплаченные статусы' в админ-панели" -ForegroundColor White
Write-Host "- Создайте тестовый заказ со статусом 'pending'" -ForegroundColor White
Write-Host "- Проверьте что заказ НЕ отмечается как оплаченный в TableCRM" -ForegroundColor White

Read-Host "Нажмите Enter для завершения" 