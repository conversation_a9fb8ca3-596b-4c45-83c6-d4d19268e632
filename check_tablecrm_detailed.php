<?php
/**
 * Детальная проверка TableCRM API
 * Тестирует различные эндпоинты для поиска тестового заказа
 */

echo "🔍 ДЕТАЛЬНАЯ ПРОВЕРКА TableCRM API\n";
echo "===================================\n\n";

// Используем токен из ссылки
$api_token = 'af1874616430e04cfd4bce30035789907e899fc7c3a1a4bb27254828ff304a77';
$api_url = 'https://app.tablecrm.com/api/v1/';

echo "📋 Параметры подключения:\n";
echo "API URL: $api_url\n";
echo "Token: " . substr($api_token, 0, 10) . "...\n\n";

/**
 * Функция для выполнения API запроса
 */
function make_api_request($endpoint, $params = [], $method = 'GET') {
    global $api_url, $api_token;
    
    $url = rtrim($api_url, '/') . '/' . ltrim($endpoint, '/');
    
    // Добавляем токен к параметрам
    $params['token'] = $api_token;
    
    if ($method === 'GET') {
        $url .= '?' . http_build_query($params);
        $context_options = [
            'http' => [
                'method' => 'GET',
                'header' => [
                    'User-Agent: TableCRM-Detailed-Check/1.0',
                    'Accept: application/json'
                ],
                'timeout' => 30
            ]
        ];
    }
    
    $context = stream_context_create($context_options);
    
    echo "🔗 $method: " . str_replace($api_token, '***', $url) . "\n";
    
    $response = file_get_contents($url, false, $context);
    
    if ($response === false) {
        echo "❌ Ошибка при выполнении запроса\n";
        return false;
    }
    
    // Получаем HTTP статус
    $http_status = 200;
    if (isset($http_response_header[0])) {
        preg_match('/HTTP\/\d\.\d\s+(\d+)/', $http_response_header[0], $matches);
        if (isset($matches[1])) {
            $http_status = (int)$matches[1];
        }
    }
    
    echo "📊 HTTP: $http_status";
    
    if ($http_status !== 200) {
        echo " ❌ ОШИБКА\n";
        echo "Ответ: " . substr($response, 0, 200) . "...\n\n";
        return false;
    }
    
    echo " ✅ OK\n";
    
    $decoded = json_decode($response, true);
    if ($decoded === null) {
        echo "❌ Ошибка JSON\n";
        echo "Ответ: " . substr($response, 0, 200) . "...\n\n";
        return false;
    }
    
    return $decoded;
}

/**
 * Показать краткую информацию о результате
 */
function show_results_summary($data, $title = "Результат") {
    echo "📊 $title:\n";
    
    if (isset($data['count'])) {
        echo "   Всего записей: {$data['count']}\n";
    }
    
    if (isset($data['results'])) {
        $count = count($data['results']);
        echo "   Получено: $count записей\n";
        
        if ($count > 0) {
            echo "   Первая запись:\n";
            $first = $data['results'][0];
            foreach ($first as $key => $value) {
                if (is_scalar($value)) {
                    $display_value = strlen($value) > 50 ? substr($value, 0, 50) . '...' : $value;
                    echo "     $key: $display_value\n";
                }
            }
        }
    } else {
        echo "   Структура: " . implode(', ', array_keys($data)) . "\n";
    }
    
    echo "\n";
}

// Список эндпоинтов для проверки
$endpoints_to_check = [
    'organizations/' => 'Организации',
    'contragents/' => 'Контрагенты', 
    'docs_sales/' => 'Документы продаж',
    'nomenclature/' => 'Номенклатура',
    'docs/' => 'Документы (общие)',
    'orders/' => 'Заказы',
    'deals/' => 'Сделки',
    'projects/' => 'Проекты',
    'payboxes/' => 'Кассы (Payboxes)',
    'warehouses/' => 'Склады'
];

echo "🔍 ТЕСТИРОВАНИЕ ДОСТУПНЫХ ЭНДПОИНТОВ\n";
echo "====================================\n\n";

$available_endpoints = [];
$accessible_data = [];

foreach ($endpoints_to_check as $endpoint => $description) {
    echo "🔹 Проверка: $description ($endpoint)\n";
    
    $result = make_api_request($endpoint, ['limit' => 5]);
    
    if ($result !== false) {
        $available_endpoints[] = $endpoint;
        $accessible_data[$endpoint] = $result;
        show_results_summary($result, $description);
    } else {
        echo "   ❌ Недоступен\n\n";
    }
}

echo "📋 СВОДКА ДОСТУПНЫХ ЭНДПОИНТОВ\n";
echo "==============================\n";
echo "✅ Доступно: " . count($available_endpoints) . " из " . count($endpoints_to_check) . "\n";
foreach ($available_endpoints as $endpoint) {
    echo "   - $endpoint ({$endpoints_to_check[$endpoint]})\n";
}
echo "\n";

// Детальная проверка payboxes (так как в ссылке был payboxes)
if (in_array('payboxes/', $available_endpoints)) {
    echo "💰 ДЕТАЛЬНАЯ ПРОВЕРКА PAYBOXES\n";
    echo "==============================\n";
    
    $payboxes_data = $accessible_data['payboxes/'];
    
    if (isset($payboxes_data['results']) && !empty($payboxes_data['results'])) {
        foreach ($payboxes_data['results'] as $index => $paybox) {
            echo "📦 Касса #" . ($index + 1) . ":\n";
            echo "   ID: " . ($paybox['id'] ?? 'не указан') . "\n";
            echo "   Название: " . ($paybox['name'] ?? 'не указано') . "\n";
            echo "   Тип: " . ($paybox['type'] ?? 'не указан') . "\n";
            echo "   Организация: " . ($paybox['organization'] ?? 'не указана') . "\n";
            echo "   Активна: " . (($paybox['is_active'] ?? false) ? 'Да' : 'Нет') . "\n";
            
            if (isset($paybox['balance'])) {
                echo "   Баланс: {$paybox['balance']} руб.\n";
            }
            
            echo "\n";
        }
    }
}

// Поиск недавних записей во всех доступных эндпоинтах
echo "🕐 ПОИСК НЕДАВНИХ ЗАПИСЕЙ\n";
echo "=========================\n";

$recent_records = [];
$cutoff_time = time() - 7 * 24 * 3600; // Неделя назад

foreach ($available_endpoints as $endpoint) {
    if (!isset($accessible_data[$endpoint]['results'])) continue;
    
    echo "🔍 Проверка $endpoint на недавние записи...\n";
    
    foreach ($accessible_data[$endpoint]['results'] as $record) {
        $record_time = null;
        
        // Ищем поля с датой/временем
        $time_fields = ['created_at', 'updated_at', 'dated', 'date_created', 'timestamp'];
        
        foreach ($time_fields as $field) {
            if (isset($record[$field])) {
                $record_time = strtotime($record[$field]);
                break;
            }
        }
        
        if ($record_time && $record_time > $cutoff_time) {
            $recent_records[] = [
                'endpoint' => $endpoint,
                'record' => $record,
                'time' => $record_time,
                'age_hours' => round((time() - $record_time) / 3600, 1)
            ];
        }
    }
}

if (!empty($recent_records)) {
    // Сортируем по времени (новые первые)
    usort($recent_records, function($a, $b) {
        return $b['time'] - $a['time'];
    });
    
    echo "✅ Найдено недавних записей: " . count($recent_records) . "\n\n";
    
    foreach ($recent_records as $index => $item) {
        echo "📄 Запись #" . ($index + 1) . " (возраст: {$item['age_hours']} часов):\n";
        echo "   Источник: {$item['endpoint']}\n";
        echo "   ID: " . ($item['record']['id'] ?? 'не указан') . "\n";
        
        // Показываем ключевые поля
        $key_fields = ['name', 'number', 'title', 'email', 'phone', 'comment'];
        foreach ($key_fields as $field) {
            if (isset($item['record'][$field]) && !empty($item['record'][$field])) {
                $value = $item['record'][$field];
                if (strlen($value) > 100) $value = substr($value, 0, 100) . '...';
                echo "   " . ucfirst($field) . ": $value\n";
            }
        }
        
        // Поиск признаков тестовых данных
        $test_indicators = [];
        foreach ($item['record'] as $key => $value) {
            if (is_string($value) && 
                (stripos($value, 'тест') !== false || 
                 stripos($value, 'test') !== false ||
                 stripos($value, 'Айгуль') !== false)) {
                $test_indicators[] = "$key содержит '$value'";
            }
        }
        
        if (!empty($test_indicators)) {
            echo "   🧪 ПРИЗНАКИ ТЕСТОВЫХ ДАННЫХ:\n";
            foreach ($test_indicators as $indicator) {
                echo "     - $indicator\n";
            }
        }
        
        echo "\n";
    }
    
} else {
    echo "📝 Недавних записей не найдено\n";
    echo "Возможно, тестовые заказы еще не создавались или система не активна.\n\n";
}

// Проверка специфических тестовых паттернов
echo "🧪 ПОИСК ТЕСТОВЫХ ДАННЫХ\n";
echo "========================\n";

$test_patterns = ['тест', 'test', 'demo', 'sample', 'Айгуль', 'example'];
$found_test_data = [];

foreach ($available_endpoints as $endpoint) {
    if (!isset($accessible_data[$endpoint]['results'])) continue;
    
    foreach ($accessible_data[$endpoint]['results'] as $record) {
        foreach ($test_patterns as $pattern) {
            foreach ($record as $field => $value) {
                if (is_string($value) && stripos($value, $pattern) !== false) {
                    $found_test_data[] = [
                        'endpoint' => $endpoint,
                        'field' => $field,
                        'value' => $value,
                        'pattern' => $pattern,
                        'record_id' => $record['id'] ?? 'unknown'
                    ];
                }
            }
        }
    }
}

if (!empty($found_test_data)) {
    echo "✅ Найдены тестовые данные: " . count($found_test_data) . " совпадений\n\n";
    
    foreach ($found_test_data as $index => $item) {
        echo "🧪 Тестовые данные #" . ($index + 1) . ":\n";
        echo "   Эндпоинт: {$item['endpoint']}\n";
        echo "   ID записи: {$item['record_id']}\n";
        echo "   Поле: {$item['field']}\n";
        echo "   Значение: {$item['value']}\n";
        echo "   Совпадение с: '{$item['pattern']}'\n\n";
    }
    
} else {
    echo "📝 Тестовые данные не найдены\n";
    echo "Система содержит только производственные данные.\n\n";
}

echo "✅ ПРОВЕРКА ЗАВЕРШЕНА\n";
echo "====================\n";
echo "🔗 Использованный токен: " . substr($api_token, 0, 10) . "...\n";
echo "⏱️ Время выполнения: " . round((microtime(true) - $_SERVER['REQUEST_TIME_FLOAT']) * 1000, 2) . " мс\n";
echo "📊 Протестировано эндпоинтов: " . count($endpoints_to_check) . "\n";
echo "✅ Доступных эндпоинтов: " . count($available_endpoints) . "\n";

if (!empty($recent_records)) {
    echo "🕐 Найдено недавних записей: " . count($recent_records) . "\n";
}

if (!empty($found_test_data)) {
    echo "🧪 Найдено тестовых данных: " . count($found_test_data) . "\n";
}