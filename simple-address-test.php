<?php
/**
 * Простой тест для проверки удаления имени из адреса доставки
 * Без зависимостей от WordPress
 */

echo "=== Тест удаления имени из адреса доставки ===\n\n";

// Создаем упрощенную версию класса для тестирования
class AddressCleanupTest {
    
    // Копируем метод из основного класса
    private function get_recipient_name_for_removal($order) {
        // Получаем все возможные варианты имени
        $names = array();

        // Имя из доставки
        $shipping_first = trim($order['shipping_first_name']);
        $shipping_last = trim($order['shipping_last_name']);
        if (!empty($shipping_first)) $names[] = $shipping_first;
        if (!empty($shipping_last)) $names[] = $shipping_last;
        if (!empty($shipping_first) && !empty($shipping_last)) {
            $names[] = $shipping_first . ' ' . $shipping_last;
        }

        // Имя из платежных данных
        $billing_first = trim($order['billing_first_name']);
        $billing_last = trim($order['billing_last_name']);
        if (!empty($billing_first)) $names[] = $billing_first;
        if (!empty($billing_last)) $names[] = $billing_last;
        if (!empty($billing_first) && !empty($billing_last)) {
            $names[] = $billing_first . ' ' . $billing_last;
        }

        // Возвращаем самое длинное имя для более точного удаления
        if (!empty($names)) {
            usort($names, function($a, $b) {
                return strlen($b) - strlen($a);
            });
            return $names[0];
        }

        return '';
    }
    
    // Копируем метод очистки адреса
    private function clean_delivery_address($address, $order) {
        if (empty($address)) {
            return '';
        }

        // Убираем имя получателя из конца адреса
        $recipient_name = $this->get_recipient_name_for_removal($order);
        if (!empty($recipient_name) && !empty($address)) {
            $original_address = $address;
            // Убираем имя из конца адреса (с запятой или без)
            $address = preg_replace('/,\s*' . preg_quote($recipient_name, '/') . '\s*$/ui', '', $address);
            $address = preg_replace('/\s+' . preg_quote($recipient_name, '/') . '\s*$/ui', '', $address);

            if ($original_address !== $address) {
                echo "Удалено имя '{$recipient_name}' из адреса. Было: '{$original_address}', стало: '{$address}'\n";
            }
        }

        return trim($address);
    }
    
    // Публичный метод для тестирования
    public function test_address_cleanup($address, $order) {
        return $this->clean_delivery_address($address, $order);
    }
}

// Создаем тестовые данные
$test_cases = [
    [
        'name' => 'Тест 1: Имя в конце адреса с запятой',
        'address' => 'г Москва, ул Мантулинская, д 16, Иван Петров',
        'order' => [
            'shipping_first_name' => 'Иван',
            'shipping_last_name' => 'Петров',
            'billing_first_name' => '',
            'billing_last_name' => ''
        ]
    ],
    [
        'name' => 'Тест 2: Имя в конце адреса без запятой',
        'address' => 'г Москва, ул Мантулинская, д 16 Иван Петров',
        'order' => [
            'shipping_first_name' => 'Иван',
            'shipping_last_name' => 'Петров',
            'billing_first_name' => '',
            'billing_last_name' => ''
        ]
    ],
    [
        'name' => 'Тест 3: Только имя в конце',
        'address' => 'г Москва, ул Мантулинская, д 16, Иван',
        'order' => [
            'shipping_first_name' => 'Иван',
            'shipping_last_name' => '',
            'billing_first_name' => '',
            'billing_last_name' => ''
        ]
    ],
    [
        'name' => 'Тест 4: Имя в середине адреса (не должно удаляться)',
        'address' => 'г Москва, ул Иван Петров, д 16',
        'order' => [
            'shipping_first_name' => 'Иван',
            'shipping_last_name' => 'Петров',
            'billing_first_name' => '',
            'billing_last_name' => ''
        ]
    ],
    [
        'name' => 'Тест 5: Адрес без имени',
        'address' => 'г Москва, ул Мантулинская, д 16',
        'order' => [
            'shipping_first_name' => 'Иван',
            'shipping_last_name' => 'Петров',
            'billing_first_name' => '',
            'billing_last_name' => ''
        ]
    ],
    [
        'name' => 'Тест 6: Приоритет billing над shipping',
        'address' => 'г Москва, ул Мантулинская, д 16, Анна Сидорова',
        'order' => [
            'shipping_first_name' => 'Иван',
            'shipping_last_name' => 'Петров',
            'billing_first_name' => 'Анна',
            'billing_last_name' => 'Сидорова'
        ]
    ]
];

$tester = new AddressCleanupTest();

foreach ($test_cases as $test) {
    echo "--- {$test['name']} ---\n";
    echo "Исходный адрес: '{$test['address']}'\n";
    
    $cleaned = $tester->test_address_cleanup($test['address'], $test['order']);
    echo "Результат: '{$cleaned}'\n";
    
    // Проверяем результат
    $expected_name = '';
    if (!empty($test['order']['billing_first_name']) && !empty($test['order']['billing_last_name'])) {
        $expected_name = $test['order']['billing_first_name'] . ' ' . $test['order']['billing_last_name'];
    } elseif (!empty($test['order']['shipping_first_name']) && !empty($test['order']['shipping_last_name'])) {
        $expected_name = $test['order']['shipping_first_name'] . ' ' . $test['order']['shipping_last_name'];
    } elseif (!empty($test['order']['billing_first_name'])) {
        $expected_name = $test['order']['billing_first_name'];
    } elseif (!empty($test['order']['shipping_first_name'])) {
        $expected_name = $test['order']['shipping_first_name'];
    }
    
    if (!empty($expected_name) && strpos($cleaned, $expected_name) !== false) {
        echo "⚠️ ВНИМАНИЕ: Имя '{$expected_name}' все еще присутствует в адресе!\n";
    } else {
        echo "✅ Тест пройден\n";
    }
    
    echo "\n";
}

echo "=== Тест завершен ===\n";
?>
