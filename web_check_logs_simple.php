<?php
// Simple TableCRM log checker - drop into site root and open in browser.
header('Content-Type: text/html; charset=utf-8');

// Try to locate wp-load.php automatically (max 5 levels up)
$wp_load = null;
$dir = __DIR__;
for ($i = 0; $i < 5; $i++) {
    if (file_exists($dir . '/wp-load.php')) {
        $wp_load = $dir . '/wp-load.php';
        break;
    }
    $dir = dirname($dir);
}

if (!$wp_load) {
    die('<h1 style="color:red">⚠️ Не найден wp-load.php. Поместите файл в корень WordPress.</h1>');
}

require_once $wp_load;

echo "<html><head><title>TableCRM Quick Log Check</title><style>
body{background:#1e1e1e;color:#fff;font-family:monospace;padding:20px}
pre{background:#2d2d2d;padding:10px;overflow-x:auto}
.success{color:#4CAF50}.error{color:#f44336}.warning{color:#ff9800}.info{color:#03A9F4}
.section{margin:20px 0;padding:10px;border:1px solid #333}
</style></head><body>\n";

echo '<h1>🔍 TableCRM Quick Log Check</h1>';

echo "<div class='success'>✅ WordPress загружен</div>";

$log_paths = array(
    WP_CONTENT_DIR . '/uploads/tablecrm_integration_complete.log',
    WP_CONTENT_DIR . '/tablecrm_complete.log',
);

foreach ($log_paths as $log_file) {
    echo "<div class='section'>";
    echo "<h2>Файл лога: <code>$log_file</code></h2>";
    if (file_exists($log_file)) {
        echo "<div class='success'>Файл найден, размер " . number_format(filesize($log_file)) . " байт</div>";
        $lines = file($log_file);
        $total = count($lines);
        echo "<p>Всего строк: $total</p>";
        echo '<pre>';
        $start = max(0, $total - 150);
        for ($i = $start; $i < $total; $i++) {
            $line = htmlspecialchars(trim($lines[$i]));
            if (strpos($line, 'ERROR') !== false) {
                echo "<span class='error'>$line</span>\n";
            } elseif (strpos($line, 'WARNING') !== false) {
                echo "<span class='warning'>$line</span>\n";
            } elseif (strpos($line, 'SUCCESS') !== false) {
                echo "<span class='success'>$line</span>\n";
            } else {
                echo "<span class='info'>$line</span>\n";
            }
        }
        echo '</pre>';
    } else {
        echo "<div class='error'>Файл не найден</div>";
    }
    echo '</div>';
}

echo "<div class='section'><h2>Последние заказы WooCommerce</h2>";
if (function_exists('wc_get_orders')) {
    $orders = wc_get_orders(array('limit'=>5,'orderby'=>'date','order'=>'DESC'));
    foreach ($orders as $order) {
        $id = $order->get_id();
        $status = $order->get_status();
        $doc = $order->get_meta('_tablecrm_complete_document_id');
        echo "<p>Заказ #$id — статус <strong>$status</strong> — " . ($doc?"<span class='success'>отправлен (Doc $doc)</span>":"<span class='error'>не отправлен</span>") . '</p>';
    }
} else {
    echo "<div class='error'>WooCommerce не активен</div>";
}

echo '</div>';

echo "<div class='success'><h2>Готово</h2></div></body></html>"; 