<?php
/**
 * Простой скрипт для извлечения API настроек TableCRM из WordPress
 * Запуск: php get_api_settings.php > api_settings.txt
 */

// Подключение к WordPress (попробуем разные пути)
$wp_paths = array(
    '/wp-config.php',
    '../wp-config.php', 
    '../../wp-config.php',
    '../../../wp-config.php'
);

$wp_found = false;
foreach ($wp_paths as $path) {
    if (file_exists(__DIR__ . $path)) {
        require_once(__DIR__ . $path);
        require_once(ABSPATH . 'wp-load.php');
        $wp_found = true;
        echo "# WordPress найден по пути: " . $path . "\n";
        break;
    }
}

if (!$wp_found) {
    echo "# WordPress не найден. Ручной поиск в базе данных...\n";
    echo "# Проверьте файл wp-config.php в корне сайта\n";
    exit(1);
}

echo "# Настройки TableCRM из WordPress\n";
echo "# Сгенерировано: " . date('Y-m-d H:i:s') . "\n";
echo "# ================================\n\n";

// Функция для расшифровки API ключа
function decrypt_api_key() {
    $stored = get_option('tablecrm_api_key');
    if (!$stored) {
        return '';
    }
    
    // Если ключ зашифрован
    if (strpos($stored, 'enc:') === 0) {
        if (function_exists('tablecrm_decrypt_value')) {
            return tablecrm_decrypt_value($stored);
        }
        // Если функция расшифровки недоступна, возвращаем как есть
        return $stored;
    }
    
    return $stored;
}

// Получаем настройки
$api_url = get_option('tablecrm_api_url', 'https://app.tablecrm.com/api/v1/');
$api_key = decrypt_api_key();
$project_id = get_option('tablecrm_project_id', '');
$organization_id = get_option('tablecrm_organization_id', 38);
$cashbox_id = get_option('tablecrm_cashbox_id', 218);
$warehouse_id = get_option('tablecrm_warehouse_id', 39);
$unit_id = get_option('tablecrm_unit_id', 116);

echo "API_URL=" . $api_url . "\n";
echo "API_KEY=" . $api_key . "\n";
echo "PROJECT_ID=" . $project_id . "\n";
echo "ORGANIZATION_ID=" . $organization_id . "\n";
echo "CASHBOX_ID=" . $cashbox_id . "\n";
echo "WAREHOUSE_ID=" . $warehouse_id . "\n";
echo "UNIT_ID=" . $unit_id . "\n";

echo "\n# Для копирования в standalone скрипт:\n";
echo "# ===================================\n";
echo '$api_url = \'' . $api_url . '\';' . "\n";
echo '$api_key = \'' . $api_key . '\';' . "\n";
echo '$organization_id = ' . $organization_id . ';' . "\n";

echo "\n# Статус настроек:\n";
echo "# ================\n";
echo "API URL: " . (empty($api_url) ? "❌ Не настроен" : "✅ Настроен") . "\n";
echo "API Key: " . (empty($api_key) ? "❌ Не настроен" : "✅ Настроен (" . strlen($api_key) . " символов)") . "\n";
echo "Organization ID: " . (empty($organization_id) ? "❌ Не настроен" : "✅ Настроен ($organization_id)") . "\n";
?> 