<?php
/**
 * Тест правильной схемы данных для TableCRM API
 * Согласно предоставленному примеру от Рушана
 */

// Предотвращаем прямое выполнение
if (!defined('ABSPATH') && php_sapi_name() !== 'cli') {
    die('Прямой доступ запрещен');
}

echo "=== ТЕСТ ПРАВИЛЬНОЙ СХЕМЫ TABLECRM API ===\n";

// Эталонная схема из примера Рушана
$reference_schema = array(
    "dated" => 1749020764,
    "operation" => "Заказ",
    "comment" => "текст",
    "tax_included" => true,
    "tax_active" => true,
    "goods" => array(
        array(
            "price" => 500,
            "quantity" => 1,
            "unit" => 116,
            "discount" => 0,
            "sum_discounted" => 0,
            "nomenclature" => 39697
        )
    ),
    "settings" => (object)array(),
    "warehouse" => 39,
    "contragent" => 330985,
    "paybox" => 218,
    "organization" => 38,
    "status" => false,
    "paid_rubles" => "500.00",
    "paid_lt" => 0
);

echo "Эталонная схема:\n";
echo json_encode(array($reference_schema), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";

// Симулируем данные заказа WooCommerce
$woocommerce_order_data = array(
    'order_id' => 12345,
    'name' => 'Иван Петров',
    'email' => '<EMAIL>',
    'phone' => '****** 779 99 06',
    'order_total' => 1500.00,
    'order_status' => 'processing',
    'order_date' => '2025-01-06 15:30:00',
    'address' => 'ул. Ленина, 10, кв. 5',
    'city' => 'Москва',
    'postcode' => '123456',
    'payment_method' => 'Банковская карта',
    'items' => array(
        array(
            'name' => 'Хлеб белый',
            'quantity' => 2,
            'price' => 800.00,
            'unit_price' => 400.00,
            'sku' => 'BREAD-001',
            'product_id' => 101
        ),
        array(
            'name' => 'Молоко 1л',
            'quantity' => 1,
            'price' => 700.00,
            'unit_price' => 700.00,
            'sku' => 'MILK-001',
            'product_id' => 102
        )
    ),
    'utm_data' => array(
        'utm_source' => 'google',
        'utm_medium' => 'cpc',
        'utm_campaign' => 'winter_sale',
        'utm_term' => 'хлеб',
        'utm_content' => 'ad1',
        'referrer' => 'https://google.com',
        'landing_page' => 'https://example.com/products'
    )
);

echo "Данные заказа WooCommerce:\n";
echo json_encode($woocommerce_order_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";

// Функция преобразования данных согласно новой схеме
function convert_to_tablecrm_schema($order_data) {
    // Настройки TableCRM (из конфигурации плагина)
    $organization_id = 38;      // ID организации
    $cashbox_id = 218;          // ID кассы (paybox)
    $warehouse_id = 39;         // ID склада
    $unit_id = 116;             // ID единицы измерения
    $contragent_id = 330985;    // ID контрагента (будет получен/создан через API)
    
    // Формируем товары
    $goods = array();
    if (!empty($order_data['items'])) {
        foreach ($order_data['items'] as $item) {
            $goods[] = array(
                'price' => floatval($item['unit_price']),
                'quantity' => intval($item['quantity']),
                'unit' => $unit_id,
                'discount' => 0,
                'sum_discounted' => 0,
                'nomenclature' => 39697 // Заглушка, будет получен/создан через API
            );
        }
    } else {
        // Если нет товаров, создаем один общий
        $goods[] = array(
            'price' => floatval($order_data['order_total']),
            'quantity' => 1,
            'unit' => $unit_id,
            'discount' => 0,
            'sum_discounted' => 0,
            'nomenclature' => 39697
        );
    }
    
    // Формируем комментарий
    $comment = "Заказ #{$order_data['order_id']} с сайта WordPress\n";
    $comment .= "Клиент: {$order_data['name']}\n";
    $comment .= "Email: {$order_data['email']}\n";
    $comment .= "Телефон: {$order_data['phone']}\n";
    $comment .= "Адрес: {$order_data['address']}, {$order_data['city']}\n";
    $comment .= "Способ оплаты: {$order_data['payment_method']}\n";
    
    if (!empty($order_data['items'])) {
        $comment .= "\nТовары:\n";
        foreach ($order_data['items'] as $item) {
            $comment .= "- {$item['name']} x{$item['quantity']} = {$item['price']} руб.\n";
        }
    }
    
    if (!empty($order_data['utm_data'])) {
        $comment .= "\nUTM метки:\n";
        foreach ($order_data['utm_data'] as $key => $value) {
            $comment .= "- {$key}: {$value}\n";
        }
    }
    
    // Формируем правильную схему TableCRM
    $tablecrm_data = array(
        'dated' => time(), // Unix timestamp текущего времени
        'operation' => 'Заказ',
        'comment' => $comment,
        'tax_included' => true,
        'tax_active' => true,
        'goods' => $goods,
        'settings' => (object)array(), // Пустой объект
        'warehouse' => $warehouse_id,
        'contragent' => $contragent_id,
        'paybox' => $cashbox_id,
        'organization' => $organization_id,
        'status' => false, // false = не оплачен
        'paid_rubles' => number_format(floatval($order_data['order_total']), 2, '.', ''),
        'paid_lt' => 0
    );
    
    return array($tablecrm_data); // TableCRM требует массив объектов
}

// Преобразуем данные
$converted_data = convert_to_tablecrm_schema($woocommerce_order_data);

echo "Преобразованные данные для TableCRM API:\n";
echo json_encode($converted_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";

// Проверяем соответствие схеме
function validate_schema($data, $reference) {
    $errors = array();
    $first_item = $data[0];
    
    // Проверяем обязательные поля
    $required_fields = array(
        'dated', 'operation', 'comment', 'tax_included', 'tax_active',
        'goods', 'settings', 'warehouse', 'contragent', 'paybox',
        'organization', 'status', 'paid_rubles', 'paid_lt'
    );
    
    foreach ($required_fields as $field) {
        if (!array_key_exists($field, $first_item)) {
            $errors[] = "Отсутствует обязательное поле: $field";
        }
    }
    
    // Проверяем типы данных
    if (isset($first_item['dated']) && !is_int($first_item['dated'])) {
        $errors[] = "Поле 'dated' должно быть integer (Unix timestamp)";
    }
    
    if (isset($first_item['tax_included']) && !is_bool($first_item['tax_included'])) {
        $errors[] = "Поле 'tax_included' должно быть boolean";
    }
    
    if (isset($first_item['tax_active']) && !is_bool($first_item['tax_active'])) {
        $errors[] = "Поле 'tax_active' должно быть boolean";
    }
    
    if (isset($first_item['status']) && !is_bool($first_item['status'])) {
        $errors[] = "Поле 'status' должно быть boolean";
    }
    
    if (isset($first_item['goods']) && !is_array($first_item['goods'])) {
        $errors[] = "Поле 'goods' должно быть массивом";
    }
    
    if (isset($first_item['settings']) && !is_object($first_item['settings'])) {
        $errors[] = "Поле 'settings' должно быть объектом";
    }
    
    // Проверяем структуру товаров
    if (isset($first_item['goods']) && is_array($first_item['goods'])) {
        foreach ($first_item['goods'] as $index => $good) {
            $required_good_fields = array('price', 'quantity', 'unit', 'discount', 'sum_discounted', 'nomenclature');
            foreach ($required_good_fields as $field) {
                if (!array_key_exists($field, $good)) {
                    $errors[] = "Товар $index: отсутствует поле '$field'";
                }
            }
        }
    }
    
    return $errors;
}

$validation_errors = validate_schema($converted_data, $reference_schema);

if (empty($validation_errors)) {
    echo "✅ СХЕМА ВАЛИДНА! Данные соответствуют требованиям TableCRM API\n";
} else {
    echo "❌ ОШИБКИ ВАЛИДАЦИИ:\n";
    foreach ($validation_errors as $error) {
        echo "- $error\n";
    }
}

echo "\n=== ДОПОЛНИТЕЛЬНЫЕ ЭНДПОИНТЫ ===\n";

// Данные для создания контрагента
$contragent_data = array(
    array(
        'name' => $woocommerce_order_data['name'],
        'email' => $woocommerce_order_data['email'],
        'phone' => preg_replace('/[^0-9]/', '', $woocommerce_order_data['phone']),
        'external_id' => 'order_' . $woocommerce_order_data['order_id'],
        'type' => 'individual',
        'comment' => "Клиент с сайта WordPress. Первый заказ: #{$woocommerce_order_data['order_id']}"
    )
);

echo "Данные для создания контрагента:\n";
echo json_encode($contragent_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";

// Данные для создания номенклатуры (для каждого товара)
if (!empty($woocommerce_order_data['items'])) {
    echo "Данные для создания номенклатуры:\n";
    foreach ($woocommerce_order_data['items'] as $item) {
        $nomenclature_data = array(
            array(
                'name' => $item['name'],
                'code' => $item['sku'],
                'external_id' => 'product_' . $item['product_id'],
                'is_active' => true,
                'unit' => 116
            )
        );
        echo json_encode($nomenclature_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
    }
}

echo "\n=== ИНФОРМАЦИЯ О ДОСТАВКЕ ===\n";

$delivery_info = array(
    'address' => $woocommerce_order_data['address'],
    'city' => $woocommerce_order_data['city'],
    'postcode' => $woocommerce_order_data['postcode'],
    'recipient_name' => $woocommerce_order_data['name'],
    'recipient_phone' => $woocommerce_order_data['phone']
);

echo "Данные для отправки информации о доставке:\n";
echo json_encode($delivery_info, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";

echo "=== UTM МЕТКИ ===\n";

$utm_tags = $woocommerce_order_data['utm_data'];

echo "Данные для отправки UTM меток:\n";
echo json_encode($utm_tags, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";

echo "=== ПОСЛЕДОВАТЕЛЬНОСТЬ ОТПРАВКИ ===\n";
echo "1. Поиск/создание контрагента: POST /contragents/\n";
echo "2. Поиск/создание номенклатуры для каждого товара: POST /nomenclatures/\n";
echo "3. Создание документа продаж: POST /docs_sales/\n";
echo "4. Отправка информации о доставке: POST /docs_sales/{id}/delivery_info/\n";
echo "5. Отправка UTM меток: POST /docs_sales/{id}/utm/\n";

echo "\n=== ТЕСТ ЗАВЕРШЕН ===\n";
?> 