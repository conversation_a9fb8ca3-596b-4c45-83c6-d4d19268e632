<?php
/**
 * Комплексная диагностика проблемы с отправкой заказов в TableCRM
 * Использует рабочую версию v1.0.24-FIXED логики
 */

echo "🔍 ДИАГНОСТИКА ПРОБЛЕМЫ С ОТПРАВКОЙ ЗАКАЗОВ В TABLECRM\n";
echo "=" . str_repeat("=", 60) . "\n\n";

// Подключение к WordPress
$wp_config_paths = [
    __DIR__ . '/wp-config.php',
    __DIR__ . '/../wp-config.php',
    __DIR__ . '/../../wp-config.php'
];

$wp_loaded = false;
foreach ($wp_config_paths as $wp_config) {
    if (file_exists($wp_config)) {
        require_once($wp_config);
        require_once(dirname($wp_config) . '/wp-load.php');
        $wp_loaded = true;
        echo "✅ WordPress загружен: " . dirname($wp_config) . "\n\n";
        break;
    }
}

if (!$wp_loaded) {
    echo "❌ ОШИБКА: Не удалось загрузить WordPress\n";
    echo "Убедитесь, что скрипт находится в корне сайта или в папке с wp-config.php\n";
    exit(1);
}

// 1. ПРОВЕРКА СТАТУСА ПЛАГИНА
echo "1️⃣ ПРОВЕРКА СТАТУСА ПЛАГИНА\n";
echo str_repeat("-", 30) . "\n";

// Ищем активные плагины TableCRM
if (!function_exists('get_plugins')) {
    require_once(ABSPATH . 'wp-admin/includes/plugin.php');
}

$all_plugins = get_plugins();
$tablecrm_plugins = [];
$active_plugin = null;

foreach ($all_plugins as $plugin_path => $plugin_data) {
    if (stripos($plugin_path, 'tablecrm') !== false || stripos($plugin_data['Name'], 'tablecrm') !== false) {
        $tablecrm_plugins[$plugin_path] = $plugin_data;
        if (is_plugin_active($plugin_path)) {
            $active_plugin = ['path' => $plugin_path, 'data' => $plugin_data];
        }
    }
}

if (empty($tablecrm_plugins)) {
    echo "❌ Плагины TableCRM не найдены!\n";
    exit(1);
}

if (!$active_plugin) {
    echo "❌ Активный плагин TableCRM не найден!\n";
    echo "Найденные плагины:\n";
    foreach ($tablecrm_plugins as $path => $data) {
        echo "  - {$data['Name']} (v{$data['Version']}) - НЕ АКТИВЕН\n";
    }
    exit(1);
}

echo "✅ Активный плагин: {$active_plugin['data']['Name']}\n";
echo "   Версия: {$active_plugin['data']['Version']}\n";
echo "   Файл: {$active_plugin['path']}\n\n";

// 2. ПРОВЕРКА НАСТРОЕК ПЛАГИНА
echo "2️⃣ ПРОВЕРКА НАСТРОЕК ПЛАГИНА\n";
echo str_repeat("-", 30) . "\n";

// Настройки для версии v1.0.24-FIXED
$settings = [
    'tablecrm_complete_api_url' => get_option('tablecrm_complete_api_url'),
    'tablecrm_complete_api_key' => get_option('tablecrm_complete_api_key'),
    'tablecrm_complete_organization_id' => get_option('tablecrm_complete_organization_id'),
    'tablecrm_complete_cashbox_id' => get_option('tablecrm_complete_cashbox_id'),
    'tablecrm_complete_warehouse_id' => get_option('tablecrm_complete_warehouse_id'),
    'tablecrm_complete_unit_id' => get_option('tablecrm_complete_unit_id'),
    'tablecrm_complete_enable_orders' => get_option('tablecrm_complete_enable_orders'),
    'tablecrm_complete_debug_mode' => get_option('tablecrm_complete_debug_mode'),
    'tablecrm_complete_order_statuses' => get_option('tablecrm_complete_order_statuses')
];

$missing_settings = [];
foreach ($settings as $key => $value) {
    if ($value === false || $value === null || $value === '') {
        $missing_settings[] = $key;
        echo "❌ $key: НЕ НАСТРОЕН\n";
    } else {
        if ($key === 'tablecrm_complete_api_key') {
            echo "✅ $key: ***установлен***\n";
        } else {
            echo "✅ $key: " . (is_array($value) ? implode(', ', $value) : $value) . "\n";
        }
    }
}

if (!empty($missing_settings)) {
    echo "\n⚠️  ОБНАРУЖЕНЫ ОТСУТСТВУЮЩИЕ НАСТРОЙКИ!\n";
    echo "Необходимо настроить плагин в админ-панели WordPress.\n\n";
}

// 3. ПРОВЕРКА ПОДКЛЮЧЕНИЯ К API
echo "3️⃣ ПРОВЕРКА ПОДКЛЮЧЕНИЯ К API TABLECRM\n";
echo str_repeat("-", 40) . "\n";

$api_url = $settings['tablecrm_complete_api_url'];
$api_key = $settings['tablecrm_complete_api_key'];

if (!$api_url || !$api_key) {
    echo "❌ API URL или ключ не настроены\n\n";
} else {
    // Тестируем подключение
    $test_url = rtrim($api_url, '/') . '/health?token=' . $api_key;
    
    $response = wp_remote_get($test_url, [
        'timeout' => 10,
        'sslverify' => true
    ]);
    
    if (is_wp_error($response)) {
        echo "❌ Ошибка подключения: " . $response->get_error_message() . "\n\n";
    } else {
        $status_code = wp_remote_retrieve_response_code($response);
        if ($status_code === 200) {
            echo "✅ Подключение к API успешно (HTTP 200)\n\n";
        } else {
            echo "❌ API вернул код: $status_code\n";
            echo "Ответ: " . wp_remote_retrieve_body($response) . "\n\n";
        }
    }
}

// 4. ПРОВЕРКА ПОСЛЕДНИХ ЗАКАЗОВ
echo "4️⃣ ПРОВЕРКА ПОСЛЕДНИХ ЗАКАЗОВ\n";
echo str_repeat("-", 30) . "\n";

if (!function_exists('wc_get_orders')) {
    echo "❌ WooCommerce не активен или не установлен\n\n";
} else {
    $recent_orders = wc_get_orders([
        'limit' => 3,
        'orderby' => 'date',
        'order' => 'DESC',
        'status' => ['pending', 'processing', 'completed', 'on-hold']
    ]);
    
    if (empty($recent_orders)) {
        echo "❌ Заказы не найдены\n\n";
    } else {
        echo "Найдено " . count($recent_orders) . " последних заказов:\n\n";
        
        foreach ($recent_orders as $order) {
            echo "📦 Заказ #{$order->get_id()}\n";
            echo "   Дата: " . $order->get_date_created()->format('Y-m-d H:i:s') . "\n";
            echo "   Статус: " . $order->get_status() . "\n";
            echo "   Покупатель: " . $order->get_billing_first_name() . ' ' . $order->get_billing_last_name() . "\n";
            echo "   Email: " . $order->get_billing_email() . "\n";
            echo "   Сумма: " . $order->get_total() . " руб.\n";
            
            // Возможные варианты мета-ключей
            $meta_keys = [
                '_tablecrm_complete_document_id', // основная ветка Complete
                '_tablecrm_document_id',          // устаревший/другой плагин
                '_tablecrm_fixed_document_id',    // версия Fixed v2
                '_tablecrm_v3_document_id'        // версия Fixed v3
            ];

            $tablecrm_id = '';
            foreach ($meta_keys as $mk) {
                $val = $order->get_meta($mk);
                if (!empty($val)) { $tablecrm_id = $val; break; }
            }

            // Флаги отправки
            $sent_meta_keys = ['_tablecrm_complete_sent_at', '_tablecrm_sent'];
            $tablecrm_sent = false;
            foreach ($sent_meta_keys as $sk) {
                if ($order->get_meta($sk)) { $tablecrm_sent = true; break; }
            }

            // Ошибки
            $error_meta_keys = ['_tablecrm_complete_error', '_tablecrm_error'];
            $tablecrm_error = '';
            foreach ($error_meta_keys as $ek) {
                $val = $order->get_meta($ek);
                if (!empty($val)) { $tablecrm_error = $val; break; }
            }
            
            echo "   TableCRM статус:\n";
            echo "     - ID документа: " . ($tablecrm_id ?: 'НЕТ') . "\n";
            echo "     - Отправлено: " . ($tablecrm_sent ? 'ДА' : 'НЕТ') . "\n";
            if ($tablecrm_error) {
                echo "     - Ошибка: " . $tablecrm_error . "\n";
            }
            
            // Проверяем, должен ли заказ отправляться
            $order_statuses = $settings['tablecrm_complete_order_statuses'];
            $should_send = is_array($order_statuses) && in_array($order->get_status(), $order_statuses);
            echo "     - Должен отправляться: " . ($should_send ? 'ДА' : 'НЕТ') . "\n";
            
            echo "\n";
        }
    }
}

// 5. ПРОВЕРКА ЛОГОВ
echo "5️⃣ ПРОВЕРКА ЛОГОВ\n";
echo str_repeat("-", 20) . "\n";

$log_paths = [
    WP_CONTENT_DIR . '/uploads/tablecrm_integration_complete.log',
    WP_CONTENT_DIR . '/debug.log',
    WP_CONTENT_DIR . '/tablecrm.log'
];

$logs_found = false;
foreach ($log_paths as $log_path) {
    if (file_exists($log_path)) {
        echo "✅ Найден лог: " . basename($log_path) . "\n";
        
        $log_content = file_get_contents($log_path);
        $log_lines = explode("\n", $log_content);
        
        // Ищем записи за последние 24 часа
        $recent_logs = [];
        $yesterday = time() - 86400;
        
        foreach ($log_lines as $line) {
            if (stripos($line, 'tablecrm') !== false || stripos($line, 'order') !== false) {
                // Пытаемся извлечь дату из лога
                if (preg_match('/\d{4}-\d{2}-\d{2}/', $line, $matches)) {
                    $recent_logs[] = $line;
                }
            }
        }
        
        if (!empty($recent_logs)) {
            echo "   Последние записи (" . count($recent_logs) . "):\n";
            foreach (array_slice($recent_logs, -5) as $log_line) {
                echo "   " . trim($log_line) . "\n";
            }
        } else {
            echo "   Записи TableCRM не найдены\n";
        }
        echo "\n";
        $logs_found = true;
    }
}

if (!$logs_found) {
    echo "❌ Файлы логов не найдены\n";
    echo "Возможно, логирование отключено или плагин не записывает логи\n\n";
}

// 6. ПРОВЕРКА ХУКОВ WORDPRESS
echo "6️⃣ ПРОВЕРКА ХУКОВ WORDPRESS\n";
echo str_repeat("-", 30) . "\n";

$hooks_to_check = [
    'woocommerce_order_status_changed',
    'woocommerce_checkout_order_processed',
    'tablecrm_complete_schedule_send'
];

foreach ($hooks_to_check as $hook) {
    global $wp_filter;
    if (isset($wp_filter[$hook])) {
        echo "✅ Хук '$hook' зарегистрирован\n";
        $callbacks = $wp_filter[$hook]->callbacks;
        foreach ($callbacks as $priority => $functions) {
            foreach ($functions as $callback) {
                if (is_array($callback['function'])) {
                    $class = is_object($callback['function'][0]) ? get_class($callback['function'][0]) : $callback['function'][0];
                    echo "   - $class::{$callback['function'][1]} (приоритет: $priority)\n";
                } else {
                    echo "   - {$callback['function']} (приоритет: $priority)\n";
                }
            }
        }
    } else {
        echo "❌ Хук '$hook' НЕ зарегистрирован\n";
    }
}

// РЕЗЮМЕ И РЕКОМЕНДАЦИИ
echo "\n🎯 РЕЗЮМЕ И РЕКОМЕНДАЦИИ\n";
echo str_repeat("=", 30) . "\n";

$issues_found = [];

if (!$active_plugin) {
    $issues_found[] = "Плагин TableCRM не активен";
}

if (!empty($missing_settings)) {
    $issues_found[] = "Отсутствуют настройки плагина";
}

if (!$logs_found) {
    $issues_found[] = "Логирование не работает";
}

if (empty($recent_orders)) {
    $issues_found[] = "Нет заказов для тестирования";
}

if (empty($issues_found)) {
    echo "✅ Основные компоненты настроены корректно\n\n";
    echo "🔄 СЛЕДУЮЩИЕ ШАГИ:\n";
    echo "1. Создайте новый тестовый заказ\n";
    echo "2. Измените его статус на 'В обработке' или 'Выполнен'\n";
    echo "3. Проверьте логи через 2-3 минуты\n";
    echo "4. Убедитесь, что заказ появился в TableCRM\n";
} else {
    echo "❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ:\n";
    foreach ($issues_found as $issue) {
        echo "   - $issue\n";
    }
    echo "\n🔧 ДЕЙСТВИЯ ДЛЯ ИСПРАВЛЕНИЯ:\n";
    echo "1. Убедитесь, что плагин TableCRM активен\n";
    echo "2. Настройте все параметры в админ-панели\n";
    echo "3. Включите отладку в wp-config.php:\n";
    echo "   define('WP_DEBUG', true);\n";
    echo "   define('WP_DEBUG_LOG', true);\n";
    echo "4. Перезагрузите плагин или сайт\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "🏁 ДИАГНОСТИКА ЗАВЕРШЕНА\n";
?>