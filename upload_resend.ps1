# Upload and run resend order script
$server = "bekflom3.beget.tech"
$user = "bekflom3_wz"
$pass = "zl2Shj!e&6ng"

Write-Host "Uploading resend order script..." -ForegroundColor Green

try {
    $uri = "ftp://$server/public_html/resend_order.php"
    $webclient = New-Object System.Net.WebClient
    $webclient.Credentials = New-Object System.Net.NetworkCredential($user, $pass)
    $webclient.UploadFile($uri, "resend_order.php")
    Write-Host "Resend order script uploaded!" -ForegroundColor Green
    
    # Run the resend
    Write-Host "Running resend order..." -ForegroundColor Yellow
    $result = Invoke-WebRequest -Uri "https://mosbuketik.ru/resend_order.php" -UseBasicParsing
    Write-Host $result.Content -ForegroundColor White
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

$webclient.Dispose()
Write-Host "Resend order completed!" -ForegroundColor Green 