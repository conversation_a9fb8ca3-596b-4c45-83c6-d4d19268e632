<?php
/**
 * Plugin Name: TableCRM Integration Complete
 * Description: Полная интеграция WooCommerce с TableCRM с защитой от дублей
 * Version: 1.0.24-fixed
 * Author: TableCRM Integration Team
 */

if (!defined('ABSPATH')) {
    exit;
}

// Определение констант плагина
define('TABLECRM_COMPLETE_PLUGIN_URL', plugin_dir_url(__FILE__));
define('TABLECRM_COMPLETE_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('TABLECRM_COMPLETE_VERSION', '1.0.24-fixed');

class TableCRM_Integration_Complete {
    
    private $max_retries = 3;
    private $retry_delay = 2; // seconds
    
    // Настройки для Circuit Breaker
    private $circuit_breaker_threshold = 5; // 5 ошибок подряд
    private $circuit_breaker_timeout = 300; // 5 минут
    
    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'register_settings'));
        
        // Хуки для WooCommerce
        add_action('woocommerce_order_status_changed', array($this, 'handle_order_status_change'), 10, 3);
        add_action('woocommerce_checkout_order_processed', array($this, 'handle_new_order'), 10, 1);
        
        // Action Scheduler async hook
        if (function_exists('as_enqueue_async_action')) {
            add_action('tablecrm_complete_async_send', array($this, 'send_order_to_tablecrm'), 10, 1);
        }
        
        // Хук для отложенной отправки заказов (fallback)
        add_action('tablecrm_complete_delayed_send', array($this, 'handle_delayed_send'), 10, 1);
        
        // FIX: Очистка буфера для предотвращения ошибок AJAX при оформлении заказа
        add_action('woocommerce_checkout_order_processed', function() {
            if (headers_sent() === false && ob_get_level() > 0) {
                ob_end_clean();
            }
        }, 1);
        
        // AJAX хуки
        add_action('wp_ajax_test_tablecrm_connection_complete', array($this, 'test_connection'));
        add_action('wp_ajax_tablecrm_resend_order', array($this, 'handle_manual_order_resend'));
        
        // Хук активации
        register_activation_hook(__FILE__, array($this, 'activate_plugin'));
    }
    
    public function init() {
        load_plugin_textdomain('tablecrm-integration-complete', false, dirname(plugin_basename(__FILE__)) . '/languages');
    }
    
    public function activate_plugin() {
        // Устанавливаем настройки по умолчанию
        if (!get_option('tablecrm_complete_api_url')) {
            update_option('tablecrm_complete_api_url', 'https://app.tablecrm.com/api/v1/');
        }
        if (!get_option('tablecrm_complete_organization_id')) {
            update_option('tablecrm_complete_organization_id', 38);
        }
        if (!get_option('tablecrm_complete_cashbox_id')) {
            update_option('tablecrm_complete_cashbox_id', 218);
        }
        if (!get_option('tablecrm_complete_warehouse_id')) {
            update_option('tablecrm_complete_warehouse_id', 39);
        }
        if (!get_option('tablecrm_complete_unit_id')) {
            update_option('tablecrm_complete_unit_id', 116);
        }
        if (!get_option('tablecrm_complete_enable_orders')) {
            update_option('tablecrm_complete_enable_orders', 1);
        }
        if (!get_option('tablecrm_complete_debug_mode')) {
            update_option('tablecrm_complete_debug_mode', 1);
        }
        if (!get_option('tablecrm_complete_order_statuses')) {
            update_option('tablecrm_complete_order_statuses', array('processing', 'completed'));
        }
        
        $this->log_success('Плагин TableCRM Integration Complete v1.0.24-fixed активирован');
    }
    
    public function add_admin_menu() {
        add_options_page(
            'TableCRM Integration Complete Settings',
            'TableCRM Complete',
            'manage_options',
            'tablecrm-complete-settings',
            array($this, 'admin_page')
        );
        
        // Добавляем страницу для ручной отправки заказов в меню "Инструменты"
        add_management_page(
            __('Повторная отправка в TableCRM', 'tablecrm-integration-complete'),
            __('TableCRM Resend Order', 'tablecrm-integration-complete'),
            'manage_options',
            'tablecrm_resend_order',
            array($this, 'render_resend_order_page')
        );
    }
    
    public function register_settings() {
        register_setting('tablecrm_complete_settings', 'tablecrm_complete_api_url', array(
            'type' => 'string',
            'sanitize_callback' => array($this, 'sanitize_api_url'),
            'default' => 'https://app.tablecrm.com/api/v1/'
        ));
        
        // Регистрируем настройку API ключа только если не используется константа
        if (!defined('TABLECRM_COMPLETE_API_KEY') || empty(TABLECRM_COMPLETE_API_KEY)) {
            register_setting('tablecrm_complete_settings', 'tablecrm_complete_api_key', array(
                'type' => 'string',
                'sanitize_callback' => array($this, 'sanitize_api_key'),
                'default' => ''
            ));
        }
        
        register_setting('tablecrm_complete_settings', 'tablecrm_complete_organization_id', array(
            'type' => 'integer',
            'sanitize_callback' => 'absint',
            'default' => 38
        ));
        
        register_setting('tablecrm_complete_settings', 'tablecrm_complete_cashbox_id', array(
            'type' => 'integer',
            'sanitize_callback' => 'absint',
            'default' => 218
        ));
        
        register_setting('tablecrm_complete_settings', 'tablecrm_complete_warehouse_id', array(
            'type' => 'integer',
            'sanitize_callback' => 'absint',
            'default' => 39
        ));
        
        register_setting('tablecrm_complete_settings', 'tablecrm_complete_unit_id', array(
            'type' => 'integer',
            'sanitize_callback' => 'absint',
            'default' => 116
        ));
        
        register_setting('tablecrm_complete_settings', 'tablecrm_complete_enable_orders', array(
            'type' => 'boolean',
            'sanitize_callback' => array($this, 'sanitize_checkbox'),
            'default' => true
        ));
        
        register_setting('tablecrm_complete_settings', 'tablecrm_complete_debug_mode', array(
            'type' => 'boolean',
            'sanitize_callback' => array($this, 'sanitize_checkbox'),
            'default' => true
        ));
        
        register_setting('tablecrm_complete_settings', 'tablecrm_complete_order_statuses', array(
            'type' => 'array',
            'sanitize_callback' => array($this, 'sanitize_order_statuses'),
            'default' => array('processing', 'completed')
        ));
    }
    
    public function admin_page() {
        ?>
        <div class="wrap">
            <h1>TableCRM Integration Complete Settings v1.0.24-fixed</h1>
            <form method="post" action="options.php">
                <?php
                settings_fields('tablecrm_complete_settings');
                do_settings_sections('tablecrm_complete_settings');
                ?>
                <table class="form-table">
                    <tr>
                        <th scope="row">API URL</th>
                        <td>
                            <input type="url" name="tablecrm_complete_api_url" value="<?php echo esc_attr(get_option('tablecrm_complete_api_url', 'https://app.tablecrm.com/api/v1/')); ?>" class="regular-text" />
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">API Key</th>
                        <td>
                            <?php if (defined('TABLECRM_COMPLETE_API_KEY') && !empty(TABLECRM_COMPLETE_API_KEY)) : ?>
                                <p><strong>API-ключ определен в `wp-config.php`.</strong></p>
                                <input type="password" value="****************" class="regular-text" disabled />
                            <?php else : ?>
                                <input type="password" name="tablecrm_complete_api_key" value="<?php echo esc_attr(get_option('tablecrm_complete_api_key')); ?>" class="regular-text" />
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Organization ID</th>
                        <td>
                            <input type="number" name="tablecrm_complete_organization_id" value="<?php echo esc_attr(get_option('tablecrm_complete_organization_id', 38)); ?>" class="regular-text" />
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Cashbox ID</th>
                        <td>
                            <input type="number" name="tablecrm_complete_cashbox_id" value="<?php echo esc_attr(get_option('tablecrm_complete_cashbox_id', 218)); ?>" class="regular-text" />
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Warehouse ID</th>
                        <td>
                            <input type="number" name="tablecrm_complete_warehouse_id" value="<?php echo esc_attr(get_option('tablecrm_complete_warehouse_id', 39)); ?>" class="regular-text" />
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Отправлять заказы</th>
                        <td>
                            <input type="checkbox" name="tablecrm_complete_enable_orders" value="1" <?php checked(1, get_option('tablecrm_complete_enable_orders', 1)); ?> />
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Режим отладки</th>
                        <td>
                            <input type="checkbox" name="tablecrm_complete_debug_mode" value="1" <?php checked(1, get_option('tablecrm_complete_debug_mode', 1)); ?> />
                        </td>
                    </tr>
                </table>
                
                <p class="submit">
                    <input type="submit" name="submit" class="button button-primary" value="Сохранить настройки" />
                    <button type="button" id="test-connection-complete" class="button">Тестировать соединение</button>
                </p>
            </form>
            
            <div id="test-result-complete" style="margin-top: 20px;"></div>
            
            <script>
            jQuery(document).ready(function($) {
                $('#test-connection-complete').click(function() {
                    var button = $(this);
                    var result = $('#test-result-complete');
                    
                    button.prop('disabled', true).text('Тестируем...');
                    result.html('<p>Проверяем соединение с TableCRM API...</p>');
                    
                    $.ajax({
                        url: ajaxurl,
                        method: 'POST',
                        data: {
                            action: 'test_tablecrm_connection_complete',
                            nonce: '<?php echo wp_create_nonce("test_tablecrm_complete"); ?>'
                        },
                        success: function(response) {
                            if(response.success) {
                                result.html('<div class="notice notice-success"><p><strong>✅ Соединение успешно!</strong></p></div>');
                            } else {
                                result.html('<div class="notice notice-error"><p><strong>❌ Ошибка соединения:</strong><br>' + response.data + '</p></div>');
                            }
                        },
                        error: function() {
                            result.html('<div class="notice notice-error"><p><strong>❌ Ошибка AJAX запроса</strong></p></div>');
                        },
                        complete: function() {
                            button.prop('disabled', false).text('Тестировать соединение');
                        }
                    });
                });
            });
            </script>
        </div>
        <?php
    }
    
    public function test_connection() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Недостаточно прав доступа', 403);
            return;
        }
        
        if (!isset($_POST['nonce']) || !wp_verify_nonce(sanitize_text_field($_POST['nonce']), 'test_tablecrm_complete')) {
            wp_send_json_error('Неверный токен безопасности', 403);
            return;
        }
        
        $api_url = get_option('tablecrm_complete_api_url', 'https://app.tablecrm.com/api/v1/');
        $api_key = defined('TABLECRM_COMPLETE_API_KEY') ? TABLECRM_COMPLETE_API_KEY : get_option('tablecrm_complete_api_key');
        
        if (empty($api_url) || empty($api_key)) {
            wp_send_json_error('Пожалуйста, заполните API URL и API Key', 400);
            return;
        }
        
        $response = $this->make_api_request('health', array(), 'GET');
        
        if ($response['success']) {
            wp_send_json_success('Соединение установлено успешно!');
        } else {
            wp_send_json_error($response['message'], $response['status_code'] ?? 500);
        }
    }
    
    public function handle_order_status_change($order_id, $old_status = '', $new_status = '') {
        try {
            if (!get_option('tablecrm_complete_enable_orders', 1)) {
                return;
            }
            
            $order = wc_get_order($order_id);
            if (!$order) { 
                return; 
            }
            
            $trigger_statuses = get_option('tablecrm_complete_order_statuses', array('processing', 'completed'));
            if (!in_array($order->get_status(), $trigger_statuses)) {
                return;
            }
            
            // если уже отправлен или запланирован – выходим
            if (get_post_meta($order_id, '_tablecrm_complete_document_id', true) || 
                (function_exists('as_has_scheduled_action') && as_has_scheduled_action('tablecrm_complete_async_send', array('order_id' => $order_id)))) {
                return;
            }
            
            if (function_exists('as_enqueue_async_action')) {
                as_enqueue_async_action('tablecrm_complete_async_send', array('order_id' => $order_id));
                $this->log_info("Отправка заказа {$order_id} (status: {$new_status}) запланирована через Action Scheduler.");
            } else {
                $this->log_warning('Action Scheduler недоступен, выполнение отправки сразу.');
                $this->send_order_to_tablecrm($order_id);
            }
        } catch (Exception $e) {
            $this->log_error('КРИТИЧЕСКАЯ ОШИБКА в handle_order_status_change: ' . $e->getMessage());
        }
    }
    
    public function handle_new_order($order_id) {
        try {
            if (!get_option('tablecrm_complete_enable_orders', 1)) {
                return;
            }
            
            $order = wc_get_order($order_id);
            if (!$order) { 
                return; 
            }
            
            if (get_post_meta($order_id, '_tablecrm_complete_document_id', true) || 
                (function_exists('as_has_scheduled_action') && as_has_scheduled_action('tablecrm_complete_async_send', array('order_id' => $order_id)))) {
                return;
            }
            
            if (function_exists('as_enqueue_async_action')) {
                as_enqueue_async_action('tablecrm_complete_async_send', array('order_id' => $order_id));
                $this->log_info("Отправка нового заказа {$order_id} запланирована через Action Scheduler.");
            } else {
                wp_schedule_single_event(time() + 30, 'tablecrm_complete_delayed_send', array($order_id));
                $this->log_info("Отправка заказа {$order_id} отложена на 30 секунд.");
            }
        } catch (Exception $e) {
            $this->log_error('ОШИБКА в handle_new_order для заказа ' . $order_id . ': ' . $e->getMessage());
            wp_schedule_single_event(time() + 300, 'tablecrm_complete_delayed_send', array($order_id));
        }
    }
    
    public function handle_delayed_send($order_id) {
        $this->log_info("Выполнение отложенной отправки заказа {$order_id}");
        $this->send_order_to_tablecrm($order_id);
    }
    
    public function send_order_to_tablecrm($order_id) {
        $resource_id = 'order_send_' . $order_id;

        // Оборачиваем всю логику отправки в блокировку
        return $this->execute_with_lock($resource_id, function() use ($order_id) {
            try {
                $this->log_info("Начинаем отправку заказа $order_id в TableCRM");
                
                $order = wc_get_order($order_id);
                if (!$order) {
                    $this->log_error("Заказ $order_id не найден");
                    return false;
                }

                // Проверяем, не был ли заказ уже отправлен
                $existing_doc_id = get_post_meta($order_id, '_tablecrm_complete_document_id', true);
                if (!empty($existing_doc_id)) {
                    $this->log_info("Заказ $order_id уже был отправлен (Document ID: $existing_doc_id)");
                    return true;
                }

                // Получаем настройки
                $api_url = get_option('tablecrm_complete_api_url', 'https://app.tablecrm.com/api/v1/');
                $api_token = defined('TABLECRM_COMPLETE_API_KEY') ? TABLECRM_COMPLETE_API_KEY : get_option('tablecrm_complete_api_key');
                $warehouse_id = get_option('tablecrm_complete_warehouse_id');
                $organization_id = get_option('tablecrm_complete_organization_id');
                $paybox_id = get_option('tablecrm_complete_cashbox_id');

                if (empty($api_url) || empty($api_token) || empty($warehouse_id) || empty($organization_id) || empty($paybox_id)) {
                    $this->log_error("Не все настройки API заполнены");
                    return false;
                }
                
                $api_url = trailingslashit($api_url);

                // 1. Получаем или создаем контрагента
                $contragent_id = $this->get_or_create_contragent($order, $api_url, $api_token);
                if (!$contragent_id) {
                    $this->log_error("Не удалось получить или создать контрагента для заказа $order_id");
                    return false;
                }

                // 2. Получаем или создаем номенклатуры для всех товаров
                $nomenclature_ids = array();
                $items = $order->get_items();
                $failed_items = array();
                
                foreach ($items as $item_id => $item) {
                    $product_name = $item->get_name();
                    $nomenclature_id = $this->get_or_create_nomenclature($product_name, $api_url, $api_token);
                    if (!$nomenclature_id) {
                        $this->log_warning("Не удалось получить или создать номенклатуру для товара '$product_name' - пропускаем этот товар");
                        $failed_items[] = $product_name;
                        continue;
                    }
                    $nomenclature_ids[$item_id] = $nomenclature_id;
                }

                // Если не удалось создать номенклатуры ни для одного товара
                if (empty($nomenclature_ids)) {
                    $this->log_error("Не удалось создать номенклатуры ни для одного товара в заказе $order_id");
                    return false;
                }
                
                // Логируем информацию о пропущенных товарах
                if (!empty($failed_items)) {
                    $this->log_warning("Пропущены товары: " . implode(', ', $failed_items));
                }

                // 3. Формируем данные заказа
                $adapted_data = $this->adapt_data_format($order, $warehouse_id, $organization_id, $paybox_id, $contragent_id, $nomenclature_ids);

                if (!$adapted_data) {
                    $this->log_error("Не удалось адаптировать данные заказа $order_id");
                    return false;
                }

                // 4. Отправляем заказ
                $response = $this->make_api_request('docs_sales/', $adapted_data, 'POST');
                
                if ($response['success']) {
                    $document_id = $this->extract_document_id($response);
                    
                    if ($document_id && $document_id !== 'unknown') {
                        update_post_meta($order_id, '_tablecrm_complete_document_id', $document_id);
                        update_post_meta($order_id, '_tablecrm_complete_sent_at', current_time('mysql'));
                        
                        $this->log_success("Заказ $order_id успешно отправлен. Document ID: $document_id");
                        $this->add_order_note($order_id, "Заказ успешно отправлен в TableCRM. Document ID: $document_id");
                        
                        // Отправка UTM-данных через специальный API эндпоинт
                        $this->send_utm_data($document_id, $this->get_analytics_data($order));
                        
                        // Отправка информации о доставке через специальный API эндпоинт
                        $this->send_delivery_info($document_id, $order);
                        
                        return true;
                    }
                }
                
                $this->log_error("Ошибка отправки заказа $order_id: " . $response['message']);
                $this->add_order_note($order_id, "Ошибка отправки в TableCRM: " . $response['message']);
                return false;
                
            } catch (Exception $e) {
                $this->log_error("КРИТИЧЕСКАЯ ОШИБКА при отправке заказа $order_id в TableCRM: " . $e->getMessage());
                $this->log_error("Stack trace: " . $e->getTraceAsString());
                
                // Добавляем заметку к заказу
                try {
                    $this->add_order_note($order_id, "Критическая ошибка отправки в TableCRM: " . $e->getMessage());
                } catch (Exception $note_e) {
                    $this->log_error("Не удалось добавить заметку к заказу $order_id: " . $note_e->getMessage());
                }
                
                return false;
            }
        });
    }
    
    // Получение или создание контрагента
    private function get_or_create_contragent($order, $api_url, $api_token) {
        $phone = $this->get_recipient_phone($order);
        if (empty($phone)) {
            $this->log_warning("Телефон клиента в заказе #{$order->get_id()} не найден");
            return null;
        }

        // Нормализуем номер телефона - убираем все кроме цифр и добавляем +7
        $normalized_phone = preg_replace('/[^0-9]/', '', $phone);
        if (strlen($normalized_phone) == 11 && substr($normalized_phone, 0, 1) == '8') {
            $normalized_phone = '7' . substr($normalized_phone, 1);
        }
        if (strlen($normalized_phone) == 10) {
            $normalized_phone = '7' . $normalized_phone;
        }
        $search_phone = '+' . $normalized_phone;

        $this->log_info("Поиск/создание контрагента для телефона: {$phone} (нормализованный: {$search_phone})");
        
        // Выполняем операцию с блокировкой для предотвращения состояний гонки
        $resource_id = 'contragent_' . md5($normalized_phone);
        return $this->execute_with_lock($resource_id, function() use ($api_url, $api_token, $normalized_phone, $search_phone, $order) {
            return $this->create_contragent_safe($api_url, $api_token, $normalized_phone, $search_phone, $order);
        });
    }
    
    // Безопасное создание контрагента с проверками
    private function create_contragent_safe($api_url, $api_token, $normalized_phone, $search_phone, $order) {

        // 1. Поиск контрагента по телефону с правильными параметрами
        // Используем параметр phone для прямого поиска
        $search_url = $api_url . 'contragents/?token=' . $api_token . 
                      '&limit=100&offset=0&sort=created_at:desc&phone=' . urlencode($normalized_phone);
        $search_response = wp_remote_get($search_url);

        if (!is_wp_error($search_response)) {
            $search_response_code = wp_remote_retrieve_response_code($search_response);
            $search_body = wp_remote_retrieve_body($search_response);
            $this->log_info("Поиск контрагента - Status: {$search_response_code}, Response: " . substr($search_body, 0, 200));
            
            $search_data = json_decode($search_body, true);
            
            // Проверяем разные форматы ответа
            if (!empty($search_data)) {
                // Если ответ содержит result (правильный формат API)
                if (isset($search_data['result']) && is_array($search_data['result']) && !empty($search_data['result'])) {
                    // Берем первого найденного контрагента (API уже отфильтровал по номеру)
                    $contragent_id = $search_data['result'][0]['id'];
                    $this->log_success("Контрагент найден в result. ID: {$contragent_id}");
                    return $contragent_id;
                }
                // Если ответ содержит results (альтернативный формат)
                else if (isset($search_data['results']) && is_array($search_data['results']) && !empty($search_data['results'])) {
                    $contragent_id = $search_data['results'][0]['id'];
                    $this->log_success("Контрагент найден в results. ID: {$contragent_id}");
                    return $contragent_id;
                }
                // Если ответ - прямой массив (только если это действительно массив контрагентов)
                else if (is_array($search_data) && !isset($search_data['result']) && !isset($search_data['results']) && !empty($search_data) && isset($search_data[0]['id'])) {
                    $contragent_id = $search_data[0]['id'];
                    $this->log_success("Контрагент найден в массиве. ID: {$contragent_id}");
                    return $contragent_id;
                }
            }
        } else {
            $this->log_error("Ошибка поиска контрагента: " . $search_response->get_error_message());
        }

        // 2. Создание нового контрагента
        $this->log_info("Контрагент не найден, создаем нового");
        $create_url = $api_url . 'contragents/?token=' . $api_token;
        
        // Формируем имя без телефона
        $first_name = $order->get_billing_first_name();
        $last_name = $order->get_billing_last_name();
        
        // Функция для проверки, является ли строка телефоном
        $is_phone = function($str) {
            if (empty($str)) return false;
            // Проверяем на наличие телефонных паттернов
            return preg_match('/^[\+]?[0-9\(\)\-\s]{7,}$/', $str) || 
                   strpos($str, '+7') === 0 || 
                   strpos($str, '8(') === 0 ||
                   preg_match('/\d{3}.*\d{3}.*\d{2}.*\d{2}/', $str);
        };
        
        // Очищаем имя и фамилию от телефонов
        $clean_first_name = (!empty($first_name) && !$is_phone($first_name)) ? $first_name : '';
        $clean_last_name = (!empty($last_name) && !$is_phone($last_name)) ? $last_name : '';
        
        $full_name = trim($clean_first_name . ' ' . $clean_last_name);
        
        // Если имя пустое, используем email как fallback
        if (empty($full_name)) {
            $full_name = $order->get_billing_email();
        }
        
        $email = $order->get_billing_email();

        $create_data = array(
            'name' => $full_name,
            'phone' => $search_phone, // Используем нормализованный номер с +7
            'email' => $email,
            'our_cost' => false
        );
        
        $this->log_info("Создаем контрагента: имя='$full_name', телефон='$search_phone', email='$email'");
        
        $create_response = wp_remote_post($create_url, array(
            'method' => 'POST',
            'headers' => array('Content-Type' => 'application/json; charset=utf-8'),
            'body' => json_encode($create_data, JSON_UNESCAPED_UNICODE)
        ));

        if (!is_wp_error($create_response)) {
            $create_response_code = wp_remote_retrieve_response_code($create_response);
            $create_response_body = wp_remote_retrieve_body($create_response);
            $this->log_info("Создание контрагента - Status: {$create_response_code}, Response: " . substr($create_response_body, 0, 200));
            
            $create_result = json_decode($create_response_body, true);

            if ($create_response_code >= 200 && $create_response_code < 300) {
                if (isset($create_result['id'])) {
                    $new_contragent_id = $create_result['id'];
                    $this->log_success("Новый контрагент создан. ID: {$new_contragent_id}");
                    return $new_contragent_id;
                } else if (is_array($create_result) && isset($create_result[0]['id'])) {
                    $new_contragent_id = $create_result[0]['id'];
                    $this->log_success("Новый контрагент создан (массив). ID: {$new_contragent_id}");
                    return $new_contragent_id;
                }
            } else {
                // Если контрагент уже существует, попробуем найти его еще раз
                if ($create_response_code == 400 && strpos($create_response_body, 'Phone number already in use') !== false) {
                    $this->log_info("Контрагент уже существует, повторный поиск...");
                    
                    // Повторный поиск с параметром phone
                    $search_again_url = $api_url . 'contragents/?token=' . $api_token . 
                                       '&limit=100&offset=0&sort=created_at:desc&phone=' . urlencode($normalized_phone);
                    $search_again_response = wp_remote_get($search_again_url);
                    
                    if (!is_wp_error($search_again_response)) {
                        $again_body = wp_remote_retrieve_body($search_again_response);
                        $again_data = json_decode($again_body, true);
                        
                        if (isset($again_data['result']) && !empty($again_data['result'])) {
                            $found_contragent_id = $again_data['result'][0]['id'];
                            $this->log_success("Контрагент найден при повторном поиске. ID: {$found_contragent_id}");
                            return $found_contragent_id;
                        }
                    }
                }
            }
        } else {
            $this->log_error("Ошибка создания контрагента: " . $create_response->get_error_message());
        }

        $this->log_error("Не удалось создать контрагента");
        return null;
    }
    
    // Получение или создание номенклатуры с защитой от состояний гонки
    private function get_or_create_nomenclature($item_name, $api_url, $api_token) {
        $this->log_info("Поиск/создание номенклатуры для товара: '{$item_name}'");
        
        // Выполняем операцию с блокировкой для предотвращения дублирования
        $resource_id = 'nomenclature_' . md5($item_name);
        $result = $this->execute_with_lock($resource_id, function() use ($item_name, $api_url, $api_token) {
            return $this->create_nomenclature_safe($item_name, $api_url, $api_token);
        });
        
        if (is_array($result) && isset($result['success']) && !$result['success']) {
            $this->log_error("Ошибка при работе с номенклатурой: " . ($result['message'] ?? 'Неизвестная ошибка'));
            return null;
        }
        
        return $result;
    }
    
    // Безопасное создание номенклатуры
    private function create_nomenclature_safe($item_name, $api_url, $api_token) {
        // 1. Поиск номенклатуры через новый API метод
        $search_result = $this->make_api_request('nomenclature/search', array(
            'name' => $item_name,
            'limit' => 1
        ), 'POST');
        
        if ($search_result && $search_result['success']) {
            $search_data = $search_result['data'];
            
            if (isset($search_data['data']) && !empty($search_data['data'])) {
                $nomenclature_id = $search_data['data'][0]['id'];
                $this->log_success("Номенклатура найдена через API. ID: {$nomenclature_id}");
                return $nomenclature_id;
            }
        }

        // 2. Создание новой номенклатуры через API метод
        $this->log_info("Номенклатура не найдена, создаем новую через API");
        $unit_id = get_option('tablecrm_complete_unit_id', 116);
        
        $create_result = $this->make_api_request('nomenclature/create', array(
            'name' => sanitize_text_field($item_name),
            'unit' => intval($unit_id)
        ), 'POST');
        
        if ($create_result && $create_result['success']) {
            $create_data = $create_result['data'];
            
            if (isset($create_data['data']) && !empty($create_data['data'])) {
                $nomenclature_id = $create_data['data'][0]['id'];
                $this->log_success("Номенклатура создана через API. ID: {$nomenclature_id}");
                return $nomenclature_id;
            }
        }

        // 3. Fallback к старому методу (если новый API не работает)
        return $this->create_nomenclature_legacy($item_name, $api_url, $api_token);
    }
    
    // Резервный метод создания номенклатуры (старый способ)
    private function create_nomenclature_legacy($item_name, $api_url, $api_token) {
        $this->log_info("Fallback к legacy методу создания номенклатуры");
        
        // 1. Поиск номенклатуры по названию с правильными параметрами
        $search_url = $api_url . 'nomenclature/?token=' . $api_token . 
                      '&name=' . urlencode($item_name) . 
                      '&limit=100&offset=0&with_prices=false&with_balance=false&with_attributes=false&only_main_from_group=false';
        $search_response = wp_remote_get($search_url);

        if (!is_wp_error($search_response)) {
            $search_response_code = wp_remote_retrieve_response_code($search_response);
            $search_body = wp_remote_retrieve_body($search_response);
            $this->log_info("Поиск номенклатуры - Status: {$search_response_code}, Response: " . substr($search_body, 0, 200));
            
            $search_data = json_decode($search_body, true);
            
            // Проверяем разные форматы ответа
            if (!empty($search_data)) {
                // Если ответ содержит result (правильный формат API)
                if (isset($search_data['result']) && is_array($search_data['result']) && !empty($search_data['result'])) {
                    $nomenclature_id = $search_data['result'][0]['id'];
                    $this->log_success("Номенклатура найдена в result. ID: {$nomenclature_id}");
                    return $nomenclature_id;
                }
                // Если ответ содержит results (альтернативный формат)
                else if (isset($search_data['results']) && is_array($search_data['results']) && !empty($search_data['results'])) {
                    $nomenclature_id = $search_data['results'][0]['id'];
                    $this->log_success("Номенклатура найдена в results. ID: {$nomenclature_id}");
                    return $nomenclature_id;
                }
            }
        } else {
            $this->log_error("Ошибка поиска номенклатуры: " . $search_response->get_error_message());
        }

        // 2. Создание новой номенклатуры
        $this->log_info("Номенклатура не найдена, создаем новую");
        $create_url = $api_url . 'nomenclature/?token=' . $api_token;
        
        $unit_id = get_option('tablecrm_complete_unit_id', 116);
        // API ожидает массив объектов, а не один объект
        $create_data = array(array('name' => $item_name, 'unit' => $unit_id));
        
        $create_response = wp_remote_post($create_url, array(
            'method' => 'POST',
            'headers' => array('Content-Type' => 'application/json; charset=utf-8'),
            'body' => json_encode($create_data, JSON_UNESCAPED_UNICODE)
        ));

        if (!is_wp_error($create_response)) {
            $create_response_code = wp_remote_retrieve_response_code($create_response);
            $create_response_body = wp_remote_retrieve_body($create_response);
            $this->log_info("Создание номенклатуры - Status: {$create_response_code}, Response: " . substr($create_response_body, 0, 200));
            
            $create_result = json_decode($create_response_body, true);

            if ($create_response_code >= 200 && $create_response_code < 300) {
                if (isset($create_result['id'])) {
                    $new_nomenclature_id = $create_result['id'];
                    $this->log_success("Новая номенклатура создана. ID: {$new_nomenclature_id}");
                    return $new_nomenclature_id;
                } else if (is_array($create_result) && isset($create_result[0]['id'])) {
                    $new_nomenclature_id = $create_result[0]['id'];
                    $this->log_success("Новая номенклатура создана (массив). ID: {$new_nomenclature_id}");
                    return $new_nomenclature_id;
                }
            }
        } else {
            $this->log_error("Ошибка создания номенклатуры: " . $create_response->get_error_message());
        }

        $this->log_error("Не удалось создать номенклатуру для товара '{$item_name}'");
        return null;
    }
    
    // Адаптация данных для API
    private function adapt_data_format($order, $warehouse_id, $organization_id, $paybox_id, $contragent_id, $nomenclature_ids_map) {
        $order_id = $order->get_id();
        $items = $order->get_items();
        $goods = array();

        $this->log_info("Формирование данных для заказа #$order_id");
        
        // Получаем и нормализуем основной телефон
        $phone = $this->get_recipient_phone($order);
        $normalized_phone = '';
        if (!empty($phone)) {
            $normalized_phone = preg_replace('/[^0-9]/', '', $phone);
            if (strlen($normalized_phone) == 11 && substr($normalized_phone, 0, 1) == '8') {
                $normalized_phone = '7' . substr($normalized_phone, 1);
            }
            if (strlen($normalized_phone) == 10) {
                $normalized_phone = '7' . $normalized_phone;
            }
            $normalized_phone = '+' . $normalized_phone;
        }

        $this->log_info("Количество товаров в заказе: " . count($items));
        $this->log_info("Количество номенклатур: " . count($nomenclature_ids_map));
        $this->log_info("Основной телефон клиента: " . $normalized_phone);

        foreach ($items as $item_id => $item) {
            $product = $item->get_product();
            $item_name = $item->get_name();

            $nomenclature_id = isset($nomenclature_ids_map[$item_id]) ? $nomenclature_ids_map[$item_id] : null;
            if (!$nomenclature_id) {
                $this->log_error("ID номенклатуры для товара '{$item_name}' не найден");
                continue;
            }
            
            // Рассчитываем итоговую цену за единицу, уже включая скидку
            $line_total = (float) $item->get_total(); // Это итоговая сумма по строке со скидкой
            $quantity   = $item->get_quantity();
            $unit_price = $quantity > 0 ? $line_total / $quantity : 0; // Итоговая цена за единицу

            $goods[] = array(
                'price'          => number_format($unit_price, 2, '.', ''), // Цена за единицу (уже со скидкой)
                'quantity'       => $quantity,
                'unit'           => (int) get_option('tablecrm_complete_unit_id', 116),
                'discount'       => 0, // Скидка больше не передается
                'sum_discounted' => number_format($line_total, 2, '.', ''), // Итоговая сумма по строке
                'nomenclature'   => (int) $nomenclature_id,
                'name'           => $item_name,
                'sku'            => $product ? $product->get_sku() : '',
            );
            
            $this->log_info("Добавлен товар: '{$item_name}' (nomenclature: $nomenclature_id, final_price: $unit_price, qty: " . $quantity . ") - скидка убрана из передачи.");
        }

        // Добавляем стоимость доставки как отдельный товар в том же заказе
        $shipping_total = $order->get_shipping_total();
        if ($shipping_total > 0) {
            $this->log_info("Добавляем стоимость доставки как отдельный товар: {$shipping_total}");
            
            // Создаем или находим номенклатуру для доставки
            $api_url = get_option('tablecrm_complete_api_url', 'https://app.tablecrm.com/api/v1/');
            $api_token = defined('TABLECRM_COMPLETE_API_KEY') ? TABLECRM_COMPLETE_API_KEY : get_option('tablecrm_complete_api_key');
            $api_url = trailingslashit($api_url);
            
            $delivery_nomenclature_id = $this->get_or_create_nomenclature('Доставка', $api_url, $api_token);
            if ($delivery_nomenclature_id) {
                $goods[] = array(
                    'price' => number_format((float) $shipping_total, 2, '.', ''),
                    'quantity' => 1,
                    'unit' => (int) get_option('tablecrm_complete_unit_id', 116),
                    'discount' => 0,
                    'sum_discounted' => number_format((float) $shipping_total, 2, '.', ''),
                    'nomenclature' => (int) $delivery_nomenclature_id,
                    'name' => 'Доставка',
                    'sku' => '',
                );
                $this->log_success("Доставка добавлена как товар с ID номенклатуры: {$delivery_nomenclature_id}");
            } else {
                $this->log_warning("Не удалось создать номенклатуру для доставки");
            }
        }

        if (empty($goods)) {
            $this->log_error("В заказе #{$order_id} нет товаров для отправки");
            return null;
        }
        
        // Рассчитываем итоговую сумму на основе подготовленных данных для CRM
        $total_sum_for_crm = 0;
        foreach ($goods as $good) {
            $total_sum_for_crm += (float) $good['sum_discounted'];
        }
        
        $this->log_info("Итого товаров для отправки: " . count($goods));
        
        $comment_text = "Заказ с сайта №{$order_id}";
        if (!empty($normalized_phone)) {
            $comment_text .= ". Телефон клиента: " . $normalized_phone;
        }
        if ($note = $order->get_customer_note()) {
            $comment_text .= ". Комментарий клиента: " . $note;
        }
        
        // Добавляем информацию о способе доставки в комментарий
        if ($shipping_total > 0) {
            $shipping_method = $order->get_shipping_method();
            if ($shipping_method) {
                $comment_text .= ". Способ доставки: " . $shipping_method;
            }
        }
        
        // Получение UTM и аналитических данных из заказа
        $analytics = $this->get_analytics_data($order);
        
        // Формирование аналитической информации для комментария
        $analytics_comment = $this->format_analytics_comment($analytics);
        
        $comment_text .= $analytics_comment;
        
        // Определяем статус оплаты
        $is_paid = false;
        $order_status = $order->get_status();
        $paid_statuses = get_option('tablecrm_complete_paid_statuses', array('processing', 'completed'));
        if (in_array($order_status, $paid_statuses)) {
            $is_paid = true;
        }
        
        $final_data = array(array(
            'dated' => time(),
            'operation' => 'Заказ',
            'comment' => $comment_text,
            'tax_included' => true,
            'tax_active' => true,
            'goods' => $goods,
            'settings' => new stdClass(),
            'warehouse' => (int) $warehouse_id,
            'contragent' => (int) $contragent_id,
            'paybox' => (int) $paybox_id,
            'organization' => (int) $organization_id,
            'status' => $is_paid,
            'paid_rubles' => $is_paid ? number_format($total_sum_for_crm, 2, '.', '') : '0.00',
            'paid_lt' => 0,
        ));
        
        $this->log_info("Данные для отправки сформированы успешно. Статус оплаты: " . ($is_paid ? 'Оплачено' : 'Не оплачено') . ". Итоговая сумма: " . $total_sum_for_crm);
        return $final_data;
    }
    
    // Получение UTM и аналитических данных из заказа
    private function get_analytics_data($order) {
        $analytics = array();
        $meta_data = $order->get_meta_data();
        
        foreach ($meta_data as $meta) {
            $key = $meta->get_data()['key'];
            $value = $meta->get_data()['value'];
            
            // WooCommerce Order Attribution data
            if (strpos($key, '_wc_order_attribution_') === 0) {
                $clean_key = str_replace('_wc_order_attribution_', '', $key);
                $analytics[$clean_key] = $value;
            }
            
            // Standard UTM parameters
            if (strpos($key, 'utm_') === 0) {
                $analytics[$key] = $value;
            }
            
            // Extended UTM parameters from API documentation
            $extended_utm_fields = array(
                'utm_name', 'utm_phone', 'utm_email', 'utm_leadid', 
                'utm_yclientid', 'utm_gaclientid'
            );
            if (in_array($key, $extended_utm_fields)) {
                $analytics[$key] = $value;
            }
            
            // Other tracking data
            if (in_array($key, array('gclid', 'fbclid', 'yclid', 'referrer', 'landing_page', 'source', 'medium', 'campaign'))) {
                $analytics[$key] = $value;
            }
        }
        
        // Также проверяем GET и POST параметры если они сохранены
        if (isset($_GET)) {
            foreach ($_GET as $key => $value) {
                if (strpos($key, 'utm_') === 0 && !isset($analytics[$key])) {
                    $analytics[$key] = sanitize_text_field($value);
                }
            }
        }
        
        // Проверяем куки для UTM-данных
        if (isset($_COOKIE)) {
            foreach ($_COOKIE as $key => $value) {
                if (strpos($key, 'utm_') === 0 && !isset($analytics[$key])) {
                    $analytics[$key] = sanitize_text_field($value);
                }
            }
        }
        
        return $analytics;
    }
    
    // Формирование аналитической информации для комментария
    private function format_analytics_comment($analytics) {
        if (empty($analytics)) {
            return '';
        }
        
        $comment_parts = array();
        
        // Источник трафика
        if (isset($analytics['utm_source']) || isset($analytics['source_type'])) {
            $source = isset($analytics['utm_source']) ? $analytics['utm_source'] : $analytics['source_type'];
            $comment_parts[] = "Источник: $source";
        }
        
        // Канал/медиум
        if (isset($analytics['utm_medium'])) {
            $comment_parts[] = "Канал: " . $analytics['utm_medium'];
        }
        
        // Кампания
        if (isset($analytics['utm_campaign'])) {
            $comment_parts[] = "Кампания: " . $analytics['utm_campaign'];
        }
        
        // Реферер
        if (isset($analytics['referrer'])) {
            $referrer_domain = parse_url($analytics['referrer'], PHP_URL_HOST);
            if ($referrer_domain) {
                $comment_parts[] = "Реферер: $referrer_domain";
            }
        }
        
        // Google Ads
        if (isset($analytics['gclid'])) {
            $comment_parts[] = "Google Ads: " . substr($analytics['gclid'], 0, 20) . "...";
        }
        
        // Facebook Ads
        if (isset($analytics['fbclid'])) {
            $comment_parts[] = "Facebook Ads: " . substr($analytics['fbclid'], 0, 20) . "...";
        }
        
        // Yandex Direct
        if (isset($analytics['yclid'])) {
            $comment_parts[] = "Yandex Direct: " . substr($analytics['yclid'], 0, 20) . "...";
        }
        
        return empty($comment_parts) ? '' : '. Аналитика: ' . implode(', ', $comment_parts);
    }
    
    // Извлечение Document ID из ответа
    private function extract_document_id($response) {
        if (isset($response['document_id'])) {
            return $response['document_id'];
        } elseif (isset($response['data']) && is_array($response['data']) && !empty($response['data'][0]['id'])) {
            return $response['data'][0]['id'];
        }
        return 'unknown';
    }
    
    // Добавление заметки к заказу
    private function add_order_note($order_id, $note) {
        $order = wc_get_order($order_id);
        if ($order) {
            $order->add_order_note($note);
            $order->save();
        }
    }
    
    // Получение номера телефона получателя
    private function get_recipient_phone($order) {
        $phone = '';
        $billing_phone = $order->get_billing_phone();
        $shipping_phone = $order->get_shipping_phone();
        
        if (!empty($billing_phone)) {
            $phone = $billing_phone;
        } elseif (!empty($shipping_phone)) {
            $phone = $shipping_phone;
        }
        
        return $phone;
    }
    
    // Отправка UTM-данных через специальный API эндпоинт
    private function send_utm_data($document_id, $analytics) {
        if (empty($analytics) || empty($document_id)) {
            return true; // Нет данных для отправки
        }
        
        $this->log_info("Отправка UTM-данных для документа $document_id");
        
        // Формируем данные для UTM API согласно официальной документации
        $utm_data = array();
        
        // Основные UTM параметры
        if (isset($analytics['utm_source'])) {
            $utm_data['utm_source'] = $analytics['utm_source'];
        }
        if (isset($analytics['utm_medium'])) {
            $utm_data['utm_medium'] = $analytics['utm_medium'];
        }
        if (isset($analytics['utm_campaign'])) {
            $utm_data['utm_campaign'] = $analytics['utm_campaign'];
        }
        if (isset($analytics['utm_term'])) {
            // utm_term может быть массивом согласно API
            $utm_data['utm_term'] = is_array($analytics['utm_term']) ? $analytics['utm_term'] : array($analytics['utm_term']);
        }
        if (isset($analytics['utm_content'])) {
            $utm_data['utm_content'] = $analytics['utm_content'];
        }
        
        // Дополнительные UTM поля
        if (isset($analytics['utm_name'])) {
            $utm_data['utm_name'] = $analytics['utm_name'];
        }
        if (isset($analytics['utm_phone'])) {
            $utm_data['utm_phone'] = $analytics['utm_phone'];
        }
        if (isset($analytics['utm_email'])) {
            $utm_data['utm_email'] = $analytics['utm_email'];
        }
        if (isset($analytics['utm_leadid'])) {
            $utm_data['utm_leadid'] = $analytics['utm_leadid'];
        }
        if (isset($analytics['utm_yclientid'])) {
            $utm_data['utm_yclientid'] = $analytics['utm_yclientid'];
        }
        if (isset($analytics['utm_gaclientid'])) {
            $utm_data['utm_gaclientid'] = $analytics['utm_gaclientid'];
        }
        
        // Дополнительные параметры (для обратной совместимости)
        if (isset($analytics['referrer'])) {
            $utm_data['referrer'] = $analytics['referrer'];
        }
        if (isset($analytics['source_type'])) {
            $utm_data['source_type'] = $analytics['source_type'];
        }
        if (isset($analytics['device_type'])) {
            $utm_data['device_type'] = $analytics['device_type'];
        }
        if (isset($analytics['user_agent'])) {
            $utm_data['user_agent'] = substr($analytics['user_agent'], 0, 255); // Ограничиваем длину
        }
        
        // Google/Facebook/Yandex click IDs
        if (isset($analytics['gclid'])) {
            $utm_data['gclid'] = $analytics['gclid'];
        }
        if (isset($analytics['fbclid'])) {
            $utm_data['fbclid'] = $analytics['fbclid'];
        }
        if (isset($analytics['yclid'])) {
            $utm_data['yclid'] = $analytics['yclid'];
        }
        
        if (empty($utm_data)) {
            $this->log_info("Нет UTM-данных для отправки");
            return true;
        }
        
        $this->log_info("UTM-данные для отправки: " . json_encode($utm_data, JSON_UNESCAPED_UNICODE));
        
        // Отправляем через специальный эндпоинт
        $response = $this->make_api_request("docs_sales/$document_id/utm/", $utm_data, 'POST');
        
        if ($response['success']) {
            $this->log_success("UTM-данные успешно отправлены для документа $document_id");
            return true;
        } else {
            $this->log_warning("Ошибка отправки UTM-данных: " . $response['message']);
            return false; // Не критичная ошибка, продолжаем работу
        }
    }
    
    // Отправка информации о доставке через специальный API эндпоинт
    private function send_delivery_info($document_id, $order) {
        $this->log_info("Запуск функции send_delivery_info для заказа ID: " . $order->get_id());

        $api_url = get_option('tablecrm_complete_api_url');
        $api_token = defined('TABLECRM_COMPLETE_API_KEY') ? TABLECRM_COMPLETE_API_KEY : get_option('tablecrm_complete_api_key');
        $endpoint = "docs_sales/{$document_id}/delivery_info/";

        // Получаем дату и время доставки в виде timestamp
        $delivery_timestamp = $this->get_delivery_date($order);

        // Формируем массив данных для отправки
        $delivery_data = [
            'recipient' => [
                'name'  => trim($order->get_shipping_first_name() . ' ' . $order->get_shipping_last_name()) ?: trim($order->get_billing_first_name() . ' ' . $order->get_billing_last_name()),
                'phone' => $this->get_recipient_phone($order),
            ],
            'address' => $this->get_recipient_address($order),
            'note'    => $order->get_customer_note(),
        ];

        // Добавляем дату доставки, только если она была найдена
        if ($delivery_timestamp) {
            $delivery_data['delivery_date'] = $delivery_timestamp;
            $this->log_info("Дата доставки (timestamp) для заказа ID " . $order->get_id() . ": " . $delivery_timestamp);
        } else {
            $this->log_warning("Дата доставки не найдена для заказа ID " . $order->get_id() . ". Поле 'delivery_date' не будет отправлено.");
        }

        // Логирование подготовленных данных перед отправкой
        $this->log_info("Подготовленные данные для API доставки (заказ ID: " . $order->get_id() . "): " . json_encode($delivery_data, JSON_UNESCAPED_UNICODE));

        // Отправка данных
        $response = $this->make_api_request($endpoint, $delivery_data, 'POST');

        // Обработка ответа
        if ($response && $response['success']) {
            $this->log_success("Информация о доставке для заказа ID " . $order->get_id() . " успешно отправлена в TableCRM. Document ID: {$document_id}");
            $this->add_order_note($order->get_id(), 'Информация о доставке успешно отправлена в TableCRM.');
        } else {
            $error_message = 'Не удалось отправить информацию о доставке в TableCRM.';
            if (isset($response['message'])) {
                $error_message .= ' Причина: ' . $response['message'];
            }
            if(isset($response['data']['errors'])) {
                // Более детальное логирование ошибки 422
                $error_details = [];
                foreach($response['data']['errors'] as $error) {
                    $field = implode('.', $error['loc']);
                    $error_details[] = "Поле '{$field}': {$error['msg']}";
                }
                $error_message .= ' Детали ошибки: ' . implode('; ', $error_details);
            }
             if (is_wp_error($response)) {
                $error_message .= ' WP_Error: ' . $response->get_error_message();
            }

            $this->log_error("Ошибка отправки информации о доставке для заказа ID " . $order->get_id() . ". " . $error_message);
            $this->add_order_note($order->get_id(), $error_message);
        }
    }

    /**
     * Получает дату доставки из мета-полей заказа и конвертирует в Unix timestamp.
     * Сначала проверяем плагин CodeRockz, затем стандартные поля.
     *
     * @param WC_Order $order
     * @return int|null Unix timestamp или null, если дата не найдена.
     */
    private function get_delivery_date($order) {
        $this->log_info("Поиск даты доставки для заказа ID: " . $order->get_id());

        // 1. Проверяем мета-поля от плагина "CodeRockz WooCommerce Delivery Date Time Pro"
        $coderockz_date = $this->get_order_meta($order, 'delivery_date'); // e.g., '14/08/2024'
        $coderockz_time = $this->get_order_meta($order, 'delivery_time'); // e.g., '10:00 - 12:00'

        if (!empty($coderockz_date)) {
            $this->log_info("Найдена дата от CodeRockz: '{$coderockz_date}'");
            
            $datetime_string = str_replace('/', '-', $coderockz_date); // 'd-m-Y' to be safe with strtotime

            if (!empty($coderockz_time)) {
                $this->log_info("Найдено время от CodeRockz: '{$coderockz_time}'");
                // Берем начало временного диапазона для точности
                $time_parts = explode(' - ', $coderockz_time);
                $start_time = trim($time_parts[0]);
                $datetime_string .= ' ' . $start_time; // e.g., '14-08-2024 10:00'
            } else {
                $datetime_string .= ' 00:00:00'; // полночь, если время не указано
            }

            // Создаем объект DateTime для корректного парсинга d-m-Y H:i
            $date_obj = DateTime::createFromFormat('d-m-Y H:i', $datetime_string);
            if($date_obj === false) {
                 // Попытка парсинга без времени
                 $date_obj = DateTime::createFromFormat('d-m-Y', $datetime_string);
                 if ($date_obj) $date_obj->setTime(0, 0, 0);
            }


            if ($date_obj === false) {
                $this->log_error("Не удалось преобразовать строку '{$datetime_string}' в объект DateTime. Проверьте формат даты.");
                // Попытка прямого преобразования, если формат другой
                $timestamp = strtotime($datetime_string);
                 if($timestamp === false){
                    $this->log_error("Резервная функция strtotime также не смогла обработать '{$datetime_string}'.");
                    return null;
                }
            } else {
                 $timestamp = $date_obj->getTimestamp();
            }
            
            $this->log_success("Дата и время от CodeRockz ('{$datetime_string}') успешно преобразованы в timestamp: {$timestamp}");
            return $timestamp;
        }

        // 2. Проверяем другие возможные мета-поля
        $legacy_delivery_date = $this->get_order_meta($order, '_delivery_date');
        if (!empty($legacy_delivery_date)) {
            $this->log_info("Найдена дата в устаревшем поле _delivery_date: {$legacy_delivery_date}");
            $timestamp = strtotime($legacy_delivery_date);
            if ($timestamp !== false) {
                $this->log_success("Устаревшая дата успешно преобразована в timestamp: {$timestamp}");
                return $timestamp;
            }
        }

        $this->log_warning("Дата доставки не найдена ни в одном из известных мета-полей для заказа ID: " . $order->get_id());
        return null;
    }

    // HPOS-совместимое получение мета-данных заказа
    private function get_order_meta($order, $key) {
        if (method_exists($order, 'get_meta')) {
            return $order->get_meta($key);
        } else {
            return get_post_meta($order->get_id(), $key, true);
        }
    }

    // Формирует адрес получателя с максимальным количеством деталей
    private function get_recipient_address($order) {
        $address_parts = array(
            $order->get_shipping_address_1(),
            $order->get_shipping_address_2(),
            $order->get_shipping_city(),
            $order->get_shipping_state(),
            $order->get_shipping_postcode(),
        );
        // Если адрес доставки отсутствует, используем платежный
        if (implode('', $address_parts) === '') {
            $address_parts = array(
                $order->get_billing_address_1(),
                $order->get_billing_address_2(),
                $order->get_billing_city(),
                $order->get_billing_state(),
                $order->get_billing_postcode(),
            );
        }
        return trim(preg_replace('/\s+,/', ',', implode(', ', array_filter($address_parts))));
    }
    
    // Выполнение API запроса с улучшенной обработкой ошибок
    private function make_api_request($endpoint, $data = array(), $method = 'POST', $attempt = 1) {
        // Проверка Circuit Breaker
        if ($this->is_circuit_breaker_open()) {
            $this->log_error("Circuit Breaker открыт. Запрос к {$endpoint} заблокирован.");
            return array('success' => false, 'message' => 'API временно недоступен (Circuit Breaker).');
        }

        // Проверка лимита запросов
        if (!$this->check_rate_limit($endpoint)) {
            $this->log_warning("API запрос к {$endpoint} заблокирован лимитом частоты");
            return array('success' => false, 'message' => 'Превышен лимит частоты запросов');
        }
        
        $api_url = get_option('tablecrm_complete_api_url', 'https://app.tablecrm.com/api/v1/');
        $api_key = defined('TABLECRM_COMPLETE_API_KEY') ? TABLECRM_COMPLETE_API_KEY : get_option('tablecrm_complete_api_key');
        
        $url = rtrim($api_url, '/') . '/' . ltrim($endpoint, '/') . '?token=' . $api_key;
        
        $args = array(
            'method' => $method,
            'headers' => array(
                'Content-Type' => 'application/json; charset=utf-8',
                'User-Agent' => 'WordPress-TableCRM-Integration-Complete/1.0.24-fixed'
            ),
            'timeout' => 30,
            'sslverify' => true
        );
        
        if (in_array($method, array('POST', 'PUT')) && !empty($data)) {
            $args['body'] = json_encode($data, JSON_UNESCAPED_UNICODE);
        }
        
        $this->log_info("API запрос: $method $url (попытка $attempt)");
        $response = wp_remote_request($url, $args);
        
        // Обработка ошибок HTTP
        if (is_wp_error($response)) {
            $error_message = $response->get_error_message();
            $this->log_error("Ошибка HTTP запроса (попытка $attempt): $error_message");
            
            $this->handle_circuit_breaker_failure();

            if ($attempt < $this->max_retries) {
                sleep($this->retry_delay * $attempt);
                return $this->make_api_request($endpoint, $data, $method, $attempt + 1);
            }
            
            return array('success' => false, 'message' => $error_message);
        }
        
        $status_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        
        if ($status_code >= 200 && $status_code < 300) {
            $decoded = json_decode($body, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->log_error('Некорректный JSON в ответе API: ' . json_last_error_msg());
                return array('success' => false, 'message' => 'Некорректный JSON в ответе API');
            }
            
            $this->handle_circuit_breaker_success();
            return array('success' => true, 'data' => $decoded, 'status_code' => $status_code);
        } else {
            $error_message = "HTTP $status_code: $body";
            $this->log_error("API ошибка (попытка $attempt): $error_message");
            
            $this->handle_circuit_breaker_failure();

            if ($attempt < $this->max_retries && $status_code >= 500) {
                sleep($this->retry_delay * $attempt);
                return $this->make_api_request($endpoint, $data, $method, $attempt + 1);
            }
            
            return array('success' => false, 'message' => $error_message, 'status_code' => $status_code);
        }
    }
    
    // Проверка лимита запросов
    private function check_rate_limit($endpoint) {
        $current_time = time();
        $limit_key = 'tablecrm_rate_limit_' . md5($endpoint . (is_user_logged_in() ? get_current_user_id() : ($_SERVER['REMOTE_ADDR'] ?? '127.0.0.1')));
        
        $requests = get_transient($limit_key);
        if ($requests === false) {
            $requests = array();
        }
        
        // Удаляем запросы старше минуты
        $requests = array_filter($requests, function($time) use ($current_time) {
            return ($current_time - $time) < 60;
        });
        
        // Максимум 30 запросов в минуту
        if (count($requests) >= 30) {
            return false;
        }
        
        $requests[] = $current_time;
        set_transient($limit_key, $requests, 60);
        
        return true;
    }
    
    // Проверка, открыт ли Circuit Breaker
    private function is_circuit_breaker_open() {
        $state = get_transient('tablecrm_cb_state');
        if ($state === 'OPEN') {
            $open_time = get_transient('tablecrm_cb_open_timestamp');
            if (time() > $open_time + $this->circuit_breaker_timeout) {
                set_transient('tablecrm_cb_state', 'HALF-OPEN', $this->circuit_breaker_timeout);
                $this->log_warning('Circuit Breaker перешел в состояние HALF-OPEN.');
                return false;
            }
            return true;
        }
        return false;
    }

    // Обработка неудачного вызова API
    private function handle_circuit_breaker_failure() {
        $state = get_transient('tablecrm_cb_state');
        if ($state === 'HALF-OPEN') {
            set_transient('tablecrm_cb_state', 'OPEN', $this->circuit_breaker_timeout);
            set_transient('tablecrm_cb_open_timestamp', time(), $this->circuit_breaker_timeout);
            $this->log_error('Тестовый запрос API не удался. Circuit Breaker снова открыт.');
        } else {
            $failures = (int) get_transient('tablecrm_cb_failures');
            $failures++;
            set_transient('tablecrm_cb_failures', $failures, $this->circuit_breaker_timeout);

            if ($failures >= $this->circuit_breaker_threshold) {
                set_transient('tablecrm_cb_state', 'OPEN', $this->circuit_breaker_timeout);
                set_transient('tablecrm_cb_open_timestamp', time(), $this->circuit_breaker_timeout);
                $this->log_error("Превышен порог ошибок API. Circuit Breaker открыт на {$this->circuit_breaker_timeout} секунд.");
            }
        }
    }

    // Обработка успешного вызова API
    private function handle_circuit_breaker_success() {
        delete_transient('tablecrm_cb_failures');
        delete_transient('tablecrm_cb_state');
        delete_transient('tablecrm_cb_open_timestamp');
    }
    
    // Получение блокировки ресурса
    private function acquire_transaction_lock($resource_id, $timeout = 60) {
        $lock_key = 'tablecrm_lock_' . $resource_id;
        // add_transient атомарен и вернет false, если транзиент уже существует.
        if (add_transient($lock_key, true, $timeout)) {
            return true;
        }
        return false;
    }

    // Освобождение блокировки ресурса
    private function release_transaction_lock($resource_id) {
        $lock_key = 'tablecrm_lock_' . $resource_id;
        delete_transient($lock_key);
    }
    
    // Выполнение функции с блокировкой
    private function execute_with_lock($resource_id, $callback, $timeout = 60) {
        if (!$this->acquire_transaction_lock($resource_id, $timeout)) {
            $this->log_warning("Не удалось получить блокировку для {$resource_id}.");
            
            if (strpos($resource_id, 'order_send_') === 0 && function_exists('as_schedule_single_action')) {
                $order_id = (int) str_replace('order_send_', '', $resource_id);
                as_schedule_single_action(time() + 60, 'tablecrm_complete_async_send', array('order_id' => $order_id));
                $this->log_info("Повторная отправка заказа {$order_id} запланирована через 1 минуту.");
            }

            return ['success' => false, 'message' => 'Не удалось получить блокировку ресурса'];
        }
        
        try {
            $result = call_user_func($callback);
            return $result;
        } catch (Exception $e) {
            $this->log_error("Ошибка в заблокированной операции {$resource_id}: " . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        } finally {
            $this->release_transaction_lock($resource_id);
        }
    }
    
    // Логирование
    private function log($message, $level = 'INFO') {
        if (!get_option('tablecrm_complete_debug_mode', 1)) {
            return;
        }

        try {
            $timestamp = current_time('Y-m-d H:i:s');
            $log_entry = "[$timestamp] [$level] $message" . PHP_EOL;
            
            $upload_dir = wp_upload_dir();
            $log_dir = $upload_dir['basedir'] . '/tablecrm-logs';
            if (!file_exists($log_dir)) {
                wp_mkdir_p($log_dir);
            }
            $log_file = $log_dir . '/tablecrm_integration_complete.log';
            
            @file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
            
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("TableCRM Integration: " . trim($log_entry));
            }
        } catch (Exception $e) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("TableCRM Integration logging error: " . $e->getMessage());
            }
        }
    }

    private function log_error($message) { $this->log($message, 'ERROR'); }
    private function log_info($message)  { $this->log($message, 'INFO'); }
    private function log_success($message) { $this->log($message, 'SUCCESS'); }
    private function log_warning($message) { $this->log($message, 'WARNING'); }
    
    // Санитизация
    public function sanitize_api_url($value) {
        if (empty($value)) {
            return 'https://app.tablecrm.com/api/v1/';
        }
        
        $value = esc_url_raw($value);
        if (!filter_var($value, FILTER_VALIDATE_URL)) {
            return 'https://app.tablecrm.com/api/v1/';
        }
        
        return rtrim($value, '/') . '/';
    }
    
    public function sanitize_api_key($value) {
        return sanitize_text_field(trim($value));
    }
    
    public function sanitize_checkbox($value) {
        return !empty($value) ? 1 : 0;
    }
    
    public function sanitize_order_statuses($value) {
        if (!is_array($value) || !function_exists('wc_get_order_statuses')) {
            return array('processing', 'completed');
        }
        
        $allowed_statuses = array_keys(wc_get_order_statuses());
        $allowed_statuses = array_map(function($s) { return str_replace('wc-', '', $s); }, $allowed_statuses);
        
        $sanitized = array();
        foreach ($value as $status) {
            $status = sanitize_key($status);
            if (in_array($status, $allowed_statuses)) {
                $sanitized[] = $status;
            }
        }
        
        return empty($sanitized) ? array('processing', 'completed') : $sanitized;
    }
}

// Инициализация плагина
function initialize_tablecrm_integration_complete() {
    global $tablecrm_integration_complete;
    $tablecrm_integration_complete = new TableCRM_Integration_Complete();
}
add_action('plugins_loaded', 'initialize_tablecrm_integration_complete');