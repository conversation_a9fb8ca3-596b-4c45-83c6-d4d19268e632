# Upload and run full logs check
$server = "bekflom3.beget.tech"
$user = "bekflom3_wz"
$pass = "zl2Shj!e&6ng"

Write-Host "Uploading full logs check script..." -ForegroundColor Green

try {
    $uri = "ftp://$server/public_html/check_all_logs.php"
    $webclient = New-Object System.Net.WebClient
    $webclient.Credentials = New-Object System.Net.NetworkCredential($user, $pass)
    $webclient.UploadFile($uri, "check_all_logs.php")
    Write-Host "Full logs check script uploaded!" -ForegroundColor Green
    
    # Run the check
    Write-Host "Running full logs check..." -ForegroundColor Yellow
    $result = Invoke-WebRequest -Uri "https://mosbuketik.ru/check_all_logs.php" -UseBasicParsing
    Write-Host $result.Content -ForegroundColor White
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

$webclient.Dispose()
Write-Host "Full logs check completed!" -ForegroundColor Green 