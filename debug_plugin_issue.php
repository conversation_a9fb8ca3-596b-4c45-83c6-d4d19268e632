<?php
// Deep debug of TableCRM Complete plugin issues
echo "=== ГЛУБОКАЯ ДИАГНОСТИКА ПЛАГИНА TABLECRM COMPLETE ===\n";

$wp_path = '/home/<USER>/bekflom3/mosbuketik.ru/public_html';
require_once($wp_path . '/wp-config.php');
require_once($wp_path . '/wp-load.php');

echo "WordPress загружен успешно\n\n";

// 1. Check if plugin is really active
echo "=== 1. ПРОВЕРКА АКТИВНОСТИ ПЛАГИНА ===\n";
if (!function_exists('is_plugin_active')) {
    require_once(ABSPATH . 'wp-admin/includes/plugin.php');
}

// Check different possible plugin paths
$possible_paths = array(
    'tablecrm-integration-complete-v1.0.24.php',
    'tablecrm-integration-complete/tablecrm-integration-complete-v1.0.24.php',
    'tablecrm-integration-complete-v1.0.24/tablecrm-integration-complete-v1.0.24.php'
);

$plugin_found = false;
foreach ($possible_paths as $path) {
    if (is_plugin_active($path)) {
        echo "✅ Плагин активен: $path\n";
        $plugin_found = true;
        $active_plugin_path = $path;
        break;
    }
}

if (!$plugin_found) {
    echo "❌ Плагин не найден среди активных!\n";
    
    // Check what plugins are actually active
    $active_plugins = get_option('active_plugins', array());
    echo "Активные плагины:\n";
    foreach ($active_plugins as $plugin) {
        echo "  - $plugin\n";
    }
}

// 2. Check if plugin class exists
echo "\n=== 2. ПРОВЕРКА КЛАССА ПЛАГИНА ===\n";
if (class_exists('TableCRM_Integration_Complete')) {
    echo "✅ Класс TableCRM_Integration_Complete найден\n";
    
    // Check if instance exists
    global $tablecrm_integration_complete;
    if (isset($tablecrm_integration_complete)) {
        echo "✅ Экземпляр плагина создан\n";
    } else {
        echo "❌ Экземпляр плагина НЕ создан\n";
    }
} else {
    echo "❌ Класс TableCRM_Integration_Complete НЕ найден\n";
}

// 3. Check WordPress hooks
echo "\n=== 3. ПРОВЕРКА ХУКОВ WORDPRESS ===\n";
global $wp_filter;

$hooks_to_check = array(
    'admin_menu',
    'admin_init', 
    'init',
    'plugins_loaded'
);

foreach ($hooks_to_check as $hook) {
    if (isset($wp_filter[$hook])) {
        echo "Hook '$hook' зарегистрирован\n";
        
        // Check if our plugin has callbacks on this hook
        foreach ($wp_filter[$hook]->callbacks as $priority => $callbacks) {
            foreach ($callbacks as $callback) {
                if (is_array($callback['function']) && 
                    is_object($callback['function'][0]) && 
                    get_class($callback['function'][0]) === 'TableCRM_Integration_Complete') {
                    echo "  ✅ TableCRM callback найден: {$callback['function'][1]}\n";
                }
            }
        }
    } else {
        echo "Hook '$hook' НЕ зарегистрирован\n";
    }
}

// 4. Try to manually get options
echo "\n=== 4. РУЧНАЯ ПРОВЕРКА ОПЦИЙ ===\n";
$test_options = array(
    'tablecrm_complete_api_url',
    'tablecrm_complete_api_key',
    'tablecrm_complete_organization_id'
);

foreach ($test_options as $option) {
    $value = get_option($option, 'DEFAULT_NOT_FOUND');
    echo "$option: " . ($value === 'DEFAULT_NOT_FOUND' ? 'НЕ НАЙДЕНО' : $value) . "\n";
}

// 5. Try to manually set and get an option
echo "\n=== 5. ТЕСТ СОХРАНЕНИЯ ОПЦИЙ ===\n";
$test_option = 'tablecrm_complete_test_option';
$test_value = 'test_value_' . time();

echo "Попытка сохранить тестовую опцию...\n";
$result = update_option($test_option, $test_value);
echo "Результат update_option: " . ($result ? 'УСПЕХ' : 'ОШИБКА') . "\n";

$retrieved = get_option($test_option, 'NOT_FOUND');
echo "Получено значение: $retrieved\n";

if ($retrieved === $test_value) {
    echo "✅ Сохранение опций работает\n";
} else {
    echo "❌ Проблема с сохранением опций\n";
}

// Clean up
delete_option($test_option);

// 6. Check for PHP errors
echo "\n=== 6. ПРОВЕРКА ОШИБОК PHP ===\n";
$error_log = ini_get('error_log');
if ($error_log && file_exists($error_log)) {
    echo "Файл ошибок: $error_log\n";
    $errors = file_get_contents($error_log);
    $tablecrm_errors = array_filter(explode("\n", $errors), function($line) {
        return strpos($line, 'tablecrm') !== false || strpos($line, 'TableCRM') !== false;
    });
    
    if (!empty($tablecrm_errors)) {
        echo "Найдены ошибки TableCRM:\n";
        foreach (array_slice($tablecrm_errors, -5) as $error) {
            echo "  $error\n";
        }
    } else {
        echo "Ошибки TableCRM не найдены\n";
    }
} else {
    echo "Файл ошибок не найден\n";
}

// 7. Check plugin file exists
echo "\n=== 7. ПРОВЕРКА ФАЙЛОВ ПЛАГИНА ===\n";
$plugin_dir = WP_PLUGIN_DIR;
echo "Директория плагинов: $plugin_dir\n";

$plugin_file = $plugin_dir . '/tablecrm-integration-complete-v1.0.24.php';
if (file_exists($plugin_file)) {
    echo "✅ Файл плагина найден: $plugin_file\n";
    echo "Размер файла: " . filesize($plugin_file) . " байт\n";
    
    // Check if file is readable
    if (is_readable($plugin_file)) {
        echo "✅ Файл читаемый\n";
    } else {
        echo "❌ Файл НЕ читаемый\n";
    }
} else {
    echo "❌ Файл плагина НЕ найден: $plugin_file\n";
    
    // List all files in plugins directory
    echo "Файлы в директории плагинов:\n";
    $files = scandir($plugin_dir);
    foreach ($files as $file) {
        if (strpos($file, 'tablecrm') !== false) {
            echo "  - $file\n";
        }
    }
}

echo "\n=== ДИАГНОСТИКА ЗАВЕРШЕНА ===\n";
?> 