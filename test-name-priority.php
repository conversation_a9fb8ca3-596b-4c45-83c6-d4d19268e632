<?php
/**
 * Тест для проверки приоритета имен: заказчик (billing) должен иметь приоритет над получателем (shipping)
 */

echo "=== Тест приоритета имен: заказчик vs получатель ===\n\n";

// Создаем упрощенную версию класса для тестирования
class NamePriorityTest {
    
    // Копируем логику из метода send_delivery_info
    public function get_recipient_name_for_crm($order) {
        // Получаем имя для CRM - приоритет: заказчик (billing), затем получатель (shipping)
        $billing_first_name = $order['billing_first_name'];
        $billing_last_name = $order['billing_last_name'];
        $recipient_name = trim($billing_first_name . ' ' . $billing_last_name);

        // Если имя заказчика пустое, используем имя из данных доставки
        if (empty($recipient_name)) {
            $shipping_first_name = $order['shipping_first_name'];
            $shipping_last_name = $order['shipping_last_name'];
            $recipient_name = trim($shipping_first_name . ' ' . $shipping_last_name);

            if (!empty($recipient_name)) {
                echo "⚠️ Имя заказчика из платежных данных пустое. Используется имя из данных доставки: \"{$recipient_name}\"\n";
            } else {
                // Если и в данных доставки пусто, используем email
                $email = $order['billing_email'];
                if (!empty($email)) {
                    $recipient_name = $email;
                    echo "⚠️ Имя не найдено ни в платежных, ни в данных доставки. Используется email: {$email}\n";
                } else {
                    $recipient_name = 'Получатель';
                    echo "⚠️ Имя не найдено. Используется стандартное значение: \"Получатель\"\n";
                }
            }
        }
        
        return $recipient_name;
    }
    
    // Копируем исправленный метод get_recipient_name
    public function get_recipient_name_cleaned($order) {
        // ИСПРАВЛЕНО: Сначала получаем имя из платежных данных (заказчик)
        $billing_name = trim($order['billing_first_name'] . ' ' . $order['billing_last_name']);
        if ($billing_name !== '') {
            $name = $billing_name;
        } else {
            // Если в платежных данных нет, берем из данных доставки
            $shipping_name = trim($order['shipping_first_name'] . ' ' . $order['shipping_last_name']);
            if ($shipping_name !== '') {
                $name = $shipping_name;
            } else {
                // Если и там нет, берем email или плейсхолдер
                $email = $order['billing_email'];
                $name = $email !== '' ? $email : 'Получатель';
            }
        }
        
        // Очищаем имя от телефонов и лишних пробелов
        $name = preg_replace('/\s*\+?[78]?[\s\-\(\)\+]*\d[\s\-\(\)\d]*\d\s*/', ' ', $name);
        $name = trim(preg_replace('/\s+/', ' ', $name));
        
        return $name;
    }
}

// Создаем тестовые данные
$test_cases = [
    [
        'name' => 'Тест 1: Есть и заказчик и получатель - должен выбираться заказчик',
        'order' => [
            'billing_first_name' => 'Анна',
            'billing_last_name' => 'Заказчикова',
            'shipping_first_name' => 'Иван',
            'shipping_last_name' => 'Получателев',
            'billing_email' => '<EMAIL>'
        ],
        'expected' => 'Анна Заказчикова'
    ],
    [
        'name' => 'Тест 2: Пустой заказчик, есть получатель - должен выбираться получатель',
        'order' => [
            'billing_first_name' => '',
            'billing_last_name' => '',
            'shipping_first_name' => 'Иван',
            'shipping_last_name' => 'Получателев',
            'billing_email' => '<EMAIL>'
        ],
        'expected' => 'Иван Получателев'
    ],
    [
        'name' => 'Тест 3: Пустые имена, есть email - должен использоваться email',
        'order' => [
            'billing_first_name' => '',
            'billing_last_name' => '',
            'shipping_first_name' => '',
            'shipping_last_name' => '',
            'billing_email' => '<EMAIL>'
        ],
        'expected' => '<EMAIL>'
    ],
    [
        'name' => 'Тест 4: Все пустое - должен использоваться "Получатель"',
        'order' => [
            'billing_first_name' => '',
            'billing_last_name' => '',
            'shipping_first_name' => '',
            'shipping_last_name' => '',
            'billing_email' => ''
        ],
        'expected' => 'Получатель'
    ],
    [
        'name' => 'Тест 5: Только имя заказчика без фамилии',
        'order' => [
            'billing_first_name' => 'Анна',
            'billing_last_name' => '',
            'shipping_first_name' => 'Иван',
            'shipping_last_name' => 'Получателев',
            'billing_email' => '<EMAIL>'
        ],
        'expected' => 'Анна'
    ]
];

$tester = new NamePriorityTest();

foreach ($test_cases as $test) {
    echo "--- {$test['name']} ---\n";
    echo "Данные заказчика (billing): '{$test['order']['billing_first_name']} {$test['order']['billing_last_name']}'\n";
    echo "Данные получателя (shipping): '{$test['order']['shipping_first_name']} {$test['order']['shipping_last_name']}'\n";
    echo "Email: '{$test['order']['billing_email']}'\n";
    
    // Тестируем основной метод (из send_delivery_info)
    $result_crm = $tester->get_recipient_name_for_crm($test['order']);
    echo "Результат (CRM метод): '{$result_crm}'\n";
    
    // Тестируем исправленный метод get_recipient_name
    $result_cleaned = $tester->get_recipient_name_cleaned($test['order']);
    echo "Результат (исправленный метод): '{$result_cleaned}'\n";
    
    // Проверяем результат
    if ($result_crm === $test['expected'] && $result_cleaned === $test['expected']) {
        echo "✅ Тест пройден - оба метода возвращают ожидаемый результат: '{$test['expected']}'\n";
    } elseif ($result_crm === $test['expected']) {
        echo "⚠️ CRM метод правильный, но исправленный метод возвращает: '{$result_cleaned}' вместо '{$test['expected']}'\n";
    } elseif ($result_cleaned === $test['expected']) {
        echo "⚠️ Исправленный метод правильный, но CRM метод возвращает: '{$result_crm}' вместо '{$test['expected']}'\n";
    } else {
        echo "❌ Оба метода неправильные! CRM: '{$result_crm}', Исправленный: '{$result_cleaned}', Ожидалось: '{$test['expected']}'\n";
    }
    
    echo "\n";
}

echo "=== Тест завершен ===\n";
?>
