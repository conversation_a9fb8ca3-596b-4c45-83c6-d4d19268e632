# Analyze delivery date sources
$localFile = "analyze_delivery_date_sources.php"
$remoteFile = "/home/<USER>/bekflom3/mosbuketik.ru/public_html/analyze_delivery_date_sources.php"
$server = "bekflom3.beget.tech"
$username = "bekflom3"

Write-Host "=== ANALYZING DELIVERY DATE SOURCES ===" -ForegroundColor Green

# Upload analysis file
Write-Host "Uploading analysis script..." -ForegroundColor Yellow
scp $localFile "${username}@${server}:${remoteFile}"

if ($LASTEXITCODE -eq 0) {
    Write-Host "Analysis script uploaded successfully" -ForegroundColor Green
    Write-Host ""
    Write-Host "This script will analyze:" -ForegroundColor Cyan
    Write-Host "- All order meta fields related to delivery dates" -ForegroundColor White
    Write-Host "- Popular delivery date plugins" -ForegroundColor White
    Write-Host "- Standard WooCommerce date fields" -ForegroundColor White
    Write-Host "- Recommended fallback strategies" -ForegroundColor White
    Write-Host ""
    Write-Host "Run command: php analyze_delivery_date_sources.php" -ForegroundColor Cyan
} else {
    Write-Host "Error uploading analysis script" -ForegroundColor Red
}

Write-Host "=== COMPLETED ===" -ForegroundColor Green 