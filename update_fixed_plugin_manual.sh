#!/bin/bash

# Скрипт для ручного обновления исправленного плагина TableCRM Integration на сервере
# Исправляет проблему с несуществующей организацией ID 38

echo "=== Обновление плагина TableCRM Integration ==="
echo "Исправление: убираем жестко прописанные ID организации 38"
echo ""

# Показываем содержимое исправленного файла
echo "Проверяем исправления в файле tablecrm-integration.php:"
echo ""
grep -n "organization_id.*get_option" tablecrm-integration.php
grep -n "cashbox_id.*get_option" tablecrm-integration.php  
grep -n "manager_id.*get_option" tablecrm-integration.php
echo ""

echo "✅ Исправления внесены в локальный файл!"
echo ""
echo "СЛЕДУЮЩИЕ ШАГИ:"
echo ""
echo "1. Вручную загрузите файл tablecrm-integration.php на сервер:"
echo "   - Откройте FileZilla или другой FTP клиент"
echo "   - Подключитесь к bekflom3.beget.tech"
echo "   - Перейдите в папку /mosbuketik.ru/public_html/wp-content/plugins/tablecrm-integration/"
echo "   - Замените файл tablecrm-integration.php новым"
echo ""
echo "2. Войдите в админ-панель WordPress: https://mosbuketik.ru/wp-admin/"
echo "3. Перейдите в Настройки → TableCRM Integration"
echo "4. Укажите правильные ID:"
echo ""
echo "   📋 ВАЖНЫЕ НАСТРОЙКИ:"
echo "   - ID Организации: (найдите в TableCRM в выпадающем списке 'Введите организацию')"
echo "   - ID Кассы: 1 (обычно)"  
echo "   - ID Менеджера: (ваш ID пользователя в TableCRM)"
echo ""
echo "5. Нажмите 'Тестировать соединение' для проверки!"
echo ""
echo "🔍 Как найти ID организации в TableCRM:"
echo "   1. Откройте TableCRM → Продажи → Новая продажа"
echo "   2. Начните вводить в поле 'Введите организацию'"
echo "   3. В выпадающем списке найдите вашу организацию"
echo "   4. Запомните её ID (число перед названием)" 