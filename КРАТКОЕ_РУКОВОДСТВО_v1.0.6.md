# 🚀 КРАТКОЕ РУКОВОДСТВО TableCRM Integration v1.0.6

**Дата выпуска**: 6 июня 2025  
**Версия**: 1.0.6 - Production Ready  
**Статус**: ✅ ВСЕ ФУНКЦИИ РЕАЛИЗОВАНЫ

---

## 🚨 ВАЖНО: ЧТО ИСПРАВЛЕНО В v1.0.6

### ❌ **Проблемы предыдущих версий:**
- **Contact Form 7** - не работал (пустой метод)
- **UTM аналитика** - не сохранялась в заказы  
- **Обновление статусов** - была только заглушка
- **API** - не поддерживал PUT запросы

### ✅ **Исправления в v1.0.6:**
- **Contact Form 7** ✅ Полностью работает! Создает лиды в CRM
- **UTM аналитика** ✅ Сохраняет данные в мета-поля заказов
- **Обновление статусов** ✅ Синхронизирует изменения с CRM
- **API** ✅ Поддерживает все HTTP методы (GET, POST, PUT)

---

## ⚡ БЫСТРАЯ УСТАНОВКА

### 1️⃣ **Скачать и установить**
```bash
# Скачать архив
wget tablecrm-integration-fixed-v3-v1.0.6.zip

# Или установить через WordPress админку:
# Плагины → Добавить новый → Загрузить плагин
```

### 2️⃣ **Настроить (2 минуты)**
1. **Настройки** → **TableCRM Integration (Fixed v3)**
2. Заполнить:
   - **API URL**: `https://app.tablecrm.com/api/v1/`
   - **API Key**: ваш ключ из TableCRM
   - **Organization ID**: ID организации (например: 38)
   - **Cashbox ID**: ID кассы (например: 218)
3. **Сохранить настройки**
4. **Тестировать соединение** - должно показать ✅ Успех

### 3️⃣ **Проверить работу**
```bash
# Смотреть логи плагина
tail -f /wp-content/debug.log | grep "TableCRM v3"

# Создать тестовый заказ в WooCommerce
# Проверить появление в TableCRM
```

---

## 🎯 ЧТО ТЕПЕРЬ РАБОТАЕТ

### ✅ **100% рабочие функции:**

#### 📦 **Заказы WooCommerce → TableCRM**
- Автоматическая отправка при создании заказа
- Защита от дублирования через Action Scheduler
- Поддержка всех статусов заказов

#### 📧 **Contact Form 7 → TableCRM (НОВОЕ!)**
- Автоматическое создание лидов из форм
- Сохранение UTM данных в лиды
- Поля формы: `your-name`, `your-email`, `your-phone`, `your-message`

#### 📊 **UTM аналитика (ИСПРАВЛЕНО!)**
- JavaScript скрипт сохраняет UTM в cookies
- Автоматическое сохранение UTM в мета-поля заказов
- Отслеживание источников трафика

#### 🔄 **Обновление статусов (НОВОЕ!)**
- Синхронизация изменений статусов заказов с TableCRM
- Отправка данных об оплате
- Автоматическое создание заказа если его нет в CRM

---

## 🔧 ДОПОЛНИТЕЛЬНЫЕ НАСТРОЙКИ

### 📧 **Для Contact Form 7:**
1. **Включить CF7 интеграцию** в настройках плагина
2. Убедиться что поля формы называются:
   - `your-name` - имя
   - `your-email` - email  
   - `your-phone` - телефон
   - `your-message` - сообщение

### 📊 **Для UTM аналитики:**
1. Убедиться что файл `utm-tracking.js` загружается на сайте
2. UTM параметры автоматически сохраняются в cookies
3. При создании заказа UTM данные попадают в мета-поля

### 🔄 **Для обновления статусов:**
1. Функция работает автоматически
2. При изменении статуса заказа данные отправляются в TableCRM
3. Если заказа нет в CRM - создается новый

---

## 🛠️ УСТРАНЕНИЕ ПРОБЛЕМ

### ❌ **Заказы не отправляются**
**Решение:**
1. Проверить API ключ в настройках
2. Протестировать соединение
3. Посмотреть логи: `tail -f /wp-content/debug.log | grep "TableCRM"`

### ❌ **Contact Form 7 не работает**
**Решение:**
1. Включить интеграцию CF7 в настройках плагина
2. Проверить названия полей в форме
3. Убедиться что CF7 плагин активен

### ❌ **UTM данные не сохраняются**
**Решение:**
1. Проверить загрузку файла `utm-tracking.js`
2. Открыть консоль браузера - должны быть сообщения "TableCRM: Сохранен UTM..."
3. Проверить cookies в браузере

### ❌ **Дублирование заказов**
**Решение:**
1. Убедиться что WooCommerce ≥ 8.5
2. Проверить что Action Scheduler работает
3. В v1.0.6 эта проблема решена автоматически

---

## 📊 МОНИТОРИНГ

### 🔍 **Проверка логов**
```bash
# Основные логи плагина
tail -f /wp-content/debug.log | grep "TableCRM v3"

# Логи успешных отправок
grep "SUCCESS" /wp-content/debug.log | grep "TableCRM"

# Логи ошибок
grep "ERROR" /wp-content/debug.log | grep "TableCRM"
```

### 📈 **Action Scheduler**
1. **WooCommerce** → **Статус** → **Scheduled Actions**
2. Искать задачи с группами:
   - `tablecrm_orders` - заказы
   - `tablecrm_cf7_leads` - лиды CF7
   - `tablecrm_order_updates` - обновления заказов

---

## 📋 ТРЕБОВАНИЯ

### Минимальные:
- **WordPress**: ≥ 5.0
- **WooCommerce**: ≥ 8.5 (обязательно!)
- **PHP**: ≥ 7.4
- **TableCRM**: активный аккаунт с API ключом

### Для дополнительных функций:
- **Contact Form 7**: ≥ 5.0 (для интеграции форм)
- **JavaScript**: включен в браузере (для UTM отслеживания)

---

## 🎉 ЗАКЛЮЧЕНИЕ

**TableCRM Integration v1.0.6** - это полностью функциональная интеграция между WordPress/WooCommerce и TableCRM с поддержкой:

✅ **Автоматической отправки заказов**  
✅ **Интеграции с Contact Form 7**  
✅ **UTM аналитики**  
✅ **Обновления статусов заказов**  
✅ **Защиты от дублирования**  

**Все заявленные функции теперь работают на 100%!**

---

**Готово к использованию в продакшене!** 🚀

*Последнее обновление: 6 июня 2025* 