# Upload and run test for order 21780
$server = "bekflom3.beget.tech"
$user = "bekflom3_wz"
$pass = "zl2Shj!e&6ng"

Write-Host "Uploading test script for order 21780..." -ForegroundColor Green

try {
    $uri = "ftp://$server/public_html/test_order_21780_again.php"
    $webclient = New-Object System.Net.WebClient
    $webclient.Credentials = New-Object System.Net.NetworkCredential($user, $pass)
    $webclient.UploadFile($uri, "test_order_21780_again.php")
    Write-Host "Test script uploaded!" -ForegroundColor Green
    
    # Run the test
    Write-Host "Running test for order 21780..." -ForegroundColor Yellow
    $result = Invoke-WebRequest -Uri "https://mosbuketik.ru/test_order_21780_again.php" -UseBasicParsing
    Write-Host $result.Content -ForegroundColor White
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

$webclient.Dispose()
Write-Host "Test completed!" -ForegroundColor Green 