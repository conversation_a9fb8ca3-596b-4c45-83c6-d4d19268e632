<?php
/**
 * Финальный тест для проверки устранения путаницы между заказчиком и получателем
 */

echo "=== ФИНАЛЬНЫЙ ТЕСТ: Устранение путаницы заказчик vs получатель ===\n\n";

echo "🎯 ЦЕЛЬ: Убедиться, что приоритет везде одинаковый:\n";
echo "   1️⃣ ЗАКАЗЧИК (billing) - первый приоритет\n";
echo "   2️⃣ ПОЛУЧАТЕЛЬ (shipping) - второй приоритет\n";
echo "   3️⃣ EMAIL - третий приоритет\n";
echo "   4️⃣ 'Получатель' - fallback\n\n";

// Тестовые сценарии
$scenarios = [
    [
        'name' => '🔥 КРИТИЧЕСКИЙ ТЕСТ: Заказчик ≠ Получатель',
        'description' => 'Самый важный случай - когда заказчик и получатель разные люди',
        'order' => [
            'billing_first_name' => 'Анна',
            'billing_last_name' => 'Заказчикова',
            'shipping_first_name' => 'Иван', 
            'shipping_last_name' => 'Получателев',
            'billing_email' => '<EMAIL>'
        ],
        'expected_priority' => 'billing',
        'expected_name' => 'Анна Заказчикова',
        'explanation' => 'Должен выбираться ЗАКАЗЧИК (billing), а не получатель (shipping)'
    ],
    [
        'name' => '⚠️ FALLBACK ТЕСТ: Пустой заказчик',
        'description' => 'Когда заказчик пустой, должен использоваться получатель',
        'order' => [
            'billing_first_name' => '',
            'billing_last_name' => '',
            'shipping_first_name' => 'Мария',
            'shipping_last_name' => 'Получательница',
            'billing_email' => '<EMAIL>'
        ],
        'expected_priority' => 'shipping',
        'expected_name' => 'Мария Получательница',
        'explanation' => 'Fallback на получателя (shipping) когда заказчик пустой'
    ],
    [
        'name' => '📧 EMAIL ТЕСТ: Пустые имена',
        'description' => 'Когда и заказчик и получатель пустые',
        'order' => [
            'billing_first_name' => '',
            'billing_last_name' => '',
            'shipping_first_name' => '',
            'shipping_last_name' => '',
            'billing_email' => '<EMAIL>'
        ],
        'expected_priority' => 'email',
        'expected_name' => '<EMAIL>',
        'explanation' => 'Fallback на email когда все имена пустые'
    ]
];

// Симулируем исправленную логику
function get_name_with_priority($order) {
    // 1️⃣ ЗАКАЗЧИК (billing) - первый приоритет
    $billing_name = trim($order['billing_first_name'] . ' ' . $order['billing_last_name']);
    if (!empty($billing_name)) {
        return [
            'name' => $billing_name,
            'source' => 'billing',
            'priority' => 1,
            'description' => 'Заказчик (платежные данные)'
        ];
    }
    
    // 2️⃣ ПОЛУЧАТЕЛЬ (shipping) - второй приоритет  
    $shipping_name = trim($order['shipping_first_name'] . ' ' . $order['shipping_last_name']);
    if (!empty($shipping_name)) {
        return [
            'name' => $shipping_name,
            'source' => 'shipping', 
            'priority' => 2,
            'description' => 'Получатель (данные доставки)'
        ];
    }
    
    // 3️⃣ EMAIL - третий приоритет
    if (!empty($order['billing_email'])) {
        return [
            'name' => $order['billing_email'],
            'source' => 'email',
            'priority' => 3,
            'description' => 'Email'
        ];
    }
    
    // 4️⃣ FALLBACK
    return [
        'name' => 'Получатель',
        'source' => 'fallback',
        'priority' => 4,
        'description' => 'Стандартное значение'
    ];
}

// Запускаем тесты
foreach ($scenarios as $i => $scenario) {
    echo str_repeat("=", 70) . "\n";
    echo "ТЕСТ " . ($i + 1) . ": {$scenario['name']}\n";
    echo str_repeat("=", 70) . "\n";
    echo "📝 Описание: {$scenario['description']}\n\n";
    
    echo "📊 Входные данные:\n";
    echo "   👤 Заказчик (billing): '{$scenario['order']['billing_first_name']} {$scenario['order']['billing_last_name']}'\n";
    echo "   📦 Получатель (shipping): '{$scenario['order']['shipping_first_name']} {$scenario['order']['shipping_last_name']}'\n";
    echo "   📧 Email: '{$scenario['order']['billing_email']}'\n\n";
    
    $result = get_name_with_priority($scenario['order']);
    
    echo "🎯 Результат:\n";
    echo "   ✅ Выбранное имя: '{$result['name']}'\n";
    echo "   🔍 Источник: {$result['description']}\n";
    echo "   📊 Приоритет: {$result['priority']}\n\n";
    
    echo "🧪 Проверка ожиданий:\n";
    echo "   Ожидалось: '{$scenario['expected_name']}' из источника '{$scenario['expected_priority']}'\n";
    echo "   Получено: '{$result['name']}' из источника '{$result['source']}'\n";
    
    if ($result['name'] === $scenario['expected_name'] && $result['source'] === $scenario['expected_priority']) {
        echo "   🎉 ТЕСТ ПРОЙДЕН! ✅\n";
    } else {
        echo "   💥 ТЕСТ ПРОВАЛЕН! ❌\n";
        echo "   🚨 ОБНАРУЖЕНА ПУТАНИЦА!\n";
    }
    
    echo "\n💡 Объяснение: {$scenario['explanation']}\n\n";
}

echo str_repeat("=", 70) . "\n";
echo "🏁 ИТОГОВЫЙ РЕЗУЛЬТАТ\n";
echo str_repeat("=", 70) . "\n";

// Проверяем все тесты
$all_passed = true;
foreach ($scenarios as $scenario) {
    $result = get_name_with_priority($scenario['order']);
    if ($result['name'] !== $scenario['expected_name'] || $result['source'] !== $scenario['expected_priority']) {
        $all_passed = false;
        break;
    }
}

if ($all_passed) {
    echo "🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ! ПУТАНИЦА УСТРАНЕНА! ✅\n\n";
    echo "✨ Приоритет работает правильно:\n";
    echo "   1️⃣ Заказчик (billing) → 2️⃣ Получатель (shipping) → 3️⃣ Email → 4️⃣ Fallback\n\n";
    echo "🔧 Исправления в коде:\n";
    echo "   ✅ Переименована переменная: \$recipient_name → \$customer_name\n";
    echo "   ✅ Исправлен метод: get_recipient_name() → get_customer_name_for_crm()\n";
    echo "   ✅ Обновлены комментарии для ясности\n";
    echo "   ✅ Единый приоритет во всех методах\n";
} else {
    echo "💥 ЕСТЬ ПРОБЛЕМЫ! ПУТАНИЦА НЕ УСТРАНЕНА! ❌\n";
    echo "🔍 Требуется дополнительная проверка кода\n";
}

echo "\n=== ТЕСТ ЗАВЕРШЕН ===\n";
?>
