<?php
// Check settings and test order sending
echo "=== ПРОВЕРКА НАСТРОЕК И ТЕСТ ОТПРАВКИ ===\n";

$wp_path = '/home/<USER>/bekflom3/mosbuketik.ru/public_html';
require_once($wp_path . '/wp-config.php');
require_once($wp_path . '/wp-load.php');

echo "WordPress загружен успешно\n\n";

// Check settings
echo "=== ТЕКУЩИЕ НАСТРОЙКИ ===\n";
$enable_orders = get_option('tablecrm_complete_enable_orders', 0);
$trigger_statuses = get_option('tablecrm_complete_order_statuses', array());

echo "Отправка заказов включена: " . ($enable_orders ? 'ДА' : 'НЕТ') . "\n";
echo "Статусы для отправки: " . implode(', ', $trigger_statuses) . "\n";
echo "Количество статусов: " . count($trigger_statuses) . "\n";

// Check if pending is in the list
if (in_array('pending', $trigger_statuses)) {
    echo "✅ Статус 'pending' ВКЛЮЧЕН в список для отправки\n";
} else {
    echo "❌ Статус 'pending' НЕ ВКЛЮЧЕН в список для отправки\n";
}

// Test order
$test_order_id = 21773;
$order = wc_get_order($test_order_id);

if ($order) {
    echo "\n=== ИНФОРМАЦИЯ О ЗАКАЗЕ #$test_order_id ===\n";
    echo "Статус заказа: " . $order->get_status() . "\n";
    echo "Дата создания: " . $order->get_date_created()->format('Y-m-d H:i:s') . "\n";
    
    $existing_doc_id = get_post_meta($test_order_id, '_tablecrm_complete_document_id', true);
    if ($existing_doc_id) {
        echo "Уже отправлен: Document ID $existing_doc_id\n";
    } else {
        echo "Не отправлен в TableCRM\n";
    }
    
    // Check if status should trigger sending
    $should_send = in_array($order->get_status(), $trigger_statuses);
    echo "Должен ли отправляться: " . ($should_send ? 'ДА' : 'НЕТ') . "\n";
    
    if ($should_send && !$existing_doc_id) {
        echo "\n=== ТЕСТ ПРЯМОГО ВЫЗОВА МЕТОДА ===\n";
        
        global $tablecrm_integration_complete;
        if (isset($tablecrm_integration_complete)) {
            echo "Вызываем handle_new_order напрямую...\n";
            $tablecrm_integration_complete->handle_new_order($test_order_id);
            
            // Check result
            $new_doc_id = get_post_meta($test_order_id, '_tablecrm_complete_document_id', true);
            if ($new_doc_id) {
                echo "✅ УСПЕХ! Заказ отправлен, Document ID: $new_doc_id\n";
            } else {
                echo "❌ Заказ не отправлен\n";
            }
        } else {
            echo "❌ Плагин не загружен\n";
        }
    }
}

echo "\n=== ПРОВЕРКА ЗАВЕРШЕНА ===\n";
?> 